<template>
  <div class="app-container">
    <el-card class="demo-card">
      <template #header>
        <h3>批量输入组件演示</h3>
        <p class="demo-description">
          演示订单信息查询页面中的批量输入功能，支持订单号、客户订单号、自定义单号的批量输入和验证。
        </p>
      </template>

      <!-- 演示表单 -->
      <el-form :model="demoForm" label-width="120px" class="demo-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订单号">
              <batch-input
                v-model="demoForm.orderNo"
                label="订单号"
                placeholder="支持批量输入"
                :textarea-placeholder="orderNoConfig.placeholder"
                :tip-text="orderNoConfig.tipText"
                :validator="orderNoConfig.validator"
                separator=","
                :unique="true"
                @change="handleOrderNoChange"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="客户订单号">
              <batch-input
                v-model="demoForm.customerOrderNo"
                label="客户订单号"
                placeholder="支持批量输入"
                :textarea-placeholder="customerOrderNoConfig.placeholder"
                :tip-text="customerOrderNoConfig.tipText"
                :validator="customerOrderNoConfig.validator"
                separator=","
                :unique="true"
                @change="handleCustomerOrderNoChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="自定义单号">
              <batch-input
                v-model="demoForm.customOrderNo"
                label="自定义单号"
                placeholder="支持批量输入"
                :textarea-placeholder="customOrderNoConfig.placeholder"
                :tip-text="customOrderNoConfig.tipText"
                :validator="customOrderNoConfig.validator"
                separator=","
                :unique="true"
                @change="handleCustomOrderNoChange"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="渠道单号">
              <batch-input
                v-model="demoForm.channelOrderNo"
                label="渠道单号"
                placeholder="支持批量输入"
                :textarea-placeholder="channelOrderNoConfig.placeholder"
                :tip-text="channelOrderNoConfig.tipText"
                :validator="channelOrderNoConfig.validator"
                separator=","
                :unique="true"
                @change="handleChannelOrderNoChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="运单号">
              <batch-input
                v-model="demoForm.waybillNo"
                label="运单号"
                placeholder="支持批量输入"
                :textarea-placeholder="waybillNoConfig.placeholder"
                :tip-text="waybillNoConfig.tipText"
                :validator="waybillNoConfig.validator"
                separator=","
                :unique="true"
                @change="handleWaybillNoChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">模拟查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleFillSample">填充示例数据</el-button>
        </el-form-item>
      </el-form>

      <!-- 当前表单数据展示 -->
      <el-divider content-position="left">当前表单数据</el-divider>
      <div class="form-data-display">
        <pre>{{ JSON.stringify(demoForm, null, 2) }}</pre>
      </div>

      <!-- 功能说明 -->
      <el-divider content-position="left">功能说明</el-divider>
      <div class="feature-description">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never" class="feature-card">
              <template #header>
                <h4>🎯 核心功能</h4>
              </template>
              <ul>
                <li>点击输入框旁边的编辑按钮打开批量输入弹窗</li>
                <li>支持多行输入，每行一个数据</li>
                <li>支持逗号分隔，一行多个数据</li>
                <li>支持混合格式，既有多行又有逗号分隔</li>
                <li>支持粘贴多行数据，自动解析格式</li>
                <li>实时验证输入格式，显示错误信息</li>
                <li>自动去重，统计有效数据数量</li>
                <li>确认后将数据合并到原输入框</li>
              </ul>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card shadow="never" class="feature-card">
              <template #header>
                <h4>✅ 验证规则</h4>
              </template>
              <ul>
                <li><strong>订单号</strong>：3-50位，字母数字下划线中划线</li>
                <li><strong>客户订单号</strong>：3-100位，字母数字下划线中划线点号</li>
                <li><strong>自定义单号</strong>：1-100位，字母数字下划线中划线点号</li>
                <li><strong>渠道单号</strong>：3-50位，字母数字下划线中划线</li>
                <li><strong>运单号</strong>：8-30位，字母数字</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card shadow="never" class="feature-card">
              <template #header>
                <h4>🚀 使用示例</h4>
              </template>
              <div class="usage-example">
                <p><strong>1. 单个输入：</strong>直接在输入框中输入单个订单号</p>
                <p><strong>2. 多行输入：</strong>点击编辑按钮，在弹窗中输入多行数据：</p>
                <div class="example-code">
                  <pre>ORD123456789
ORD987654321
ORD555666777</pre>
                </div>
                <p><strong>3. 逗号分隔：</strong>在同一行内用逗号分隔多个数据：</p>
                <div class="example-code">
                  <pre>ORD123456789,ORD987654321,ORD555666777</pre>
                </div>
                <p><strong>4. 混合格式：</strong>既有多行，又有逗号分隔的组合：</p>
                <div class="example-code">
                  <pre>ORD123456789
ORD987654321,ORD555666777
ORD888999000</pre>
                </div>
                <p><strong>5. 粘贴数据：</strong>从Excel或其他地方复制多行数据，直接粘贴到文本框中</p>
                <p><strong>6. 格式验证：</strong>系统会自动验证每个数据的格式，显示错误信息</p>
                <p><strong>7. 去重处理：</strong>自动去除重复数据，显示最终有效数据数量</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { VALIDATORS } from '@/utils/orderValidators'

// 表单数据
const demoForm = ref({
  orderNo: '',
  customerOrderNo: '',
  customOrderNo: '',
  channelOrderNo: '',
  waybillNo: ''
})

// 验证器配置
const orderNoConfig = VALIDATORS.orderNo
const customerOrderNoConfig = VALIDATORS.customerOrderNo
const customOrderNoConfig = VALIDATORS.customOrderNo
const channelOrderNoConfig = VALIDATORS.channelOrderNo
const waybillNoConfig = VALIDATORS.waybillNo

// 事件处理
function handleOrderNoChange(value) {
  console.log('订单号变化:', value)
}

function handleCustomerOrderNoChange(value) {
  console.log('客户订单号变化:', value)
}

function handleCustomOrderNoChange(value) {
  console.log('自定义单号变化:', value)
}

function handleChannelOrderNoChange(value) {
  console.log('渠道单号变化:', value)
}

function handleWaybillNoChange(value) {
  console.log('运单号变化:', value)
}

function handleSearch() {
  console.log('模拟查询:', demoForm.value)
  ElMessage.success('查询请求已发送（模拟）')
}

function handleReset() {
  demoForm.value = {
    orderNo: '',
    customerOrderNo: '',
    customOrderNo: '',
    channelOrderNo: '',
    waybillNo: ''
  }
  ElMessage.info('表单已重置')
}

function handleFillSample() {
  demoForm.value = {
    orderNo: 'ORD123456789,ORD987654321,ORD555666777',
    customerOrderNo: 'CUST-ORD-123456,CUST.ORD.789012',
    customOrderNo: 'CUSTOM123,CUSTOM.456,CUSTOM_789',
    channelOrderNo: 'CH123456789,CH987654321',
    waybillNo: '12345678901234,ABC123456789'
  }
  ElMessage.success('示例数据已填充（混合格式）')
}
</script>

<style lang="scss" scoped>
.demo-card {
  margin-bottom: 20px;
  
  .demo-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.demo-form {
  margin-bottom: 20px;
}

.form-data-display {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  
  pre {
    margin: 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #303133;
  }
}

.feature-description {
  .feature-card {
    height: 100%;
    
    h4 {
      margin: 0;
      color: #303133;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #606266;
        line-height: 1.5;
        
        strong {
          color: #303133;
        }
      }
    }
  }
  
  .usage-example {
    p {
      margin-bottom: 12px;
      color: #606266;
      line-height: 1.6;
      
      strong {
        color: #303133;
      }
    }
    
    .example-code {
      background: #f5f7fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      margin: 12px 0;
      
      pre {
        margin: 0;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
        color: #303133;
        line-height: 1.5;
      }
    }
  }
}
</style>
