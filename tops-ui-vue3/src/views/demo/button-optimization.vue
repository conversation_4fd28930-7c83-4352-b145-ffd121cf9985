<template>
  <div class="app-container">
    <el-card class="mb-4">
      <template #header>
        <h3>按钮样式与布局优化演示</h3>
      </template>
      
      <!-- 1. 按钮对比度优化展示 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <h4>1. 按钮对比度优化（符合WCAG 2.0 AA标准）</h4>
          <div class="button-demo-group">
            <el-button type="primary">主要按钮</el-button>
            <el-button type="success">成功按钮</el-button>
            <el-button type="warning">警告按钮</el-button>
            <el-button type="danger">危险按钮</el-button>
            <el-button type="info">信息按钮</el-button>
            <el-button>默认按钮</el-button>
            <el-button link type="primary">链接按钮</el-button>
            <el-button text type="primary">文本按钮</el-button>
          </div>
        </el-col>
      </el-row>

      <!-- 2. 表格操作按钮优化展示 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <h4>2. 表格操作按钮优化</h4>
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column prop="email" label="邮箱" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                  {{ scope.row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="传统操作列" width="300">
              <template #default="scope">
                <!-- 传统方式：容易换行 -->
                <el-button link type="primary" icon="View">查看</el-button>
                <el-button link type="primary" icon="Edit">编辑</el-button>
                <el-button link type="danger" icon="Delete">删除</el-button>
                <el-button link type="primary" icon="Key">重置密码</el-button>
                <el-button link type="primary" icon="Setting">设置权限</el-button>
              </template>
            </el-table-column>
            <el-table-column label="优化后操作列" width="180" class="table-column-actions">
              <template #default="scope">
                <!-- 优化方式：使用TableActions组件 -->
                <table-actions
                  :actions="getTableActions(scope.row)"
                  :row="scope.row"
                  compact
                  :max-primary="2"
                  @action="handleTableAction"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>

      <!-- 3. 图标按钮展示 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <h4>3. 图标按钮组件</h4>
          <div class="button-demo-group">
            <icon-button icon="Edit" tooltip="编辑" type="primary" />
            <icon-button icon="Delete" tooltip="删除" danger />
            <icon-button icon="View" tooltip="查看" />
            <icon-button icon="Download" tooltip="下载" success />
            <icon-button icon="Upload" tooltip="上传" warning />
            <icon-button icon="Setting" tooltip="设置" compact />
            <icon-button icon="Refresh" tooltip="刷新" circle />
          </div>
        </el-col>
      </el-row>

      <!-- 4. 响应式按钮组展示 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <h4>4. 响应式按钮组</h4>
          <div style="border: 1px dashed #ddd; padding: 16px; resize: horizontal; overflow: auto; min-width: 200px; max-width: 100%;">
            <p style="margin-bottom: 12px; color: #666; font-size: 12px;">
              拖拽右下角调整容器宽度，观察按钮组的响应式变化
            </p>
            <responsive-button-group
              :buttons="responsiveButtons"
              @button-click="handleResponsiveButtonClick"
            />
          </div>
        </el-col>
      </el-row>

      <!-- 5. 按钮尺寸和样式变体 -->
      <el-row class="mb-4">
        <el-col :span="24">
          <h4>5. 按钮尺寸和样式变体</h4>
          <div class="button-demo-group">
            <el-button size="large" type="primary">大按钮</el-button>
            <el-button type="primary">默认按钮</el-button>
            <el-button size="small" type="primary">小按钮</el-button>
            <el-button class="el-button--mini" type="primary">超小按钮</el-button>
            <el-button class="el-button--compact" type="primary">紧凑按钮</el-button>
          </div>
          <div class="button-demo-group mt-2">
            <el-button class="btn-ghost" type="primary">幽灵按钮</el-button>
            <el-button class="btn-gradient">渐变按钮</el-button>
            <el-button round type="primary">圆角按钮</el-button>
            <el-button circle type="primary" icon="Plus"></el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Search, Refresh, Download, Upload, Plus, Edit, Delete, 
  View, Key, Setting, CircleCheck 
} from '@element-plus/icons-vue'
import TableActions from '@/components/TableActions'
import IconButton from '@/components/IconButton'
import ResponsiveButtonGroup from '@/components/ResponsiveButtonGroup'

// 表格数据
const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: 1 },
  { id: 2, name: '李四', email: '<EMAIL>', status: 0 },
  { id: 3, name: '王五', email: '<EMAIL>', status: 1 }
])

// 响应式按钮组配置
const responsiveButtons = ref([
  {
    label: '查询',
    icon: Search,
    type: 'primary',
    key: 'search'
  },
  {
    label: '重置',
    icon: Refresh,
    key: 'reset'
  },
  {
    label: '导出',
    icon: Download,
    key: 'export'
  },
  {
    label: '导入',
    icon: Upload,
    key: 'import'
  },
  {
    label: '新增',
    icon: Plus,
    type: 'success',
    key: 'add'
  }
])

// 获取表格操作按钮配置
function getTableActions(row) {
  return [
    {
      label: '查看',
      key: 'view',
      icon: 'View',
      type: 'primary'
    },
    {
      label: '编辑',
      key: 'edit',
      icon: 'Edit',
      type: 'primary'
    },
    {
      label: '删除',
      key: 'delete',
      icon: 'Delete',
      type: 'danger'
    },
    {
      label: '重置密码',
      key: 'resetPwd',
      icon: 'Key'
    },
    {
      label: '设置权限',
      key: 'setPermission',
      icon: 'Setting'
    }
  ]
}

// 处理表格操作
function handleTableAction({ action, row }) {
  ElMessage.success(`执行操作：${action}，用户：${row.name}`)
}

// 处理响应式按钮点击
function handleResponsiveButtonClick({ button }) {
  ElMessage.success(`点击了：${button.label}`)
}
</script>

<style lang="scss" scoped>
.button-demo-group {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  
  .el-button {
    margin: 0;
  }
}

.mb-4 {
  margin-bottom: 24px;
}

.mt-2 {
  margin-top: 8px;
}

h4 {
  color: #262626;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

// 演示容器样式
.el-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}
</style>
