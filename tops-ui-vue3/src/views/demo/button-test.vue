<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <h3>按钮换行问题修复测试</h3>
      </template>
      
      <el-table :data="testData" style="width: 100%">
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- 测试：传统方式（会换行） -->
        <el-table-column label="传统方式（换行）" width="300">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" size="small">修改</el-button>
            <el-button link type="danger" icon="Delete" size="small">删除</el-button>
            <el-button link type="primary" icon="Key" size="small">重置密码</el-button>
            <el-button link type="primary" icon="Setting" size="small">分配角色</el-button>
          </template>
        </el-table-column>
        
        <!-- 测试：优化后方式（不换行） -->
        <el-table-column label="优化后（一行）" width="220" class="table-column-actions" fixed="right">
          <template #default="scope">
            <table-actions
              :actions="getTestActions(scope.row)"
              :row="scope.row"
              compact
              :max-primary="2"
              @action="handleTestAction"
            />
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 调试信息 -->
      <div style="margin-top: 20px; padding: 16px; background: #f5f5f5; border-radius: 4px;">
        <h4>调试信息：</h4>
        <p><strong>期望效果：</strong>优化后的操作列应该显示2个主要按钮（修改、删除）+ 1个"更多"下拉菜单</p>
        <p><strong>问题排查：</strong>如果按钮仍然换行，请检查浏览器开发者工具中的CSS样式</p>
        <p><strong>样式文件：</strong>table-actions-fix.scss 包含了强制修复样式</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 测试数据
const testData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', status: 1 },
  { id: 2, name: '李四', email: '<EMAIL>', status: 0 },
  { id: 3, name: '王五', email: '<EMAIL>', status: 1 },
  { id: 4, name: '赵六', email: '<EMAIL>', status: 1 }
])

// 获取测试操作按钮
function getTestActions(row) {
  return [
    {
      label: '修改',
      key: 'edit',
      icon: 'Edit',
      type: 'primary',
      tooltip: '修改用户信息'
    },
    {
      label: '删除',
      key: 'delete',
      icon: 'Delete',
      type: 'danger',
      tooltip: '删除用户'
    },
    {
      label: '重置密码',
      key: 'resetPwd',
      icon: 'Key',
      tooltip: '重置用户密码'
    },
    {
      label: '分配角色',
      key: 'authRole',
      icon: 'Setting',
      tooltip: '分配用户角色'
    }
  ]
}

// 处理测试操作
function handleTestAction({ action, row }) {
  ElMessage.success(`执行操作：${action}，用户：${row.name}`)
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

h4 {
  margin: 0 0 8px 0;
  color: #333;
}

p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}
</style>
