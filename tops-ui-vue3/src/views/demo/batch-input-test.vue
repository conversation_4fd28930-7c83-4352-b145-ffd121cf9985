<template>
  <div class="app-container">
    <el-card class="test-card">
      <template #header>
        <h3>批量输入混合格式测试</h3>
        <p class="test-description">
          测试批量输入组件对多行和逗号分隔混合格式的解析能力
        </p>
      </template>

      <!-- 测试输入区域 -->
      <div class="test-section">
        <h4>测试输入</h4>
        <batch-input
          v-model="testValue"
          label="测试字段"
          placeholder="支持混合格式输入"
          :textarea-placeholder="testPlaceholder"
          :tip-text="testTipText"
          :validator="testValidator"
          separator=","
          :unique="true"
          @change="handleChange"
        />
      </div>

      <!-- 解析结果展示 -->
      <div class="result-section">
        <h4>解析结果</h4>
        <div class="result-display">
          <div class="result-item">
            <span class="label">原始输入：</span>
            <span class="value">{{ testValue || '（空）' }}</span>
          </div>
          <div class="result-item">
            <span class="label">解析后数组：</span>
            <span class="value">{{ parsedArray }}</span>
          </div>
          <div class="result-item">
            <span class="label">数据数量：</span>
            <span class="value">{{ parsedArray.length }}</span>
          </div>
        </div>
      </div>

      <!-- 测试用例 -->
      <div class="test-cases">
        <h4>预设测试用例</h4>
        <el-row :gutter="20">
          <el-col :span="8" v-for="(testCase, index) in testCases" :key="index">
            <el-card shadow="hover" class="test-case-card">
              <template #header>
                <h5>{{ testCase.name }}</h5>
              </template>
              <div class="test-case-content">
                <div class="test-input">
                  <strong>输入：</strong>
                  <pre>{{ testCase.input }}</pre>
                </div>
                <div class="expected-output">
                  <strong>期望输出：</strong>
                  <div>{{ testCase.expected.join(', ') }}</div>
                </div>
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="runTestCase(testCase)"
                  style="margin-top: 10px;"
                >
                  运行测试
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 测试结果 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h4>测试结果</h4>
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="{ 'success': result.passed, 'failed': !result.passed }"
          >
            <div class="result-header">
              <span class="test-name">{{ result.testName }}</span>
              <span class="test-status">{{ result.passed ? '✅ 通过' : '❌ 失败' }}</span>
            </div>
            <div class="result-details" v-if="!result.passed">
              <div>期望：{{ result.expected.join(', ') }}</div>
              <div>实际：{{ result.actual.join(', ') }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 测试数据
const testValue = ref('')
const testResults = ref([])

// 测试配置
const testPlaceholder = `请输入测试数据，支持以下格式：
单行：TEST123
多行：TEST123
      TEST456
逗号分隔：TEST123,TEST456
混合格式：TEST123
          TEST456,TEST789`

const testTipText = '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；测试数据只能包含字母和数字，长度3-20位'

// 简单的测试验证器
const testValidator = (value) => {
  if (!value || typeof value !== 'string') {
    return '测试数据不能为空'
  }
  
  const trimmed = value.trim()
  
  if (trimmed.length < 3 || trimmed.length > 20) {
    return '测试数据长度必须在3-20位之间'
  }
  
  const validPattern = /^[a-zA-Z0-9]+$/
  if (!validPattern.test(trimmed)) {
    return '测试数据只能包含字母和数字'
  }
  
  return true
}

// 解析结果
const parsedArray = computed(() => {
  if (!testValue.value) return []
  return testValue.value.split(',').map(v => v.trim()).filter(v => v)
})

// 测试用例
const testCases = [
  {
    name: '单行输入',
    input: 'TEST123',
    expected: ['TEST123']
  },
  {
    name: '多行输入',
    input: 'TEST123\nTEST456\nTEST789',
    expected: ['TEST123', 'TEST456', 'TEST789']
  },
  {
    name: '逗号分隔',
    input: 'TEST123,TEST456,TEST789',
    expected: ['TEST123', 'TEST456', 'TEST789']
  },
  {
    name: '混合格式1',
    input: 'TEST123\nTEST456,TEST789\nTEST000',
    expected: ['TEST123', 'TEST456', 'TEST789', 'TEST000']
  },
  {
    name: '混合格式2',
    input: 'TEST111,TEST222\nTEST333\nTEST444,TEST555,TEST666',
    expected: ['TEST111', 'TEST222', 'TEST333', 'TEST444', 'TEST555', 'TEST666']
  },
  {
    name: '包含空行',
    input: 'TEST123\n\nTEST456\n\nTEST789',
    expected: ['TEST123', 'TEST456', 'TEST789']
  },
  {
    name: '包含空格',
    input: ' TEST123 , TEST456 \n TEST789 ',
    expected: ['TEST123', 'TEST456', 'TEST789']
  },
  {
    name: '重复数据',
    input: 'TEST123,TEST456,TEST123\nTEST456',
    expected: ['TEST123', 'TEST456']
  }
]

// 事件处理
function handleChange(value) {
  console.log('值变化:', value)
}

// 运行单个测试用例
function runTestCase(testCase) {
  // 模拟批量输入组件的解析逻辑
  const parseInput = (input) => {
    if (!input.trim()) return []
    
    const allItems = []
    const lines = input.split('\n')
    
    lines.forEach(line => {
      const trimmedLine = line.trim()
      if (!trimmedLine) return
      
      const items = trimmedLine.split(',')
      items.forEach(item => {
        const trimmedItem = item.trim()
        if (trimmedItem) {
          allItems.push(trimmedItem)
        }
      })
    })
    
    // 去重
    const uniqueItems = [...new Set(allItems)]
    return uniqueItems
  }
  
  const actual = parseInput(testCase.input)
  const passed = JSON.stringify(actual.sort()) === JSON.stringify(testCase.expected.sort())
  
  const result = {
    testName: testCase.name,
    passed,
    expected: testCase.expected,
    actual
  }
  
  testResults.value.unshift(result)
  
  if (passed) {
    ElMessage.success(`测试 "${testCase.name}" 通过`)
  } else {
    ElMessage.error(`测试 "${testCase.name}" 失败`)
  }
  
  // 设置测试值以便查看
  testValue.value = actual.join(',')
}

// 运行所有测试
function runAllTests() {
  testResults.value = []
  testCases.forEach(testCase => {
    setTimeout(() => runTestCase(testCase), 100)
  })
}
</script>

<style lang="scss" scoped>
.test-card {
  .test-description {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.test-section {
  margin-bottom: 30px;
  
  h4 {
    margin-bottom: 16px;
    color: #303133;
  }
}

.result-section {
  margin-bottom: 30px;
  
  .result-display {
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
    
    .result-item {
      display: flex;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-weight: 500;
        color: #606266;
        min-width: 100px;
      }
      
      .value {
        color: #303133;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }
}

.test-cases {
  margin-bottom: 30px;
  
  .test-case-card {
    height: 100%;
    
    h5 {
      margin: 0;
      color: #303133;
    }
    
    .test-case-content {
      .test-input {
        margin-bottom: 12px;
        
        pre {
          background: #f5f7fa;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          padding: 8px;
          margin: 4px 0 0 0;
          font-size: 12px;
          line-height: 1.4;
        }
      }
      
      .expected-output {
        margin-bottom: 12px;
        font-size: 13px;
        
        div {
          margin-top: 4px;
          color: #606266;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
      }
    }
  }
}

.test-results {
  .results-list {
    .result-item {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 8px;
      
      &.success {
        border-color: #67c23a;
        background: #f0f9ff;
      }
      
      &.failed {
        border-color: #f56c6c;
        background: #fef0f0;
      }
      
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .test-name {
          font-weight: 500;
          color: #303133;
        }
        
        .test-status {
          font-size: 14px;
        }
      }
      
      .result-details {
        font-size: 13px;
        color: #606266;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        
        div {
          margin-bottom: 4px;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
