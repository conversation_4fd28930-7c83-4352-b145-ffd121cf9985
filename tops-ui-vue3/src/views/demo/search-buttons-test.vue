<template>
  <div class="app-container">
    <el-card class="mb-4">
      <template #header>
        <h3>搜索按钮换行问题修复测试</h3>
      </template>
      
      <!-- 1. 修复前的效果（容易换行） -->
      <div class="test-section">
        <h4>1. 修复前的效果（容易换行）</h4>
        <el-form :model="queryParams1" ref="queryRef1" :inline="true" label-width="80px">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="queryParams1.userName" placeholder="请输入用户名" style="width: 200px" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input v-model="queryParams1.phonenumber" placeholder="请输入手机号码" style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams1.status" placeholder="用户状态" style="width: 200px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange1"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <!-- 传统方式：容易换行 -->
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleSearch1">搜索</el-button>
            <el-button icon="Refresh" @click="handleReset1">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 2. 使用SearchButtons组件修复后的效果 -->
      <div class="test-section">
        <h4>2. 使用SearchButtons组件修复后的效果</h4>
        <el-form :model="queryParams2" ref="queryRef2" :inline="true" label-width="80px">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="queryParams2.userName" placeholder="请输入用户名" style="width: 200px" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input v-model="queryParams2.phonenumber" placeholder="请输入手机号码" style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams2.status" placeholder="用户状态" style="width: 200px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange2"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <!-- 使用SearchButtons组件：不会换行 -->
          <search-buttons @search="handleSearch2" @reset="handleReset2" />
        </el-form>
      </div>

      <!-- 3. 使用CSS类修复的效果 -->
      <div class="test-section">
        <h4>3. 使用CSS类修复的效果</h4>
        <el-form :model="queryParams3" ref="queryRef3" :inline="true" label-width="80px">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="queryParams3.userName" placeholder="请输入用户名" style="width: 200px" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input v-model="queryParams3.phonenumber" placeholder="请输入手机号码" style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams3.status" placeholder="用户状态" style="width: 200px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange3"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <!-- 使用CSS类修复 -->
          <el-form-item class="form-buttons">
            <div class="btn-group-no-wrap">
              <el-button type="primary" icon="Search" @click="handleSearch3">搜索</el-button>
              <el-button icon="Refresh" @click="handleReset3">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 4. 使用SearchForm组件的完整解决方案 -->
      <div class="test-section">
        <h4>4. 使用SearchForm组件的完整解决方案</h4>
        <search-form 
          v-model="queryParams4" 
          :show-search="true"
          @search="handleSearch4" 
          @reset="handleReset4"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="queryParams4.userName" placeholder="请输入用户名" style="width: 200px" />
          </el-form-item>
          <el-form-item label="手机号码" prop="phonenumber">
            <el-input v-model="queryParams4.phonenumber" placeholder="请输入手机号码" style="width: 200px" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams4.status" placeholder="用户状态" style="width: 200px">
              <el-option label="启用" value="1" />
              <el-option label="禁用" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange4"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
        </search-form>
      </div>

      <!-- 测试说明 -->
      <div class="test-info">
        <h4>测试说明：</h4>
        <ul>
          <li><strong>调整浏览器窗口宽度</strong>，观察不同方案下按钮的换行情况</li>
          <li><strong>方案1（传统方式）</strong>：在窗口较窄时，按钮容易换行</li>
          <li><strong>方案2（SearchButtons组件）</strong>：按钮始终保持在一行，自动右对齐</li>
          <li><strong>方案3（CSS类修复）</strong>：通过CSS强制按钮不换行</li>
          <li><strong>方案4（SearchForm组件）</strong>：完整的响应式解决方案</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance()

// 测试数据
const queryParams1 = ref({
  userName: '',
  phonenumber: '',
  status: ''
})

const queryParams2 = ref({
  userName: '',
  phonenumber: '',
  status: ''
})

const queryParams3 = ref({
  userName: '',
  phonenumber: '',
  status: ''
})

const queryParams4 = ref({
  userName: '',
  phonenumber: '',
  status: ''
})

const dateRange1 = ref([])
const dateRange2 = ref([])
const dateRange3 = ref([])
const dateRange4 = ref([])

// 处理搜索
function handleSearch1() {
  ElMessage.success('方案1：传统方式搜索')
}

function handleSearch2() {
  ElMessage.success('方案2：SearchButtons组件搜索')
}

function handleSearch3() {
  ElMessage.success('方案3：CSS类修复搜索')
}

function handleSearch4() {
  ElMessage.success('方案4：SearchForm组件搜索')
}

// 处理重置
function handleReset1() {
  proxy.resetForm('queryRef1')
  dateRange1.value = []
  ElMessage.info('方案1：传统方式重置')
}

function handleReset2() {
  proxy.resetForm('queryRef2')
  dateRange2.value = []
  ElMessage.info('方案2：SearchButtons组件重置')
}

function handleReset3() {
  proxy.resetForm('queryRef3')
  dateRange3.value = []
  ElMessage.info('方案3：CSS类修复重置')
}

function handleReset4() {
  dateRange4.value = []
  ElMessage.info('方案4：SearchForm组件重置')
}
</script>

<style lang="scss" scoped>
.test-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
  
  h4 {
    margin: 0 0 16px 0;
    color: #303133;
    font-weight: 500;
  }
}

.test-info {
  margin-top: 24px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  
  h4 {
    margin: 0 0 12px 0;
    color: #1e40af;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #374151;
      
      strong {
        color: #1e40af;
      }
    }
  }
}

.mb-4 {
  margin-bottom: 24px;
}
</style>
