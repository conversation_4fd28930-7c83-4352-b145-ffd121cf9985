<template>
  <el-tooltip 
    :content="tooltip || label" 
    placement="top"
    :disabled="!tooltip && !label"
  >
    <el-button
      :type="type"
      :size="size"
      :icon="icon"
      :disabled="disabled"
      :loading="loading"
      :link="link"
      :text="text"
      :bg="bg"
      :circle="circle"
      :round="round"
      @click="handleClick"
      :class="[
        'icon-button',
        {
          'icon-button--danger': danger,
          'icon-button--success': success,
          'icon-button--warning': warning,
          'icon-button--compact': compact
        }
      ]"
    >
      <slot>{{ showLabel ? label : '' }}</slot>
    </el-button>
  </el-tooltip>
</template>

<script setup>
const props = defineProps({
  // 按钮类型
  type: {
    type: String,
    default: 'primary'
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 图标
  icon: {
    type: [String, Object],
    required: true
  },
  // 标签文字
  label: {
    type: String,
    default: ''
  },
  // 工具提示
  tooltip: {
    type: String,
    default: ''
  },
  // 是否显示标签
  showLabel: {
    type: Boolean,
    default: false
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 是否为链接按钮
  link: {
    type: Boolean,
    default: true
  },
  // 是否为文本按钮
  text: {
    type: Boolean,
    default: false
  },
  // 是否有背景
  bg: {
    type: Boolean,
    default: false
  },
  // 是否为圆形
  circle: {
    type: Boolean,
    default: false
  },
  // 是否为圆角
  round: {
    type: Boolean,
    default: false
  },
  // 紧凑模式
  compact: {
    type: Boolean,
    default: false
  },
  // 快捷样式
  danger: {
    type: Boolean,
    default: false
  },
  success: {
    type: Boolean,
    default: false
  },
  warning: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

function handleClick(event) {
  if (props.disabled || props.loading) return
  emit('click', event)
}
</script>

<style lang="scss" scoped>
.icon-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &--compact {
    padding: 4px;
    min-width: 28px;
    height: 28px;
    
    .el-icon {
      margin: 0;
    }
  }
  
  &--danger {
    &:hover {
      color: #ff4d4f;
      background-color: rgba(255, 77, 79, 0.06);
    }
  }
  
  &--success {
    &:hover {
      color: #52c41a;
      background-color: rgba(82, 196, 26, 0.06);
    }
  }
  
  &--warning {
    &:hover {
      color: #faad14;
      background-color: rgba(250, 173, 20, 0.06);
    }
  }
}

// 在表格中的特殊样式
.el-table .icon-button {
  margin: 0 2px;
  
  &:first-child {
    margin-left: 0;
  }
  
  &:last-child {
    margin-right: 0;
  }
}
</style>
