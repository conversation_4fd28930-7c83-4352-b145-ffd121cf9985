<template>
  <div 
    ref="containerRef" 
    class="responsive-button-group"
    :class="{
      'responsive-button-group--compact': isCompact,
      'responsive-button-group--vertical': vertical
    }"
  >
    <!-- 可见按钮 -->
    <template v-for="(button, index) in visibleButtons" :key="`visible-${index}`">
      <el-button
        v-bind="button"
        @click="handleButtonClick(button, $event)"
        :class="getButtonClass(button)"
      >
        <el-icon v-if="button.icon && (isCompact || button.iconOnly)">
          <component :is="button.icon" />
        </el-icon>
        <span v-if="!isCompact || !button.icon || !button.iconOnly">
          {{ button.label }}
        </span>
      </el-button>
    </template>

    <!-- 折叠的按钮（下拉菜单） -->
    <el-dropdown 
      v-if="hiddenButtons.length > 0"
      trigger="click"
      placement="bottom-end"
      @command="handleDropdownCommand"
    >
      <el-button 
        :size="size"
        type="primary"
        :link="true"
      >
        <el-icon><more-filled /></el-icon>
        <span v-if="!isCompact">更多</span>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(button, index) in hiddenButtons"
            :key="`hidden-${index}`"
            :command="button"
            :disabled="button.disabled"
            :divided="button.divided"
          >
            <el-icon v-if="button.icon" class="dropdown-icon">
              <component :is="button.icon" />
            </el-icon>
            {{ button.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { MoreFilled } from '@element-plus/icons-vue'

const props = defineProps({
  // 按钮配置数组
  buttons: {
    type: Array,
    default: () => []
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 最大可见按钮数量
  maxVisible: {
    type: Number,
    default: 4
  },
  // 紧凑模式阈值（像素）
  compactThreshold: {
    type: Number,
    default: 300
  },
  // 垂直布局
  vertical: {
    type: Boolean,
    default: false
  },
  // 间距
  gap: {
    type: String,
    default: '8px'
  }
})

const emit = defineEmits(['button-click'])

const containerRef = ref(null)
const containerWidth = ref(0)
const isCompact = ref(false)

// 计算可见和隐藏的按钮
const visibleButtons = computed(() => {
  const maxVisible = isCompact.value ? Math.min(props.maxVisible, 2) : props.maxVisible
  return props.buttons.slice(0, maxVisible)
})

const hiddenButtons = computed(() => {
  const maxVisible = isCompact.value ? Math.min(props.maxVisible, 2) : props.maxVisible
  return props.buttons.slice(maxVisible)
})

// 监听容器宽度变化
const resizeObserver = ref(null)

function updateContainerWidth() {
  if (containerRef.value) {
    containerWidth.value = containerRef.value.offsetWidth
    isCompact.value = containerWidth.value < props.compactThreshold
  }
}

function handleButtonClick(button, event) {
  if (button.disabled) return
  
  if (typeof button.handler === 'function') {
    button.handler(event)
  } else {
    emit('button-click', {
      button,
      event
    })
  }
}

function handleDropdownCommand(button) {
  handleButtonClick(button)
}

function getButtonClass(button) {
  return [
    'responsive-btn',
    {
      'responsive-btn--icon-only': isCompact.value && button.icon && button.iconOnly !== false
    }
  ]
}

onMounted(() => {
  nextTick(() => {
    updateContainerWidth()
    
    if (window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(updateContainerWidth)
      resizeObserver.value.observe(containerRef.value)
    } else {
      window.addEventListener('resize', updateContainerWidth)
    }
  })
})

onUnmounted(() => {
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  } else {
    window.removeEventListener('resize', updateContainerWidth)
  }
})
</script>

<style lang="scss" scoped>
.responsive-button-group {
  display: flex;
  align-items: center;
  gap: v-bind(gap);
  flex-wrap: nowrap;
  overflow: hidden;
  
  &--compact {
    gap: 4px;
    
    .responsive-btn {
      padding: 4px 8px;
      min-width: auto;
      
      &--icon-only {
        padding: 4px;
        width: 28px;
        height: 28px;
        
        .el-icon {
          margin: 0;
        }
      }
    }
  }
  
  &--vertical {
    flex-direction: column;
    align-items: stretch;
    
    .el-button {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .el-button + .el-button {
    margin-left: 0;
  }
}

// 下拉菜单图标样式
.dropdown-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

// 在表格中的特殊样式
.el-table .responsive-button-group {
  white-space: nowrap;
  
  &--compact {
    .el-button {
      font-size: 12px;
    }
  }
}

// 响应式断点
@media (max-width: 768px) {
  .responsive-button-group {
    gap: 4px;
    
    .el-button {
      padding: 4px 8px;
      font-size: 12px;
    }
  }
}
</style>
