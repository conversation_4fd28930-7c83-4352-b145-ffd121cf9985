<template>
  <el-form-item class="search-buttons-wrapper">
    <div class="search-buttons">
      <el-button 
        type="primary" 
        icon="Search" 
        @click="handleSearch"
        :loading="loading"
        :size="size"
      >
        搜索
      </el-button>
      <el-button 
        icon="Refresh" 
        @click="handleReset"
        :size="size"
      >
        重置
      </el-button>
      
      <!-- 额外按钮插槽 -->
      <slot></slot>
    </div>
  </el-form-item>
</template>

<script setup>
const props = defineProps({
  // 按钮尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 搜索按钮加载状态
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['search', 'reset'])

// 处理搜索
function handleSearch() {
  emit('search')
}

// 处理重置
function handleReset() {
  emit('reset')
}
</script>

<style lang="scss" scoped>
.search-buttons-wrapper {
  margin-left: auto !important;
  margin-right: 0 !important;
  
  .search-buttons {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
    
    .el-button {
      margin: 0 !important;
      flex-shrink: 0 !important;
    }
  }
}

// 确保在表单中正确显示
:deep(.el-form--inline) {
  .search-buttons-wrapper {
    display: inline-flex !important;
    align-items: center !important;
  }
}

// 响应式处理
@media (max-width: 768px) {
  .search-buttons-wrapper {
    width: 100% !important;
    margin-left: 0 !important;
    
    .search-buttons {
      justify-content: center !important;
      
      .el-button {
        flex: 1 !important;
        max-width: 120px !important;
      }
    }
  }
}
</style>
