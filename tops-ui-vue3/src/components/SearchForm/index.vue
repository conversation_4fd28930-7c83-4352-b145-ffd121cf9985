<template>
  <el-form 
    :model="modelValue" 
    ref="formRef" 
    :inline="true" 
    v-show="showSearch"
    :label-width="labelWidth"
    class="search-form"
    :class="{ 'search-form--responsive': responsive }"
  >
    <!-- 搜索条件插槽 -->
    <slot></slot>
    
    <!-- 搜索按钮区域 -->
    <el-form-item class="search-buttons">
      <div class="button-group">
        <el-button 
          type="primary" 
          icon="Search" 
          @click="handleSearch"
          :loading="loading"
        >
          搜索
        </el-button>
        <el-button 
          icon="Refresh" 
          @click="handleReset"
        >
          重置
        </el-button>
        
        <!-- 额外按钮插槽 -->
        <slot name="buttons"></slot>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'

const props = defineProps({
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({})
  },
  // 是否显示搜索表单
  showSearch: {
    type: Boolean,
    default: true
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '68px'
  },
  // 是否启用响应式布局
  responsive: {
    type: Boolean,
    default: true
  },
  // 搜索按钮加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 重置时需要清空的日期范围字段
  dateRangeFields: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

const { proxy } = getCurrentInstance()
const formRef = ref(null)

// 处理搜索
function handleSearch() {
  emit('search')
}

// 处理重置
function handleReset() {
  // 重置表单
  if (formRef.value) {
    proxy.resetForm(formRef.value)
  }
  
  // 清空日期范围字段
  props.dateRangeFields.forEach(field => {
    if (window[field]) {
      window[field].value = []
    }
  })
  
  emit('reset')
}

// 暴露表单引用
defineExpose({
  formRef
})
</script>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 16px;
  
  // 搜索按钮区域
  .search-buttons {
    margin-left: auto;
    
    .button-group {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-wrap: nowrap;
      white-space: nowrap;
      
      .el-button {
        margin: 0;
        flex-shrink: 0;
      }
    }
  }
  
  // 响应式布局
  &--responsive {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 16px;
    
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 0;
      flex-shrink: 0;
      
      // 确保搜索按钮始终在右侧
      &.search-buttons {
        margin-left: auto;
        margin-bottom: 16px;
      }
    }
    
    // 在小屏幕上调整布局
    @media (max-width: 1200px) {
      .el-form-item {
        &:not(.search-buttons) {
          flex: 1;
          min-width: 200px;
        }
        
        &.search-buttons {
          flex: none;
          width: 100%;
          margin-left: 0;
          
          .button-group {
            justify-content: flex-start;
          }
        }
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      
      .el-form-item {
        width: 100%;
        margin-left: 0;
        
        &.search-buttons {
          .button-group {
            justify-content: center;
            
            .el-button {
              flex: 1;
              max-width: 120px;
            }
          }
        }
      }
    }
  }
}

// 确保按钮不换行的强制样式
.search-buttons {
  .button-group {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
    
    .el-button {
      margin: 0 !important;
      flex-shrink: 0 !important;
    }
  }
}

// 兼容原有的inline表单样式
:deep(.el-form--inline) {
  .el-form-item {
    margin-right: 16px;
    margin-bottom: 16px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
