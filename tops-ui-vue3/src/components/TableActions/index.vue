<template>
  <div class="table-actions" :class="{ 'table-actions--compact': compact }">
    <!-- 主要操作按钮（始终显示） -->
    <template v-for="(action, index) in primaryActions" :key="`primary-${index}`">
      <el-tooltip 
        v-if="action.tooltip" 
        :content="action.tooltip" 
        placement="top"
        :disabled="!compact"
      >
        <el-button
          :type="action.type || 'primary'"
          :icon="action.icon"
          :size="compact ? 'small' : 'default'"
          :link="action.link !== false"
          :disabled="action.disabled"
          @click="handleAction(action, $event)"
          :class="{ 'action-btn--icon-only': compact && action.icon }"
        >
          <span v-if="!compact || !action.icon">{{ action.label }}</span>
        </el-button>
      </el-tooltip>
      <el-button
        v-else
        :type="action.type || 'primary'"
        :icon="action.icon"
        :size="compact ? 'small' : 'default'"
        :link="action.link !== false"
        :disabled="action.disabled"
        @click="handleAction(action, $event)"
        :class="{ 'action-btn--icon-only': compact && action.icon }"
      >
        <span v-if="!compact || !action.icon">{{ action.label }}</span>
      </el-button>
    </template>

    <!-- 更多操作下拉菜单 -->
    <el-dropdown 
      v-if="secondaryActions.length > 0" 
      trigger="click"
      placement="bottom-end"
      @command="handleDropdownAction"
    >
      <el-button 
        :size="compact ? 'small' : 'default'"
        :link="true"
        type="primary"
        :icon="compact ? 'More' : undefined"
      >
        <span v-if="!compact">更多</span>
        <el-icon v-if="!compact" class="el-icon--right"><arrow-down /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(action, index) in secondaryActions"
            :key="`secondary-${index}`"
            :command="action"
            :disabled="action.disabled"
            :divided="action.divided"
            :icon="action.icon"
          >
            {{ action.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ArrowDown, More } from '@element-plus/icons-vue'

const props = defineProps({
  // 操作配置数组
  actions: {
    type: Array,
    default: () => []
  },
  // 紧凑模式 - 在表格中使用
  compact: {
    type: Boolean,
    default: false
  },
  // 最大主要操作数量
  maxPrimary: {
    type: Number,
    default: 3
  },
  // 行数据
  row: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['action'])

// 计算主要操作和次要操作
const primaryActions = computed(() => {
  return props.actions.slice(0, props.maxPrimary).filter(action => 
    typeof action.visible === 'function' ? action.visible(props.row) : action.visible !== false
  )
})

const secondaryActions = computed(() => {
  return props.actions.slice(props.maxPrimary).filter(action => 
    typeof action.visible === 'function' ? action.visible(props.row) : action.visible !== false
  )
})

// 处理操作点击
function handleAction(action, event) {
  if (action.disabled) return
  
  if (typeof action.handler === 'function') {
    action.handler(props.row, event)
  } else {
    emit('action', {
      action: action.key || action.label,
      row: props.row,
      event
    })
  }
}

// 处理下拉菜单操作
function handleDropdownAction(action) {
  handleAction(action)
}
</script>

<style lang="scss" scoped>
.table-actions {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  justify-content: center;
  width: 100%;

  &--compact {
    gap: 2px !important;

    .el-button {
      padding: 2px 6px !important;
      min-width: auto !important;
      font-size: 12px !important;
      margin: 0 !important;
      flex-shrink: 0;

      &.action-btn--icon-only {
        padding: 4px !important;
        width: 28px !important;
        height: 28px !important;

        .el-icon {
          margin: 0 !important;
        }
      }

      // 确保按钮文字不换行
      span {
        white-space: nowrap;
      }
    }
  }

  .el-button + .el-button {
    margin-left: 0 !important;
  }

  // 下拉菜单样式
  .el-dropdown {
    .el-button {
      padding: 2px 6px !important;
      margin: 0 !important;
    }
  }
}

// 确保在表格单元格中不换行
:deep(.el-table .table-actions) {
  white-space: nowrap !important;
  overflow: visible !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

// 下拉菜单样式优化
:deep(.el-dropdown-menu__item) {
  padding: 8px 16px;
  
  &:hover {
    background-color: #f5f5f5;
    color: #1890ff;
  }
  
  &.is-disabled {
    color: #bfbfbf;
    cursor: not-allowed;
  }
}
</style>
