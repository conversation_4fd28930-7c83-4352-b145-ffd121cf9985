<template>
  <div class="batch-input-wrapper">
    <!-- 原始输入框 -->
    <div class="input-with-button">
      <el-input
        v-model="inputValue"
        v-bind="$attrs"
        :placeholder="placeholder"
        @input="handleInput"
        @change="handleChange"
        class="batch-input-field"
      />
      
      <!-- 批量输入按钮 -->
      <el-button
        type="primary"
        :icon="Edit"
        circle
        size="small"
        class="batch-button"
        @click="openBatchDialog"
        :title="buttonTitle"
      />
    </div>

    <!-- 批量输入弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :before-close="handleClose"
      append-to-body
      class="batch-input-dialog"
    >
      <div class="batch-input-content">
        <!-- 输入提示 -->
        <div class="input-tips">
          <el-alert
            :title="tipText"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 批量输入区域 -->
        <div class="input-area">
          <el-input
            v-model="batchText"
            type="textarea"
            :rows="12"
            :placeholder="textareaPlaceholder"
            :maxlength="maxLength"
            show-word-limit
            resize="none"
            @input="handleBatchInput"
            class="batch-textarea"
          />
        </div>

        <!-- 统计信息 -->
        <div class="input-stats">
          <div class="stats-item">
            <span class="label">输入行数：</span>
            <span class="value">{{ lineCount }}</span>
          </div>
          <div class="stats-item">
            <span class="label">解析项数：</span>
            <span class="value">{{ itemCount }}</span>
          </div>
          <div class="stats-item">
            <span class="label">有效数据：</span>
            <span class="value">{{ validCount }}</span>
          </div>
          <div class="stats-item" v-if="duplicateCount > 0">
            <span class="label">重复数据：</span>
            <span class="value warning">{{ duplicateCount }}</span>
          </div>
        </div>

        <!-- 格式验证结果 -->
        <div class="validation-result" v-if="validationErrors.length > 0">
          <el-alert
            title="格式验证失败"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              <div class="error-list">
                <div v-for="(error, index) in validationErrors.slice(0, 5)" :key="index" class="error-item">
                  {{ error.message }}
                  <span class="error-value">（值：{{ error.value }}）</span>
                </div>
                <div v-if="validationErrors.length > 5" class="error-more">
                  还有{{ validationErrors.length - 5 }}个错误...
                </div>
              </div>
            </template>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clearInput">清空</el-button>
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmInput"
            :disabled="validCount === 0"
          >
            确认（{{ validCount }}条）
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Edit } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 输入值
  modelValue: {
    type: String,
    default: ''
  },
  // 字段标签（用于弹窗标题）
  label: {
    type: String,
    default: '批量输入'
  },
  // 输入框占位符
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  // 文本域占位符
  textareaPlaceholder: {
    type: String,
    default: '请输入内容，每行一个，支持粘贴多行数据'
  },
  // 最大长度
  maxLength: {
    type: Number,
    default: 10000
  },
  // 分隔符（用于合并多个值）
  separator: {
    type: String,
    default: ','
  },
  // 验证规则
  validator: {
    type: Function,
    default: null
  },
  // 是否去重
  unique: {
    type: Boolean,
    default: true
  },
  // 提示文本
  tipText: {
    type: String,
    default: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；支持粘贴多行数据，系统会自动解析'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const dialogVisible = ref(false)
const batchText = ref('')
const inputValue = ref('')
const validationErrors = ref([])

// 计算属性
const dialogTitle = computed(() => `批量输入 - ${props.label}`)
const buttonTitle = computed(() => `批量输入${props.label}`)

// 解析输入文本 - 支持多行和逗号分隔的混合格式
const parsedLines = computed(() => {
  if (!batchText.value.trim()) return []

  const allItems = []
  const lines = batchText.value.split('\n')

  lines.forEach((line, lineIndex) => {
    const trimmedLine = line.trim()
    if (!trimmedLine) return

    // 按逗号分割每一行，支持逗号分隔的多个值
    const items = trimmedLine.split(',')

    items.forEach((item, itemIndex) => {
      const trimmedItem = item.trim()
      if (trimmedItem) {
        allItems.push({
          line: lineIndex + 1,
          itemIndex: itemIndex + 1,
          original: item,
          value: trimmedItem,
          lineContent: trimmedLine, // 保存原始行内容用于错误提示
          isFromCommaDelimited: items.length > 1 // 标记是否来自逗号分隔
        })
      }
    })
  })

  return allItems
})

// 验证数据
const validatedData = computed(() => {
  const errors = []
  const validItems = []

  parsedLines.value.forEach(item => {
    if (props.validator) {
      const result = props.validator(item.value)
      if (result === true) {
        validItems.push(item)
      } else {
        // 构建更详细的错误信息
        const errorMessage = result || '格式不正确'
        const locationInfo = item.isFromCommaDelimited
          ? `第${item.line}行第${item.itemIndex}项`
          : `第${item.line}行`

        errors.push({
          line: item.line,
          itemIndex: item.itemIndex,
          value: item.value,
          message: `${locationInfo}：${errorMessage}`,
          isFromCommaDelimited: item.isFromCommaDelimited
        })
      }
    } else {
      validItems.push(item)
    }
  })

  validationErrors.value = errors
  return validItems
})

// 去重后的数据
const uniqueData = computed(() => {
  if (!props.unique) return validatedData.value
  
  const seen = new Set()
  return validatedData.value.filter(item => {
    if (seen.has(item.value)) {
      return false
    }
    seen.add(item.value)
    return true
  })
})

// 统计信息
const lineCount = computed(() => {
  if (!batchText.value.trim()) return 0
  return batchText.value.split('\n').filter(line => line.trim()).length
})
const itemCount = computed(() => parsedLines.value.length)
const validCount = computed(() => uniqueData.value.length)
const duplicateCount = computed(() => validatedData.value.length - uniqueData.value.length)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  inputValue.value = newVal || ''
}, { immediate: true })

// 处理输入
function handleInput(value) {
  emit('update:modelValue', value)
}

function handleChange(value) {
  emit('change', value)
}

// 处理批量输入
function handleBatchInput() {
  // 实时验证，无需额外处理
}

// 打开批量输入弹窗
function openBatchDialog() {
  // 将当前值转换为多行文本
  if (inputValue.value) {
    const values = inputValue.value.split(props.separator).map(v => v.trim()).filter(v => v)
    batchText.value = values.join('\n')
  } else {
    batchText.value = ''
  }

  dialogVisible.value = true
}

// 确认输入
function confirmInput() {
  if (validCount.value === 0) {
    ElMessage.warning('没有有效的数据')
    return
  }
  
  const values = uniqueData.value.map(item => item.value)
  const result = values.join(props.separator)
  
  inputValue.value = result
  emit('update:modelValue', result)
  emit('change', result)
  
  dialogVisible.value = false
  
  ElMessage.success(`成功输入${validCount.value}条数据`)
}

// 清空输入
function clearInput() {
  batchText.value = ''
  validationErrors.value = []
}

// 关闭弹窗
function handleClose() {
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.batch-input-wrapper {
  // 重置可能被全局样式影响的属性
  * {
    box-sizing: border-box;
  }

  .input-with-button {
    display: flex;
    align-items: center;
    gap: 8px;

    .batch-input-field {
      flex: 1;
      // 防止被全局样式覆盖，确保在不同页面中宽度一致
      width: auto !important;
      min-width: 0;
    }

    .batch-button {
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      // 确保按钮样式不被覆盖
      margin: 0 !important;
      padding: 0;
    }
  }
}

.batch-input-dialog {
  // 弹窗样式隔离和重置
  .el-dialog__body {
    // 确保弹窗内容不受外部样式影响
    * {
      box-sizing: border-box;
    }
  }

  .batch-input-content {
    .input-tips {
      margin-bottom: 16px;
    }

    .input-area {
      margin-bottom: 16px;

      .batch-textarea {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        // 确保文本域样式正确
        width: 100% !important;

        :deep(.el-textarea__inner) {
          line-height: 1.5;
          font-size: 13px;
          // 防止被全局样式覆盖
          width: 100% !important;
          min-height: 300px;
        }
      }
    }
    
    .input-stats {
      display: flex;
      gap: 24px;
      margin-bottom: 16px;
      padding: 12px;
      background: #f5f7fa;
      border-radius: 4px;
      
      .stats-item {
        display: flex;
        align-items: center;
        
        .label {
          color: #606266;
          font-size: 14px;
        }
        
        .value {
          font-weight: 500;
          margin-left: 4px;
          
          &.warning {
            color: #e6a23c;
          }
        }
      }
    }
    
    .validation-result {
      .error-list {
        .error-item {
          margin-bottom: 4px;
          font-size: 13px;
          color: #e6a23c;

          .error-value {
            color: #909399;
            font-size: 12px;
          }
        }

        .error-more {
          margin-top: 8px;
          font-size: 13px;
          color: #909399;
          font-style: italic;
        }
      }
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}
</style>
