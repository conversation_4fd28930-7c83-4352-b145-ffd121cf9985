// 表格操作按钮强制修复样式
// 确保按钮在一行显示，不换行

// 全局表格操作列样式
.el-table {
  // 操作列样式强化
  .table-column-actions {
    // 表头样式
    &.el-table__cell {
      padding: 8px 12px !important;
      text-align: center !important;
    }
    
    // 单元格样式
    .cell {
      padding: 8px 12px !important;
      white-space: nowrap !important;
      overflow: visible !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-width: 200px !important;
    }
  }
}

// TableActions组件强制样式
.table-actions {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  width: 100% !important;
  
  // 紧凑模式
  &--compact {
    gap: 2px !important;
  }
  
  // 所有按钮样式
  .el-button {
    margin: 0 !important;
    padding: 2px 6px !important;
    font-size: 12px !important;
    min-width: auto !important;
    flex-shrink: 0 !important;
    white-space: nowrap !important;
    
    // 按钮文字
    span {
      white-space: nowrap !important;
    }
    
    // 图标
    .el-icon {
      margin-right: 2px !important;
      
      &:only-child {
        margin: 0 !important;
      }
    }
    
    // 链接按钮特殊样式
    &.is-link {
      padding: 2px 6px !important;
      border: none !important;
      background: transparent !important;
      
      &:hover {
        background-color: rgba(24, 144, 255, 0.06) !important;
        border-radius: 4px !important;
      }
    }
    
    // 图标按钮
    &.action-btn--icon-only {
      padding: 4px !important;
      width: 28px !important;
      height: 28px !important;
      
      .el-icon {
        margin: 0 !important;
      }
      
      span {
        display: none !important;
      }
    }
  }
  
  // 下拉菜单
  .el-dropdown {
    .el-button {
      padding: 2px 6px !important;
      margin: 0 !important;
    }
  }
  
  // 工具提示
  .el-tooltip__trigger {
    display: inline-flex !important;
    align-items: center !important;
  }
}

// 强制覆盖Element Plus的默认样式
.el-table__body-wrapper {
  .el-table__row {
    .table-column-actions {
      .cell {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        white-space: nowrap !important;
        overflow: visible !important;
        
        .table-actions {
          display: flex !important;
          flex-direction: row !important;
          align-items: center !important;
          justify-content: center !important;
          gap: 4px !important;
          flex-wrap: nowrap !important;
          width: 100% !important;
        }
      }
    }
  }
}

// 确保按钮不会被挤压
.el-table .el-table__cell {
  &.table-column-actions {
    min-width: 200px !important;
    width: auto !important;
    
    .cell {
      min-width: 200px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      flex-wrap: nowrap !important;
    }
  }
}

// 响应式处理
@media (max-width: 1200px) {
  .table-actions {
    gap: 2px !important;
    
    .el-button {
      padding: 1px 4px !important;
      font-size: 11px !important;
    }
  }
}

@media (max-width: 768px) {
  .table-actions {
    .el-button {
      &:not(.action-btn--icon-only) {
        span {
          display: none !important;
        }
        
        .el-icon {
          margin: 0 !important;
        }
      }
    }
  }
}

// 调试样式（可以临时启用来检查布局）
/*
.table-actions {
  border: 2px solid red !important;
  background: rgba(255, 0, 0, 0.1) !important;
}

.table-actions .el-button {
  border: 1px solid blue !important;
  background: rgba(0, 0, 255, 0.1) !important;
}
*/
