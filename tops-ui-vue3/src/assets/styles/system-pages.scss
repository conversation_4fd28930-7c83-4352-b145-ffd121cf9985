// 系统管理页面通用样式
// 解决系统管理页面的样式重复定义和不一致问题

.system-page {
  // 搜索表单样式
  .el-form--inline {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 16px;
      
      // 统一输入框宽度
      .el-input,
      .el-select {
        width: 200px;
      }
      
      // 日期选择器宽度
      .el-date-picker {
        width: 240px;
      }
      
      // 级联选择器宽度
      .el-cascader {
        width: 200px;
      }
    }
    
    // 搜索按钮区域
    .search-buttons-item {
      margin-left: auto;
      margin-right: 0;
      
      .el-button + .el-button {
        margin-left: 8px;
      }
    }
  }
  
  // 工具栏样式
  .page-toolbar {
    margin-bottom: 16px;
    
    .el-row {
      align-items: center;
    }
    
    .el-button + .el-button {
      margin-left: 8px;
    }
    
    // 右侧工具栏
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  // 表格样式
  .el-table {
    .el-table__cell {
      text-align: left;
    }
    
    // 表格操作列
    .table-actions {
      .el-button {
        margin-right: 4px;
        
        &:last-child {
          margin-right: 0;
        }
      }
      
      // 更多操作下拉菜单
      .el-dropdown {
        margin-left: 4px;
      }
    }
    
    // 状态标签
    .status-tag {
      .el-tag {
        margin-right: 4px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  
  // 表单对话框样式
  .form-dialog {
    .el-dialog__body {
      padding: 20px;
    }
    
    .el-form {
      .el-form-item {
        margin-bottom: 20px;
        
        .el-form-item__label {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
        
        // 表单输入框统一宽度
        .el-input,
        .el-select,
        .el-textarea,
        .el-cascader {
          width: 100%;
        }
        
        // 数字输入框
        .el-input-number {
          width: 100%;
        }
        
        // 开关组件
        .el-switch {
          margin-top: 4px;
        }
      }
      
      // 表单按钮区域
      .form-buttons {
        text-align: center;
        margin-top: 30px;
        
        .el-button + .el-button {
          margin-left: 12px;
        }
      }
    }
  }
  
  // 树形组件样式
  .system-tree {
    .el-tree {
      .el-tree-node__content {
        height: 36px;
        
        .el-tree-node__label {
          font-size: 14px;
        }
      }
      
      // 自定义节点内容
      .custom-tree-node {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        .node-label {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .node-actions {
          .el-button {
            margin-left: 4px;
            padding: 4px 8px;
          }
        }
      }
    }
  }
  
  // 卡片样式
  .system-card {
    .el-card {
      margin-bottom: 16px;
      
      .el-card__header {
        padding: 16px 20px;
        border-bottom: 1px solid var(--el-border-color-light);
        
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          .header-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }
          
          .header-actions {
            .el-button + .el-button {
              margin-left: 8px;
            }
          }
        }
      }
      
      .el-card__body {
        padding: 20px;
      }
    }
  }
  
  // 标签页样式
  .system-tabs {
    .el-tabs {
      .el-tabs__header {
        margin-bottom: 20px;
      }
      
      .el-tabs__content {
        .el-tab-pane {
          // 标签页内容样式
        }
      }
    }
  }
  
  // 步骤条样式
  .system-steps {
    .el-steps {
      margin-bottom: 30px;
      
      .el-step__title {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  // 响应式处理
  @media (max-width: 1200px) {
    .el-form--inline {
      .el-form-item {
        .el-input,
        .el-select,
        .el-cascader {
          width: 180px;
        }
        
        .el-date-picker {
          width: 220px;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .el-form--inline {
      flex-direction: column;
      
      .el-form-item {
        width: 100%;
        margin-right: 0;
        
        .el-input,
        .el-select,
        .el-cascader,
        .el-date-picker {
          width: 100%;
        }
        
        &.search-buttons-item {
          .el-button {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
    
    .page-toolbar {
      .el-button {
        margin-bottom: 8px;
        width: 100%;
      }
    }
    
    .table-actions {
      .el-button {
        margin-bottom: 4px;
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

// 用户管理页面特定样式
.user-management-page {
  @extend .system-page;
  
  // 用户头像
  .user-avatar {
    .el-avatar {
      margin-right: 8px;
    }
  }
  
  // 用户状态
  .user-status {
    .el-tag {
      &.status-active {
        background-color: var(--el-color-success-light-9);
        color: var(--el-color-success);
        border-color: var(--el-color-success-light-5);
      }
      
      &.status-inactive {
        background-color: var(--el-color-danger-light-9);
        color: var(--el-color-danger);
        border-color: var(--el-color-danger-light-5);
      }
    }
  }
}

// 角色管理页面特定样式
.role-management-page {
  @extend .system-page;
  
  // 权限树
  .permission-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 10px;
  }
}

// 菜单管理页面特定样式
.menu-management-page {
  @extend .system-page;
  
  // 菜单图标预览
  .menu-icon-preview {
    display: inline-flex;
    align-items: center;
    
    .icon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

// 部门管理页面特定样式
.dept-management-page {
  @extend .system-page;
  
  // 部门层级缩进
  .dept-level {
    &.level-1 { padding-left: 0; }
    &.level-2 { padding-left: 20px; }
    &.level-3 { padding-left: 40px; }
    &.level-4 { padding-left: 60px; }
  }
}
