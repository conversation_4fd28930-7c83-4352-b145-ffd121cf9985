// 按钮增强样式 - 解决布局和对比度问题
@import './variables.module.scss';

// 按钮尺寸变体
.el-button {
  // 超小按钮 - 用于表格中的紧凑布局
  &.el-button--mini {
    padding: 2px 6px;
    font-size: 11px;
    border-radius: 4px;
    min-width: 24px;
    height: 24px;
  }
  
  // 紧凑按钮 - 减少内边距
  &.el-button--compact {
    padding: 4px 8px;
    font-size: 12px;
    min-width: auto;
    
    &.is-circle {
      width: 28px;
      height: 28px;
      padding: 0;
    }
  }
}

// 表格操作按钮专用样式
.table-action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: nowrap;
  white-space: nowrap;

  .el-button {
    margin: 0;
    padding: 4px 8px;
    font-size: 12px;
    min-width: auto;

    // 图标按钮
    &.is-icon-only {
      padding: 4px;
      width: 28px;
      height: 28px;

      .el-icon {
        margin: 0;
      }
    }

    // 链接按钮在表格中的样式
    &.is-link {
      padding: 2px 4px;

      &:hover {
        background-color: rgba(24, 144, 255, 0.06);
        border-radius: 4px;
      }
    }
  }

  // 分隔符
  .action-divider {
    width: 1px;
    height: 16px;
    background-color: #f0f0f0;
    margin: 0 4px;
  }
}

// 强化表格操作列样式
.el-table {
  // 操作列专用样式
  .table-column-actions {
    .cell {
      padding: 8px 12px !important;
      white-space: nowrap !important;
      overflow: visible !important;

      // TableActions组件样式
      .table-actions {
        display: flex !important;
        align-items: center !important;
        gap: 4px !important;
        flex-wrap: nowrap !important;
        justify-content: center;

        .el-button {
          margin: 0 !important;
          flex-shrink: 0;

          &.is-link {
            padding: 2px 6px !important;
            font-size: 12px !important;
            min-width: auto !important;

            // 确保按钮文字不换行
            span {
              white-space: nowrap;
            }
          }
        }

        // 下拉菜单按钮
        .el-dropdown {
          .el-button {
            padding: 2px 6px !important;
          }
        }
      }
    }
  }

  // 确保操作列有足够宽度
  .el-table__header-wrapper,
  .el-table__body-wrapper {
    .table-column-actions {
      min-width: 200px !important;
      width: auto !important;
    }
  }
}

// 按钮组增强
.el-button-group {
  .el-button {
    margin: 0;
    
    &:not(:first-child) {
      margin-left: -1px;
    }
    
    &:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    
    &:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    
    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
    
    &:hover {
      z-index: 1;
    }
  }
}

// 响应式按钮工具类
.btn-responsive {
  @media (max-width: 768px) {
    padding: 4px 8px;
    font-size: 12px;
    
    .el-icon {
      margin-right: 4px;
    }
  }
  
  @media (max-width: 480px) {
    padding: 2px 6px;
    font-size: 11px;
    
    // 在小屏幕上只显示图标
    &.btn-icon-mobile {
      .btn-text {
        display: none;
      }
      
      .el-icon {
        margin: 0;
      }
    }
  }
}

// 按钮状态指示器
.el-button {
  &.is-loading {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border: 2px solid transparent;
      border-top-color: currentColor;
      border-radius: inherit;
      animation: button-loading 1s linear infinite;
    }
  }
}

@keyframes button-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 按钮间距工具类
.btn-group-spaced {
  .el-button + .el-button {
    margin-left: 8px;
  }
}

.btn-group-tight {
  .el-button + .el-button {
    margin-left: 4px;
  }
}

.btn-group-none {
  .el-button + .el-button {
    margin-left: 0;
  }
}

// 特殊用途按钮样式
.btn-ghost {
  background: transparent;
  border: 1px solid currentColor;
  
  &:hover {
    background: currentColor;
    color: #ffffff;
  }
}

.btn-gradient {
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
  border: none;
  color: #ffffff;
  
  &:hover {
    background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  }
}

// 表格列宽度优化
.el-table {
  // 操作列的最小宽度设置
  .table-column-actions {
    min-width: 120px;
    
    &.compact {
      min-width: 80px;
    }
    
    &.wide {
      min-width: 160px;
    }
  }
  
  // 确保操作按钮不换行
  .cell {
    .table-action-buttons {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

// 按钮可访问性增强
.el-button {
  &:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
  
  // 高对比度模式支持
  @media (prefers-contrast: high) {
    border-width: 2px;
    font-weight: 600;
  }
  
  // 减少动画模式支持
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .el-button {
    &:not(.el-button--primary):not(.el-button--success):not(.el-button--warning):not(.el-button--danger) {
      background-color: #1f1f1f;
      border-color: #434343;
      color: #ffffff;
      
      &:hover {
        background-color: #262626;
        border-color: #595959;
      }
    }
  }
}

// 打印样式
@media print {
  .el-button {
    background: transparent !important;
    color: #000000 !important;
    border: 1px solid #000000 !important;
    box-shadow: none !important;
    transform: none !important;
  }
}
