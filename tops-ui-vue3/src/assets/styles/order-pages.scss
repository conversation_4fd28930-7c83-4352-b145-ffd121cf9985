// 订单页面通用样式
// 解决订单相关页面的样式冲突和不一致问题

.order-page {
  // 搜索表单样式
  .el-form--inline {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 16px;
      
      // 统一输入框宽度
      .el-input,
      .el-select,
      .el-textarea {
        width: 200px;
      }
      
      // 日期选择器宽度
      .el-date-picker {
        width: 240px;
      }
      
      // 批量输入组件特殊处理
      .batch-input-wrapper {
        .batch-input-field {
          width: 200px;
        }
      }
    }
    
    // 搜索按钮区域
    .search-buttons-item,
    .search-buttons-wrapper {
      margin-left: auto;
      margin-right: 0;
      
      .el-button + .el-button {
        margin-left: 8px;
      }
    }
  }
  
  // 工具栏样式
  .page-toolbar {
    margin-bottom: 16px;
    
    .el-row {
      align-items: center;
    }
    
    .el-button + .el-button {
      margin-left: 8px;
    }
  }
  
  // 表格样式
  .el-table {
    .el-table__cell {
      text-align: left;
    }
    
    // 可点击单元格
    .clickable-cell {
      cursor: pointer;
      color: var(--el-color-primary);
      transition: all 0.3s ease;
      
      &:hover {
        text-decoration: underline;
        color: var(--el-color-primary-light-3);
      }
    }
    
    // 表格操作列
    .table-actions {
      .el-button {
        margin-right: 4px;
        
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
  
  // 统计面板样式
  .stats-panel {
    margin-bottom: 16px;
    
    .el-card {
      .el-card__body {
        padding: 16px;
      }
    }
    
    .stats-item {
      text-align: center;
      
      .stats-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary);
        margin-bottom: 4px;
      }
      
      .stats-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
  
  // 图表容器样式
  .chart-container {
    .container {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      
      .chart-item {
        flex: 1;
        min-width: 0;
        
        .chart,
        .chart1 {
          width: 100%;
          height: 400px;
        }
      }
    }
  }
  
  // 抽屉容器样式
  .drawer-container {
    .drawerContainer {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      
      .drawer-item {
        flex: 1;
        min-width: 0;
        
        .drawer-left,
        .drawer-right {
          width: 100%;
          height: 400px;
        }
      }
    }
  }
  
  // 表单对话框样式
  .form-dialog {
    .el-dialog__body {
      padding: 20px;
    }
    
    .el-form {
      .el-form-item {
        margin-bottom: 20px;
        
        .el-form-item__label {
          font-weight: 500;
        }
      }
    }
  }
  
  // 详情抽屉样式
  .detail-drawer {
    .el-drawer__body {
      padding: 20px;
    }
    
    .detail-section {
      margin-bottom: 24px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--el-border-color-light);
      }
      
      .detail-item {
        display: flex;
        margin-bottom: 8px;
        
        .item-label {
          min-width: 100px;
          color: var(--el-text-color-regular);
          font-weight: 500;
        }
        
        .item-value {
          flex: 1;
          color: var(--el-text-color-primary);
        }
      }
    }
  }
  
  // 响应式处理
  @media (max-width: 1200px) {
    .el-form--inline {
      .el-form-item {
        .el-input,
        .el-select,
        .el-textarea {
          width: 180px;
        }
        
        .el-date-picker {
          width: 220px;
        }
      }
    }
    
    .chart-container .container {
      flex-direction: column;
      
      .chart-item {
        .chart,
        .chart1 {
          height: 300px;
        }
      }
    }
  }
  
  @media (max-width: 768px) {
    .el-form--inline {
      flex-direction: column;
      
      .el-form-item {
        width: 100%;
        margin-right: 0;
        
        .el-input,
        .el-select,
        .el-textarea,
        .el-date-picker {
          width: 100%;
        }
        
        &.search-buttons-item {
          .el-button {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
    
    .page-toolbar {
      .el-button {
        margin-bottom: 8px;
      }
    }
  }
}

// 订单信息页面特定样式
.order-info-page {
  // 继承通用订单页面样式
  @extend .order-page;
  
  // 页面特定的样式覆盖
  .batch-input-wrapper {
    // 确保批量输入组件在此页面中正确显示
    .input-with-button {
      .batch-input-field {
        width: 200px;
      }
    }
  }
}

// 订单请求页面特定样式
.order-request-page {
  // 继承通用订单页面样式
  @extend .order-page;
  
  // 修复原有的样式冲突
  .el-form-item__content {
    // 移除固定宽度，使用flex布局
    width: auto;
  }
  
  // 图表容器特殊处理
  .chart-container {
    .chart,
    .chart1 {
      height: 800px; // 保持原有高度
    }
  }
  
  .drawer-container {
    .drawer-left,
    .drawer-right {
      height: 800px; // 保持原有高度
    }
  }
}
