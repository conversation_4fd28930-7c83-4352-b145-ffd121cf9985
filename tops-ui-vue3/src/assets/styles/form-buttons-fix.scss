// 表单按钮换行问题修复样式
// 专门解决搜索表单中搜索和重置按钮换行的问题

// 全局搜索表单样式修复
.el-form {
  // 内联表单的按钮区域修复
  &.el-form--inline {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    gap: 16px;
    
    .el-form-item {
      margin-right: 0 !important;
      margin-bottom: 16px;
      flex-shrink: 0;
      
      // 搜索按钮区域特殊处理
      &:last-child {
        margin-left: auto;
        
        // 确保按钮不换行
        .el-button {
          margin-right: 8px;
          
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    
    // 响应式处理
    @media (max-width: 1200px) {
      .el-form-item {
        &:not(:last-child) {
          flex: 1;
          min-width: 200px;
        }
        
        &:last-child {
          flex: none;
          width: 100%;
          margin-left: 0;
          display: flex;
          justify-content: flex-start;
        }
      }
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      
      .el-form-item {
        width: 100%;
        margin-left: 0;
        
        &:last-child {
          display: flex;
          justify-content: center;
          
          .el-button {
            flex: 1;
            max-width: 120px;
          }
        }
      }
    }
  }
}

// 搜索按钮组强制不换行样式
.search-button-group {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  
  .el-button {
    margin: 0 !important;
    flex-shrink: 0 !important;
  }
}

// 修复特定页面的搜索表单
.app-container {
  .el-form {
    &[v-show] {
      // 确保显示状态下的表单正确布局
      display: flex !important;
      flex-wrap: wrap !important;
      align-items: flex-end !important;
      gap: 16px !important;
      
      .el-form-item {
        margin-right: 0 !important;
        margin-bottom: 16px !important;
        
        // 最后一个表单项（通常是按钮区域）
        &:last-child {
          margin-left: auto !important;
          
          // 按钮容器
          > div {
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
            flex-wrap: nowrap !important;
          }
        }
      }
    }
  }
}

// 系统管理页面特定修复（移除:has()伪类）
.system-page {
  .el-form--inline {
    .search-buttons-item {
      margin-left: auto;

      .el-button + .el-button {
        margin-left: 8px;
      }
    }
  }
}

// 监控页面特定修复（移除:has()伪类）
.monitor-page {
  .el-form--inline {
    .search-buttons-item {
      margin-left: auto;

      .el-button + .el-button {
        margin-left: 8px;
      }
    }
  }
}

// 通用按钮组修复类
.btn-group-no-wrap {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  
  .el-button {
    margin: 0 !important;
    flex-shrink: 0 !important;
  }
}

// 表单项内的按钮组修复
.el-form-item {
  &.form-buttons {
    margin-left: auto !important;
    
    .el-form-item__content {
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
      flex-wrap: nowrap !important;
      
      .el-button {
        margin: 0 !important;
        flex-shrink: 0 !important;
      }
    }
  }
}

// 强制修复所有搜索表单的按钮换行问题
.el-form--inline .el-form-item:last-child {
  .el-form-item__content {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
    
    .el-button {
      margin: 0 !important;
      flex-shrink: 0 !important;
    }
  }
}

// 特殊情况：当表单项只包含按钮时（移除:has()伪类以提高兼容性）
.form-item-buttons-only {
  margin-left: auto;

  .el-form-item__content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: nowrap;

    .el-button {
      margin: 0;
      flex-shrink: 0;
    }
  }
}

// 响应式断点处理
@media (max-width: 1400px) {
  .el-form--inline {
    .el-form-item {
      margin-right: 12px !important;
      
      .el-input,
      .el-select {
        width: 180px !important;
      }
    }
  }
}

@media (max-width: 1200px) {
  .el-form--inline {
    .el-form-item {
      margin-right: 8px !important;
      
      .el-input,
      .el-select {
        width: 160px !important;
      }
    }
  }
}

@media (max-width: 992px) {
  .el-form--inline {
    flex-direction: column !important;
    align-items: stretch !important;
    
    .el-form-item {
      width: 100% !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      
      &:last-child {
        display: flex !important;
        justify-content: center !important;
        
        .el-form-item__content {
          justify-content: center !important;
          
          .el-button {
            flex: 1 !important;
            max-width: 120px !important;
          }
        }
      }
    }
  }
}
