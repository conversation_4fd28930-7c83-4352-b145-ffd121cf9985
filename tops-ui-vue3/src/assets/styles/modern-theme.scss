// 现代化主题样式
@import './variables.module.scss';

// 全局现代化样式
:root {
  // 主色调
  --primary-color: #{$primary};
  --primary-light: #{$primary-light};
  --primary-dark: #{$primary-dark};
  
  // 功能色
  --success-color: #{$success};
  --warning-color: #{$warning};
  --danger-color: #{$danger};
  --info-color: #{$info};
  
  // 中性色
  --gray-50: #{$gray-50};
  --gray-100: #{$gray-100};
  --gray-200: #{$gray-200};
  --gray-300: #{$gray-300};
  --gray-400: #{$gray-400};
  --gray-500: #{$gray-500};
  --gray-600: #{$gray-600};
  --gray-700: #{$gray-700};
  --gray-800: #{$gray-800};
  --gray-900: #{$gray-900};
  
  // 字体
  --font-family: #{$font-family};
  --font-size-base: #{$font-size-base};
  --line-height-base: #{$line-height-base};
  
  // 圆角
  --border-radius-base: #{$border-radius-base};
  --border-radius-small: #{$border-radius-small};
  --border-radius-large: #{$border-radius-large};
  
  // 阴影
  --box-shadow-base: #{$box-shadow-base};
  --box-shadow-light: #{$box-shadow-light};
  --box-shadow-card: #{$box-shadow-card};
  
  // 间距
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
}

// 现代化卡片样式
.modern-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-card);
  border: 1px solid var(--gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
    
    .title {
      font-size: var(--font-size-large);
      font-weight: 600;
      color: var(--gray-800);
      margin: 0;
    }
    
    .subtitle {
      font-size: var(--font-size-small);
      color: var(--gray-500);
      margin-top: var(--spacing-xs);
    }
  }
  
  .card-body {
    padding: var(--spacing-lg);
  }
  
  .card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    border-radius: 0 0 var(--border-radius-large) var(--border-radius-large);
  }
}

// 现代化表单样式
.modern-form {
  .form-group {
    margin-bottom: var(--spacing-lg);
    
    .form-label {
      display: block;
      font-weight: 500;
      color: var(--gray-700);
      margin-bottom: var(--spacing-sm);
      font-size: var(--font-size-base);
    }
    
    .form-control {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid var(--gray-300);
      border-radius: var(--border-radius-base);
      font-size: var(--font-size-base);
      transition: all 0.3s ease;
      background: #ffffff;
      
      &:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
      
      &:hover {
        border-color: var(--primary-light);
      }
      
      &::placeholder {
        color: var(--gray-400);
      }
    }
    
    .form-help {
      font-size: var(--font-size-small);
      color: var(--gray-500);
      margin-top: var(--spacing-xs);
    }
    
    .form-error {
      font-size: var(--font-size-small);
      color: var(--danger-color);
      margin-top: var(--spacing-xs);
    }
  }
}

// 现代化按钮组
.modern-btn-group {
  display: flex;
  gap: var(--spacing-sm);
  
  .btn {
    padding: 10px 20px;
    border-radius: var(--border-radius-base);
    border: 1px solid var(--gray-300);
    background: #ffffff;
    color: var(--gray-700);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--primary-color);
      color: var(--primary-color);
      background: rgba(24, 144, 255, 0.05);
    }
    
    &.btn-primary {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: #ffffff;
      
      &:hover {
        background: var(--primary-light);
        border-color: var(--primary-light);
        color: #ffffff;
      }
    }
    
    &.btn-success {
      background: var(--success-color);
      border-color: var(--success-color);
      color: #ffffff;
      
      &:hover {
        background: lighten($success, 10%);
        border-color: lighten($success, 10%);
      }
    }
    
    &.btn-warning {
      background: var(--warning-color);
      border-color: var(--warning-color);
      color: #ffffff;
      
      &:hover {
        background: lighten($warning, 10%);
        border-color: lighten($warning, 10%);
      }
    }
    
    &.btn-danger {
      background: var(--danger-color);
      border-color: var(--danger-color);
      color: #ffffff;
      
      &:hover {
        background: lighten($danger, 10%);
        border-color: lighten($danger, 10%);
      }
    }
  }
}

// 现代化标签样式
.modern-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: var(--font-size-small);
  font-weight: 500;
  
  &.tag-primary {
    background: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
  }
  
  &.tag-success {
    background: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
  }
  
  &.tag-warning {
    background: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
  }
  
  &.tag-danger {
    background: rgba(255, 77, 79, 0.1);
    color: var(--danger-color);
  }
  
  &.tag-info {
    background: var(--gray-100);
    color: var(--gray-600);
  }
}

// 现代化进度条
.modern-progress {
  width: 100%;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
  
  .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
  }
  
  &.progress-success .progress-bar {
    background: linear-gradient(90deg, var(--success-color), lighten($success, 10%));
  }
  
  &.progress-warning .progress-bar {
    background: linear-gradient(90deg, var(--warning-color), lighten($warning, 10%));
  }
  
  &.progress-danger .progress-bar {
    background: linear-gradient(90deg, var(--danger-color), lighten($danger, 10%));
  }
}

// 现代化加载动画
.modern-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 现代化工具提示
.modern-tooltip {
  position: relative;
  
  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background: var(--gray-800);
    color: #ffffff;
    font-size: var(--font-size-small);
    border-radius: var(--border-radius-base);
    white-space: nowrap;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
  }
  
  &::after {
    opacity: 0;
    pointer-events: none;
  }
}
