/**
 * 订单相关字段验证器
 */

/**
 * 订单号验证器
 * @param {string} value - 要验证的订单号
 * @returns {boolean|string} - 验证通过返回true，失败返回错误信息
 */
export function validateOrderNo(value) {
  if (!value || typeof value !== 'string') {
    return '订单号不能为空'
  }
  
  const trimmed = value.trim()
  
  // 基本长度检查
  if (trimmed.length < 3) {
    return '订单号长度不能少于3位'
  }
  
  if (trimmed.length > 50) {
    return '订单号长度不能超过50位'
  }
  
  // 字符检查：只允许字母、数字、下划线、中划线
  const validPattern = /^[a-zA-Z0-9_-]+$/
  if (!validPattern.test(trimmed)) {
    return '订单号只能包含字母、数字、下划线、中划线'
  }
  
  return true
}

/**
 * 客户订单号验证器
 * @param {string} value - 要验证的客户订单号
 * @returns {boolean|string} - 验证通过返回true，失败返回错误信息
 */
export function validateCustomerOrderNo(value) {
  if (!value || typeof value !== 'string') {
    return '客户订单号不能为空'
  }
  
  const trimmed = value.trim()
  
  // 基本长度检查
  if (trimmed.length < 3) {
    return '客户订单号长度不能少于3位'
  }
  
  if (trimmed.length > 100) {
    return '客户订单号长度不能超过100位'
  }
  
  // 字符检查：允许更多字符，包括点号
  const validPattern = /^[a-zA-Z0-9_.-]+$/
  if (!validPattern.test(trimmed)) {
    return '客户订单号只能包含字母、数字、下划线、中划线、点号'
  }
  
  return true
}

/**
 * 自定义单号验证器
 * @param {string} value - 要验证的自定义单号
 * @returns {boolean|string} - 验证通过返回true，失败返回错误信息
 */
export function validateCustomOrderNo(value) {
  if (!value || typeof value !== 'string') {
    return '自定义单号不能为空'
  }
  
  const trimmed = value.trim()
  
  // 基本长度检查
  if (trimmed.length < 1) {
    return '自定义单号不能为空'
  }
  
  if (trimmed.length > 100) {
    return '自定义单号长度不能超过100位'
  }
  
  // 自定义单号允许更宽松的字符集
  const validPattern = /^[a-zA-Z0-9_.-]+$/
  if (!validPattern.test(trimmed)) {
    return '自定义单号只能包含字母、数字、下划线、中划线、点号'
  }
  
  return true
}

/**
 * 渠道单号验证器
 * @param {string} value - 要验证的渠道单号
 * @returns {boolean|string} - 验证通过返回true，失败返回错误信息
 */
export function validateChannelOrderNo(value) {
  if (!value || typeof value !== 'string') {
    return '渠道单号不能为空'
  }
  
  const trimmed = value.trim()
  
  if (trimmed.length < 3) {
    return '渠道单号长度不能少于3位'
  }
  
  if (trimmed.length > 50) {
    return '渠道单号长度不能超过50位'
  }
  
  const validPattern = /^[a-zA-Z0-9_-]+$/
  if (!validPattern.test(trimmed)) {
    return '渠道单号只能包含字母、数字、下划线、中划线'
  }
  
  return true
}

/**
 * 运单号验证器
 * @param {string} value - 要验证的运单号
 * @returns {boolean|string} - 验证通过返回true，失败返回错误信息
 */
export function validateWaybillNo(value) {
  if (!value || typeof value !== 'string') {
    return '运单号不能为空'
  }
  
  const trimmed = value.trim()
  
  if (trimmed.length < 8) {
    return '运单号长度不能少于8位'
  }
  
  if (trimmed.length > 30) {
    return '运单号长度不能超过30位'
  }
  
  // 运单号通常是纯数字或字母数字组合
  const validPattern = /^[a-zA-Z0-9]+$/
  if (!validPattern.test(trimmed)) {
    return '运单号只能包含字母和数字'
  }
  
  return true
}

/**
 * 通用验证器工厂函数
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度
 * @param {number} options.maxLength - 最大长度
 * @param {RegExp} options.pattern - 验证正则
 * @param {string} options.patternMessage - 格式错误信息
 * @param {string} options.fieldName - 字段名称
 * @returns {Function} - 验证函数
 */
export function createValidator(options = {}) {
  const {
    minLength = 1,
    maxLength = 100,
    pattern = /^[a-zA-Z0-9_.-]+$/,
    patternMessage = '格式不正确',
    fieldName = '字段'
  } = options
  
  return function(value) {
    if (!value || typeof value !== 'string') {
      return `${fieldName}不能为空`
    }
    
    const trimmed = value.trim()
    
    if (trimmed.length < minLength) {
      return `${fieldName}长度不能少于${minLength}位`
    }
    
    if (trimmed.length > maxLength) {
      return `${fieldName}长度不能超过${maxLength}位`
    }
    
    if (!pattern.test(trimmed)) {
      return patternMessage
    }
    
    return true
  }
}

/**
 * 批量验证工具
 * @param {Array} values - 要验证的值数组
 * @param {Function} validator - 验证函数
 * @returns {Object} - 验证结果
 */
export function batchValidate(values, validator) {
  const results = {
    valid: [],
    invalid: [],
    duplicates: []
  }
  
  const seen = new Set()
  
  values.forEach((value, index) => {
    const trimmed = value.trim()
    
    // 检查重复
    if (seen.has(trimmed)) {
      results.duplicates.push({
        index: index + 1,
        value: trimmed,
        message: '重复数据'
      })
      return
    }
    
    seen.add(trimmed)
    
    // 验证格式
    const validationResult = validator(trimmed)
    if (validationResult === true) {
      results.valid.push({
        index: index + 1,
        value: trimmed
      })
    } else {
      results.invalid.push({
        index: index + 1,
        value: trimmed,
        message: validationResult
      })
    }
  })
  
  return results
}

/**
 * 预定义的验证器配置
 */
export const VALIDATORS = {
  orderNo: {
    validator: validateOrderNo,
    tipText: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；订单号只能包含字母、数字、下划线、中划线，长度3-50位',
    placeholder: '请输入订单号，支持以下格式：\nORD123456789\nORD987654321,ORD555666777\nORD888999000'
  },

  customerOrderNo: {
    validator: validateCustomerOrderNo,
    tipText: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；客户订单号只能包含字母、数字、下划线、中划线、点号，长度3-100位',
    placeholder: '请输入客户订单号，支持以下格式：\nCUST-ORD-123456\nCUST.ORD.789012,CUST-ORD-555666\nCUST.ORD.888999'
  },

  customOrderNo: {
    validator: validateCustomOrderNo,
    tipText: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；自定义单号只能包含字母、数字、下划线、中划线、点号，长度1-100位',
    placeholder: '请输入自定义单号，支持以下格式：\nCUSTOM123\nCUSTOM.456,CUSTOM_789\nCUSTOM-000'
  },

  channelOrderNo: {
    validator: validateChannelOrderNo,
    tipText: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；渠道单号只能包含字母、数字、下划线、中划线，长度3-50位',
    placeholder: '请输入渠道单号，支持以下格式：\nCH123456789\nCH987654321,CH555666777\nCH888999000'
  },

  waybillNo: {
    validator: validateWaybillNo,
    tipText: '支持多行输入，每行一个数据；也支持逗号分隔，一行多个数据；运单号只能包含字母和数字，长度8-30位',
    placeholder: '请输入运单号，支持以下格式：\n12345678901234\nABC123456789,DEF987654321\n98765432109876'
  }
}
