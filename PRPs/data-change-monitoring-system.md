name: "数据变更监控工具模块 - JDL-Trade-OPS系统增强"
description: |

## 目的
为JDL-Trade-OPS系统设计并实现一个数据变更监控工具模块，支持多数据源监控、定时任务执行、变更检测和告警通知。

## 核心原则
1. **非侵入式集成**: 不影响现有系统功能流程
2. **功能开关控制**: 支持默认关闭，渐进式启用
3. **企业中间件优先**: 使用企业内部中间件方案
4. **前后端一体**: 基于现有Vue3 + Spring Boot架构
5. **约束遵守**: 严格遵循现有代码规范和架构模式

---

## 目标
构建一个完整的数据变更监控工具模块，包括数据源管理、监控任务配置、数据快照管理、变更检测与告警功能，以及相应的前端管理界面。

## 为什么
- **业务价值**: 提供实时数据变更监控能力，及时发现数据异常
- **运营效率**: 自动化监控减少人工巡检工作量
- **风险控制**: 快速响应数据变更，降低业务风险
- **系统集成**: 与现有JDL-Trade-OPS系统无缝集成

## 功能描述
用户可以通过Web界面配置多种数据源（MySQL、TiDB），创建监控任务，设置告警规则，查看监控历史和告警记录。系统自动执行监控SQL，保存数据快照，检测变更并发送告警通知。

### 成功标准
- [ ] 支持MySQL和TiDB数据源的动态配置和连接测试
- [ ] 支持创建和管理监控任务，包含SQL编辑和cron表达式配置
- [ ] 实现数据快照自动保存和历史管理
- [ ] 实现变更检测和阈值告警功能
- [ ] 集成咚咚/京ME消息推送
- [ ] 提供完整的前端管理界面
- [ ] 支持权限控制和功能开关

## 所需上下文

### 项目架构类型
**前后端一体项目**:
- 后端: Spring Boot 2.7.18 + MyBatis-Plus + Sa-Token + Redisson
- 前端: Vue 3.2.45 + Element Plus 2.7.8 + Pinia + Vite
- 数据库: MySQL/TiDB
- 构建: Maven多模块管理

### 核心约束规范
基于现有代码库分析的强制性约束：

**Controller层约束**:
- 必须继承BaseController
- 使用@RestController + @RequestMapping模式
- 权限控制使用@SaCheckPermission注解
- 参数验证使用@Validated注解
- 操作日志使用@Log注解
- 防重提交使用@RepeatSubmit注解

**Service层约束**:
- 使用@Service注解和@Transactional事务管理
- 类级别使用@Transactional(readOnly = true)
- 写操作方法覆盖为@Transactional
- 构造函数注入依赖，使用final字段
- 业务异常使用ServiceException

**数据访问层约束**:
- 使用MyBatis-Plus的BaseMapper
- 实体类使用@TableName注解
- 主键使用@TableId(type = IdType.AUTO)
- VO/BO模式进行数据传输

### 企业中间件集成
根据需求关键词匹配的企业中间件：

**Redis分布式缓存** (已集成):
- 版本: Redisson 3.20.1
- 用途: 缓存数据源连接信息、监控任务配置
- 配置: 已在pom.xml中配置

**DongLog统一日志组件**:
- 用途: 记录监控任务执行日志、操作审计日志
- 配置: 需要添加log-dong-boot-starter依赖

**DongSchedule任务调度框架**:
- 用途: 执行定时监控任务
- 配置: 需要添加schedule-dong-boot-starter依赖
- 与现有XXL-Job并存使用

**UMP统一监控平台**:
- 用途: 监控系统性能指标、告警统计
- 配置: 需要添加ump-dong-boot-starter依赖

**USF权限系统**:
- 用途: 数据源和任务的权限控制
- 配置: 需要添加susf-client依赖
- 与现有Sa-Token并存使用

### 文档与参考资料
```yaml
- file: middleware-docs/redis.md
  why: 获取Redis缓存的完整使用指南和配置模式

- file: middleware-docs/donglog.md  
  why: 获取DongLog日志组件的集成方式和使用模式

- file: middleware-docs/dongschedule.md
  why: 获取DongSchedule任务调度的配置和使用方式

- file: middleware-docs/ump.md
  why: 获取UMP监控平台的集成和指标记录方式

- file: middleware-docs/usf.md
  why: 获取USF权限系统的配置和权限控制方式

- file: context-engineering/examples/service-layer-patterns.md
  why: 遵循现有的服务层设计模式和最佳实践

- file: docs/design/development-specification.md
  why: 遵循项目开发规范和代码约定
```

### 现有代码库结构
```bash
jdl-trade-ops/
├── tops-admin/          # 主应用模块
├── tops-system/         # 系统管理模块  
├── tops-framework/      # 框架核心模块
├── tops-common/         # 通用工具模块
├── tops-ui-vue3/        # Vue3前端模块
└── pom.xml             # 主POM配置
```

### 期望的代码库结构
```bash
# 后端新增文件
tops-system/src/main/java/com/tops/system/
├── domain/
│   ├── DataSource.java              # 数据源实体
│   ├── MonitorTask.java             # 监控任务实体
│   ├── DataSnapshot.java            # 数据快照实体
│   └── AlertRecord.java             # 告警记录实体
├── mapper/
│   ├── DataSourceMapper.java        # 数据源Mapper
│   ├── MonitorTaskMapper.java       # 监控任务Mapper
│   ├── DataSnapshotMapper.java      # 数据快照Mapper
│   └── AlertRecordMapper.java       # 告警记录Mapper
├── service/
│   ├── IDataSourceService.java      # 数据源服务接口
│   ├── IMonitorTaskService.java     # 监控任务服务接口
│   ├── IDataSnapshotService.java    # 数据快照服务接口
│   └── IAlertRecordService.java     # 告警记录服务接口
└── service/impl/
    ├── DataSourceServiceImpl.java   # 数据源服务实现
    ├── MonitorTaskServiceImpl.java  # 监控任务服务实现
    ├── DataSnapshotServiceImpl.java # 数据快照服务实现
    └── AlertRecordServiceImpl.java  # 告警记录服务实现

tops-admin/src/main/java/com/tops/web/controller/monitor/
├── DataSourceController.java        # 数据源管理Controller
├── MonitorTaskController.java       # 监控任务Controller
├── DataSnapshotController.java      # 数据快照Controller
└── AlertRecordController.java       # 告警记录Controller

# 前端新增文件
tops-ui-vue3/src/views/monitor/
├── datasource/
│   ├── index.vue                    # 数据源管理页面
│   └── form.vue                     # 数据源表单组件
├── task/
│   ├── index.vue                    # 监控任务页面
│   └── form.vue                     # 任务表单组件
├── snapshot/
│   └── index.vue                    # 快照历史页面
└── alert/
    └── index.vue                     # 告警记录页面
```

## 实现蓝图

### 第1步: 现有系统集成分析

**现有系统检查**:
```bash
# 1. 检查当前依赖和配置
mvn dependency:tree | grep -E "(spring-boot|mybatis|redisson|sa-token)"
find . -name "application*.yml" -exec grep -l "spring\|mybatis\|redis" {} \;

# 2. 检查现有数据库表结构
mysql -u root -p -e "SHOW TABLES LIKE 'sys_%'" your_database

# 3. 检查现有权限配置
grep -r "@SaCheckPermission" tops-admin/src/main/java/
```

**企业中间件需求分析**:
```
匹配的中间件：
- 缓存需求 → Redis分布式缓存 (已集成)
- 日志需求 → DongLog统一日志组件 (需集成)  
- 定时任务需求 → DongSchedule任务调度框架 (需集成)
- 监控告警需求 → UMP统一监控平台 (需集成)
- 权限控制需求 → USF权限系统 (需集成)
```

### 第2步: 渐进式中间件集成

**Maven依赖** (添加到tops-framework/pom.xml):
```xml
<!-- DongLog统一日志组件 -->
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>log-dong-boot-starter</artifactId>
</dependency>

<!-- DongSchedule任务调度框架 -->
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>schedule-dong-boot-starter</artifactId>
</dependency>

<!-- UMP统一监控平台 -->
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>ump-dong-boot-starter</artifactId>
</dependency>

<!-- USF权限系统 -->
<dependency>
    <groupId>com.jd.susf</groupId>
    <artifactId>susf-client</artifactId>
    <version>1.2.2-SNAPSHOT</version>
</dependency>

<!-- 动态数据源支持 -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
</dependency>
```

**应用配置** (添加到application.yml):
```yaml
# 数据变更监控功能开关
monitor:
  data-change:
    enabled: ${MONITOR_DATA_CHANGE_ENABLED:false}  # 默认关闭

# DongLog配置
dong:
  log:
    enabled: ${DONGLOG_ENABLED:false}
    async:
      enabled: true
      ringBufferSize: 8192

# DongSchedule配置  
  schedule:
    enabled: ${DONGSCHEDULE_ENABLED:false}
    executor:
      port: 9527
      accessToken: ${SCHEDULE_ACCESS_TOKEN:default_token}

# UMP监控配置
  ump:
    enabled: ${UMP_ENABLED:false}
    appName: ${UMP_APP_NAME:jdl-trade-ops}
    monitor:
      enabled: true

# USF权限配置
usf:
  enabled: ${USF_ENABLED:false}
  tenementCode: ${USF_TENEMENT_CODE:jdl-tops}
  appCode: ${USF_APP_CODE:jdl-tops}
  userSystemType: 1

# 动态数据源配置
spring:
  datasource:
    dynamic:
      enabled: ${DYNAMIC_DATASOURCE_ENABLED:false}
      primary: master
      strict: false
      datasource:
        master:
          url: ${MASTER_DB_URL:************************************}
          username: ${MASTER_DB_USERNAME:root}
          password: ${MASTER_DB_PASSWORD:password}
          driver-class-name: com.mysql.cj.jdbc.Driver
```

### 第3步: 数据模型和数据库设计

**数据库表结构**:
```sql
-- 数据源配置表
CREATE TABLE `monitor_data_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_name` varchar(100) NOT NULL COMMENT '数据源名称',
  `source_type` varchar(20) NOT NULL COMMENT '数据源类型(MYSQL,TIDB)',
  `host` varchar(255) NOT NULL COMMENT '主机地址',
  `port` int(11) NOT NULL COMMENT '端口号',
  `database_name` varchar(100) NOT NULL COMMENT '数据库名',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码(加密存储)',
  `connection_params` text COMMENT '连接参数JSON',
  `status` char(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
  `remark` varchar(500) COMMENT '备注',
  `create_by` varchar(64) COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_name` (`source_name`)
) ENGINE=InnoDB COMMENT='数据源配置表';

-- 监控任务表
CREATE TABLE `monitor_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_desc` varchar(500) COMMENT '任务描述',
  `data_source_id` bigint(20) NOT NULL COMMENT '数据源ID',
  `monitor_sql` text NOT NULL COMMENT '监控SQL语句',
  `monitor_type` varchar(20) NOT NULL COMMENT '监控类型(COUNT,FIELD_CHANGE)',
  `cron_expression` varchar(100) NOT NULL COMMENT 'Cron表达式',
  `threshold_config` text COMMENT '阈值配置JSON',
  `snapshot_retention` int(11) DEFAULT 3 COMMENT '快照保留数量',
  `status` char(1) DEFAULT '1' COMMENT '状态(0停用1启用)',
  `last_execute_time` datetime COMMENT '最后执行时间',
  `next_execute_time` datetime COMMENT '下次执行时间',
  `remark` varchar(500) COMMENT '备注',
  `create_by` varchar(64) COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_name` (`task_name`),
  KEY `idx_data_source_id` (`data_source_id`),
  KEY `idx_status_next_time` (`status`, `next_execute_time`)
) ENGINE=InnoDB COMMENT='监控任务表';

-- 数据快照表
CREATE TABLE `monitor_data_snapshot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `execute_time` datetime NOT NULL COMMENT '执行时间',
  `snapshot_data` longtext COMMENT '快照数据JSON',
  `execute_status` varchar(20) NOT NULL COMMENT '执行状态(SUCCESS,FAILED)',
  `execute_message` text COMMENT '执行消息',
  `execution_time_ms` bigint(20) COMMENT '执行耗时(毫秒)',
  `create_time` datetime COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id_time` (`task_id`, `execute_time`),
  KEY `idx_execute_time` (`execute_time`)
) ENGINE=InnoDB COMMENT='数据快照表';

-- 告警记录表
CREATE TABLE `monitor_alert_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `alert_type` varchar(20) NOT NULL COMMENT '告警类型(COUNT_CHANGE,FIELD_CHANGE)',
  `alert_level` varchar(20) NOT NULL COMMENT '告警级别(INFO,WARN,ERROR)',
  `alert_title` varchar(200) NOT NULL COMMENT '告警标题',
  `alert_content` text NOT NULL COMMENT '告警内容',
  `change_detail` text COMMENT '变更详情JSON',
  `alert_time` datetime NOT NULL COMMENT '告警时间',
  `handle_status` varchar(20) DEFAULT 'PENDING' COMMENT '处理状态(PENDING,HANDLED,IGNORED)',
  `handle_by` varchar(64) COMMENT '处理人',
  `handle_time` datetime COMMENT '处理时间',
  `handle_remark` varchar(500) COMMENT '处理备注',
  `create_time` datetime COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id_time` (`task_id`, `alert_time`),
  KEY `idx_alert_time` (`alert_time`),
  KEY `idx_handle_status` (`handle_status`)
) ENGINE=InnoDB COMMENT='告警记录表';
```

### 第4步: 后端实现蓝图

**核心实体类**:
```java
// 数据源实体
@Data
@TableName("monitor_data_source")
public class DataSource extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "数据源名称不能为空")
    private String sourceName;

    @NotBlank(message = "数据源类型不能为空")
    private String sourceType;

    @NotBlank(message = "主机地址不能为空")
    private String host;

    @NotNull(message = "端口号不能为空")
    private Integer port;

    @NotBlank(message = "数据库名不能为空")
    private String databaseName;

    @NotBlank(message = "用户名不能为空")
    private String username;

    @NotBlank(message = "密码不能为空")
    private String password;

    private String connectionParams;
    private String status;
    private String remark;
}

// 监控任务实体
@Data
@TableName("monitor_task")
public class MonitorTask extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    private String taskDesc;

    @NotNull(message = "数据源ID不能为空")
    private Long dataSourceId;

    @NotBlank(message = "监控SQL不能为空")
    private String monitorSql;

    @NotBlank(message = "监控类型不能为空")
    private String monitorType;

    @NotBlank(message = "Cron表达式不能为空")
    private String cronExpression;

    private String thresholdConfig;
    private Integer snapshotRetention;
    private String status;
    private LocalDateTime lastExecuteTime;
    private LocalDateTime nextExecuteTime;
    private String remark;
}
```

**服务层实现**:
```java
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource>
    implements IDataSourceService {

    private final RedissonClient redissonClient;
    private final PasswordEncoder passwordEncoder;

    // DongLog日志组件（可选注入，支持降级）
    private static final DongLogger logger = DongLogger.getLogger(DataSourceServiceImpl.class);

    // UMP监控服务（可选注入，支持降级）
    @Autowired(required = false)
    private UmpClient umpClient;

    @Override
    @Transactional
    public Boolean insertByBo(DataSourceBo bo) {
        // 1. 参数验证
        validateDataSource(bo);

        // 2. 密码加密
        DataSource dataSource = BeanUtil.toBean(bo, DataSource.class);
        dataSource.setPassword(passwordEncoder.encode(bo.getPassword()));

        // 3. 保存数据源
        boolean result = save(dataSource);

        // 4. 记录操作日志（支持降级）
        recordOperationLog("CREATE_DATASOURCE", dataSource.getSourceName(), result);

        // 5. 记录监控指标（支持降级）
        recordMetrics("datasource.create", result ? 1 : 0);

        return result;
    }

    @Override
    public Boolean testConnection(DataSourceBo bo) {
        try {
            // 构建连接URL
            String url = buildConnectionUrl(bo);

            // 测试连接
            try (Connection conn = DriverManager.getConnection(url, bo.getUsername(), bo.getPassword())) {
                return conn.isValid(5); // 5秒超时
            }
        } catch (Exception e) {
            logger.error("数据源连接测试失败: {}", e.getMessage(), e);
            return false;
        }
    }

    private void validateDataSource(DataSourceBo bo) {
        // 检查名称唯一性
        if (exists(Wrappers.<DataSource>lambdaQuery()
            .eq(DataSource::getSourceName, bo.getSourceName()))) {
            throw new ServiceException("数据源名称已存在");
        }

        // 验证连接参数
        if (!testConnection(bo)) {
            throw new ServiceException("数据源连接测试失败");
        }
    }

    private void recordOperationLog(String operation, String target, boolean success) {
        try {
            if (logger != null) {
                logger.info("数据源操作,operation={},target={},success={},timestamp={}",
                    operation, target, success, System.currentTimeMillis());
            }
        } catch (Exception e) {
            // 日志记录失败不影响主流程
        }
    }

    private void recordMetrics(String metricName, double value) {
        try {
            if (umpClient != null) {
                umpClient.recordMetric(metricName, value);
            }
        } catch (Exception e) {
            // 监控记录失败不影响主流程
        }
    }
}
```

### 第5步: 前端实现蓝图

**Vue组件结构**:
```vue
<!-- 数据源管理页面 -->
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="数据源名称" prop="sourceName">
        <el-input v-model="queryParams.sourceName" placeholder="请输入数据源名称" />
      </el-form-item>
      <el-form-item label="数据源类型" prop="sourceType">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据源类型">
          <el-option label="MySQL" value="MYSQL" />
          <el-option label="TiDB" value="TIDB" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['monitor:datasource:add']">
          新增
        </el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataSourceList">
      <el-table-column label="数据源名称" prop="sourceName" />
      <el-table-column label="数据源类型" prop="sourceType" />
      <el-table-column label="主机地址" prop="host" />
      <el-table-column label="端口" prop="port" />
      <el-table-column label="数据库名" prop="databaseName" />
      <el-table-column label="状态" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="操作" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['monitor:datasource:edit']">修改</el-button>
          <el-button type="text" icon="Connection" @click="handleTestConnection(scope.row)">
            测试连接</el-button>
          <el-button type="text" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['monitor:datasource:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改对话框 -->
    <DataSourceForm ref="dataSourceFormRef" @success="getList" />
  </div>
</template>

<script setup name="DataSource">
import { listDataSource, delDataSource, testDataSourceConnection } from '@/api/monitor/datasource'
import DataSourceForm from './form.vue'

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

const dataSourceList = ref([])
const loading = ref(true)
const total = ref(0)

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  sourceName: undefined,
  sourceType: undefined
})

// 查询数据源列表
function getList() {
  loading.value = true
  listDataSource(queryParams.value).then(response => {
    dataSourceList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 新增按钮操作
function handleAdd() {
  proxy.$refs.dataSourceFormRef.open()
}

// 修改按钮操作
function handleUpdate(row) {
  proxy.$refs.dataSourceFormRef.open(row.id)
}

// 测试连接
function handleTestConnection(row) {
  proxy.$modal.loading('正在测试连接...')
  testDataSourceConnection(row.id).then(response => {
    proxy.$modal.closeLoading()
    if (response.data) {
      proxy.$modal.msgSuccess('连接测试成功')
    } else {
      proxy.$modal.msgError('连接测试失败')
    }
  }).catch(() => {
    proxy.$modal.closeLoading()
    proxy.$modal.msgError('连接测试失败')
  })
}

// 删除按钮操作
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除数据源"' + row.sourceName + '"？').then(() => {
    return delDataSource(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess('删除成功')
  }).catch(() => {})
}

onMounted(() => {
  getList()
})
</script>
```

### 第6步: 任务清单

```yaml
任务1 - 数据库初始化:
  执行SQL脚本:
    - 创建monitor_data_source表
    - 创建monitor_task表
    - 创建monitor_data_snapshot表
    - 创建monitor_alert_record表
    - 添加必要的索引和约束

任务2 - 后端核心功能:
  创建实体类:
    - DataSource.java (数据源实体)
    - MonitorTask.java (监控任务实体)
    - DataSnapshot.java (数据快照实体)
    - AlertRecord.java (告警记录实体)

  创建Mapper接口:
    - DataSourceMapper.java
    - MonitorTaskMapper.java
    - DataSnapshotMapper.java
    - AlertRecordMapper.java

  创建Service层:
    - IDataSourceService.java + DataSourceServiceImpl.java
    - IMonitorTaskService.java + MonitorTaskServiceImpl.java
    - IDataSnapshotService.java + DataSnapshotServiceImpl.java
    - IAlertRecordService.java + AlertRecordServiceImpl.java

  创建Controller层:
    - DataSourceController.java
    - MonitorTaskController.java
    - DataSnapshotController.java
    - AlertRecordController.java

任务3 - 企业中间件集成:
  DongSchedule定时任务:
    - 创建MonitorTaskJob.java
    - 实现定时执行监控SQL
    - 保存数据快照
    - 检测数据变更

  DongLog日志记录:
    - 集成DongLog组件
    - 记录操作审计日志
    - 记录任务执行日志

  UMP监控指标:
    - 记录任务执行次数
    - 记录告警触发次数
    - 记录系统性能指标

  USF权限控制:
    - 配置数据源管理权限
    - 配置监控任务权限
    - 配置告警记录权限

任务4 - 前端界面开发:
  创建Vue页面:
    - views/monitor/datasource/index.vue (数据源管理)
    - views/monitor/task/index.vue (监控任务管理)
    - views/monitor/snapshot/index.vue (快照历史)
    - views/monitor/alert/index.vue (告警记录)

  创建API接口:
    - api/monitor/datasource.js
    - api/monitor/task.js
    - api/monitor/snapshot.js
    - api/monitor/alert.js

  配置路由和菜单:
    - 添加监控管理菜单
    - 配置子菜单和权限
```

## 验证门控

### Level 1: 编译和配置检查
```bash
# 后端编译检查
mvn clean compile                    # 编译检查
mvn dependency:tree                  # 依赖检查
mvn spring-boot:run --dry-run       # 配置检查

# 前端编译检查
cd tops-ui-vue3/
npm install                          # 依赖安装检查
npm run build                        # 编译检查

# 预期: 无编译错误，依赖正确，配置有效
```

### Level 2: 单元测试
```bash
# 后端单元测试
mvn test -Dtest=DataSourceServiceTest
mvn test -Dtest=MonitorTaskServiceTest

# 前端单元测试
cd tops-ui-vue3/
npm run test

# 预期: 所有测试通过
```

### Level 3: 集成测试
```bash
# 后端启动
mvn spring-boot:run &
BACKEND_PID=$!
sleep 10

# 前端启动
cd tops-ui-vue3/
npm run dev &
FRONTEND_PID=$!
sleep 5

# API连通性测试
curl -f http://localhost:8080/api/health
curl -f http://localhost:3000

# 功能测试
curl -X POST http://localhost:8080/monitor/datasource/list \
  -H "Content-Type: application/json" \
  -d '{"pageNum": 1, "pageSize": 10}'

# 清理进程
kill $BACKEND_PID $FRONTEND_PID

# 预期: 服务正常启动，API调用成功
```

## 最终验证清单
- [ ] 所有测试通过: `mvn test`
- [ ] 无编译错误: `mvn compile`
- [ ] 应用正常启动: `mvn spring-boot:run`
- [ ] 前端正常启动: `npm run dev`
- [ ] 数据源管理功能正常
- [ ] 监控任务创建和执行正常
- [ ] 数据快照保存正常
- [ ] 告警检测和通知正常
- [ ] 权限控制生效
- [ ] 企业中间件集成正常工作

---

## 要避免的反模式

### 中间件使用反模式
- ❌ 不要使用开源中间件替代企业中间件
- ❌ 不要忽略企业中间件的版本要求和配置规范
- ❌ 不要跳过中间件的降级处理机制

### 开发反模式
- ❌ 不要修改现有系统的核心功能
- ❌ 不要忽略现有的代码规范和架构模式
- ❌ 不要硬编码配置值，要支持环境变量
- ❌ 不要忽略权限控制和安全验证
- ❌ 不要忘记添加操作日志和监控指标

---

## 信心评估

**评分**: 9/10

**评估理由**:
1. **完整性**: 覆盖了数据源管理、监控任务、快照管理、告警通知的完整功能
2. **架构合理**: 基于现有Spring Boot + Vue3架构，遵循现有代码规范
3. **中间件集成**: 正确匹配和集成了5个企业中间件
4. **非侵入式**: 不影响现有系统功能，支持功能开关
5. **可验证性**: 提供了完整的验证门控和测试方案

**需要改进的地方**:
- 需要在实际开发中根据具体的企业中间件版本调整配置
- 需要根据实际的数据库环境调整SQL脚本
