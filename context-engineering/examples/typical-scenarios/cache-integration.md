# 典型场景：为现有查询添加缓存功能

## 场景描述
- 现有用户查询服务响应较慢
- 需要添加缓存提高查询性能
- 缓存失败时降级到数据库查询
- 支持缓存的启用和禁用

## 关键设计模式

### 1. 透明缓存集成
- 现有查询逻辑保持不变
- 缓存作为透明层添加
- 缓存失败自动降级到数据库

### 2. 缓存策略
- 查询时先检查缓存
- 缓存未命中时查询数据库并更新缓存
- 数据更新时主动清除相关缓存

### 3. 配置驱动
- 支持缓存的开启和关闭
- 可配置缓存TTL和策略
- 支持不同查询方法的独立缓存配置

## 代码示例

### 现有代码（保持不变）

```java
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String username;
    private String email;
    private String phone;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 现有的getter/setter方法保持不变
    // ...
}

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 现有的查询方法保持不变
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    List<User> findByCreateTimeBetween(LocalDateTime start, LocalDateTime end);
}
```

### 现有服务类（需要增强）

```java
@Service
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    // ============ 新增：缓存相关依赖（可选注入） ============
    
    @Autowired(required = false)
    private CacheManager cacheManager;
    
    @Value("${cache.user.enabled:false}")
    private boolean userCacheEnabled;
    
    @Value("${cache.user.ttl:3600}")
    private int userCacheTtl;
    
    private static final String USER_CACHE_NAME = "users";
    private static final String USER_BY_ID_PREFIX = "user:id:";
    private static final String USER_BY_USERNAME_PREFIX = "user:username:";
    private static final String USER_BY_EMAIL_PREFIX = "user:email:";
    
    // ============ 现有方法（增强缓存功能） ============
    
    public User getUserById(Long userId) {
        if (userId == null) {
            return null;
        }
        
        // 1. 尝试从缓存获取
        User cachedUser = getCachedUser(USER_BY_ID_PREFIX + userId);
        if (cachedUser != null) {
            log.debug("用户缓存命中: userId={}", userId);
            return cachedUser;
        }
        
        // 2. 缓存未命中，从数据库查询
        Optional<User> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            // 3. 将结果存入缓存
            setCachedUser(user);
            return user;
        }
        
        return null;
    }
    
    public User getUserByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        // 1. 尝试从缓存获取
        User cachedUser = getCachedUser(USER_BY_USERNAME_PREFIX + username);
        if (cachedUser != null) {
            log.debug("用户名缓存命中: username={}", username);
            return cachedUser;
        }
        
        // 2. 缓存未命中，从数据库查询
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            // 3. 将结果存入缓存
            setCachedUser(user);
            return user;
        }
        
        return null;
    }
    
    public User getUserByEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return null;
        }
        
        // 1. 尝试从缓存获取
        User cachedUser = getCachedUser(USER_BY_EMAIL_PREFIX + email);
        if (cachedUser != null) {
            log.debug("邮箱缓存命中: email={}", email);
            return cachedUser;
        }
        
        // 2. 缓存未命中，从数据库查询
        Optional<User> userOpt = userRepository.findByEmail(email);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            // 3. 将结果存入缓存
            setCachedUser(user);
            return user;
        }
        
        return null;
    }
    
    // ============ 现有更新方法（增强缓存清除） ============
    
    public User updateUser(Long userId, UpdateUserRequest request) {
        // 1. 现有更新逻辑（保持不变）
        Optional<User> userOpt = userRepository.findById(userId);
        if (!userOpt.isPresent()) {
            throw new UserNotFoundException("用户不存在: " + userId);
        }
        
        User user = userOpt.get();
        String oldUsername = user.getUsername();
        String oldEmail = user.getEmail();
        
        // 更新用户信息
        if (request.getUsername() != null) {
            user.setUsername(request.getUsername());
        }
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
        }
        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }
        user.setUpdateTime(LocalDateTime.now());
        
        user = userRepository.save(user);
        
        // ============ 新增：清除相关缓存 ============
        evictUserCache(user, oldUsername, oldEmail);
        
        return user;
    }
    
    public void deleteUser(Long userId) {
        // 1. 获取用户信息（用于缓存清除）
        Optional<User> userOpt = userRepository.findById(userId);
        
        // 2. 现有删除逻辑（保持不变）
        userRepository.deleteById(userId);
        
        // ============ 新增：清除相关缓存 ============
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            evictUserCache(user, user.getUsername(), user.getEmail());
        }
    }
    
    // ============ 新增：缓存操作方法 ============
    
    /**
     * 从缓存获取用户
     */
    private User getCachedUser(String cacheKey) {
        if (!userCacheEnabled || cacheManager == null) {
            return null;
        }
        
        try {
            Cache cache = cacheManager.getCache(USER_CACHE_NAME);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(cacheKey);
                if (wrapper != null) {
                    return (User) wrapper.get();
                }
            }
        } catch (Exception e) {
            log.warn("缓存获取失败: key={}, error={}", cacheKey, e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 将用户存入缓存
     */
    private void setCachedUser(User user) {
        if (!userCacheEnabled || cacheManager == null || user == null) {
            return;
        }
        
        try {
            Cache cache = cacheManager.getCache(USER_CACHE_NAME);
            if (cache != null) {
                // 按ID缓存
                cache.put(USER_BY_ID_PREFIX + user.getId(), user);
                
                // 按用户名缓存
                if (user.getUsername() != null) {
                    cache.put(USER_BY_USERNAME_PREFIX + user.getUsername(), user);
                }
                
                // 按邮箱缓存
                if (user.getEmail() != null) {
                    cache.put(USER_BY_EMAIL_PREFIX + user.getEmail(), user);
                }
                
                log.debug("用户缓存存储成功: userId={}", user.getId());
            }
        } catch (Exception e) {
            log.warn("缓存存储失败: userId={}, error={}", user.getId(), e.getMessage());
        }
    }
    
    /**
     * 清除用户相关缓存
     */
    private void evictUserCache(User user, String oldUsername, String oldEmail) {
        if (!userCacheEnabled || cacheManager == null || user == null) {
            return;
        }
        
        try {
            Cache cache = cacheManager.getCache(USER_CACHE_NAME);
            if (cache != null) {
                // 清除ID缓存
                cache.evict(USER_BY_ID_PREFIX + user.getId());
                
                // 清除当前用户名缓存
                if (user.getUsername() != null) {
                    cache.evict(USER_BY_USERNAME_PREFIX + user.getUsername());
                }
                
                // 清除旧用户名缓存
                if (oldUsername != null && !oldUsername.equals(user.getUsername())) {
                    cache.evict(USER_BY_USERNAME_PREFIX + oldUsername);
                }
                
                // 清除当前邮箱缓存
                if (user.getEmail() != null) {
                    cache.evict(USER_BY_EMAIL_PREFIX + user.getEmail());
                }
                
                // 清除旧邮箱缓存
                if (oldEmail != null && !oldEmail.equals(user.getEmail())) {
                    cache.evict(USER_BY_EMAIL_PREFIX + oldEmail);
                }
                
                log.info("用户缓存已清除: userId={}", user.getId());
            }
        } catch (Exception e) {
            log.warn("缓存清除失败: userId={}, error={}", user.getId(), e.getMessage());
        }
    }
    
    // ============ 现有其他方法（保持不变） ============
    
    public List<User> getUsersByDateRange(LocalDateTime start, LocalDateTime end) {
        // 注意：列表查询通常不适合缓存，保持原有逻辑
        return userRepository.findByCreateTimeBetween(start, end);
    }
}
```

### 缓存配置类

```java
@Configuration
@EnableCaching
@ConditionalOnProperty(name = "cache.user.enabled", havingValue = "true")
public class UserCacheConfig {
    
    @Value("${cache.user.ttl:3600}")
    private int userCacheTtl;
    
    @Value("${cache.user.max-size:10000}")
    private int maxSize;
    
    @Bean
    public CacheManager userCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(maxSize)
            .expireAfterWrite(userCacheTtl, TimeUnit.SECONDS)
            .recordStats());
        return cacheManager;
    }
}
```

### 配置文件示例

```yaml
# application.yml

# 用户缓存配置
cache:
  user:
    enabled: ${USER_CACHE_ENABLED:true}
    ttl: ${USER_CACHE_TTL:3600}      # 1小时
    max-size: ${USER_CACHE_SIZE:10000}
    
# Spring Cache配置
spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=3600s
```

### 测试示例

```java
@SpringBootTest
class UserServiceCacheTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Test
    void testUserCacheById() {
        // 准备测试数据
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user = userRepository.save(user);
        
        // 第一次查询 - 缓存未命中
        User result1 = userService.getUserById(user.getId());
        assertThat(result1).isNotNull();
        assertThat(result1.getUsername()).isEqualTo("testuser");
        
        // 第二次查询 - 缓存命中
        User result2 = userService.getUserById(user.getId());
        assertThat(result2).isNotNull();
        assertThat(result2.getUsername()).isEqualTo("testuser");
        
        // 验证缓存存在
        Cache cache = cacheManager.getCache("users");
        assertThat(cache.get("user:id:" + user.getId())).isNotNull();
    }
    
    @Test
    void testUserCacheEviction() {
        // 准备测试数据
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user = userRepository.save(user);
        
        // 查询并缓存
        userService.getUserById(user.getId());
        
        // 更新用户
        UpdateUserRequest request = new UpdateUserRequest();
        request.setUsername("newusername");
        userService.updateUser(user.getId(), request);
        
        // 验证缓存已清除
        Cache cache = cacheManager.getCache("users");
        assertThat(cache.get("user:id:" + user.getId())).isNull();
        assertThat(cache.get("user:username:testuser")).isNull();
    }
}
```

## 实现要点总结

1. **透明缓存集成**：现有查询逻辑保持不变，缓存作为透明层添加
2. **多维度缓存**：支持按ID、用户名、邮箱等多个维度缓存
3. **主动缓存清除**：数据更新时主动清除相关缓存，保证数据一致性
4. **降级保护**：缓存操作失败不影响业务逻辑，自动降级到数据库查询
5. **配置驱动**：支持缓存的开启关闭和参数配置
