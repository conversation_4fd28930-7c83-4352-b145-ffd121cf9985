# 典型场景：在现有订单系统上增加积分功能

## 场景描述
- 现有订单系统已稳定运行
- 需要在下单成功后发放积分
- 积分发放失败不能影响下单流程
- 支持功能开关控制

## 关键设计模式

### 1. 非侵入式集成
- 现有核心逻辑保持不变
- 新功能作为可选步骤添加
- 使用`@Autowired(required = false)`可选注入

### 2. 降级保护
- 新功能异常不影响主流程
- 使用try-catch包装新功能调用
- 提供功能开关控制

### 3. 配置驱动
- 使用`@ConditionalOnProperty`控制Bean创建
- 支持环境变量配置
- 默认关闭新功能

## 代码示例

### 现有代码（保持不变）

```java
@Entity
@Table(name = "orders")
public class Order {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private Long userId;
    private BigDecimal amount;
    private String status;
    private LocalDateTime createTime;
    
    // 现有的getter/setter方法保持不变
    // ...
}

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    // 现有的查询方法保持不变
    List<Order> findByUserId(Long userId);
    List<Order> findByStatus(String status);
}
```

### 现有服务类（需要增强）

```java
@Service
public class OrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    // 现有依赖保持不变
    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private InventoryService inventoryService;
    
    // ============ 新增：积分相关依赖（可选注入） ============
    
    @Autowired(required = false)
    private PointsService pointsService;
    
    @Value("${feature.points.enabled:false}")
    private boolean pointsEnabled;
    
    // ============ 现有方法（核心逻辑不变） ============
    
    public Order createOrder(CreateOrderRequest request) {
        // 1. 现有业务逻辑（保持不变）
        validateOrderRequest(request);
        
        // 2. 库存检查（现有逻辑）
        inventoryService.checkStock(request.getProductId(), request.getQuantity());
        
        // 3. 创建订单（现有逻辑）
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setAmount(request.getAmount());
        order.setStatus("CREATED");
        order.setCreateTime(LocalDateTime.now());
        
        order = orderRepository.save(order);
        
        // 4. 支付处理（现有逻辑）
        PaymentResult paymentResult = paymentService.processPayment(order);
        
        if (paymentResult.isSuccess()) {
            order.setStatus("PAID");
            order = orderRepository.save(order);
            
            // ============ 新增：积分发放（非侵入式） ============
            handlePointsReward(order);
        }
        
        return order;
    }
    
    // ============ 新增：积分处理方法 ============
    
    /**
     * 处理积分奖励
     * 注意：此方法失败不影响主流程
     */
    private void handlePointsReward(Order order) {
        // 功能开关检查
        if (!pointsEnabled) {
            log.debug("积分功能未启用，跳过积分发放");
            return;
        }
        
        // 服务可用性检查
        if (pointsService == null) {
            log.warn("积分服务不可用，跳过积分发放");
            return;
        }
        
        try {
            // 计算积分
            int points = calculatePoints(order.getAmount());
            
            // 发放积分
            pointsService.awardPoints(order.getUserId(), points, "订单奖励", order.getId());
            
            log.info("积分发放成功: userId={}, orderId={}, points={}", 
                order.getUserId(), order.getId(), points);
                
        } catch (Exception e) {
            // 积分发放失败不影响订单流程
            log.error("积分发放失败: userId={}, orderId={}, error={}", 
                order.getUserId(), order.getId(), e.getMessage());
        }
    }
    
    /**
     * 积分计算规则
     */
    private int calculatePoints(BigDecimal amount) {
        // 简单规则：每消费1元获得1积分
        return amount.intValue();
    }
    
    // ============ 现有私有方法（保持不变） ============
    
    private void validateOrderRequest(CreateOrderRequest request) {
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("订单金额必须大于0");
        }
    }
}
```

### 新增：积分服务接口

```java
public interface PointsService {
    /**
     * 发放积分
     */
    void awardPoints(Long userId, int points, String reason, Long orderId);
    
    /**
     * 查询用户积分
     */
    int getUserPoints(Long userId);
}
```

### 新增：积分服务实现

```java
@Service
@ConditionalOnProperty(name = "feature.points.enabled", havingValue = "true")
public class PointsServiceImpl implements PointsService {
    
    @Autowired
    private PointsRepository pointsRepository;
    
    // 可选：集成企业中间件
    @Autowired(required = false)
    private CacheService cacheService;
    
    @Autowired(required = false)
    private MessageService messageService;
    
    @Override
    public void awardPoints(Long userId, int points, String reason, Long orderId) {
        // 1. 保存积分记录
        PointsRecord record = new PointsRecord();
        record.setUserId(userId);
        record.setPoints(points);
        record.setReason(reason);
        record.setOrderId(orderId);
        record.setCreateTime(LocalDateTime.now());
        
        pointsRepository.save(record);
        
        // 2. 更新用户总积分
        updateUserTotalPoints(userId, points);
        
        // 3. 发送积分变动通知（可选）
        sendPointsNotification(userId, points, reason);
    }
    
    @Override
    public int getUserPoints(Long userId) {
        // 先从缓存获取
        if (cacheService != null) {
            Integer cachedPoints = cacheService.get("user:points:" + userId, Integer.class);
            if (cachedPoints != null) {
                return cachedPoints;
            }
        }
        
        // 从数据库获取
        int totalPoints = pointsRepository.sumPointsByUserId(userId);
        
        // 更新缓存
        if (cacheService != null) {
            cacheService.put("user:points:" + userId, totalPoints, Duration.ofMinutes(30));
        }
        
        return totalPoints;
    }
    
    private void updateUserTotalPoints(Long userId, int points) {
        // 更新数据库中的用户总积分
        // 这里可以是更新用户表，或者实时计算
    }
    
    private void sendPointsNotification(Long userId, int points, String reason) {
        try {
            if (messageService != null) {
                PointsNotification notification = new PointsNotification();
                notification.setUserId(userId);
                notification.setPoints(points);
                notification.setReason(reason);
                
                messageService.sendNotification(notification);
            }
        } catch (Exception e) {
            log.warn("积分通知发送失败: userId={}, error={}", userId, e.getMessage());
        }
    }
}
```

### 新增：配置类

```java
@Configuration
public class PointsConfig {
    
    @Bean
    @ConditionalOnProperty(name = "feature.points.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public PointsService pointsService() {
        return new PointsServiceImpl();
    }
}
```

### 配置文件示例

```yaml
# application.yml

# 积分功能配置
feature:
  points:
    enabled: ${POINTS_ENABLED:false}  # 默认关闭
    
# 如果启用积分功能，可能需要的中间件配置
# 这些配置会根据实际使用的中间件自动生成

spring:
  datasource:
    # 现有数据源配置保持不变
    
# 企业中间件配置（如果需要）
enterprise:
  cache:
    enabled: ${CACHE_ENABLED:false}
  message:
    enabled: ${MESSAGE_ENABLED:false}
```

### 测试示例

```java
@SpringBootTest
class OrderServiceTest {
    
    @Autowired
    private OrderService orderService;
    
    @MockBean
    private PointsService pointsService;
    
    @Test
    void testCreateOrderWithPoints() {
        // 准备测试数据
        CreateOrderRequest request = new CreateOrderRequest();
        request.setUserId(1L);
        request.setAmount(new BigDecimal("100.00"));
        
        // 执行测试
        Order order = orderService.createOrder(request);
        
        // 验证订单创建成功
        assertThat(order.getId()).isNotNull();
        assertThat(order.getStatus()).isEqualTo("PAID");
        
        // 验证积分发放被调用（如果启用）
        if (pointsService != null) {
            verify(pointsService).awardPoints(eq(1L), eq(100), eq("订单奖励"), eq(order.getId()));
        }
    }
    
    @Test
    void testCreateOrderWithPointsFailure() {
        // 模拟积分服务异常
        doThrow(new RuntimeException("积分服务异常")).when(pointsService)
            .awardPoints(anyLong(), anyInt(), anyString(), anyLong());
        
        CreateOrderRequest request = new CreateOrderRequest();
        request.setUserId(1L);
        request.setAmount(new BigDecimal("100.00"));
        
        // 执行测试 - 订单应该仍然成功创建
        Order order = orderService.createOrder(request);
        
        // 验证订单创建成功（不受积分服务影响）
        assertThat(order.getId()).isNotNull();
        assertThat(order.getStatus()).isEqualTo("PAID");
    }
}
```

## 实现要点总结

1. **非侵入式集成**：现有核心逻辑保持不变，新功能作为可选步骤添加
2. **降级保护**：新功能异常不影响主流程，使用try-catch包装
3. **配置驱动**：使用`@ConditionalOnProperty`控制Bean创建，支持环境变量配置
4. **测试友好**：新功能可以独立测试，支持Mock测试，验证降级逻辑
