# Java Context Engineering 示例说明

## 📁 目录结构

```
examples/
├── README.md                    # 本文件
├── typical-scenarios/           # 典型需求场景示例
│   ├── README.md
│   ├── order-enhancement.md     # 订单系统积分功能增强
│   └── cache-integration.md     # 查询缓存集成
├── middleware-integration/      # 中间件集成示例
│   ├── README.md
│   └── cache-fallback.md       # 多级缓存降级处理
└── code-patterns/              # 代码模式参考
    ├── README.md
    ├── service-layer-patterns.md
    └── testing-patterns.md
```

## 🎯 示例文件格式说明

### 为什么使用Markdown格式？

我们将示例代码从`.java`文件转换为`.md`文件，原因如下：

1. **避免IDE报错**：`.java`文件在没有完整工程环境时会产生编译错误
2. **保持AI参考价值**：Markdown中的代码块仍然能为AI提供完整的代码参考
3. **更好的文档体验**：支持丰富的说明文档和代码注释
4. **易于维护**：不需要维护复杂的依赖关系

### Markdown示例文件结构

每个示例文件包含以下部分：

```markdown
# 场景标题

## 场景描述
- 业务背景
- 技术要求
- 约束条件

## 关键设计模式
- 设计原则说明
- 架构模式介绍

## 代码示例
```java
// 完整的Java代码示例
```

## 实现要点总结
- 关键技术点
- 最佳实践
```

## 🔧 其他解决方案

如果您希望在IDE中有完整的Java项目体验，可以考虑以下方案：

### 方案A：创建独立示例项目

```bash
# 在examples目录下创建独立的Spring Boot项目
cd examples
mkdir sample-project
cd sample-project

# 使用Spring Initializr创建项目
curl https://start.spring.io/starter.zip \
  -d dependencies=web,jpa,cache,redis \
  -d groupId=com.example \
  -d artifactId=context-engineering-samples \
  -d name=context-engineering-samples \
  -d packageName=com.example.samples \
  -o sample-project.zip

unzip sample-project.zip
```

### 方案B：使用代码片段文件

创建`.java.snippet`文件，IDE不会尝试编译但仍能提供语法高亮：

```bash
# 重命名示例文件
mv order-enhancement.md order-enhancement.java.snippet
mv cache-integration.md cache-integration.java.snippet
```

### 方案C：使用IDE排除规则

在IDE中排除examples目录的编译检查：

```xml
<!-- 在.idea/compiler.xml中添加 -->
<excludeFromCompile>
  <directory url="file://$PROJECT_DIR$/java-context-engineering/examples" includeSubdirectories="true" />
</excludeFromCompile>
```

## 📚 如何使用这些示例

### 1. AI参考使用

在INITIAL.md中引用示例：

```markdown
## EXAMPLES:
参考 examples/typical-scenarios/order-enhancement.md 中的非侵入式集成模式，
特别是：
- 可选依赖注入的使用方式
- 降级保护的异常处理
- 配置开关的实现方法
```

### 2. 开发参考使用

开发时可以：
1. 打开对应的.md文件查看完整代码
2. 复制代码块到实际项目中
3. 根据示例调整具体实现

### 3. 模式学习使用

每个示例都展示了特定的设计模式：
- **非侵入式集成**：新功能不影响现有代码
- **降级保护**：新功能失败不影响主流程
- **配置驱动**：支持功能的开启和关闭
- **多级缓存**：企业中间件的降级策略

## 🛠️ 维护指南

### 添加新示例

1. 创建新的.md文件
2. 按照标准结构编写内容
3. 更新对应目录的README.md
4. 在主README.md中添加引用

### 更新现有示例

1. 直接编辑.md文件
2. 确保代码示例的完整性
3. 更新相关文档说明

### 验证示例质量

使用提供的工具脚本验证：

```bash
# 验证示例文档格式
./tools/validate-examples.sh

# 检查代码片段语法
./tools/check-code-syntax.sh examples/
```

## 💡 最佳实践

1. **保持示例简洁**：专注于展示特定模式，避免过度复杂
2. **提供完整上下文**：包含必要的依赖和配置信息
3. **注重实用性**：示例应该能直接应用到实际项目中
4. **定期更新**：保持与最新技术栈和最佳实践同步

## 🔗 相关资源

- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [企业中间件文档](../middleware-docs/)
- [Context Engineering最佳实践](../README.md)
