# 测试模式和最佳实践

## 概述
测试是保证代码质量的重要手段。本文档总结了Java项目中常见的测试模式，包括单元测试、集成测试和Mock使用方式。

## 代码示例

### 基础单元测试模式
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private EmailService emailService;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    @DisplayName("创建用户成功")
    void shouldCreateUserSuccessfully() {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        
        User savedUser = new User();
        savedUser.setId(1L);
        savedUser.setUsername("testuser");
        savedUser.setEmail("<EMAIL>");
        
        when(userRepository.existsByUsername("testuser")).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(savedUser);
        
        // When
        User result = userService.createUser(request);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getUsername()).isEqualTo("testuser");
        
        verify(userRepository).existsByUsername("testuser");
        verify(userRepository).save(any(User.class));
        verify(emailService).sendWelcomeEmail("<EMAIL>");
    }
    
    @Test
    @DisplayName("用户名已存在时抛出异常")
    void shouldThrowExceptionWhenUsernameExists() {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("existinguser");
        
        when(userRepository.existsByUsername("existinguser")).thenReturn(true);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(request))
            .isInstanceOf(BusinessException.class)
            .hasMessage("用户名已存在");
        
        verify(userRepository).existsByUsername("existinguser");
        verify(userRepository, never()).save(any(User.class));
        verify(emailService, never()).sendWelcomeEmail(anyString());
    }
}
```

### 参数化测试模式
```java
class ValidationTest {
    
    @ParameterizedTest
    @DisplayName("用户名验证测试")
    @ValueSource(strings = {"", " ", "  ", "\t", "\n"})
    void shouldRejectInvalidUsernames(String username) {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername(username);
        
        // When & Then
        assertThatThrownBy(() -> userService.createUser(request))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("用户名不能为空");
    }
    
    @ParameterizedTest
    @DisplayName("邮箱格式验证")
    @CsvSource({
        "<EMAIL>, true",
        "invalid-email, false",
        "@example.com, false",
        "test@, false",
        "test.example.com, false"
    })
    void shouldValidateEmailFormat(String email, boolean expected) {
        // When
        boolean result = EmailValidator.isValid(email);
        
        // Then
        assertThat(result).isEqualTo(expected);
    }
    
    @ParameterizedTest
    @DisplayName("积分计算测试")
    @MethodSource("provideOrderAmountsAndExpectedPoints")
    void shouldCalculatePointsCorrectly(BigDecimal amount, int expectedPoints) {
        // Given
        Order order = new Order();
        order.setAmount(amount);
        
        // When
        int points = pointsService.calculatePoints(order);
        
        // Then
        assertThat(points).isEqualTo(expectedPoints);
    }
    
    private static Stream<Arguments> provideOrderAmountsAndExpectedPoints() {
        return Stream.of(
            Arguments.of(new BigDecimal("100.00"), 100),
            Arguments.of(new BigDecimal("99.99"), 99),
            Arguments.of(new BigDecimal("0.01"), 0),
            Arguments.of(new BigDecimal("1000.00"), 1000)
        );
    }
}
```

### Spring Boot集成测试模式
```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "feature.points.enabled=true"
})
@Transactional
class UserServiceIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRepository userRepository;
    
    @MockBean
    private EmailService emailService;
    
    @Test
    @DisplayName("用户创建集成测试")
    void shouldCreateUserWithDatabaseIntegration() {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("integrationtest");
        request.setEmail("<EMAIL>");
        
        // When
        User createdUser = userService.createUser(request);
        
        // Then
        assertThat(createdUser.getId()).isNotNull();
        
        // 验证数据库中的数据
        Optional<User> savedUser = userRepository.findById(createdUser.getId());
        assertThat(savedUser).isPresent();
        assertThat(savedUser.get().getUsername()).isEqualTo("integrationtest");
        
        // 验证邮件服务被调用
        verify(emailService).sendWelcomeEmail("<EMAIL>");
    }
    
    @Test
    @DisplayName("用户查询集成测试")
    void shouldFindUserById() {
        // Given - 先创建用户
        User user = new User();
        user.setUsername("querytest");
        user.setEmail("<EMAIL>");
        user.setCreateTime(LocalDateTime.now());
        User savedUser = userRepository.save(user);
        
        // When
        User foundUser = userService.getUserById(savedUser.getId());
        
        // Then
        assertThat(foundUser).isNotNull();
        assertThat(foundUser.getUsername()).isEqualTo("querytest");
    }
}
```

### Web层测试模式
```java
@WebMvcTest(UserController.class)
class UserControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    @DisplayName("创建用户API测试")
    void shouldCreateUserViaAPI() throws Exception {
        // Given
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("apitest");
        request.setEmail("<EMAIL>");
        
        User createdUser = new User();
        createdUser.setId(1L);
        createdUser.setUsername("apitest");
        createdUser.setEmail("<EMAIL>");
        
        when(userService.createUser(any(CreateUserRequest.class))).thenReturn(createdUser);
        
        // When & Then
        mockMvc.perform(post("/api/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.username").value("apitest"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
        
        verify(userService).createUser(any(CreateUserRequest.class));
    }
    
    @Test
    @DisplayName("获取用户API测试")
    void shouldGetUserViaAPI() throws Exception {
        // Given
        Long userId = 1L;
        User user = new User();
        user.setId(userId);
        user.setUsername("gettest");
        
        when(userService.getUserById(userId)).thenReturn(user);
        
        // When & Then
        mockMvc.perform(get("/api/users/{id}", userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.username").value("gettest"));
    }
    
    @Test
    @DisplayName("用户不存在时返回404")
    void shouldReturn404WhenUserNotFound() throws Exception {
        // Given
        Long userId = 999L;
        when(userService.getUserById(userId)).thenThrow(new UserNotFoundException("用户不存在"));
        
        // When & Then
        mockMvc.perform(get("/api/users/{id}", userId))
                .andExpect(status().isNotFound());
    }
}
```

### 异步方法测试模式
```java
@ExtendWith(MockitoExtension.class)
class EmailServiceTest {
    
    @Mock
    private EmailRepository emailRepository;
    
    @Mock
    private EmailSender emailSender;
    
    @InjectMocks
    private EmailService emailService;
    
    @Test
    @DisplayName("异步发送邮件测试")
    void shouldSendEmailAsynchronously() throws Exception {
        // Given
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String content = "Test Content";
        
        Email savedEmail = new Email();
        savedEmail.setId(1L);
        savedEmail.setTo(to);
        savedEmail.setStatus(EmailStatus.SENDING);
        
        when(emailRepository.save(any(Email.class))).thenReturn(savedEmail);
        
        // When
        CompletableFuture<Void> future = emailService.sendEmailAsync(to, subject, content);
        
        // Then
        assertThat(future).succeedsWithin(Duration.ofSeconds(5));
        
        verify(emailRepository, times(2)).save(any(Email.class)); // 创建和更新
        verify(emailSender).send(to, subject, content);
    }
    
    @Test
    @DisplayName("邮件发送失败处理测试")
    void shouldHandleEmailSendingFailure() throws Exception {
        // Given
        String to = "<EMAIL>";
        String subject = "Test Subject";
        String content = "Test Content";
        
        when(emailRepository.save(any(Email.class))).thenReturn(new Email());
        doThrow(new RuntimeException("发送失败")).when(emailSender).send(to, subject, content);
        
        // When
        CompletableFuture<Void> future = emailService.sendEmailAsync(to, subject, content);
        
        // Then
        assertThat(future).failsWithin(Duration.ofSeconds(5))
                .withThrowableOfType(ExecutionException.class);
    }
}
```

### 测试数据构建模式
```java
// 测试数据构建器
public class UserTestDataBuilder {
    
    private Long id;
    private String username = "defaultuser";
    private String email = "<EMAIL>";
    private LocalDateTime createTime = LocalDateTime.now();
    
    public static UserTestDataBuilder aUser() {
        return new UserTestDataBuilder();
    }
    
    public UserTestDataBuilder withId(Long id) {
        this.id = id;
        return this;
    }
    
    public UserTestDataBuilder withUsername(String username) {
        this.username = username;
        return this;
    }
    
    public UserTestDataBuilder withEmail(String email) {
        this.email = email;
        return this;
    }
    
    public UserTestDataBuilder withCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
        return this;
    }
    
    public User build() {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setEmail(email);
        user.setCreateTime(createTime);
        return user;
    }
}

// 使用测试数据构建器
@Test
void shouldProcessUserCorrectly() {
    // Given
    User user = UserTestDataBuilder.aUser()
        .withId(1L)
        .withUsername("testuser")
        .withEmail("<EMAIL>")
        .build();
    
    // When & Then
    // 测试逻辑...
}
```

### Mock配置模式
```java
@TestConfiguration
public class TestConfig {
    
    @Bean
    @Primary
    public EmailService mockEmailService() {
        return Mockito.mock(EmailService.class);
    }
    
    @Bean
    @Primary
    public PaymentGateway mockPaymentGateway() {
        PaymentGateway mock = Mockito.mock(PaymentGateway.class);
        
        // 默认行为配置
        when(mock.charge(any(PaymentRequest.class)))
            .thenReturn(new PaymentResponse("SUCCESS", "txn_123"));
        
        return mock;
    }
}

// 在测试类中使用
@SpringBootTest
@Import(TestConfig.class)
class PaymentServiceIntegrationTest {
    
    @Autowired
    private PaymentService paymentService;
    
    @Autowired
    private PaymentGateway paymentGateway; // 这是Mock对象
    
    @Test
    void shouldProcessPaymentWithMockedGateway() {
        // Given
        PaymentRequest request = new PaymentRequest();
        request.setAmount(new BigDecimal("100.00"));
        
        // When
        PaymentResult result = paymentService.processPayment(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        verify(paymentGateway).charge(request);
    }
}
```

## 最佳实践

### 1. 测试命名
- **使用@DisplayName注解**，提供清晰的测试描述
- **方法名采用should_when_given模式**，明确测试意图
- **测试类名以Test结尾**，便于识别和运行

### 2. 测试结构
- **使用Given-When-Then结构**，清晰分离测试步骤
- **一个测试方法只验证一个行为**，保持测试的单一职责
- **使用@Nested注解分组相关测试**，提高测试组织性

### 3. Mock使用
- **只Mock直接依赖**，避免过度Mock
- **使用@MockBean替换Spring容器中的Bean**，进行集成测试
- **验证重要的交互**，但不要过度验证

### 4. 测试数据
- **使用测试数据构建器**，提高测试数据的可维护性
- **每个测试独立准备数据**，避免测试间的依赖
- **使用有意义的测试数据**，便于理解测试意图

### 5. 异常测试
- **使用assertThatThrownBy验证异常**，比@Test(expected)更精确
- **验证异常消息**，确保错误信息的准确性
- **测试异常场景下的副作用**，如事务回滚

## 注意事项

### 避免的反模式
- ❌ **测试实现细节**：测试应该关注行为而不是实现
- ❌ **脆弱的测试**：过度依赖Mock的调用顺序和次数
- ❌ **测试间依赖**：一个测试的失败导致其他测试失败
- ❌ **过度Mock**：Mock了不必要的依赖，降低测试价值

### 性能考虑
- **使用@MockBean谨慎**，会重启Spring上下文
- **合理使用@DirtiesContext**，避免不必要的上下文重启
- **大型集成测试使用TestContainers**，提供真实的外部依赖

### 维护性
- **定期重构测试代码**，保持测试的可读性
- **删除无用的测试**，避免测试代码膨胀
- **保持测试和生产代码的同步**，及时更新测试