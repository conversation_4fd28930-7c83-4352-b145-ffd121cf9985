# 代码模式参考

本目录包含常用的Java代码模式和最佳实践，支持extract-patterns命令自动提取和更新。

## 📁 模式分类

### 核心模式
- `service-layer-patterns.md` - 服务层设计模式
- `configuration-patterns.md` - Spring配置模式
- `error-handling-patterns.md` - 异常处理模式

### 测试模式
- `testing-patterns.md` - 单元测试和集成测试模式
- `mock-patterns.md` - Mock和测试数据模式

### 集成模式
- `middleware-integration-patterns.md` - 中间件集成模式
- `data-access-patterns.md` - 数据访问模式

## 🔧 自动提取机制

### extract-patterns命令
使用 `/extract-patterns` 命令可以自动从现有项目代码中提取常见模式：

```bash
# 在Claude Code中运行
/extract-patterns
```

### 可自动识别的模式
1. **服务层模式**
   - @Service注解的类结构
   - 依赖注入模式
   - 业务逻辑组织方式

2. **配置模式**
   - @Configuration类的结构
   - @Bean定义模式
   - 条件化配置模式

3. **测试模式**
   - JUnit测试类结构
   - Mock使用方式
   - 测试数据准备模式

4. **异常处理模式**
   - 全局异常处理器
   - 业务异常定义
   - 错误响应格式

5. **数据访问模式**
   - Repository接口定义
   - JPA实体映射
   - 查询方法命名

## 📋 模式文档结构

每个模式文档包含：

```markdown
# 模式名称

## 概述
模式的简要描述和适用场景

## 代码示例

### 基础用法
```java
// 基础代码示例
```

### 高级用法
```java
// 复杂场景的代码示例
```

## 最佳实践
- 实践要点1
- 实践要点2

## 注意事项
- 需要注意的问题
- 常见陷阱
```

## 🎯 使用方式

### 手动维护
对于复杂的业务模式，可以手动创建和维护模式文档。

### 自动提取
对于通用的技术模式，使用extract-patterns命令自动提取和更新。

### 混合模式
结合手动维护和自动提取，保持模式文档的准确性和时效性。

## 📊 模式更新策略

1. **定期提取** - 定期运行extract-patterns更新通用模式
2. **增量更新** - 发现新模式时手动添加
3. **质量检查** - 定期检查模式文档的准确性
4. **版本管理** - 跟踪模式的变化和演进