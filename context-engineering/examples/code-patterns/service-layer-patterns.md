# 服务层设计模式

## 概述
服务层是业务逻辑的核心，负责协调不同组件完成业务功能。本文档总结了常见的服务层设计模式和最佳实践。

## 代码示例

### 基础服务类结构
```java
@Service
@Transactional(readOnly = true)
public class UserService {
    
    private final UserRepository userRepository;
    private final EmailService emailService;
    
    // 构造函数注入（推荐）
    public UserService(UserRepository userRepository, EmailService emailService) {
        this.userRepository = userRepository;
        this.emailService = emailService;
    }
    
    @Transactional
    public User createUser(CreateUserRequest request) {
        // 1. 参数验证
        validateCreateUserRequest(request);
        
        // 2. 业务逻辑
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setCreateTime(LocalDateTime.now());
        
        // 3. 持久化
        User savedUser = userRepository.save(user);
        
        // 4. 后续处理
        emailService.sendWelcomeEmail(savedUser.getEmail());
        
        return savedUser;
    }
    
    public User getUserById(Long userId) {
        return userRepository.findById(userId)
            .orElseThrow(() -> new UserNotFoundException("用户不存在: " + userId));
    }
    
    private void validateCreateUserRequest(CreateUserRequest request) {
        if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }
    }
}
```

### 可选依赖注入模式
```java
@Service
public class OrderService {
    
    private final OrderRepository orderRepository;
    
    // 可选依赖（用于功能增强）
    private final PointsService pointsService;
    private final NotificationService notificationService;
    
    public OrderService(OrderRepository orderRepository,
                       @Autowired(required = false) PointsService pointsService,
                       @Autowired(required = false) NotificationService notificationService) {
        this.orderRepository = orderRepository;
        this.pointsService = pointsService;
        this.notificationService = notificationService;
    }
    
    @Transactional
    public Order createOrder(CreateOrderRequest request) {
        // 核心业务逻辑
        Order order = new Order(request);
        order = orderRepository.save(order);
        
        // 可选功能（不影响核心流程）
        handleOptionalFeatures(order);
        
        return order;
    }
    
    private void handleOptionalFeatures(Order order) {
        // 积分发放（可选）
        if (pointsService != null) {
            try {
                pointsService.awardPoints(order.getUserId(), calculatePoints(order));
            } catch (Exception e) {
                log.warn("积分发放失败: orderId={}, error={}", order.getId(), e.getMessage());
            }
        }
        
        // 消息通知（可选）
        if (notificationService != null) {
            try {
                notificationService.sendOrderNotification(order);
            } catch (Exception e) {
                log.warn("订单通知发送失败: orderId={}, error={}", order.getId(), e.getMessage());
            }
        }
    }
}
```

### 服务层异常处理模式
```java
@Service
public class PaymentService {
    
    private final PaymentRepository paymentRepository;
    private final ExternalPaymentGateway paymentGateway;
    
    @Transactional
    public PaymentResult processPayment(PaymentRequest request) {
        try {
            // 1. 预处理
            validatePaymentRequest(request);
            
            // 2. 调用外部服务
            ExternalPaymentResponse response = paymentGateway.charge(request);
            
            // 3. 处理响应
            Payment payment = createPaymentRecord(request, response);
            paymentRepository.save(payment);
            
            return PaymentResult.success(payment.getId());
            
        } catch (ValidationException e) {
            log.warn("支付参数验证失败: {}", e.getMessage());
            return PaymentResult.failure("参数错误: " + e.getMessage());
            
        } catch (ExternalServiceException e) {
            log.error("外部支付服务异常: {}", e.getMessage());
            return PaymentResult.failure("支付服务暂时不可用");
            
        } catch (Exception e) {
            log.error("支付处理异常: request={}, error={}", request, e.getMessage(), e);
            return PaymentResult.failure("支付处理失败");
        }
    }
    
    private void validatePaymentRequest(PaymentRequest request) {
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ValidationException("支付金额必须大于0");
        }
        // 其他验证逻辑...
    }
}
```

### 批量操作模式
```java
@Service
public class UserBatchService {
    
    private final UserRepository userRepository;
    
    @Value("${batch.size:100}")
    private int batchSize;
    
    @Transactional
    public BatchResult<User> batchCreateUsers(List<CreateUserRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return BatchResult.empty();
        }
        
        List<User> successUsers = new ArrayList<>();
        List<BatchError> errors = new ArrayList<>();
        
        // 分批处理
        for (int i = 0; i < requests.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, requests.size());
            List<CreateUserRequest> batch = requests.subList(i, endIndex);
            
            processBatch(batch, successUsers, errors);
        }
        
        return new BatchResult<>(successUsers, errors);
    }
    
    private void processBatch(List<CreateUserRequest> batch, 
                             List<User> successUsers, 
                             List<BatchError> errors) {
        for (int i = 0; i < batch.size(); i++) {
            CreateUserRequest request = batch.get(i);
            try {
                User user = createSingleUser(request);
                successUsers.add(user);
            } catch (Exception e) {
                errors.add(new BatchError(i, request, e.getMessage()));
            }
        }
    }
    
    private User createSingleUser(CreateUserRequest request) {
        // 单个用户创建逻辑
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        return userRepository.save(user);
    }
}
```

### 异步处理模式
```java
@Service
public class EmailService {
    
    private final EmailRepository emailRepository;
    private final EmailSender emailSender;
    
    @Async("emailTaskExecutor")
    public CompletableFuture<Void> sendEmailAsync(String to, String subject, String content) {
        try {
            // 1. 创建邮件记录
            Email email = new Email();
            email.setTo(to);
            email.setSubject(subject);
            email.setContent(content);
            email.setStatus(EmailStatus.SENDING);
            email = emailRepository.save(email);
            
            // 2. 发送邮件
            emailSender.send(to, subject, content);
            
            // 3. 更新状态
            email.setStatus(EmailStatus.SENT);
            email.setSentTime(LocalDateTime.now());
            emailRepository.save(email);
            
            log.info("邮件发送成功: to={}, subject={}", to, subject);
            return CompletableFuture.completedFuture(null);
            
        } catch (Exception e) {
            log.error("邮件发送失败: to={}, subject={}, error={}", to, subject, e.getMessage());
            
            // 更新失败状态
            updateEmailStatus(to, subject, EmailStatus.FAILED);
            
            return CompletableFuture.failedFuture(e);
        }
    }
    
    @Async
    public void sendBulkEmails(List<EmailRequest> requests) {
        log.info("开始批量发送邮件: count={}", requests.size());
        
        List<CompletableFuture<Void>> futures = requests.stream()
            .map(request -> sendEmailAsync(request.getTo(), request.getSubject(), request.getContent()))
            .collect(Collectors.toList());
        
        // 等待所有邮件发送完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenRun(() -> log.info("批量邮件发送完成"))
            .exceptionally(throwable -> {
                log.error("批量邮件发送异常: {}", throwable.getMessage());
                return null;
            });
    }
}
```

## 最佳实践

### 1. 依赖注入
- **优先使用构造函数注入**，确保依赖的不可变性
- **使用final字段**，防止依赖被意外修改
- **可选依赖使用@Autowired(required = false)**，支持功能降级

### 2. 事务管理
- **类级别使用@Transactional(readOnly = true)**，默认只读事务
- **写操作方法使用@Transactional**，覆盖只读设置
- **异常处理要考虑事务回滚**，区分业务异常和系统异常

### 3. 异常处理
- **使用特定的业务异常类**，便于上层处理
- **记录详细的错误日志**，包含上下文信息
- **对外部服务调用进行异常包装**，避免暴露内部实现

### 4. 参数验证
- **在服务层进行业务规则验证**，不仅仅是格式验证
- **使用专门的验证方法**，保持主业务逻辑清晰
- **验证失败抛出明确的异常**，包含具体错误信息

### 5. 日志记录
- **记录关键业务操作**，便于问题排查
- **使用结构化日志**，包含必要的业务标识
- **区分不同级别的日志**，避免日志过多或过少

## 注意事项

### 避免的反模式
- ❌ **贫血模型**：服务类包含所有逻辑，实体类只有getter/setter
- ❌ **上帝类**：单个服务类承担过多职责
- ❌ **循环依赖**：服务之间相互依赖形成环路
- ❌ **事务滥用**：不必要的事务或事务范围过大

### 性能考虑
- **批量操作优于单个操作**，减少数据库交互次数
- **合理使用缓存**，避免重复查询
- **异步处理非关键路径**，提高响应速度
- **分页处理大数据集**，避免内存溢出

### 测试友好
- **依赖注入便于Mock**，提高单元测试覆盖率
- **方法职责单一**，便于编写针对性测试
- **避免静态方法调用**，难以Mock和测试
- **提供测试专用的构造函数或方法**，简化测试数据准备