# 企业中间件集成场景

本目录包含在现有系统中集成企业中间件的典型场景，展示非侵入式集成和降级处理模式。

## 📁 集成场景

### 缓存集成
- `cache-fallback.java` - 缓存集成和多级降级处理
- `distributed-cache.java` - 分布式缓存使用模式

### RPC调用集成
- `rpc-integration.java` - RPC服务调用和降级处理
- `service-discovery.java` - 服务发现和负载均衡

### 消息队列集成
- `message-producer.java` - 消息生产者集成模式
- `message-consumer.java` - 消息消费者集成模式

### 配置中心集成
- `dynamic-config.java` - 动态配置集成和热更新
- `config-fallback.java` - 配置中心降级处理

## 🎯 集成原则

### 1. 非侵入式集成
- 不修改现有核心业务逻辑
- 使用可选依赖注入 `@Autowired(required = false)`
- 通过配置开关控制功能启用

### 2. 降级保护机制
- 中间件不可用时自动降级
- 多级降级：企业中间件 → 开源方案 → 本地实现
- 异常不影响主业务流程

### 3. 配置驱动
- 使用 `@ConditionalOnProperty` 条件化创建Bean
- 支持环境变量配置
- 默认关闭新功能，测试通过后启用

### 4. 监控和日志
- 记录中间件使用情况
- 监控降级触发频率
- 提供健康检查端点

## 📋 使用模式

每个集成示例都包含：

1. **现有系统代码**（保持不变）
2. **中间件集成代码**（新增）
3. **降级处理逻辑**（保护机制）
4. **配置管理**（开关控制）
5. **测试验证**（质量保证）

## 🔧 配置示例

```yaml
# 企业中间件配置模板
enterprise:
  # JSF RPC框架
  jsf:
    enabled: ${JSF_ENABLED:false}
    registry:
      address: ${JSF_REGISTRY:test.jsf.local}
  
  # JMQ消息队列
  jmq:
    enabled: ${JMQ_ENABLED:false}
    address: ${JMQ_ADDRESS:test.jmq.local}
  
  # JIMDB分布式缓存
  jimdb:
    enabled: ${JIMDB_ENABLED:false}
    jim-url: ${JIMDB_URL:}
  
  # DUCC配置中心
  ducc:
    enabled: ${DUCC_ENABLED:false}
    app-name: ${DUCC_APP:my-app}
```