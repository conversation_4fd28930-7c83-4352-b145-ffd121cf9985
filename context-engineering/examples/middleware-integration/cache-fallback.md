# 企业中间件集成场景：缓存集成和多级降级处理

## 场景描述
- 现有系统需要添加缓存功能
- 优先使用企业JIMDB分布式缓存
- JIMDB不可用时降级到Redis
- Redis不可用时降级到本地缓存
- 支持配置开关控制

## 关键设计模式

### 1. 多级降级策略
- 第一级：企业JIMDB缓存（优先）
- 第二级：Redis缓存（备选）
- 第三级：本地缓存（兜底）

### 2. 可选依赖注入
- 使用`@Autowired(required = false)`处理可选中间件
- 运行时检查服务可用性
- 自动选择最佳可用缓存

### 3. 统一缓存接口
- 抽象缓存操作，屏蔽底层实现差异
- 支持TTL配置
- 统一异常处理

## 代码示例

### 现有业务服务（需要增强缓存）

```java
@Service
public class ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    // ============ 新增：多级缓存依赖（可选注入） ============
    
    // 第一级：企业JIMDB缓存
    @Autowired(required = false)
    private JimdbCacheService jimdbCacheService;
    
    // 第二级：Redis缓存
    @Autowired(required = false)
    private RedisCacheService redisCacheService;
    
    // 第三级：本地缓存（兜底方案）
    @Autowired
    private LocalCacheService localCacheService;
    
    @Value("${cache.enabled:false}")
    private boolean cacheEnabled;
    
    @Value("${cache.ttl:1800}")
    private int cacheTtlSeconds;
    
    private static final String CACHE_PREFIX = "product:";
    
    // ============ 现有方法（增强缓存功能） ============
    
    public Product getProductById(Long productId) {
        if (productId == null) {
            return null;
        }
        
        // 1. 尝试从多级缓存获取
        Product cachedProduct = getCachedProduct(productId);
        if (cachedProduct != null) {
            return cachedProduct;
        }
        
        // 2. 缓存未命中，从数据库查询
        Product product = productRepository.findById(productId).orElse(null);
        
        // 3. 将结果存入缓存
        if (product != null) {
            setCachedProduct(productId, product);
        }
        
        return product;
    }
    
    // ============ 新增：多级缓存操作方法 ============
    
    /**
     * 多级缓存获取策略
     */
    private Product getCachedProduct(Long productId) {
        if (!cacheEnabled) {
            return null;
        }
        
        String cacheKey = CACHE_PREFIX + productId;
        
        // 第一级：尝试JIMDB缓存
        Product product = getFromJimdb(cacheKey);
        if (product != null) {
            log.debug("从JIMDB缓存命中: productId={}", productId);
            return product;
        }
        
        // 第二级：尝试Redis缓存
        product = getFromRedis(cacheKey);
        if (product != null) {
            log.debug("从Redis缓存命中: productId={}", productId);
            // 回写到JIMDB（如果可用）
            setToJimdb(cacheKey, product);
            return product;
        }
        
        // 第三级：尝试本地缓存
        product = getFromLocal(cacheKey);
        if (product != null) {
            log.debug("从本地缓存命中: productId={}", productId);
            // 回写到上级缓存（如果可用）
            setToRedis(cacheKey, product);
            setToJimdb(cacheKey, product);
            return product;
        }
        
        log.debug("所有缓存未命中: productId={}", productId);
        return null;
    }
    
    /**
     * 多级缓存存储策略
     */
    private void setCachedProduct(Long productId, Product product) {
        if (!cacheEnabled || product == null) {
            return;
        }
        
        String cacheKey = CACHE_PREFIX + productId;
        
        // 存储到所有可用的缓存层级
        setToLocal(cacheKey, product);
        setToRedis(cacheKey, product);
        setToJimdb(cacheKey, product);
    }
    
    // ============ JIMDB缓存操作 ============
    
    private Product getFromJimdb(String cacheKey) {
        try {
            if (jimdbCacheService != null && jimdbCacheService.isAvailable()) {
                return jimdbCacheService.get(cacheKey, Product.class);
            }
        } catch (Exception e) {
            log.warn("JIMDB缓存获取失败: key={}, error={}", cacheKey, e.getMessage());
        }
        return null;
    }
    
    private void setToJimdb(String cacheKey, Product product) {
        try {
            if (jimdbCacheService != null && jimdbCacheService.isAvailable()) {
                jimdbCacheService.set(cacheKey, product, cacheTtlSeconds);
                log.debug("JIMDB缓存存储成功: key={}", cacheKey);
            }
        } catch (Exception e) {
            log.warn("JIMDB缓存存储失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
    
    // ============ Redis缓存操作 ============
    
    private Product getFromRedis(String cacheKey) {
        try {
            if (redisCacheService != null && redisCacheService.isAvailable()) {
                return redisCacheService.get(cacheKey, Product.class);
            }
        } catch (Exception e) {
            log.warn("Redis缓存获取失败: key={}, error={}", cacheKey, e.getMessage());
        }
        return null;
    }
    
    private void setToRedis(String cacheKey, Product product) {
        try {
            if (redisCacheService != null && redisCacheService.isAvailable()) {
                redisCacheService.set(cacheKey, product, cacheTtlSeconds);
                log.debug("Redis缓存存储成功: key={}", cacheKey);
            }
        } catch (Exception e) {
            log.warn("Redis缓存存储失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
    
    // ============ 本地缓存操作 ============
    
    private Product getFromLocal(String cacheKey) {
        try {
            return localCacheService.get(cacheKey, Product.class);
        } catch (Exception e) {
            log.warn("本地缓存获取失败: key={}, error={}", cacheKey, e.getMessage());
            return null;
        }
    }
    
    private void setToLocal(String cacheKey, Product product) {
        try {
            localCacheService.set(cacheKey, product, cacheTtlSeconds);
            log.debug("本地缓存存储成功: key={}", cacheKey);
        } catch (Exception e) {
            log.warn("本地缓存存储失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
    
    // ============ 缓存管理方法 ============
    
    /**
     * 清除产品缓存
     */
    public void evictProductCache(Long productId) {
        if (!cacheEnabled) {
            return;
        }
        
        String cacheKey = CACHE_PREFIX + productId;
        
        // 从所有缓存层级清除
        evictFromLocal(cacheKey);
        evictFromRedis(cacheKey);
        evictFromJimdb(cacheKey);
        
        log.info("产品缓存已清除: productId={}", productId);
    }
    
    private void evictFromJimdb(String cacheKey) {
        try {
            if (jimdbCacheService != null && jimdbCacheService.isAvailable()) {
                jimdbCacheService.delete(cacheKey);
            }
        } catch (Exception e) {
            log.warn("JIMDB缓存清除失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
    
    private void evictFromRedis(String cacheKey) {
        try {
            if (redisCacheService != null && redisCacheService.isAvailable()) {
                redisCacheService.delete(cacheKey);
            }
        } catch (Exception e) {
            log.warn("Redis缓存清除失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
    
    private void evictFromLocal(String cacheKey) {
        try {
            localCacheService.delete(cacheKey);
        } catch (Exception e) {
            log.warn("本地缓存清除失败: key={}, error={}", cacheKey, e.getMessage());
        }
    }
}
```

### 缓存服务接口定义

```java
/**
 * 统一缓存服务接口
 */
public interface CacheService {
    
    /**
     * 检查服务是否可用
     */
    boolean isAvailable();
    
    /**
     * 获取缓存值
     */
    <T> T get(String key, Class<T> type);
    
    /**
     * 设置缓存值
     */
    void set(String key, Object value, int ttlSeconds);
    
    /**
     * 删除缓存
     */
    void delete(String key);
    
    /**
     * 检查key是否存在
     */
    boolean exists(String key);
}
```

### JIMDB缓存服务实现

```java
@Service
@ConditionalOnProperty(name = "cache.jimdb.enabled", havingValue = "true")
public class JimdbCacheService implements CacheService {
    
    @Autowired
    private JimdbTemplate jimdbTemplate;
    
    @Value("${cache.jimdb.cluster:default}")
    private String clusterName;
    
    private volatile boolean available = true;
    
    @Override
    public boolean isAvailable() {
        return available && jimdbTemplate != null;
    }
    
    @Override
    public <T> T get(String key, Class<T> type) {
        try {
            String value = jimdbTemplate.get(key);
            if (value != null) {
                return JsonUtils.fromJson(value, type);
            }
            return null;
        } catch (Exception e) {
            available = false;
            log.error("JIMDB获取失败: key={}, error={}", key, e.getMessage());
            throw new CacheException("JIMDB获取失败", e);
        }
    }
    
    @Override
    public void set(String key, Object value, int ttlSeconds) {
        try {
            String jsonValue = JsonUtils.toJson(value);
            jimdbTemplate.setex(key, ttlSeconds, jsonValue);
            available = true;
        } catch (Exception e) {
            available = false;
            log.error("JIMDB存储失败: key={}, error={}", key, e.getMessage());
            throw new CacheException("JIMDB存储失败", e);
        }
    }
    
    @Override
    public void delete(String key) {
        try {
            jimdbTemplate.del(key);
        } catch (Exception e) {
            log.error("JIMDB删除失败: key={}, error={}", key, e.getMessage());
            throw new CacheException("JIMDB删除失败", e);
        }
    }
    
    @Override
    public boolean exists(String key) {
        try {
            return jimdbTemplate.exists(key);
        } catch (Exception e) {
            log.error("JIMDB检查失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }
}
```

### 配置文件示例

```yaml
# application.yml

# 缓存总开关
cache:
  enabled: ${CACHE_ENABLED:true}
  ttl: ${CACHE_TTL:1800}  # 30分钟
  
  # JIMDB配置（企业分布式缓存）
  jimdb:
    enabled: ${JIMDB_ENABLED:true}
    cluster: ${JIMDB_CLUSTER:default}
    timeout: ${JIMDB_TIMEOUT:3000}
    
  # Redis配置（备选方案）
  redis:
    enabled: ${REDIS_ENABLED:true}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    timeout: ${REDIS_TIMEOUT:3000}
    
  # 本地缓存配置（兜底方案）
  local:
    enabled: true
    max-size: ${LOCAL_CACHE_SIZE:10000}
    expire-after-write: ${LOCAL_CACHE_TTL:1800}
```

## 实现要点总结

1. **多级降级策略**：JIMDB → Redis → 本地缓存，确保高可用性
2. **可选依赖注入**：使用`@Autowired(required = false)`处理可选中间件
3. **统一异常处理**：缓存操作失败不影响业务逻辑
4. **自动回写机制**：低级缓存命中时自动回写到高级缓存
5. **健康检查**：实时监控各级缓存服务可用性
