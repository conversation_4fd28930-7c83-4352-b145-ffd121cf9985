#!/bin/bash

# Java Context Engineering模板验证脚本

echo "🔍 验证Java Context Engineering模板完整性..."

MISSING_COUNT=0

# 检查核心文件
echo "📋 检查核心文件..."

# 检查是否在部署后的context-engineering目录中
if [[ -f "CONTEXT_AI_INSTRUCTIONS.md" ]]; then
    # 在部署后的context-engineering目录中
    CORE_FILES=(
        "CONTEXT_AI_INSTRUCTIONS.md"
        "CONTEXT_INITIAL.md"
        "INITIAL_EXAMPLE.md"
        "workflows/generate-prp.md"
        "workflows/execute-prp.md"
        "workflows/extract-patterns.md"
        "workflows/README.md"
        "platform-specific/README.md"
        "PRPs/templates/prp_base.md"
        "middleware-docs/middleware-list.md"
        "middleware-docs/middleware-template.md"
        "QUICK_START.md"
    )
else
    # 在原始模板目录中
    CORE_FILES=(
        "README.md"
        "AI-INSTRUCTIONS.md"
        "INITIAL.md"
        "INITIAL_EXAMPLE.md"
        "workflows/generate-prp.md"
        "workflows/execute-prp.md"
        "workflows/extract-patterns.md"
        ".claude/commands/generate-prp.md"
        ".claude/commands/execute-prp.md"
        ".claude/commands/extract-patterns.md"
        "PRPs/templates/prp_base.md"
        "middleware-docs/middleware-list.md"
        "middleware-docs/middleware-template.md"
        "QUICK_START.md"
    )
fi

for file in "${CORE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✅ $file"
    else
        echo "  ❌ $file (缺失)"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
done

# 检查中间件文档
echo ""
echo "🏢 检查中间件文档..."

MIDDLEWARE_COUNT=$(find middleware-docs -name "*.md" -not -name "middleware-list.md" -not -name "middleware-template.md" | wc -l)
echo "  📊 发现 $MIDDLEWARE_COUNT 个中间件文档"

if [ $MIDDLEWARE_COUNT -lt 3 ]; then
    echo "  ⚠️  中间件文档数量偏少，建议至少有3个完整文档"
fi

# 验证中间件文档质量
VALID_MIDDLEWARE_COUNT=0
for middleware_file in middleware-docs/*.md; do
    if [[ "$middleware_file" != *"middleware-list.md" && "$middleware_file" != *"middleware-template.md" ]]; then
        if ./tools/validate-middleware.sh "$middleware_file" > /dev/null 2>&1; then
            VALID_MIDDLEWARE_COUNT=$((VALID_MIDDLEWARE_COUNT + 1))
        fi
    fi
done

echo "  📊 有效中间件文档: $VALID_MIDDLEWARE_COUNT/$MIDDLEWARE_COUNT"

# 检查示例目录
echo ""
echo "📚 检查示例目录..."

EXAMPLE_DIRS=(
    "examples/typical-scenarios"
    "examples/middleware-integration"
    "examples/code-patterns"
)

for dir in "${EXAMPLE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        file_count=$(find "$dir" -name "*.java" -o -name "*.md" | wc -l)
        echo "  ✅ $dir ($file_count 个文件)"
    else
        echo "  ❌ $dir (缺失)"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
done

# 检查工具脚本
echo ""
echo "🛠️  检查工具脚本..."

TOOL_SCRIPTS=(
    "tools/validate-middleware.sh"
    "tools/validate-template.sh"
)

for script in "${TOOL_SCRIPTS[@]}"; do
    if [ -f "$script" ] && [ -x "$script" ]; then
        echo "  ✅ $script (可执行)"
    elif [ -f "$script" ]; then
        echo "  ⚠️  $script (存在但不可执行)"
        chmod +x "$script"
        echo "  ✅ $script (已设置为可执行)"
    else
        echo "  ❌ $script (缺失)"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
done

# 检查Java代码示例（Markdown格式）
echo ""
echo "☕ 检查Java代码示例..."

# 检查Markdown格式的Java示例文件
JAVA_MD_FILE_COUNT=$(find examples -name "*.md" | wc -l)
echo "  📊 发现 $JAVA_MD_FILE_COUNT 个Java示例文档（Markdown格式）"

if [ $JAVA_MD_FILE_COUNT -lt 3 ]; then
    echo "  ⚠️  Java示例文档数量偏少，建议至少有3个"
    # 注意：这里不增加MISSING_COUNT，因为这只是建议
fi

# 检查Markdown文件中是否包含Java代码块
JAVA_CODE_BLOCKS=0
JAVA_SYNTAX_ERRORS=0
for md_file in $(find examples -name "*.md"); do
    # 跳过README文件，因为它们通常不包含完整的Java代码
    if [[ "$(basename "$md_file")" == "README.md" ]]; then
        continue
    fi

    # 检查是否包含Java代码块
    if grep -q '```java' "$md_file"; then
        JAVA_CODE_BLOCKS=$((JAVA_CODE_BLOCKS + 1))

        # 简单检查Java代码块内容
        if ! grep -A 20 '```java' "$md_file" | grep -q "class\|interface\|enum\|@\|public\|private"; then
            echo "  ⚠️  $md_file 中的Java代码块可能不完整"
            JAVA_SYNTAX_ERRORS=$((JAVA_SYNTAX_ERRORS + 1))
        fi
    fi
done

echo "  📊 发现 $JAVA_CODE_BLOCKS 个包含Java代码块的文档"

if [ $JAVA_SYNTAX_ERRORS -eq 0 ]; then
    echo "  ✅ Java代码示例格式检查通过"
else
    echo "  ⚠️  发现 $JAVA_SYNTAX_ERRORS 个可能的Java代码问题"
fi

# 检查工作流和命令配置
echo ""
echo "🤖 检查工作流和命令配置..."

# 检查通用工作流（优先）
if [[ -f "workflows/generate-prp.md" ]]; then
    if grep -q "企业中间件" workflows/generate-prp.md; then
        echo "  ✅ workflows/generate-prp.md 包含企业中间件逻辑"
    else
        echo "  ❌ workflows/generate-prp.md 缺少企业中间件逻辑"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi

    if grep -q "Java" workflows/execute-prp.md; then
        echo "  ✅ workflows/execute-prp.md 包含Java相关内容"
    else
        echo "  ❌ workflows/execute-prp.md 缺少Java相关内容"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi

    if grep -q "模式" workflows/extract-patterns.md; then
        echo "  ✅ workflows/extract-patterns.md 包含模式提取逻辑"
    else
        echo "  ❌ workflows/extract-patterns.md 缺少模式提取逻辑"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
elif [[ -f ".claude/commands/generate-prp.md" ]]; then
    # 回退到Claude命令检查
    if grep -q "企业中间件" .claude/commands/generate-prp.md; then
        echo "  ✅ generate-prp.md 包含企业中间件逻辑"
    else
        echo "  ❌ generate-prp.md 缺少企业中间件逻辑"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi

    if grep -q "Java" .claude/commands/execute-prp.md; then
        echo "  ✅ execute-prp.md 包含Java相关内容"
    else
        echo "  ❌ execute-prp.md 缺少Java相关内容"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi

    if grep -q "模式提取" .claude/commands/extract-patterns.md; then
        echo "  ✅ extract-patterns.md 包含模式提取逻辑"
    else
        echo "  ❌ extract-patterns.md 缺少模式提取逻辑"
        MISSING_COUNT=$((MISSING_COUNT + 1))
    fi
else
    echo "  ⚠️  未找到工作流或Claude命令文件"
fi

# 检查PRP模板
echo ""
echo "📄 检查PRP模板..."

if grep -q "企业中间件" PRPs/templates/prp_base.md; then
    echo "  ✅ PRP模板包含企业中间件集成说明"
else
    echo "  ❌ PRP模板缺少企业中间件集成说明"
    MISSING_COUNT=$((MISSING_COUNT + 1))
fi

if grep -q "mvn" PRPs/templates/prp_base.md; then
    echo "  ✅ PRP模板包含Maven验证命令"
else
    echo "  ❌ PRP模板缺少Maven验证命令"
    MISSING_COUNT=$((MISSING_COUNT + 1))
fi

# 总结验证结果
echo ""
echo "📊 验证结果总结:"
echo "  - 核心文件: $(( ${#CORE_FILES[@]} - MISSING_COUNT )) / ${#CORE_FILES[@]}"
echo "  - 中间件文档: $VALID_MIDDLEWARE_COUNT 个有效文档"
echo "  - 示例文件: $JAVA_MD_FILE_COUNT 个Java示例文档"
echo "  - Java代码块: $JAVA_CODE_BLOCKS 个代码示例"
echo "  - 工具脚本: $(( ${#TOOL_SCRIPTS[@]} )) 个可用脚本"

echo ""
if [ $MISSING_COUNT -eq 0 ] && [ $JAVA_SYNTAX_ERRORS -eq 0 ]; then
    echo "✅ Java Context Engineering模板验证通过"
    echo ""
    echo "🎉 模板已准备就绪，可以开始使用！"
    echo ""
    echo "📖 使用指南:"
    echo "  1. 编辑 INITIAL.md 描述要开发的功能"
    echo "  2. 运行 /generate-prp INITIAL.md 生成实现计划"
    echo "  3. 运行 /execute-prp PRPs/your-feature.md 执行实现"
    echo "  4. 运行 /extract-patterns 提取项目代码模式"
    exit 0
else
    echo "❌ Java Context Engineering模板验证失败"
    echo "   发现 $MISSING_COUNT 个缺失项和 $JAVA_SYNTAX_ERRORS 个语法问题"
    echo ""
    echo "🔧 修复建议:"
    if [ $MISSING_COUNT -gt 0 ]; then
        echo "  - 补充缺失的核心文件"
        echo "  - 完善中间件文档"
        echo "  - 添加更多Java代码示例"
    fi
    if [ $JAVA_SYNTAX_ERRORS -gt 0 ]; then
        echo "  - 检查Java代码示例的语法"
        echo "  - 确保示例代码的完整性"
    fi
    exit 1
fi