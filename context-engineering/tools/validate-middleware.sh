#!/bin/bash
# 中间件文档验证脚本
# 用法: ./validate-middleware.sh <middleware-file.md>

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查参数
if [ $# -eq 0 ]; then
    echo -e "${RED}错误: 请提供中间件文档文件${NC}"
    echo "用法: ./validate-middleware.sh <middleware-file.md>"
    echo "示例: ./validate-middleware.sh jsf.md"
    exit 1
fi

MIDDLEWARE_FILE=$1

# 检查文件是否存在
if [ ! -f "$MIDDLEWARE_FILE" ]; then
    echo -e "${RED}错误: 文件 '$MIDDLEWARE_FILE' 不存在${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 验证中间件文档: $MIDDLEWARE_FILE${NC}"
echo "=================================================="

# 验证计数器
PASS_COUNT=0
FAIL_COUNT=0

# 验证函数
check_section() {
    local section="$1"
    local description="$2"

    if grep -q "$section" "$MIDDLEWARE_FILE"; then
        echo -e "  ${GREEN}✅ $description${NC}"
        ((PASS_COUNT++))
    else
        echo -e "  ${RED}❌ $description (缺失)${NC}"
        ((FAIL_COUNT++))
    fi
}

check_content() {
    local pattern="$1"
    local description="$2"

    if grep -q "$pattern" "$MIDDLEWARE_FILE"; then
        echo -e "  ${GREEN}✅ $description${NC}"
        ((PASS_COUNT++))
    else
        echo -e "  ${RED}❌ $description (缺失)${NC}"
        ((FAIL_COUNT++))
    fi
}

check_count() {
    local pattern="$1"
    local min_count="$2"
    local description="$3"

    local count=$(grep -c "$pattern" "$MIDDLEWARE_FILE" || true)
    if [ "$count" -ge "$min_count" ]; then
        echo -e "  ${GREEN}✅ $description (找到 $count 个)${NC}"
        ((PASS_COUNT++))
    else
        echo -e "  ${RED}❌ $description (只找到 $count 个，需要至少 $min_count 个)${NC}"
        ((FAIL_COUNT++))
    fi
}

# 1. 检查必需章节
echo -e "${YELLOW}📋 检查必需章节...${NC}"
check_section "## 基本信息" "基本信息章节"
check_section "## Maven依赖" "Maven依赖章节"
check_section "## 配置" "配置章节"
check_section "## 使用代码" "使用代码章节"
check_section "## 功能特性" "功能特性章节"
check_section "## 注意事项" "注意事项章节"

# 2. 检查基本信息内容
echo -e "\n${YELLOW}📝 检查基本信息内容...${NC}"
check_content "版本.*:" "版本信息"
check_content "关键词.*:" "关键词设置"
check_content "适用场景.*:" "适用场景说明"
check_content "依赖要求.*:" "依赖要求说明"

# 3. 检查关键词数量
echo -e "\n${YELLOW}🔑 检查关键词数量...${NC}"
KEYWORDS_LINE=$(grep "关键词.*:" "$MIDDLEWARE_FILE" || echo "")
if [ -n "$KEYWORDS_LINE" ]; then
    KEYWORD_COUNT=$(echo "$KEYWORDS_LINE" | grep -o "," | wc -l)
    KEYWORD_COUNT=$((KEYWORD_COUNT + 1))
    if [ "$KEYWORD_COUNT" -ge 3 ]; then
        echo -e "  ${GREEN}✅ 关键词数量充足 ($KEYWORD_COUNT 个)${NC}"
        ((PASS_COUNT++))
    else
        echo -e "  ${RED}❌ 关键词数量不足 ($KEYWORD_COUNT 个，建议至少3个)${NC}"
        ((FAIL_COUNT++))
    fi
else
    echo -e "  ${RED}❌ 未找到关键词行${NC}"
    ((FAIL_COUNT++))
fi

# 4. 检查Maven依赖
echo -e "\n${YELLOW}📦 检查Maven依赖...${NC}"
check_content "<dependency>" "Maven依赖配置"
check_content "<groupId>com.jd.framework</groupId>" "企业groupId"
check_content "<artifactId>" "artifactId配置"

# 5. 检查配置内容
echo -e "\n${YELLOW}⚙️  检查配置内容...${NC}"
check_content "### 基础配置" "基础配置章节"
check_content "enabled: \${.*_ENABLED:false}" "功能开关配置"
check_content "### 环境变量" "环境变量章节"
check_content "### 配置参数详解" "配置参数详解章节"

# 6. 检查代码示例
echo -e "\n${YELLOW}💻 检查代码示例...${NC}"
check_count "\`\`\`java" 3 "Java代码块"
check_content "### 基础用法" "基础用法章节"
check_content "### 常见场景" "常见场景章节"
check_content "### 集成示例" "集成示例章节"

# 7. 检查现有系统集成友好性
echo -e "\n${YELLOW}🔗 检查现有系统集成友好性...${NC}"
check_content "@Autowired(required = false)" "条件化注入"
check_content "@ConditionalOnProperty" "条件化配置"
check_content "ExistingBusinessService" "现有系统集成示例"
check_content "降级" "降级处理机制"

# 8. 检查功能特性内容
echo -e "\n${YELLOW}⭐ 检查功能特性内容...${NC}"
check_content "### 核心能力" "核心能力章节"
check_content "### 技术特点" "技术特点章节"
check_content "### 适用场景" "适用场景章节"

# 9. 检查注意事项内容
echo -e "\n${YELLOW}⚠️  检查注意事项内容...${NC}"
check_content "### 使用约束" "使用约束章节"
check_content "### 性能考虑" "性能考虑章节"
check_content "### 安全要求" "安全要求章节"

# 10. 检查可选章节（不计入通过率）
echo -e "\n${YELLOW}📚 检查可选章节...${NC}"
if grep -q "## 最佳实践" "$MIDDLEWARE_FILE"; then
    echo -e "  ${GREEN}✅ 最佳实践章节 (可选)${NC}"
else
    echo -e "  ${YELLOW}⚪ 最佳实践章节 (可选，整理成本高可省略)${NC}"
fi

if grep -q "## 常见问题" "$MIDDLEWARE_FILE"; then
    echo -e "  ${GREEN}✅ 常见问题章节 (可选)${NC}"
else
    echo -e "  ${YELLOW}⚪ 常见问题章节 (可选，整理成本高可省略)${NC}"
fi

# 11. 检查文档结构
echo -e "\n${YELLOW}📖 检查文档结构...${NC}"
# 检查是否有占位符未替换
PLACEHOLDER_COUNT=$(grep -c "\[.*\]" "$MIDDLEWARE_FILE" || true)
if [ "$PLACEHOLDER_COUNT" -eq 0 ]; then
    echo -e "  ${GREEN}✅ 无未替换的占位符${NC}"
    ((PASS_COUNT++))
else
    echo -e "  ${RED}❌ 发现 $PLACEHOLDER_COUNT 个未替换的占位符${NC}"
    echo -e "  ${YELLOW}   请检查并替换所有 [占位符] 内容${NC}"
    ((FAIL_COUNT++))
fi

# 检查代码块是否正确闭合
CODE_OPEN=$(grep -c "\`\`\`" "$MIDDLEWARE_FILE" || true)
CODE_CLOSE=$(grep -c "\`\`\`$" "$MIDDLEWARE_FILE" || true)
# 代码块总数应该是偶数（每个开始都有对应的结束）
if [ $((CODE_OPEN % 2)) -eq 0 ] && [ "$CODE_OPEN" -eq $((CODE_CLOSE * 2)) ]; then
    echo -e "  ${GREEN}✅ 代码块正确闭合${NC}"
    ((PASS_COUNT++))
else
    echo -e "  ${RED}❌ 代码块可能未正确闭合 (总标记:$CODE_OPEN, 结束标记:$CODE_CLOSE)${NC}"
    ((FAIL_COUNT++))
fi

# 12. 输出验证结果
echo -e "\n=================================================="
echo -e "${BLUE}📊 验证结果统计${NC}"
echo -e "通过项目: ${GREEN}$PASS_COUNT${NC}"
echo -e "失败项目: ${RED}$FAIL_COUNT${NC}"
echo -e "总计项目: $((PASS_COUNT + FAIL_COUNT))"

PASS_RATE=$((PASS_COUNT * 100 / (PASS_COUNT + FAIL_COUNT)))
echo -e "通过率: ${BLUE}$PASS_RATE%${NC}"

# 13. 给出最终结论
echo -e "\n${BLUE}🎯 验证结论${NC}"
if [ "$FAIL_COUNT" -eq 0 ]; then
    echo -e "${GREEN}✅ 文档验证通过！文档符合标准要求。${NC}"
    exit 0
elif [ "$PASS_RATE" -ge 80 ]; then
    echo -e "${YELLOW}⚠️  文档基本合格，但有 $FAIL_COUNT 个问题需要修复。${NC}"
    echo -e "${YELLOW}   建议修复后再次验证。${NC}"
    exit 1
else
    echo -e "${RED}❌ 文档验证失败！有 $FAIL_COUNT 个严重问题需要修复。${NC}"
    echo -e "${RED}   请参考模板和示例文档进行修改。${NC}"
    exit 1
fi