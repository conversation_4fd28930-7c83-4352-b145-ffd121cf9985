#!/bin/bash

# deploy-template.sh
# Java Context Engineering Template Deployment Script
# 支持创建新项目或集成到现有项目，包含部署后验证功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "Java Context Engineering Template 部署脚本"
    echo ""
    echo "用法: $0 [选项] <项目根目录>"
    echo ""
    echo "部署规范:"
    echo "  - 模板将自动部署到项目根目录下的 context-engineering/ 子目录"
    echo "  - 不会影响项目原有文件（README.md、CLAUDE.md等）"
    echo "  - Claude命令将部署到项目根目录的 .claude/commands/ 下"
    echo ""
    echo "模式:"
    echo "  新项目模式     创建全新的项目目录（默认）"
    echo "  集成模式       集成到现有项目（使用 --integrate）"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -i, --integrate         集成到现有项目（推荐用法）"
    echo "  -v, --validate-only     仅执行验证，不进行部署"
    echo "  -f, --force            强制覆盖已存在的文件"
    echo "  --skip-validation      跳过部署后验证"
    echo ""
    echo "示例:"
    echo "  # 集成到现有项目（推荐）"
    echo "  $0 --integrate /path/to/existing/project"
    echo ""
    echo "  # 创建新项目"
    echo "  $0 /path/to/new/project"
    echo ""
    echo "  # 强制重新部署"
    echo "  $0 --integrate --force /path/to/existing/project"
    echo ""
    echo "  # 仅验证模板完整性"
    echo "  $0 --validate-only"
}

# 默认参数
VALIDATE_ONLY=false
FORCE_DEPLOY=false
SKIP_VALIDATION=false
INTEGRATE_MODE=false
TARGET_DIR=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        -f|--force)
            FORCE_DEPLOY=true
            shift
            ;;
        -i|--integrate)
            INTEGRATE_MODE=true
            shift
            ;;
        --skip-validation)
            SKIP_VALIDATION=true
            shift
            ;;
        -*)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$TARGET_DIR" ]]; then
                TARGET_DIR="$1"
            else
                print_error "太多参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 获取脚本所在目录（模板根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATE_DIR="$(dirname "$SCRIPT_DIR")"

print_info "Java Context Engineering Template 部署脚本启动"
print_info "模板目录: $TEMPLATE_DIR"

# 验证模板完整性
validate_template() {
    print_info "验证模板完整性..."

    # 检查核心文件（兼容新旧结构）
    local required_files=(
        "AI-INSTRUCTIONS.md"  # 新的统一AI指令文件
        "INITIAL.md"
        "README.md"
        "workflows/generate-prp.md"  # 新的通用工作流
        "workflows/execute-prp.md"
        "workflows/extract-patterns.md"
        ".claude/commands/generate-prp.md"  # 保持Claude兼容性
        ".claude/commands/execute-prp.md"
        ".claude/commands/extract-patterns.md"
        "PRPs/templates/prp_base.md"
        "middleware-docs/middleware-list.md"
        "tools/validate-template.sh"
        "tools/validate-middleware.sh"
    )
    
    local missing_files=()
    local optional_files=()  # 用于标记可选文件

    for file in "${required_files[@]}"; do
        if [[ -f "$TEMPLATE_DIR/$file" ]]; then
            print_success "核心文件存在: $file"
        else
            # 检查是否是可选的兼容性文件
            if [[ "$file" == ".claude/commands/"* ]]; then
                print_warning "Claude兼容文件缺失（可选）: $file"
                optional_files+=("$file")
            else
                print_error "核心文件缺失: $file"
                missing_files+=("$file")
            fi
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        print_error "模板验证失败，缺失 ${#missing_files[@]} 个核心文件"
        return 1
    fi

    if [[ ${#optional_files[@]} -gt 0 ]]; then
        print_info "注意：缺失 ${#optional_files[@]} 个可选的Claude兼容文件，Claude Code功能可能受限"
    fi
    
    # 检查中间件文档
    local middleware_count=$(find "$TEMPLATE_DIR/middleware-docs" -name "*.md" -not -name "middleware-list.md" -not -name "middleware-template.md" 2>/dev/null | wc -l)
    print_info "发现 $middleware_count 个中间件文档"
    
    if [[ $middleware_count -lt 3 ]]; then
        print_warning "中间件文档数量偏少，建议至少包含3-5个"
    fi
    
    # 检查示例代码
    local java_examples=$(find "$TEMPLATE_DIR/examples" -name "*.java" 2>/dev/null | wc -l)
    print_info "发现 $java_examples 个Java示例文件"
    
    if [[ $java_examples -lt 2 ]]; then
        print_warning "Java示例文件数量偏少"
    fi
    
    print_success "模板验证完成"
    return 0
}

# 验证和准备目标目录
prepare_target_directory() {
    local project_root="$1"
    
    if [[ -z "$project_root" ]]; then
        print_error "未指定项目根目录"
        return 1
    fi
    
    if [[ "$INTEGRATE_MODE" == true ]]; then
        # 集成模式：项目根目录必须存在且是一个项目
        if [[ ! -d "$project_root" ]]; then
            print_error "集成模式要求项目根目录必须存在: $project_root"
            return 1
        fi
        
        print_info "集成模式：验证现有项目 $project_root"
        
        # 检查是否看起来像一个项目目录
        if [[ -f "$project_root/pom.xml" ]]; then
            print_success "检测到Maven项目"
        elif [[ -f "$project_root/build.gradle" ]] || [[ -f "$project_root/build.gradle.kts" ]]; then
            print_success "检测到Gradle项目"
        elif [[ -f "$project_root/package.json" ]]; then
            print_success "检测到Node.js项目"
        else
            print_warning "未检测到常见的项目配置文件，但继续集成"
        fi
        
        # 创建 context-engineering 子目录
        local context_dir="$project_root/context-engineering"
        print_info "创建 Context Engineering 子目录: $context_dir"
        mkdir -p "$context_dir"
        
        # 检查是否已经集成过（检查子目录和根目录的Claude命令）
        if [[ -d "$context_dir/PRPs" ]] && [[ -d "$project_root/.claude/commands" ]] && [[ -f "$project_root/.claude/commands/generate-prp.md" ]]; then
            if [[ "$FORCE_DEPLOY" != true ]]; then
                print_error "项目似乎已经集成了Java Context Engineering模板"
                print_info "使用 --force 选项强制重新集成"
                return 1
            else
                print_warning "强制模式：重新集成模板"
            fi
        fi
        
        # 设置全局变量用于后续使用
        CONTEXT_TARGET_DIR="$context_dir"
        PROJECT_ROOT_DIR="$project_root"
        
    else
        # 新项目模式：目标目录不应该存在
        if [[ -e "$project_root" ]] && [[ "$FORCE_DEPLOY" != true ]]; then
            print_error "目标目录已存在: $project_root"
            print_info "使用 --integrate 选项集成到现有项目，或使用 --force 选项强制覆盖"
            return 1
        fi
        
        print_info "新项目模式：创建目标目录 $project_root"
        mkdir -p "$project_root"
        
        # 在新项目中也创建 context-engineering 子目录
        local context_dir="$project_root/context-engineering"
        print_info "创建 Context Engineering 子目录: $context_dir"
        mkdir -p "$context_dir"
        
        # 设置全局变量用于后续使用
        CONTEXT_TARGET_DIR="$context_dir"
        PROJECT_ROOT_DIR="$project_root"
    fi
    
    print_success "目标目录准备完成"
    print_info "Context Engineering 模板将部署到: $CONTEXT_TARGET_DIR"
    print_info "Claude 命令将部署到: $PROJECT_ROOT_DIR/.claude/commands/"
    return 0
}

# 复制模板文件
copy_template_files() {
    # 使用全局变量，不再传入参数
    print_info "开始复制模板文件..."
    copy_template_files_to_context_dir
}

# 复制模板文件到context-engineering目录
copy_template_files_to_context_dir() {
    # 复制到 context-engineering 子目录的文件（支持新的多平台结构）
    local context_items=(
        "AI-INSTRUCTIONS.md:CONTEXT_AI_INSTRUCTIONS.md"  # 新的统一AI指令文件
        "INITIAL.md:CONTEXT_INITIAL.md"  # 重命名以避免冲突
        "INITIAL_EXAMPLE.md"
        "workflows/"  # 新增：通用工作流目录
        "platform-specific/"  # 新增：平台特定配置
        "PRPs/"
        "middleware-docs/"
        "examples/"
        "tools/"
        "QUICK_START.md"  # 新增：快速开始指南
    )
    
    print_info "复制文件到 Context Engineering 目录: $CONTEXT_TARGET_DIR"
    
    for item in "${context_items[@]}"; do
        # 检查是否有重命名映射
        if [[ "$item" == *":"* ]]; then
            local source_name="${item%:*}"
            local target_name="${item#*:}"
            copy_item_to_context "$source_name" "$target_name"
        else
            copy_item_to_context "$item" "$item"
        fi
    done
    
    # 复制 Claude 命令到项目根目录
    print_info "复制 Claude 命令到项目根目录: $PROJECT_ROOT_DIR/.claude/commands/"
    handle_claude_directory_new
    
    print_success "模板文件复制完成"
}

# 复制单个文件或目录到context目录
copy_item_to_context() {
    local source_name="$1"
    local target_name="$2"
    local source_path="$TEMPLATE_DIR/$source_name"
    local target_path="$CONTEXT_TARGET_DIR/$target_name"
    
    if [[ ! -e "$source_path" ]]; then
        print_warning "源文件不存在: $source_name"
        return 1
    fi
    
    if [[ -e "$target_path" ]] && [[ "$FORCE_DEPLOY" != true ]]; then
        print_warning "目标已存在，跳过: $target_name（使用 --force 强制覆盖）"
        return 0
    fi
    
    if [[ -d "$source_path" ]]; then
        print_info "复制目录: $source_name -> $target_name"
        cp -r "$source_path" "$target_path"
    else
        print_info "复制文件: $source_name -> $target_name"
        cp "$source_path" "$target_path"
    fi
    
    print_success "已复制: $target_name"
    return 0
}

# 处理Claude目录（新版本）
handle_claude_directory_new() {
    local source_claude="$TEMPLATE_DIR/.claude"
    local target_claude="$PROJECT_ROOT_DIR/.claude"
    
    if [[ ! -d "$source_claude" ]]; then
        print_warning "源.claude目录不存在"
        return 1
    fi
    
    # 创建.claude目录（如果不存在）
    mkdir -p "$target_claude"
    
    # 处理commands目录
    if [[ -d "$source_claude/commands" ]]; then
        mkdir -p "$target_claude/commands"
        
        print_info "复制 Claude 命令..."
        for cmd_file in "$source_claude/commands"/*.md; do
            if [[ -f "$cmd_file" ]]; then
                local cmd_name="$(basename "$cmd_file")"
                local target_cmd="$target_claude/commands/$cmd_name"
                
                if [[ -f "$target_cmd" ]] && [[ "$FORCE_DEPLOY" != true ]]; then
                    print_warning "命令已存在: $cmd_name（使用 --force 强制覆盖）"
                else
                    cp "$cmd_file" "$target_cmd"
                    print_success "已添加命令: $cmd_name"
                fi
            fi
        done
    fi
    
    # 处理其他.claude目录下的文件
    for item in "$source_claude"/*; do
        local item_name="$(basename "$item")"
        
        # 跳过已处理的commands目录
        if [[ "$item_name" == "commands" ]]; then
            continue
        fi
        
        local target_item="$target_claude/$item_name"
        
        if [[ -e "$target_item" ]] && [[ "$FORCE_DEPLOY" != true ]]; then
            print_warning "配置文件已存在: .claude/$item_name（使用 --force 强制覆盖）"
        else
            if [[ -d "$item" ]]; then
                cp -r "$item" "$target_item"
            else
                cp "$item" "$target_item"
            fi
            print_success "已添加配置: .claude/$item_name"
        fi
    done
    
    print_success "Claude配置处理完成"
}

# 设置文件权限
set_file_permissions() {
    print_info "设置文件权限..."
    
    # 设置脚本文件为可执行
    find "$CONTEXT_TARGET_DIR/tools" -name "*.sh" -type f -exec chmod +x {} \; 2>/dev/null || true
    
    # 设置.claude目录权限
    if [[ -d "$PROJECT_ROOT_DIR/.claude" ]]; then
        chmod -R 755 "$PROJECT_ROOT_DIR/.claude"
    fi
    
    print_success "文件权限设置完成"
}

# 验证部署结果
validate_deployment() {
    print_info "验证部署结果..."
    
    # 检查context-engineering目录下的核心文件（支持新的多平台结构）
    local context_required_files=(
        "CONTEXT_AI_INSTRUCTIONS.md"  # 新的统一AI指令文件
        "CONTEXT_INITIAL.md"
        "INITIAL_EXAMPLE.md"
        "workflows/generate-prp.md"  # 新增：通用工作流
        "workflows/execute-prp.md"
        "workflows/extract-patterns.md"
        "workflows/README.md"
        "platform-specific/README.md"  # 新增：平台特定配置
        "PRPs/templates/prp_base.md"
        "middleware-docs/middleware-list.md"
        "tools/validate-template.sh"
        "QUICK_START.md"  # 新增：快速开始指南
    )
    
    # 检查项目根目录下的Claude命令
    local claude_required_files=(
        ".claude/commands/generate-prp.md"
        ".claude/commands/execute-prp.md"
        ".claude/commands/extract-patterns.md"
    )
    
    local validation_failed=false
    
    print_info "验证 Context Engineering 模板文件..."
    for file in "${context_required_files[@]}"; do
        if [[ -f "$CONTEXT_TARGET_DIR/$file" ]]; then
            print_success "部署验证: context-engineering/$file"
        else
            print_error "部署验证失败: context-engineering/$file"
            validation_failed=true
        fi
    done
    
    print_info "验证 Claude 命令文件..."
    for file in "${claude_required_files[@]}"; do
        if [[ -f "$PROJECT_ROOT_DIR/$file" ]]; then
            print_success "部署验证: $file"
        else
            print_error "部署验证失败: $file"
            validation_failed=true
        fi
    done
    
    # 运行模板验证脚本
    if [[ -f "$CONTEXT_TARGET_DIR/tools/validate-template.sh" ]]; then
        print_info "运行模板验证脚本..."
        cd "$CONTEXT_TARGET_DIR"
        if bash tools/validate-template.sh; then
            print_success "模板验证脚本通过"
        else
            print_error "模板验证脚本失败"
            validation_failed=true
        fi
        cd - > /dev/null
    fi
    
    if [[ "$validation_failed" == true ]]; then
        print_error "部署验证失败"
        return 1
    fi
    
    print_success "部署验证通过"
    return 0
}
# 显示部署后说明
show_deployment_info() {
    echo ""
    print_success "🎉 Java Context Engineering Template 部署完成！"
    echo ""
    print_info "项目根目录: $PROJECT_ROOT_DIR"
    print_info "Context Engineering 目录: $CONTEXT_TARGET_DIR"
    echo ""
    print_info "部署内容:"
    echo "• context-engineering/workflows/ - 通用工作流（适用所有AI平台）"
    echo "• context-engineering/platform-specific/ - 平台特定配置"
    echo "• context-engineering/PRPs/ - 产品需求计划模板和示例"
    echo "• context-engineering/middleware-docs/ - 企业中间件文档"
    echo "• context-engineering/examples/ - Java代码示例和模式"
    echo "• context-engineering/tools/ - 验证和部署工具"
    echo "• context-engineering/QUICK_START.md - 快速开始指南"
    echo "• .claude/commands/ - Claude Code命令（向后兼容）"
    echo ""
    print_info "后续步骤:"
    echo "1. 查看快速开始指南: context-engineering/QUICK_START.md"
    echo "2. 创建或编辑 context-engineering/CONTEXT_INITIAL.md 描述功能需求"
    echo "3. 根据你的AI平台选择使用方式："
    echo "   • Augment/Cursor: \"请按照 workflows/generate-prp.md 生成PRP\""
    echo "   • Claude Code: /generate-prp context-engineering/CONTEXT_INITIAL.md"
    echo "   • GitHub Copilot: 参考 platform-specific/copilot-templates/"
    echo "   • TRAE: 导入 platform-specific/trae-config/ 配置"
    echo "4. 提取现有代码模式（可选）"
    echo ""
    print_info "可用工具:"
    echo "• 验证模板: bash context-engineering/tools/validate-template.sh"
    echo "• 验证中间件文档: bash context-engineering/tools/validate-middleware.sh <middleware-file>"
    echo "• 通用工作流: context-engineering/workflows/"
    echo "• 平台特定配置: context-engineering/platform-specific/"
    echo ""
    print_info "文档说明:"
    echo "• context-engineering/CONTEXT_AI_INSTRUCTIONS.md - 统一AI开发规范"
    echo "• context-engineering/CONTEXT_INITIAL.md - 需求描述模板"
    echo "• context-engineering/workflows/ - 通用工作流文档"
    echo "• context-engineering/platform-specific/ - 平台特定配置"
    echo "• context-engineering/middleware-docs/ - 企业中间件文档"
    echo "• context-engineering/examples/ - 代码示例和模式"
    echo ""
    print_info "特性优势:"
    echo "• 无侵入式集成 - 不影响原项目任何文件"
    echo "• 独立目录 - 所有Context Engineering相关文件在context-engineering/下"
    echo "• 多平台支持 - 兼容Augment、Cursor、GitHub Copilot、Claude Code、TRAE"
    echo "• 统一工作流 - 所有平台基于相同的核心流程"
    echo "• 向后兼容 - 保持对Claude Code用户的完整支持"
    echo "• 首次使用建议查看 context-engineering/QUICK_START.md"
    echo ""
}

# 主执行流程
main() {
    # 如果只是验证模式
    if [[ "$VALIDATE_ONLY" == true ]]; then
        if validate_template; then
            print_success "模板验证通过 ✅"
            exit 0
        else
            print_error "模板验证失败 ❌"
            exit 1
        fi
    fi
    
    # 检查是否提供了目标目录
    if [[ -z "$TARGET_DIR" ]]; then
        print_error "请指定目标目录"
        show_help
        exit 1
    fi
    
    # 执行部署流程
    print_info "开始部署流程..."
    
    # 1. 验证模板
    if ! validate_template; then
        print_error "模板验证失败，终止部署"
        exit 1
    fi
    
    # 2. 准备目标目录
    if ! prepare_target_directory "$TARGET_DIR"; then
        print_error "准备目标目录失败，终止部署"
        exit 1
    fi
    
    # 3. 复制模板文件
    if ! copy_template_files; then
        print_error "复制模板文件失败，终止部署"
        exit 1
    fi
    
    # 4. 设置文件权限
    set_file_permissions
    
    # 5. 验证部署结果（如果未跳过）
    if [[ "$SKIP_VALIDATION" != true ]]; then
        if ! validate_deployment; then
            print_error "部署验证失败"
            exit 1
        fi
    fi
    
    # 6. 显示部署后说明
    show_deployment_info
    
    print_success "部署完成！"
}

# 执行主函数
main "$@"