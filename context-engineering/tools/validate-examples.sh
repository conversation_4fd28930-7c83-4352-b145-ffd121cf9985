#!/bin/bash

# 验证示例文档的完整性和格式
# 使用方法: ./tools/validate-examples.sh

set -e

EXAMPLES_DIR="examples"
ERRORS=0

echo "🔍 验证示例文档..."

# 检查examples目录是否存在
if [ ! -d "$EXAMPLES_DIR" ]; then
    echo "❌ examples目录不存在"
    exit 1
fi

# 验证README文件
validate_readme() {
    local dir=$1
    local readme_file="$dir/README.md"
    
    if [ ! -f "$readme_file" ]; then
        echo "❌ 缺少README文件: $readme_file"
        ((ERRORS++))
        return
    fi
    
    echo "✅ README文件存在: $readme_file"
}

# 验证示例文档格式
validate_example_doc() {
    local file=$1
    
    if [ ! -f "$file" ]; then
        echo "❌ 文件不存在: $file"
        ((ERRORS++))
        return
    fi
    
    # 检查必需的章节
    local required_sections=("场景描述" "代码示例" "实现要点")
    
    for section in "${required_sections[@]}"; do
        if ! grep -q "$section" "$file"; then
            echo "❌ 缺少必需章节 '$section': $file"
            ((ERRORS++))
        fi
    done
    
    # 检查是否包含Java代码块
    if ! grep -q '```java' "$file"; then
        echo "⚠️  未发现Java代码块: $file"
    fi
    
    echo "✅ 示例文档格式正确: $file"
}

# 验证代码语法（简单检查）
validate_code_syntax() {
    local file=$1
    
    # 提取Java代码块
    awk '/```java/,/```/' "$file" | grep -v '```' > /tmp/temp_code.java 2>/dev/null || true
    
    if [ -s /tmp/temp_code.java ]; then
        # 简单的语法检查
        if grep -q 'public class\|@Service\|@Repository\|@Controller' /tmp/temp_code.java; then
            echo "✅ 代码块包含有效的Java结构: $file"
        else
            echo "⚠️  代码块可能缺少完整的Java结构: $file"
        fi
    fi
    
    rm -f /tmp/temp_code.java
}

# 主验证流程
main() {
    echo "📁 验证目录结构..."
    
    # 验证主要子目录的README
    for subdir in "typical-scenarios" "middleware-integration" "code-patterns"; do
        validate_readme "$EXAMPLES_DIR/$subdir"
    done
    
    echo ""
    echo "📄 验证示例文档..."
    
    # 查找所有.md文件（排除README）
    find "$EXAMPLES_DIR" -name "*.md" -not -name "README.md" | while read -r file; do
        echo "检查: $file"
        validate_example_doc "$file"
        validate_code_syntax "$file"
        echo ""
    done
    
    echo "📊 验证统计..."
    
    # 统计示例文件数量
    local total_examples=$(find "$EXAMPLES_DIR" -name "*.md" -not -name "README.md" | wc -l)
    echo "总示例文档数量: $total_examples"
    
    # 统计代码块数量
    local total_code_blocks=$(find "$EXAMPLES_DIR" -name "*.md" -exec grep -l '```java' {} \; | wc -l)
    echo "包含Java代码的文档: $total_code_blocks"
    
    if [ $ERRORS -eq 0 ]; then
        echo ""
        echo "🎉 所有示例文档验证通过！"
        exit 0
    else
        echo ""
        echo "❌ 发现 $ERRORS 个错误，请修复后重试"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "示例文档验证工具"
    echo ""
    echo "使用方法:"
    echo "  $0                    验证所有示例文档"
    echo "  $0 --help           显示此帮助信息"
    echo ""
    echo "验证内容:"
    echo "  - README文件完整性"
    echo "  - 示例文档格式规范"
    echo "  - Java代码块语法"
    echo "  - 必需章节完整性"
}

# 参数处理
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        main
        ;;
    *)
        echo "未知参数: $1"
        echo "使用 --help 查看帮助信息"
        exit 1
        ;;
esac
