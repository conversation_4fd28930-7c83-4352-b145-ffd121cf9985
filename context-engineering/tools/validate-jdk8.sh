#!/bin/bash

# 验证JDK 1.8配置和兼容性
# 使用方法: ./tools/validate-jdk8.sh [项目路径]

set -e

PROJECT_DIR="${1:-.}"
ERRORS=0

echo "☕ 验证JDK 1.8配置和兼容性..."

# 检查Java版本
check_java_version() {
    echo "🔍 检查Java版本..."
    
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        echo "当前Java版本: $JAVA_VERSION"
        
        if [[ $JAVA_VERSION == 1.8* ]]; then
            echo "✅ Java版本符合要求 (JDK 1.8)"
        else
            echo "⚠️  当前Java版本不是JDK 1.8，可能影响编译"
        fi
    else
        echo "❌ 未找到Java命令"
        ((ERRORS++))
    fi
}

# 检查Maven配置
check_maven_config() {
    echo ""
    echo "🔍 检查Maven配置..."
    
    local pom_file="$PROJECT_DIR/pom.xml"
    
    if [ ! -f "$pom_file" ]; then
        echo "⚠️  未找到pom.xml文件: $pom_file"
        return
    fi
    
    # 检查Java版本配置
    if grep -q "<maven.compiler.source>1.8</maven.compiler.source>" "$pom_file"; then
        echo "✅ Maven编译源版本配置正确 (1.8)"
    else
        echo "❌ Maven编译源版本配置缺失或错误"
        ((ERRORS++))
    fi
    
    if grep -q "<maven.compiler.target>1.8</maven.compiler.target>" "$pom_file"; then
        echo "✅ Maven编译目标版本配置正确 (1.8)"
    else
        echo "❌ Maven编译目标版本配置缺失或错误"
        ((ERRORS++))
    fi
    
    # 检查Spring Boot版本
    if grep -q "<version>2\." "$pom_file" && grep -q "spring-boot-starter-parent" "$pom_file"; then
        echo "✅ Spring Boot版本配置正确 (2.x系列)"
    elif grep -q "<version>3\." "$pom_file" && grep -q "spring-boot-starter-parent" "$pom_file"; then
        echo "❌ Spring Boot 3.x需要JDK 17+，与JDK 1.8不兼容"
        ((ERRORS++))
    else
        echo "⚠️  未检测到Spring Boot版本配置"
    fi
}

# 检查代码中的JDK 9+特性
check_jdk9_features() {
    echo ""
    echo "🔍 检查代码中的JDK 9+特性..."
    
    local java_files=$(find "$PROJECT_DIR" -name "*.java" 2>/dev/null || true)
    
    if [ -z "$java_files" ]; then
        echo "⚠️  未找到Java源文件"
        return
    fi
    
    # 检查var关键字 (JDK 10+)
    if echo "$java_files" | xargs grep -l "var " 2>/dev/null; then
        echo "❌ 发现var关键字使用 (JDK 10+特性)"
        ((ERRORS++))
    fi
    
    # 检查List.of() (JDK 9+)
    if echo "$java_files" | xargs grep -l "List\.of(" 2>/dev/null; then
        echo "❌ 发现List.of()使用 (JDK 9+特性)"
        ((ERRORS++))
    fi
    
    # 检查String.isBlank() (JDK 11+)
    if echo "$java_files" | xargs grep -l "\.isBlank()" 2>/dev/null; then
        echo "❌ 发现String.isBlank()使用 (JDK 11+特性)"
        ((ERRORS++))
    fi
    
    # 检查文本块 (JDK 15+)
    if echo "$java_files" | xargs grep -l '"""' 2>/dev/null; then
        echo "❌ 发现文本块使用 (JDK 15+特性)"
        ((ERRORS++))
    fi
    
    if [ $ERRORS -eq 0 ]; then
        echo "✅ 未发现JDK 9+特性使用"
    fi
}

# 检查推荐的JDK 1.8特性使用
check_jdk8_features() {
    echo ""
    echo "🔍 检查JDK 1.8特性使用情况..."
    
    local java_files=$(find "$PROJECT_DIR" -name "*.java" 2>/dev/null || true)
    
    if [ -z "$java_files" ]; then
        return
    fi
    
    # 检查Lambda表达式
    if echo "$java_files" | xargs grep -l " -> " 2>/dev/null; then
        echo "✅ 发现Lambda表达式使用"
    fi
    
    # 检查Stream API
    if echo "$java_files" | xargs grep -l "\.stream()" 2>/dev/null; then
        echo "✅ 发现Stream API使用"
    fi
    
    # 检查Optional
    if echo "$java_files" | xargs grep -l "Optional<" 2>/dev/null; then
        echo "✅ 发现Optional使用"
    fi
    
    # 检查java.time API
    if echo "$java_files" | xargs grep -l "LocalDate\|LocalDateTime\|LocalTime" 2>/dev/null; then
        echo "✅ 发现java.time API使用"
    fi
    
    # 检查方法引用
    if echo "$java_files" | xargs grep -l "::" 2>/dev/null; then
        echo "✅ 发现方法引用使用"
    fi
}

# 生成JDK 1.8兼容的pom.xml模板
generate_pom_template() {
    echo ""
    echo "📝 生成JDK 1.8兼容的pom.xml模板..."
    
    cat > "$PROJECT_DIR/pom-jdk8-template.xml" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <groupId>com.example</groupId>
    <artifactId>jdk8-project</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
EOF

    echo "✅ JDK 1.8兼容的pom.xml模板已生成: $PROJECT_DIR/pom-jdk8-template.xml"
}

# 显示帮助信息
show_help() {
    echo "JDK 1.8配置验证工具"
    echo ""
    echo "使用方法:"
    echo "  $0 [项目路径]        验证指定项目的JDK 1.8配置"
    echo "  $0 --help          显示此帮助信息"
    echo ""
    echo "验证内容:"
    echo "  - Java版本检查"
    echo "  - Maven配置验证"
    echo "  - JDK 9+特性检测"
    echo "  - JDK 1.8特性使用情况"
    echo "  - 生成兼容的pom.xml模板"
}

# 主函数
main() {
    check_java_version
    check_maven_config
    check_jdk9_features
    check_jdk8_features
    generate_pom_template
    
    echo ""
    echo "📊 验证完成"
    
    if [ $ERRORS -eq 0 ]; then
        echo "🎉 JDK 1.8配置验证通过！"
        exit 0
    else
        echo "❌ 发现 $ERRORS 个配置问题，请修复后重试"
        exit 1
    fi
}

# 参数处理
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main
        ;;
esac
