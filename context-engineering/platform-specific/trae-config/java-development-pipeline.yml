# TRAE Java Context Engineering 自动化流水线

name: "Java Context Engineering Pipeline"
version: "1.0"
description: "基于Context Engineering的Java功能自动化开发流水线"

# 全局变量
variables:
  project_root: "${WORKSPACE}"
  java_version: "1.8"
  spring_boot_version: "2.x"
  maven_goals: "clean compile test"

# 流水线阶段
stages:
  # 阶段1：需求分析
  - name: "需求分析"
    description: "分析功能需求并生成结构化文档"
    tasks:
      - name: "读取需求文件"
        type: "file_analysis"
        inputs: 
          - "INITIAL.md"
          - "AI-INSTRUCTIONS.md"
        script: |
          # 解析INITIAL.md中的功能需求
          # 提取关键词和技术要求
          # 生成结构化的需求分析报告
        outputs: 
          - "analysis/requirements.json"
          - "analysis/keywords.txt"
        
      - name: "中间件匹配分析"
        type: "middleware_matching"
        depends_on: ["读取需求文件"]
        inputs:
          - "analysis/keywords.txt"
          - "middleware-docs/middleware-list.md"
        script: |
          # 基于关键词匹配企业中间件
          # 读取相关中间件文档
          # 生成中间件集成方案
        outputs:
          - "analysis/middleware-plan.json"

  # 阶段2：设计阶段  
  - name: "设计阶段"
    description: "生成详细的实现设计和PRP文档"
    depends_on: ["需求分析"]
    tasks:
      - name: "代码库模式分析"
        type: "codebase_analysis"
        inputs:
          - "examples/"
          - "src/" # 如果存在现有代码
        script: |
          # 分析现有代码模式
          # 识别可复用的设计模式
          # 生成模式参考文档
        outputs:
          - "design/patterns.json"
          
      - name: "PRP自动生成"
        type: "prp_generation"
        depends_on: ["中间件匹配分析", "代码库模式分析"]
        inputs:
          - "analysis/requirements.json"
          - "analysis/middleware-plan.json"
          - "design/patterns.json"
          - "workflows/generate-prp.md"
        script: |
          # 基于工作流自动生成PRP
          # 包含完整的实现上下文
          # 生成验证检查点
        outputs:
          - "PRPs/auto-generated-${FEATURE_NAME}.md"

  # 阶段3：实现阶段
  - name: "实现阶段"
    description: "基于PRP自动生成Java代码"
    depends_on: ["设计阶段"]
    tasks:
      - name: "项目结构创建"
        type: "project_setup"
        inputs:
          - "PRPs/auto-generated-${FEATURE_NAME}.md"
        script: |
          # 创建Maven项目结构
          # 生成pom.xml和基础配置
          # 设置包结构和目录
        outputs:
          - "pom.xml"
          - "src/main/java/"
          - "src/test/java/"
          - "src/main/resources/"
          
      - name: "核心代码生成"
        type: "code_generation"
        depends_on: ["项目结构创建"]
        inputs:
          - "PRPs/auto-generated-${FEATURE_NAME}.md"
          - "workflows/execute-prp.md"
        script: |
          # 基于PRP生成Java代码
          # 包含Controller、Service、Repository层
          # 集成企业中间件配置
        outputs:
          - "src/main/java/**/*.java"
          - "src/main/resources/application.yml"
          
      - name: "测试代码生成"
        type: "test_generation"
        depends_on: ["核心代码生成"]
        inputs:
          - "src/main/java/**/*.java"
          - "examples/code-patterns/testing-patterns.md"
        script: |
          # 生成单元测试和集成测试
          # 确保测试覆盖率达标
          # 包含Mock和测试数据
        outputs:
          - "src/test/java/**/*.java"

  # 阶段4：验证阶段
  - name: "验证阶段"
    description: "执行编译、测试和质量检查"
    depends_on: ["实现阶段"]
    tasks:
      - name: "编译验证"
        type: "compilation"
        script: "mvn clean compile"
        success_criteria:
          - "exit_code == 0"
          - "no compilation errors"
          
      - name: "单元测试验证"
        type: "unit_testing"
        depends_on: ["编译验证"]
        script: "mvn test"
        success_criteria:
          - "exit_code == 0"
          - "test_pass_rate >= 100%"
          - "test_coverage >= 80%"
          
      - name: "应用启动验证"
        type: "application_startup"
        depends_on: ["单元测试验证"]
        script: |
          mvn spring-boot:run &
          sleep 30
          curl -f http://localhost:8080/actuator/health
          pkill -f spring-boot
        success_criteria:
          - "application starts successfully"
          - "health check returns 200"
          
      - name: "代码质量检查"
        type: "quality_check"
        depends_on: ["应用启动验证"]
        inputs:
          - "src/main/java/**/*.java"
          - "AI-INSTRUCTIONS.md"
        script: |
          # 检查代码规范符合性
          # 验证企业中间件集成
          # 检查安全和性能问题
        outputs:
          - "reports/quality-report.json"

  # 阶段5：文档和部署
  - name: "文档和部署"
    description: "生成文档和准备部署"
    depends_on: ["验证阶段"]
    tasks:
      - name: "API文档生成"
        type: "documentation"
        inputs:
          - "src/main/java/**/*.java"
        script: |
          # 生成API文档
          # 创建使用指南
          # 生成部署说明
        outputs:
          - "docs/api-documentation.md"
          - "docs/deployment-guide.md"
          
      - name: "部署包准备"
        type: "packaging"
        depends_on: ["API文档生成"]
        script: "mvn package -DskipTests"
        outputs:
          - "target/*.jar"

# 错误处理和重试策略
error_handling:
  retry_policy:
    max_retries: 3
    retry_delay: "30s"
    
  failure_actions:
    - name: "编译失败处理"
      condition: "stage == '验证阶段' && task == '编译验证'"
      action: |
        # 分析编译错误
        # 尝试自动修复常见问题
        # 生成错误报告
        
    - name: "测试失败处理"
      condition: "stage == '验证阶段' && task == '单元测试验证'"
      action: |
        # 分析测试失败原因
        # 尝试修复测试用例
        # 调整代码实现

# 通知配置
notifications:
  on_success:
    message: "Java功能开发完成，所有验证通过"
    channels: ["email", "slack"]
    
  on_failure:
    message: "开发过程中出现错误：${ERROR_MESSAGE}"
    channels: ["email", "slack"]
    include_logs: true

# 输出和报告
outputs:
  - name: "开发报告"
    path: "reports/development-summary.md"
    content: |
      # Java功能开发报告
      
      ## 功能概述
      ${FEATURE_DESCRIPTION}
      
      ## 技术栈
      - Java: ${java_version}
      - Spring Boot: ${spring_boot_version}
      - 企业中间件: ${MIDDLEWARE_LIST}
      
      ## 验证结果
      - 编译状态: ${COMPILATION_STATUS}
      - 测试通过率: ${TEST_PASS_RATE}
      - 代码覆盖率: ${CODE_COVERAGE}
      
      ## 部署信息
      - 构建产物: ${BUILD_ARTIFACTS}
      - 部署指南: docs/deployment-guide.md
      
  - name: "质量报告"
    path: "reports/quality-summary.json"
    format: "json"
