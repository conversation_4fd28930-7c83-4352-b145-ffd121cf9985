# GitHub Copilot 使用指南

## 概述

GitHub Copilot主要通过代码补全工作，需要通过详细的注释来引导代码生成。这里提供了专门的注释模板来配合Java Context Engineering使用。

## 使用方式

### 1. 功能开发流程
1. 先阅读 `workflows/generate-prp.md` 了解需要实现的功能
2. 使用下面的注释模板创建Java文件
3. 让Copilot基于注释生成代码实现
4. 参考 `workflows/execute-prp.md` 进行验证和测试

### 2. 注释模板使用
- 复制相应的注释模板到你的Java文件中
- 根据具体需求修改注释内容
- 让Copilot基于注释生成代码
- 根据生成结果进行调整和优化

## 注释模板

### 企业级服务模板
```java
/**
 * [服务名称] - 企业级Java服务实现
 * 
 * Context Engineering模式：
 * - 参考文档：AI-INSTRUCTIONS.md
 * - 代码示例：examples/typical-scenarios/[相关示例].md
 * - 中间件集成：[JSF/JMQ/JIMDB等]
 * 
 * 功能需求：
 * 1. [主要功能1]
 * 2. [主要功能2]
 * 3. [主要功能3]
 * 
 * 技术要求：
 * - JDK 1.8兼容
 * - Spring Boot 2.x
 * - Maven依赖管理
 * - JUnit测试覆盖
 * 
 * 企业中间件：
 * - [具体中间件名称]：[使用目的和方式]
 * - 降级处理：[降级策略说明]
 * - 错误处理：[异常处理方式]
 */
@Service
@Slf4j
public class [ServiceName]Service {
    
    // Copilot会基于上述注释生成相应的字段和方法
    
}
```

### 控制器模板
```java
/**
 * [功能模块]控制器
 * 
 * RESTful API设计：
 * - 遵循REST规范
 * - 统一响应格式
 * - 参数验证和异常处理
 * 
 * 接口列表：
 * - GET /api/[resource] - [功能描述]
 * - POST /api/[resource] - [功能描述]
 * - PUT /api/[resource]/{id} - [功能描述]
 * - DELETE /api/[resource]/{id} - [功能描述]
 * 
 * 参考：examples/code-patterns/controller-patterns.md
 */
@RestController
@RequestMapping("/api/[resource]")
@Validated
public class [Resource]Controller {
    
    // Copilot会生成相应的接口方法
    
}
```

### 配置类模板
```java
/**
 * [功能模块]配置类
 * 
 * 配置内容：
 * - [配置项1]：[说明]
 * - [配置项2]：[说明]
 * 
 * 企业中间件配置：
 * - [中间件名称]：[配置说明]
 * - 连接池配置：[参数说明]
 * - 超时和重试：[策略说明]
 * 
 * 参考：middleware-docs/[相关中间件].md
 */
@Configuration
@EnableConfigurationProperties([ConfigProperties].class)
public class [Module]Configuration {
    
    // Copilot会生成Bean定义和配置方法
    
}
```

### 测试类模板
```java
/**
 * [被测试类]的单元测试
 * 
 * 测试策略：
 * - 正常流程测试
 * - 边界条件测试
 * - 异常情况测试
 * - Mock外部依赖
 * 
 * 测试覆盖：
 * - [方法1]：[测试场景]
 * - [方法2]：[测试场景]
 * 
 * 参考：examples/code-patterns/testing-patterns.md
 */
@ExtendWith(MockitoExtension.class)
class [ClassName]Test {
    
    @Mock
    private [Dependency] [dependencyName];
    
    @InjectMocks
    private [ClassName] [instanceName];
    
    // Copilot会生成测试方法
    
}
```

## 中间件集成注释模板

### JSF远程调用
```java
/**
 * JSF远程服务调用实现
 * 
 * 服务配置：
 * - 服务别名：[alias]
 * - 超时时间：[timeout]ms
 * - 重试次数：[retries]
 * 
 * 降级策略：
 * - 缓存降级：使用本地缓存数据
 * - 默认值降级：返回预设默认值
 * - 异常处理：记录日志并抛出业务异常
 * 
 * 参考：middleware-docs/jsf.md
 */
@Component
public class [Service]RemoteClient {
    
    @JSFConsumer(alias = "[serviceName]", timeout = 5000)
    private [RemoteService] [remoteService];
    
    // Copilot会生成调用方法和降级处理
    
}
```

### JIMDB缓存操作
```java
/**
 * JIMDB缓存操作实现
 * 
 * 缓存策略：
 * - 缓存键格式：[key-pattern]
 * - 过期时间：[expiration]秒
 * - 更新策略：[update-strategy]
 * 
 * 操作类型：
 * - 基础操作：get/set/delete
 * - 批量操作：mget/mset
 * - 分布式锁：lock/unlock
 * 
 * 参考：middleware-docs/jimdb.md
 */
@Component
public class [Module]CacheService {
    
    @Autowired
    private JimdbClient jimdbClient;
    
    // Copilot会生成缓存操作方法
    
}
```

### JMQ消息处理
```java
/**
 * JMQ消息处理实现
 * 
 * 消息配置：
 * - Topic：[topic-name]
 * - 消费组：[consumer-group]
 * - 消息类型：[message-type]
 * 
 * 处理策略：
 * - 消息验证：[validation-rules]
 * - 重试机制：[retry-policy]
 * - 死信处理：[dead-letter-handling]
 * 
 * 参考：middleware-docs/jmq.md
 */
@Component
public class [Module]MessageHandler {
    
    // 消息生产者
    @JMQProducer(topic = "[topic]")
    private MessageProducer messageProducer;
    
    // 消息消费者
    @JMQConsumer(topic = "[topic]", consumerGroup = "[group]")
    public void handleMessage([MessageType] message) {
        // Copilot会生成消息处理逻辑
    }
    
}
```

## 使用技巧

### 1. 渐进式开发
- 先写基础的类结构和注释
- 让Copilot生成基础实现
- 逐步添加详细的业务逻辑
- 最后完善异常处理和测试

### 2. 充分利用注释
- 在方法前添加详细的功能说明
- 使用TODO注释描述需要实现的功能
- 引用相关的文档和示例文件
- 说明预期的输入输出格式

### 3. 模式引用
- 在注释中明确引用examples/中的相关模式
- 说明要遵循的企业规范和约定
- 指明需要集成的中间件和配置要求

### 4. 质量保证
- 生成代码后对照AI-INSTRUCTIONS.md检查规范
- 运行编译和测试验证代码质量
- 检查企业中间件集成是否正确
- 确保异常处理和日志记录完整
