# 平台特定配置说明

## 概述

大部分AI编程助手可以直接使用通用的工作流文档（`workflows/`目录）和AI指令（`AI-INSTRUCTIONS.md`）。只有少数平台因为特殊的交互方式或工具集成需要额外的配置。

## 平台支持情况

### 🟢 直接支持（无需额外配置）
- **Augment** - 使用通用工作流，充分利用其代码库理解能力
- **Cursor** - 使用通用工作流，通过Ctrl+K或对话方式交互
- **Claude Code** - 保持原有命令系统，同时支持新工作流

### 🟡 需要轻微配置
- **GitHub Copilot** - 需要特定的注释模板来引导代码生成

### 🔴 需要专门配置
- **TRAE** - 需要YAML任务定义文件进行自动化配置

## 使用指南

### 对于大多数平台（Augment、Cursor等）
直接使用以下方式：

```
请按照 workflows/generate-prp.md 为我生成Java PRP
请按照 workflows/execute-prp.md 执行 PRPs/feature.md
请按照 workflows/extract-patterns.md 提取代码模式
```

### 对于GitHub Copilot
参考 `platform-specific/copilot-templates/` 中的注释模板

### 对于TRAE
参考 `platform-specific/trae-config/` 中的任务定义文件

### 对于Claude Code
继续使用原有命令：
```
/generate-prp INITIAL.md
/execute-prp PRPs/feature.md
/extract-patterns
```

或者使用新的通用工作流（两种方式都支持）

## 设计原则

1. **最小差异化**：只在必要时创建平台特定配置
2. **向后兼容**：保持对现有Claude用户的支持
3. **统一体验**：尽可能提供一致的使用体验
4. **渐进增强**：基础功能通用，高级功能可以平台特定优化
