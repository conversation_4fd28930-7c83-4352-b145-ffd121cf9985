# 通用工作流使用指南

## 概述

这个目录包含了适用于所有AI编程助手的通用工作流文档，替代了原有的Claude特定命令系统。

## 工作流文件

- **`generate-prp.md`** - PRP生成工作流
- **`execute-prp.md`** - PRP执行工作流  
- **`extract-patterns.md`** - 代码模式提取工作流

## 使用方式

### 对于Augment用户
```
"请按照 workflows/generate-prp.md 的步骤为我生成PRP"
```

### 对于Cursor用户
```
Ctrl+K: "基于 workflows/generate-prp.md 生成Java功能实现方案"
```

### 对于GitHub Copilot用户
```
// 参考 workflows/generate-prp.md 的步骤
// 请为以下功能生成实现方案
```

### 对于Claude Code用户
```
继续使用原有命令：/generate-prp, /execute-prp, /extract-patterns
或者使用新的工作流：按照 workflows/generate-prp.md 执行
```

## 工作流特点

1. **平台无关**：适用于任何支持文件读取的AI助手
2. **交互式**：通过对话收集参数，替代自动参数传递
3. **标准化**：统一的步骤和输出格式
4. **可扩展**：易于添加新的验证步骤和功能

## 从Claude命令迁移

| 原Claude命令 | 新工作流使用方式 |
|-------------|-----------------|
| `/generate-prp INITIAL.md` | "请按照 workflows/generate-prp.md 为 INITIAL.md 生成PRP" |
| `/execute-prp PRPs/feature.md` | "请按照 workflows/execute-prp.md 执行 PRPs/feature.md" |
| `/extract-patterns` | "请按照 workflows/extract-patterns.md 提取代码模式" |

## 注意事项

- 确保AI助手能够访问项目的所有相关文件
- 工作流中的步骤可以根据具体需求进行调整
- 建议在执行前先阅读对应的工作流文档了解完整流程
