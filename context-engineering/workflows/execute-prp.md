# Java PRP执行工作流

## 使用方式

### 对话式请求格式
```
请按照此工作流执行PRP实现：
- PRP文件：PRPs/[文件名].md
- 验证要求：[编译、测试、启动检查等]
```

## 工作流步骤

### 步骤1：PRP加载和理解
1. **读取PRP文件**
   - 完整读取指定的PRP文件
   - 理解所有上下文、需求和约束
   - 确认实现目标和成功标准

2. **上下文验证**
   - 确保能够访问PRP中引用的所有文档
   - 验证企业中间件文档的可用性
   - 检查代码示例和模式的完整性

3. **补充研究（如需要）**
   - 如果PRP中的信息不足，进行额外研究
   - 搜索最新的技术文档和最佳实践
   - 探索代码库中的相关实现

### 步骤2：实现计划制定
1. **深度分析**
   - 分析PRP中的所有需求
   - 识别技术难点和风险点
   - 理解企业中间件集成要求

2. **任务分解**
   - 将复杂功能分解为可管理的小任务
   - 按依赖关系排序实现步骤
   - 确定每个步骤的验证方法

3. **模式识别**
   - 从现有代码中识别要遵循的实现模式
   - 确定代码结构和架构方式
   - 规划测试策略

### 步骤3：代码实现
1. **项目结构创建**
   - 创建必要的包结构和目录
   - 设置Maven配置和依赖
   - 配置Spring Boot基础设置

2. **核心功能实现**
   - 按照PRP中的实现蓝图编写代码
   - 遵循AI-INSTRUCTIONS.md中的编码规范
   - 实现业务逻辑和数据访问层

3. **企业中间件集成**
   - 根据PRP中的中间件方案进行集成
   - 实现配置类和客户端代码
   - 添加降级处理和错误恢复机制

4. **测试代码编写**
   - 创建单元测试覆盖主要功能
   - 编写集成测试验证中间件集成
   - 确保测试覆盖率满足要求

### 步骤4：验证和测试
1. **编译验证**
   ```bash
   mvn clean compile
   ```
   - 确保所有代码能够正常编译
   - 解决编译错误和依赖问题

2. **单元测试验证**
   ```bash
   mvn test
   ```
   - 运行所有单元测试
   - 确保测试通过率100%
   - 修复失败的测试用例

3. **应用启动验证**
   ```bash
   mvn spring-boot:run --dry-run
   ```
   - 验证Spring Boot应用配置正确
   - 检查依赖注入和Bean配置

4. **集成测试验证（如适用）**
   ```bash
   mvn spring-boot:run &
   sleep 10
   curl -f http://localhost:8080/actuator/health
   pkill -f spring-boot
   ```
   - 启动应用进行端到端测试
   - 验证健康检查和基本功能
   - 测试企业中间件连接

### 步骤5：问题修复和迭代
1. **错误处理**
   - 如果验证失败，分析错误原因
   - 使用PRP中的错误处理模式进行修复
   - 重新运行验证直到全部通过

2. **代码优化**
   - 检查代码质量和性能
   - 优化企业中间件使用方式
   - 确保符合企业开发规范

3. **文档更新**
   - 更新代码注释和文档
   - 记录重要的设计决策
   - 添加使用说明和配置指南

### 步骤6：最终验证和完成
1. **完整性检查**
   - 对照PRP检查所有功能是否实现
   - 验证所有需求是否满足
   - 确认企业中间件集成正常

2. **质量检查清单**
   - [ ] 所有代码编译通过
   - [ ] 单元测试100%通过
   - [ ] 应用能够正常启动
   - [ ] 企业中间件集成正常
   - [ ] 代码符合规范要求
   - [ ] 文档完整准确

3. **最终验证套件**
   ```bash
   # 完整验证流程
   mvn clean compile test
   mvn spring-boot:run &
   sleep 15
   # 执行功能测试
   curl -f http://localhost:8080/actuator/health
   # 清理
   pkill -f spring-boot
   ```

4. **完成报告**
   - 报告实现完成状态
   - 说明实现的功能和特性
   - 提供使用指南和注意事项

## 重要注意事项

### Java特定要求
- **JDK 1.8兼容性**：确保所有代码和依赖支持JDK 1.8
- **Maven配置**：正确配置编译器版本和依赖管理
- **Spring Boot版本**：使用2.x系列，避免3.x版本
- **企业中间件**：按照企业规范进行集成和配置

### 错误处理策略
- 如果验证失败，不要放弃，使用PRP中的错误模式
- 逐步调试，从简单问题开始解决
- 必要时回到PRP重新理解需求
- 可以请求用户澄清模糊的需求

### 成功标准
- 所有验证命令都能成功执行
- 代码质量符合企业标准
- 功能完整实现PRP中的所有需求
- 企业中间件集成稳定可靠

## 示例使用

### 用户请求示例
```
请按照 workflows/execute-prp.md 执行以下PRP：
- PRP文件：PRPs/user-management-service.md
- 验证要求：完整的编译、测试、启动验证
- 特别关注：JIMDB缓存集成和JMQ消息处理
```

### AI执行流程
1. 读取并理解PRP文件内容
2. 制定详细的实现计划
3. 按步骤实现所有功能
4. 运行验证确保质量
5. 修复问题直到全部通过
6. 报告完成状态和使用指南
