# Java代码模式提取工作流

## 使用方式

### 对话式请求格式
```
请按照此工作流提取项目代码模式：
- 分析范围：[整个项目 或 指定目录/文件]
- 关注模式：[测试、配置、服务层等，或全部]
- 输出方式：[更新现有文档 或 创建新文档]
```

## 核心约束原则

### 🚫 严禁行为
- **禁止"脑补"代码**：不能添加项目中不存在的代码或功能
- **禁止"理想化"**：不能为了"完整性"而修改实际代码结构
- **禁止"最佳实践化"**：不能将实际做法包装成理论上的最佳实践
- **禁止"通用化"**：不能为了通用性而去除实际的业务逻辑特征

### ✅ 必须行为
- **必须基于实际代码**：每个模式都必须在实际代码中找到对应实现
- **必须保持原样**：代码片段必须保持实际项目中的原始结构
- **必须如实记录**：包括不完整、有问题或技术债务的部分
- **必须标注缺失**：明确记录项目中缺失的常见模式

## 工作流步骤

### 步骤1：项目代码事实收集（严格按实际代码）
1. **代码库扫描**
   - **仅扫描**项目中实际存在的Java源代码文件
   - **仅识别**项目实际使用的技术栈和框架（从pom.xml和代码中确认）
   - **仅分析**实际存在的代码结构和组织方式

2. **技术栈事实确认**
   - **从pom.xml确认**：Spring Boot版本和实际依赖
   - **从代码确认**：实际使用的数据访问技术（检查实际的Mapper、Entity等）
   - **从代码确认**：实际存在的测试框架和测试类
   - **从代码确认**：实际集成的企业中间件（不能假设）

3. **架构事实分析**
   - **仅记录实际存在的**：Controller、Service、Repository类
   - **仅记录实际使用的**：依赖注入和Bean管理方式
   - **仅记录实际配置的**：配置管理和环境区分方式

**关键原则：只记录实际存在的代码，不添加"应该有的"内容**

### 步骤2：实际模式识别（严格基于现有代码）
**核心原则：只识别实际存在的模式，不推测或补充**

1. **测试模式事实识别**
   - **仅记录实际存在的**：JUnit测试类结构和命名约定
   - **仅记录实际使用的**：Mock对象使用方式和测试数据准备
   - **仅记录实际组织的**：集成测试和单元测试的组织方式
   - **如果不存在测试，明确记录：项目当前无测试模式**

2. **配置模式事实识别**
   - **仅记录实际存在的**：Spring配置类和Bean定义方式
   - **仅记录实际使用的**：属性配置和环境变量使用
   - **仅记录实际配置的**：Profile管理和自定义配置
   - **不要假设"应该有的"配置模式**

3. **服务层模式事实识别**
   - **仅记录实际存在的**：Service类设计和业务逻辑组织
   - **仅记录实际使用的**：事务管理和异常处理方式
   - **仅记录实际实现的**：服务间调用和依赖管理
   - **仅记录实际存在的**：业务验证和数据转换模式

4. **数据访问模式事实识别**
   - **仅记录实际存在的**：Mapper接口设计和实现方式
   - **仅记录实际使用的**：实体映射和关系处理
   - **仅记录实际定义的**：查询方法和自定义查询
   - **仅记录实际配置的**：数据库事务和连接管理

5. **异常处理模式事实识别**
   - **仅记录实际存在的**：全局异常处理器设计
   - **仅记录实际定义的**：自定义异常类和错误码
   - **仅记录实际使用的**：错误响应格式
   - **如果不存在完整异常处理，如实记录现状**

6. **中间件集成模式事实识别**
   - **仅记录实际集成的**：企业中间件客户端配置和使用
   - **仅记录实际实现的**：降级处理和熔断机制
   - **仅记录实际使用的**：缓存使用和失效策略
   - **如果没有中间件集成，明确记录：项目当前无中间件集成**

### 步骤3：严格按实际代码提取和文档化
1. **代码片段严格提取**
   - **严格复制实际代码**：选择具有代表性的实际代码示例
   - **禁止修改结构**：保持实际的业务逻辑和结构，不要"通用化"
   - **禁止添加不存在的代码**：只添加必要的注释说明，不添加实际不存在的方法或功能
   - **保持实际状态**：如果代码不完整或有技术债务，如实记录现状

2. **模式文档真实创建**
   - **只为实际存在的模式创建文档**：如果某个模式不存在，不要创建文档
   - **如实描述现状**：包含实际的使用方式，不要描述"理想状态"
   - **明确标注限制**：如果某个模式有局限性或不完整，明确标注
   - **记录实际陷阱**：记录项目中实际遇到的问题，不要假设"常见陷阱"

### 步骤4：代码事实核查（强制执行）
1. **逐一核查代码片段**
   - **对每个提取的代码片段**：在项目中找到对应的原始文件和行号
   - **记录代码来源**：文件路径、类名、方法名
   - **验证代码完整性**：确保没有遗漏关键部分或添加不存在的部分

2. **功能存在性核查**
   - **对每个描述的功能**：在实际代码中验证是否真实存在
   - **标注功能状态**：完整实现、部分实现、或不存在
   - **记录实际限制**：如果功能有局限性，如实记录

3. **反向验证**
   - **从文档回到代码**：确保文档中的每个描述都能在代码中找到对应
   - **检查是否有"理想化"描述**：删除任何不基于实际代码的描述
   - **确保一致性**：文档描述与代码实现完全一致

### 步骤5：文档更新和组织
1. **基于实际代码更新模式文档**
   - **只更新实际存在模式的文档**：
     * `examples/code-patterns/service-layer-patterns.md` - 仅当实际有Service层时
     * `examples/code-patterns/configuration-patterns.md` - 仅当实际有配置类时
     * `examples/code-patterns/data-access-patterns.md` - 仅当实际有数据访问层时
   - **对于不存在的模式**：在文档中明确标注"项目当前未实现此模式"

2. **创建缺失模式说明文档**
   - **记录项目当前缺失的常见模式**：如测试模式、异常处理模式等
   - **说明缺失原因**：技术债务、项目阶段、业务需求等
   - **更新 `examples/code-patterns/README.md`**：明确标注哪些模式存在，哪些不存在

### 步骤5：事实性质量检查和验证
1. **模式真实性验证**
   - **验证提取的模式确实存在于代码中**：每个模式都能在实际代码中找到对应实现
   - **验证模式描述的准确性**：描述与实际代码实现完全一致
   - **禁止添加"最佳实践"建议**：只记录项目实际采用的做法

2. **代码示例事实验证**
   - **确保代码示例来自实际项目**：每个示例都能在项目中找到原始代码
   - **验证示例的原始性**：没有为了"完整性"而修改实际代码
   - **检查注释的事实性**：注释描述的是实际功能，不是理想功能

3. **文档事实性检查**
   - **检查文档是否如实反映代码现状**：包括不完整或有问题的部分
   - **验证没有"脑补"内容**：所有描述都基于实际代码
   - **确保缺失模式被明确标注**：如果某个常见模式不存在，明确记录

## 输出格式标准

### 事实性模式文档模板
```markdown
# [模式名称] - 项目实际实现

## 实现状态
**当前状态**：[完整实现/部分实现/未实现]
**代码位置**：[具体的文件路径和类名]

## 概述
[基于实际代码的模式描述，不添加理想化内容]

## 实际使用场景
- [项目中实际使用的场景1]
- [项目中实际使用的场景2]
- **注意**：仅列出项目中实际存在的使用场景

## 代码示例（来自实际项目）

### 实际实现
```java
/**
 * 来源：[具体文件路径]
 * 说明：[实际功能描述]
 */
// 从项目中直接复制的代码，保持原始结构
```

### 实际配置（如存在）
```java
/**
 * 来源：[具体配置文件路径]
 * 说明：[实际配置说明]
 */
// 实际的配置代码
```

## 项目实际做法
- [实际做法1：项目中真实采用的方式]
- [实际做法2：另一个真实的实现方式]
- **不包含**：理论上的"最佳实践"建议

## 当前限制和问题
- [实际存在的限制1]
- [实际存在的技术债务2]
- [需要改进的地方3]

## 相关实现
- [项目中相关的其他实现：链接到实际存在的模式]
- [依赖的实际组件：实际使用的库或框架]
```

## 支持的模式类型

### 核心模式
- **Spring Boot应用模式**：应用启动、配置管理、Bean生命周期
- **REST API模式**：Controller设计、请求响应处理、参数验证
- **业务服务模式**：Service层设计、事务管理、业务逻辑组织
- **数据持久化模式**：Repository设计、JPA使用、查询优化

### 质量保证模式
- **测试模式**：单元测试、集成测试、Mock使用、测试数据管理
- **异常处理模式**：全局异常处理、自定义异常、错误响应
- **日志监控模式**：日志记录、性能监控、健康检查、指标收集

### 企业集成模式
- **中间件集成模式**：JSF、JMQ、JIMDB等企业中间件使用
- **安全模式**：认证授权、输入验证、安全配置
- **配置管理模式**：多环境配置、动态配置、配置中心集成

## 示例使用

### 用户请求示例
```
请按照 workflows/extract-patterns.md 提取项目代码模式：
- 分析整个项目的Java代码
- 重点关注服务层和测试模式
- 更新 examples/code-patterns/ 中的相关文档
```

### AI执行流程（强调事实性）
1. **严格扫描**：仅扫描项目中实际存在的Java源代码文件
2. **事实识别**：仅识别实际存在的代码模式，不推测或补充
3. **原样提取**：提取实际代码片段，保持原始结构和内容
4. **强制核查**：对每个代码片段进行事实性核查，记录来源
5. **如实文档**：创建基于实际代码的模式文档，标注限制和问题
6. **事实验证**：验证文档内容与实际代码的一致性
7. **诚实报告**：报告实际存在的模式和明确缺失的模式
