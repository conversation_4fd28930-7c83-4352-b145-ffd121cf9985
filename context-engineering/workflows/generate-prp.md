# Java PRP生成工作流

## 使用方式

### 对话式请求格式
```
请按照此工作流为我生成Java PRP：
- 功能需求文件：[INITIAL.md 或直接描述功能]
- 输出文件名：[可选，默认基于功能名自动生成]
```

## 工作流步骤

### 步骤1：需求分析和参数收集
1. **读取功能需求**
   - 如果用户提供了文件路径，读取该文件
   - 如果用户直接描述，基于描述进行分析
   - 理解功能需求、技术要求和业务场景

2. **确认输出路径**
   - 默认保存到：`PRPs/{feature-name}.md`
   - 用户可以指定自定义路径

### 步骤2：企业中间件发现
1. **读取中间件清单**
   - 始终先读取 `middleware-docs/middleware-list.md`
   - 了解可用的企业中间件和匹配规则

2. **关键词匹配**
   - 分析功能需求中的关键词：
     - RPC、远程调用、服务调用 → JSF
     - 消息、队列、异步、事件 → JMQ
     - 缓存、Redis、快速访问 → Redis
     - 配置、参数、开关 → DUCC
     - 日志、监控 → DongLog

3. **读取匹配的中间件文档**
   - **重要**：只读取匹配的中间件文档，避免上下文浪费
   - 理解集成方式、配置要求和最佳实践

### 步骤3：核心约束和规范读取（必须执行）
1. **强制读取核心约束**
   - **必须先读取** `context-engineering/examples/README.md` 了解约束体系
   - **必须严格遵守** `context-engineering/examples/code-patterns/` 中的核心规范：
     * 如果涉及API开发：严格遵守 `rest-api-controller-patterns.md` 中的约束
     * 如果涉及服务层：严格遵守 `service-layer-patterns.md` 中的约束
     * 如果涉及数据访问：严格遵守 `data-access-patterns.md` 中的约束
     * 如果涉及安全认证：严格遵守 `security-authentication-patterns.md` 中的约束
     * 如果涉及配置管理：严格遵守 `configuration-patterns.md` 中的约束
   - **必须检查** `context-engineering/examples/typical-scenarios/` 中的强制性场景约束

2. **约束遵守性分析**
   - **识别强制性约束**：examples中标注为"必须遵守"的规范
   - **识别禁止性约束**：examples中标注为"禁止使用"的模式
   - **识别命名约束**：examples中规定的命名规范和代码结构要求
   - **制定遵守策略**：确保新实现严格符合examples中的约束要求

### 步骤4：项目架构分析
1. **项目类型识别（必须执行）**
   - **检查是否存在前端目录**：查看是否存在 `frontend/`、`web/`、`client/` 等前端目录
   - **识别前端技术栈**：检查 `package.json`、`vue.config.js`、`vite.config.js` 等配置文件
   - **确定项目架构类型**：
     * 纯后端项目：只有 Java/Spring Boot
     * 前后端分离：独立的前端项目
     * 前后端一体：在同一代码库中包含前后端
   - **分析前端框架**：Vue.js、React、Angular 等及其版本

2. **后端代码库分析**
   - 在代码库中搜索类似的Java功能实现
   - 识别现有的Spring Boot配置和Maven依赖
   - 注意项目特有的编码约定和架构模式

3. **前端代码库分析（如果存在前端）**
   - **组件架构分析**：识别现有Vue组件、页面结构、路由配置
   - **状态管理模式**：Vuex、Pinia 或其他状态管理方案
   - **UI框架识别**：TailwindCSS、Element Plus、Ant Design 等
   - **API集成方式**：axios配置、API调用模式、错误处理方式
   - **权限控制模式**：路由守卫、组件权限控制、菜单动态显示等

4. **分析测试模式**
   - 检查现有的JUnit测试结构（后端）
   - 检查前端测试配置（如果存在）：Jest、Vitest、Cypress 等
   - 了解测试覆盖率要求和Mock使用方式
   - 确定验证方法

### 步骤5：外部研究（如需要）
1. **后端技术文档研究**
   - 搜索Java/Spring Boot官方文档
   - 查找Maven依赖的最新兼容版本
   - 研究企业中间件最佳实践

2. **前端技术文档研究（如果存在前端）**
   - 搜索前端框架官方文档（Vue.js、React等）
   - 查找UI框架和组件库文档
   - 研究前后端通信最佳实践

3. **收集参考资料**
   - 包含具体的文档URL和章节
   - 记录重要的配置示例
   - 注意常见陷阱和解决方案

### 步骤6：用户澄清（如需要）
如果需要更多信息，询问用户：
- 特定的Java模式偏好
- 前端交互设计偏好（如果有前端）
- 企业中间件集成要求
- 性能和安全要求
- 现有系统集成约束

### 步骤7：PRP生成
使用 `PRPs/templates/prp_base.md` 作为模板，生成包含以下内容的完整PRP：

#### 必须包含的关键上下文
- **核心约束规范**：必须从context-engineering/examples/中识别的强制性约束和禁止性约束
- **约束遵守策略**：确保新实现严格符合examples中约束要求的具体方案
- **项目架构类型**：明确标识项目是纯后端、前后端分离还是前后端一体
- **后端技术栈**：Java/Spring Boot文档URL和具体章节、Maven配置、版本要求
- **前端技术栈**（如果存在）：Vue.js/React等框架版本、UI库、状态管理方案
- **企业中间件**：仅包含匹配的中间件文档内容
- **代码示例**：来自代码库的真实代码片段（前后端）
- **实现模式**：要遵循的现有架构模式

#### 实现蓝图
**后端实现蓝图：**
- Java类结构和包组织
- Spring Boot配置和依赖管理
- 企业中间件集成（包含降级机制）
- REST API设计和安全配置

**前端实现蓝图（如果存在前端）：**
- Vue组件结构和页面设计
- 路由配置和权限控制
- 状态管理和API集成
- UI组件复用和主题适配

**集成方案：**
- 前后端数据交互协议
- 权限验证和错误处理
- 按优先级排序的实现任务列表

#### 验证门控
**后端验证命令：**
```bash
# 编译检查
mvn clean compile

# 单元测试
mvn test

# 应用启动检查
mvn spring-boot:run --dry-run

# 集成测试（如适用）
mvn spring-boot:run &
sleep 10
curl -f http://localhost:8080/actuator/health
pkill -f spring-boot
```

**前端验证命令（如果存在前端）：**
```bash
# 进入前端目录
cd frontend/

# 依赖安装检查
npm install

# 编译检查
npm run build

# 开发服务器启动检查
npm run dev &
sleep 5
curl -f http://localhost:3000 || curl -f http://localhost:5173
pkill -f vite || pkill -f webpack
```

**全栈集成验证（如果是前后端一体项目）：**
```bash
# 后端启动
mvn spring-boot:run &
BACKEND_PID=$!
sleep 10

# 前端启动
cd frontend/
npm run dev &
FRONTEND_PID=$!
sleep 5

# API连通性测试
curl -f http://localhost:8080/api/health
curl -f http://localhost:3000

# 清理进程
kill $BACKEND_PID $FRONTEND_PID
```

### 步骤8：质量检查
确保生成的PRP包含：
- [ ] 所有必要的上下文信息
- [ ] **强制要求**：examples中核心约束的识别和遵守策略
- [ ] **强制要求**：明确的约束遵守检查机制
- [ ] **强制要求**：项目架构类型识别（纯后端/前后端分离/前后端一体）
- [ ] 可执行的验证门控（后端必须，前端如适用）
- [ ] 对现有模式的引用（前후端）
- [ ] 清晰的实现路径
- [ ] 完整的错误处理方案
- [ ] 企业中间件集成方案
- [ ] **前端特有检查**（如果存在前端）：
  - [ ] Vue/React组件设计方案
  - [ ] 路由和权限控制方案
  - [ ] API集成和状态管理方案
  - [ ] UI一致性和主题适配方案

### 步骤9：输出和评估
1. **保存PRP文件**
   - 保存到指定路径
   - 确认文件格式正确

2. **信心评估**
   - 在1-10范围内评估PRP质量
   - 说明评估理由
   - 如果评分低于8，说明需要改进的地方

## 重要提醒

### Examples核心约束遵守逻辑
在编写PRP之前，确保：
1. **必须读取**：context-engineering/examples/中的核心约束和规范
2. **必须识别**：强制性约束、禁止性约束、命名约束
3. **必须制定**：严格的约束遵守策略和检查机制
4. **必须确保**：新实现完全符合examples中的约束要求

### 企业中间件集成逻辑
在编写PRP之前，确保：
1. 已读取 `middleware-docs/middleware-list.md`
2. 正确识别匹配的企业中间件
3. 只读取相关的中间件文档
4. 规划了适当的降级机制

### 成功标准
目标是生成一个包含完整上下文的PRP，使得AI助手能够一次性成功实现功能，无需额外的研究或澄清。

## 示例使用

### 纯后端项目示例
```
请按照 workflows/generate-prp.md 为以下功能生成PRP：
- 功能：用户管理服务，支持注册、登录、信息更新
- 需要：缓存用户信息，异步通知状态变更
- 参考：examples/typical-scenarios/user-service.md
```

### 前后端一体项目示例
```
请按照 workflows/generate-prp.md 为 context-engineering/INITIAL.md 生成PRP：
- 功能：用户、角色与权限管理系统增强
- 项目类型：前后端一体（Spring Boot + Vue.js）
- 前端目录：frontend/
- 现有页面：用户管理页面需要增强，需新增角色和权限管理页面
```

### AI响应流程

#### 纯后端项目流程
1. 分析需求：用户管理服务
2. 项目架构识别：纯后端项目
3. 匹配中间件：缓存→JIMDB，异步通知→JMQ
4. **强制读取约束**：查看context-engineering/examples/中的核心约束和规范
5. **强制约束分析**：识别强制性约束、禁止性约束，制定遵守策略
6. 读取相关文档：jimdb.md, jmq.md
7. 搜索代码库中的用户服务模式
8. 生成完整PRP到 PRPs/user-management-service.md（必须包含约束遵守策略）

#### 前后端一体项目流程
1. 分析需求：RBAC权限管理系统
2. **项目架构识别**：检测到frontend/目录，确认为前后端一体项目
3. **前端技术栈分析**：识别Vue.js 3 + Vite + TailwindCSS + Pinia
4. **后端技术栈分析**：确认Spring Boot + MyBatis + MySQL
5. **强制读取约束**：查看context-engineering/examples/中的核心约束和规范
6. **强制约束分析**：识别前后端的强制性约束、禁止性约束，制定遵守策略
7. **前端代码库分析**：检查现有组件、路由、状态管理模式
8. **后端代码库分析**：检查现有Controller、Service、Mapper模式
9. 生成完整PRP到 PRPs/user-role-permission-management-system.md（包含前后端完整方案）
