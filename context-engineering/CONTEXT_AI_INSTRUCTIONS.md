# Java Context Engineering AI指令

## 适用范围
本指令适用于所有AI编程助手，包括但不限于：Augment、Cursor、GitHub Copilot、Claude Code等。

### 🔄 项目感知与上下文
- **在每次新对话开始时，必须阅读项目文档**，了解项目架构、目标、风格和约束条件。
- **在开始新任务前，检查现有代码模式**，保持一致性。
- **使用项目中已建立的命名约定、文件结构和架构模式**。
- **使用Maven**进行依赖管理和构建生命周期。

### ☕ Java基础规则 (JDK 1.8)
- **严格使用JDK 1.8语法特性**，不使用JDK 9+的新特性：
  - ✅ 可以使用：Lambda表达式、Stream API、Optional、新的日期时间API (java.time)
  - ❌ 禁止使用：模块系统、var关键字、文本块、记录类、密封类等JDK 9+特性
- **Maven配置必须指定JDK 1.8**：
  ```xml
  <properties>
      <maven.compiler.source>1.8</maven.compiler.source>
      <maven.compiler.target>1.8</maven.compiler.target>
      <java.version>1.8</java.version>
  </properties>
  ```
- **使用JDK 1.8兼容的依赖版本**：
  - Spring Boot: 2.x系列（不使用3.x，因为需要JDK 17+）
  - Spring Framework: 5.x系列
  - 确保所有第三方库支持JDK 1.8

### 🧱 代码结构与模块化
- **单个文件代码行数不得超过500行**。如果接近此限制，通过拆分模块或辅助类进行重构。
- **将代码组织成清晰分离的层次**，按功能或职责分组：
  - `controller/` - REST端点和Web层
  - `service/` - 业务逻辑和服务层
  - `repository/` - 数据访问层
  - `config/` - 配置类
  - `model/` - 数据模型和DTO
- **使用清晰、一致的导入**，遵循Java包命名约定。
- **使用Spring Boot的配置属性**处理环境变量和外部配置。

### 🧪 测试与可靠性
- **为新功能始终创建JUnit测试**（服务、控制器、仓储等）。
- **更新任何逻辑后**，检查现有单元测试是否需要更新，如需要则立即更新。
- **测试应位于`src/test/java/`**，镜像主源码结构。
  - 至少包含：
    - 1个预期用例测试（正常路径）
    - 1个边界情况测试
    - 1个失败情况测试
- **使用Mockito模拟依赖**进行单元测试。
- **使用@SpringBootTest进行集成测试**，当需要测试完整应用上下文时。
- **JDK 1.8测试注意事项**：
  - 使用JUnit 4.x或JUnit 5.x（确保与JDK 1.8兼容）
  - Mockito版本选择2.x或3.x系列
  - 避免使用需要JDK 9+的测试特性

### ✅ 任务完成标准
- **在认为任务完成前，使用`mvn clean compile`验证代码编译**。
- **运行`mvn test`确保没有回归问题**。
- **使用`mvn spring-boot:run`检查应用启动**进行集成验证。
- **确保JDK 1.8兼容性**：
  - 检查编译时没有使用JDK 9+特性的警告
  - 验证所有依赖都支持JDK 1.8

### 📎 代码风格与约定
- **使用Java作为主要语言**，配合**Spring Boot框架**。
- **遵循Java命名约定**：类名使用PascalCase，方法和变量使用camelCase。
- **适当使用Spring注解**（@Service、@Repository、@Controller、@Configuration）。
- **适当使用Lombok**减少样板代码，但注意JDK 1.8兼容性。
- **为公共方法编写Javadoc**，使用标准格式：
  ```java
  /**
   * 方法功能的简要描述。
   *
   * @param param1 参数描述
   * @return 返回值描述
   * @throws ExceptionType 抛出此异常的条件描述
   */
  public ReturnType methodName(ParamType param1) {
      // 实现代码
  }
  ```
- **JDK 1.8代码风格要求**：
  - 优先使用Lambda表达式和Stream API提高代码简洁性
  - 使用Optional避免空指针异常
  - 使用java.time包处理日期时间（避免使用Date和Calendar）
  - 接口中可以使用default方法
  - 合理使用方法引用（::）简化Lambda表达式

### 🏢 企业中间件集成
- **需要中间件功能时，始终先检查middleware-docs/middleware-list.md**。
- **优先使用企业中间件**而非公共替代方案：
  - 使用JSF进行RPC调用，而非标准HTTP客户端
  - 使用JMQ进行消息传递，而非RabbitMQ/Kafka
  - 使用JIMDB进行缓存，而非标准Redis
- **为企业中间件不可用情况实现降级机制**。
- **使用`@ConditionalOnProperty`进行条件配置**实现中间件集成。
- **JDK 1.8中间件集成注意事项**：
  - 确保企业中间件客户端库支持JDK 1.8
  - 使用CompletableFuture处理异步操作
  - 避免使用需要JDK 9+的响应式编程特性

### 📚 文档与可解释性
- **当添加新功能、依赖变更或设置步骤修改时，更新README.md**。
- **为非显而易见的代码添加注释**，确保中级Java开发者能够理解。
- **添加内联注释**解释业务逻辑和集成模式。
- **使用中文注释**提高团队理解效率。

### 🧠 AI行为规则
- **永远不要假设缺失的上下文。如有不确定，请提问。**
- **永远不要虚构库或依赖**——只使用已知、经过验证的Java包和企业中间件。
- **实现中间件功能前，始终检查middleware-docs/**。
- **在代码或测试中引用文件路径和类名前，始终确认其存在**。
- **除非明确指示，否则永远不要删除或覆盖现有代码**。
- **严格按照中间件文档中指定的Maven坐标使用**。
- **JDK 1.8特定规则**：
  - 生成的代码必须能在JDK 1.8环境中编译和运行
  - 推荐的依赖版本必须支持JDK 1.8
  - 不使用任何JDK 9+的语法特性或API

### 📦 JDK 1.8推荐依赖版本
```xml
<!-- Spring Boot 2.x系列（最后支持JDK 1.8的版本） -->
<parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.18</version>
    <relativePath/>
</parent>

<!-- 常用依赖推荐版本 -->
<dependencies>
    <!-- JUnit 5 (支持JDK 1.8) -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.8.2</version>
        <scope>test</scope>
    </dependency>

    <!-- Mockito (JDK 1.8兼容) -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>3.12.4</version>
        <scope>test</scope>
    </dependency>

    <!-- Lombok (JDK 1.8兼容) -->
    <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.24</version>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

### 💡 JDK 1.8最佳实践示例
```java
// ✅ 推荐：使用Lambda和Stream API
List<String> result = list.stream()
    .filter(item -> item.length() > 3)
    .map(String::toUpperCase)
    .collect(Collectors.toList());

// ✅ 推荐：使用Optional避免空指针
Optional<User> user = userRepository.findById(id);
return user.map(User::getName).orElse("Unknown");

// ✅ 推荐：使用java.time API
LocalDateTime now = LocalDateTime.now();
LocalDate date = LocalDate.of(2023, 12, 25);

// ✅ 推荐：接口default方法
public interface UserService {
    List<User> findAll();

    default List<User> findActive() {
        return findAll().stream()
            .filter(User::isActive)
            .collect(Collectors.toList());
    }
}

// ❌ 避免：JDK 9+特性
// var name = "test";  // var关键字是JDK 10+
// List.of("a", "b");  // List.of是JDK 9+
// String.isBlank();   // isBlank是JDK 11+
```