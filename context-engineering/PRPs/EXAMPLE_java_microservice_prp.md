name: "Java微服务系统：订单处理服务与库存管理子服务"
description: |

## 目的
构建一个基于Spring Boot的微服务系统，其中主要的订单处理服务使用JSF RPC调用库存管理子服务，并集成JMQ消息队列进行异步通知。这展示了企业中间件集成模式和微服务间通信。

## 核心原则
1. **上下文为王**：包含所有必要的文档、示例和注意事项
2. **验证循环**：提供AI可以运行和修复的可执行测试/检查
3. **信息密集**：使用代码库中的关键词和模式
4. **渐进成功**：从简单开始，验证，然后增强
5. **全局规则**：确保遵循CLAUDE.md中的所有规则
6. **企业中间件**：优先使用企业内部中间件方案

---

## 目标
创建一个生产就绪的订单处理微服务系统，用户可以通过REST API创建订单，订单服务会调用库存服务检查库存，并通过消息队列发送订单状态通知。系统应支持多种企业中间件并处理各种异常情况。

## 为什么
- **业务价值**：自动化订单处理和库存管理工作流程
- **集成**：展示企业级Spring Boot微服务架构模式
- **解决的问题**：减少手动订单处理，提高库存管理效率
- **与现有功能的集成**：与现有用户系统和支付系统集成
- **受益人群**：客户、运营人员、库存管理员

## 功能描述
一个基于REST API的微服务应用，其中：
- 客户通过REST API提交订单请求
- 订单服务验证订单信息并调用库存服务
- 库存服务检查商品库存并预留库存
- 订单服务创建订单并发送异步通知
- 支持订单状态查询和库存实时更新

### 成功标准
- [ ] 订单服务成功通过JSF调用库存服务
- [ ] 库存服务正确处理库存检查和预留
- [ ] JMQ消息队列正常发送订单状态通知
- [ ] REST API提供完整的订单CRUD操作
- [ ] 所有测试通过且代码符合质量标准
- [ ] 支持多级缓存和降级处理
- [ ] 集成企业监控和日志系统

## 所需上下文

### 文档与参考资料
```yaml
# 必读 - 在上下文窗口中包含这些内容
- file: middleware-docs/middleware-list.md
  why: 了解企业中间件清单，进行关键词匹配

# AI两步查询逻辑：
# 第一步：总是先读取 middleware-list.md 了解可用中间件
# 第二步：根据需求关键词匹配，只读取匹配的中间件文档文件
# 禁止：读取所有中间件文档（浪费上下文）

# 示例：需求匹配到RPC调用
- file: middleware-docs/jsf.md
  why: 获取JSF RPC框架的完整使用指南（依赖、配置、示例）

# 示例：需求匹配到消息队列
- file: middleware-docs/jmq.md
  why: 获取JMQ消息队列的完整使用指南（生产者、消费者、配置）

# 示例：需求匹配到缓存
- file: middleware-docs/jimdb.md
  why: 获取JIMDB分布式缓存的完整使用指南（依赖、配置、示例）

- file: examples/typical-scenarios/order-enhancement.md
  why: 订单系统增强的非侵入式集成模式

- file: examples/middleware-integration/cache-fallback.md
  why: 多级缓存降级处理模式

- file: CLAUDE.md
  why: Java开发规范和JDK 1.8要求
```

### 当前代码库结构
```bash
.
├── examples/
│   ├── typical-scenarios/
│   │   ├── order-enhancement.md
│   │   └── cache-integration.md
│   ├── middleware-integration/
│   │   └── cache-fallback.md
│   └── code-patterns/
│       ├── service-layer-patterns.md
│       └── testing-patterns.md
├── middleware-docs/
│   ├── middleware-list.md
│   ├── jsf.md
│   ├── jmq.md
│   └── jimdb.md
├── PRPs/
│   └── templates/
│       └── prp_base.md
├── CLAUDE.md
└── README.md
```

### 期望的代码库结构（需要添加的文件）
```bash
order-microservice/
├── order-service/
│   ├── src/main/java/com/company/order/
│   │   ├── OrderServiceApplication.java     # Spring Boot主类
│   │   ├── controller/
│   │   │   ├── OrderController.java         # REST控制器
│   │   │   └── HealthController.java        # 健康检查
│   │   ├── service/
│   │   │   ├── OrderService.java           # 订单业务逻辑
│   │   │   ├── InventoryServiceClient.java  # 库存服务客户端
│   │   │   └── NotificationService.java     # 通知服务
│   │   ├── model/
│   │   │   ├── Order.java                  # 订单实体
│   │   │   ├── OrderItem.java              # 订单项实体
│   │   │   └── dto/                        # DTO类
│   │   ├── repository/
│   │   │   └── OrderRepository.java        # 订单数据访问
│   │   ├── config/
│   │   │   ├── JsfConfig.java              # JSF配置
│   │   │   ├── JmqConfig.java              # JMQ配置
│   │   │   └── CacheConfig.java            # 缓存配置
│   │   └── exception/
│   │       └── OrderException.java         # 自定义异常
│   ├── src/test/java/                      # 测试代码
│   └── pom.xml                             # Maven配置
├── inventory-service/
│   ├── src/main/java/com/company/inventory/
│   │   ├── InventoryServiceApplication.java # Spring Boot主类
│   │   ├── service/
│   │   │   └── InventoryService.java       # 库存业务逻辑
│   │   ├── model/
│   │   │   └── Inventory.java              # 库存实体
│   │   └── config/
│   │       └── JsfProviderConfig.java      # JSF服务提供者配置
│   ├── src/test/java/                      # 测试代码
│   └── pom.xml                             # Maven配置
├── common/
│   ├── src/main/java/com/company/common/
│   │   ├── dto/                            # 共享DTO
│   │   ├── exception/                      # 共享异常
│   │   └── util/                           # 工具类
│   └── pom.xml                             # 公共模块配置
├── docker-compose.yml                      # 本地开发环境
├── README.md                               # 项目文档
└── pom.xml                                 # 父级Maven配置
```

### 已知陷阱和库特性
```java
// 关键：JDK 1.8兼容性 - 不使用JDK 9+特性
// 关键：JSF RPC调用需要正确的服务注册和发现配置
// 关键：JMQ消息发送需要处理序列化和反序列化
// 关键：Spring Boot 2.x版本兼容性确保
// 关键：数据库事务和分布式事务处理
// 关键：缓存一致性和多级降级策略
// 关键：异常处理和服务降级机制
// 关键：配置外部化和环境变量管理
```

## 实现蓝图

### 数据模型和结构

```java
// Order.java - 订单实体
@Entity
@Table(name = "orders")
public class Order {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String orderNo;
    private Long customerId;
    private BigDecimal totalAmount;
    private OrderStatus status;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL)
    private List<OrderItem> items;

    // getters/setters...
}

// OrderItem.java - 订单项实体
@Entity
@Table(name = "order_items")
public class OrderItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "order_id")
    private Order order;

    private Long productId;
    private String productName;
    private Integer quantity;
    private BigDecimal unitPrice;
    private BigDecimal totalPrice;

    // getters/setters...
}

// CreateOrderRequest.java - 创建订单请求DTO
public class CreateOrderRequest {
    @NotNull
    private Long customerId;

    @NotEmpty
    private List<OrderItemRequest> items;

    private String remark;

    // getters/setters...
}

// InventoryCheckRequest.java - 库存检查请求DTO
public class InventoryCheckRequest {
    @NotNull
    private Long productId;

    @Min(1)
    private Integer quantity;

    // getters/setters...
}

// OrderStatusNotification.java - 订单状态通知消息
public class OrderStatusNotification implements Serializable {
    private String orderNo;
    private OrderStatus status;
    private Long customerId;
    private LocalDateTime timestamp;

    // getters/setters...
}
```

### 待完成任务列表

```yaml
任务1：设置项目结构和Maven配置
创建 pom.xml (父级):
  - 模式：使用Spring Boot 2.7.18 (JDK 1.8兼容)
  - 配置多模块Maven项目
  - 添加企业中间件依赖管理

创建子模块pom.xml:
  - order-service: Web服务模块
  - inventory-service: 库存服务模块
  - common: 公共模块

任务2：实现公共模块
创建 common/src/main/java/com/company/common/:
  - 模式：遵循examples中的DTO模式
  - 创建共享DTO类和异常类
  - 实现序列化和工具类
  - 添加验证注解

任务3：实现库存服务
创建 inventory-service/:
  - 模式：遵循CLAUDE.md中的服务层模式
  - 实现InventoryService业务逻辑
  - 配置JSF服务提供者
  - 添加库存检查和预留功能
  - 实现缓存机制

任务4：实现订单服务核心功能
创建 order-service/service/:
  - 模式：参考examples/typical-scenarios/order-enhancement.md
  - 实现OrderService主要业务逻辑
  - 添加订单创建、查询、更新功能
  - 实现事务管理

任务5：集成JSF RPC调用
创建 order-service/service/InventoryServiceClient.java:
  - 模式：遵循middleware-docs/jsf.md配置
  - 实现JSF客户端调用库存服务
  - 添加超时和重试机制
  - 实现降级处理

任务6：集成JMQ消息队列
创建 order-service/service/NotificationService.java:
  - 模式：遵循middleware-docs/jmq.md配置
  - 实现JMQ消息生产者
  - 发送订单状态变更通知
  - 处理消息发送失败情况

任务7：实现REST API控制器
创建 order-service/controller/:
  - 模式：遵循CLAUDE.md中的控制器模式
  - 实现订单CRUD操作的REST端点
  - 添加参数验证和异常处理
  - 实现分页查询

任务8：集成多级缓存
创建缓存配置和服务:
  - 模式：参考examples/middleware-integration/cache-fallback.md
  - 配置JIMDB分布式缓存
  - 实现本地缓存降级
  - 添加缓存更新策略

任务9：添加配置管理
创建配置类:
  - 模式：使用Spring Boot配置属性
  - 外部化所有配置参数
  - 支持不同环境配置
  - 添加配置验证

任务10：实现全面测试
创建测试类:
  - 模式：遵循CLAUDE.md中的测试要求
  - 单元测试覆盖所有服务类
  - 集成测试验证RPC调用
  - Mock外部依赖进行测试

任务11：添加监控和日志
集成企业监控:
  - 添加健康检查端点
  - 集成UMP监控埋点
  - 配置结构化日志
  - 实现性能指标收集

任务12：创建部署配置
创建部署相关文件:
  - Docker镜像构建配置
  - docker-compose本地开发环境
  - 应用配置文件模板
  - 部署文档和运维指南
```

### 每个任务的伪代码

```java
// 任务4：订单服务核心功能
@Service
@Transactional
public class OrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private InventoryServiceClient inventoryServiceClient;

    @Autowired
    private NotificationService notificationService;

    public Order createOrder(CreateOrderRequest request) {
        // 1. 验证订单请求
        validateOrderRequest(request);

        // 2. 检查库存（JSF RPC调用）
        List<InventoryCheckResult> inventoryResults = checkInventory(request.getItems());

        // 3. 创建订单
        Order order = buildOrder(request, inventoryResults);
        order = orderRepository.save(order);

        // 4. 预留库存
        reserveInventory(order);

        // 5. 发送异步通知（JMQ消息）
        sendOrderNotification(order, OrderStatus.CREATED);

        return order;
    }

    private List<InventoryCheckResult> checkInventory(List<OrderItemRequest> items) {
        // 模式：批量RPC调用优化
        return items.stream()
            .map(item -> inventoryServiceClient.checkInventory(
                new InventoryCheckRequest(item.getProductId(), item.getQuantity())))
            .collect(Collectors.toList());
    }
}

// 任务5：JSF RPC客户端
@Component
public class InventoryServiceClient {

    @Reference(version = "1.0.0", timeout = 3000)
    private InventoryService inventoryService;

    @Autowired(required = false)
    private CacheManager cacheManager;

    public InventoryCheckResult checkInventory(InventoryCheckRequest request) {
        // 1. 先检查缓存
        String cacheKey = "inventory:" + request.getProductId();
        InventoryCheckResult cached = getCachedResult(cacheKey);
        if (cached != null && cached.isValid()) {
            return cached;
        }

        try {
            // 2. JSF RPC调用
            InventoryCheckResult result = inventoryService.checkInventory(request);

            // 3. 更新缓存
            setCachedResult(cacheKey, result);

            return result;
        } catch (Exception e) {
            log.error("库存服务调用失败: {}", e.getMessage());
            // 4. 降级处理
            return handleInventoryServiceFailure(request);
        }
    }

    private InventoryCheckResult handleInventoryServiceFailure(InventoryCheckRequest request) {
        // 模式：服务降级策略
        // 可以返回默认库存状态或抛出业务异常
        throw new InventoryServiceException("库存服务暂时不可用，请稍后重试");
    }
}

// 任务6：JMQ消息队列集成
@Component
public class NotificationService {

    @Autowired
    private JmqProducer jmqProducer;

    @Value("${jmq.topic.order-status:order-status-topic}")
    private String orderStatusTopic;

    public void sendOrderStatusNotification(Order order, OrderStatus status) {
        try {
            OrderStatusNotification notification = new OrderStatusNotification();
            notification.setOrderNo(order.getOrderNo());
            notification.setStatus(status);
            notification.setCustomerId(order.getCustomerId());
            notification.setTimestamp(LocalDateTime.now());

            // JMQ异步发送
            jmqProducer.send(orderStatusTopic, notification);

            log.info("订单状态通知已发送: orderNo={}, status={}",
                order.getOrderNo(), status);

        } catch (Exception e) {
            // 消息发送失败不影响主流程
            log.error("订单状态通知发送失败: orderNo={}, error={}",
                order.getOrderNo(), e.getMessage());
        }
    }
}
```

### 集成点
```yaml
环境配置:
  - 添加到: application.yml
  - 配置: |
      # 服务基础配置
      server:
        port: 8080
        servlet:
          context-path: /order-service

      spring:
        application:
          name: order-service
        datasource:
          url: ${DB_URL:************************************}
          username: ${DB_USERNAME:root}
          password: ${DB_PASSWORD:password}
        jpa:
          hibernate:
            ddl-auto: validate
          show-sql: false

      # JSF配置
      jsf:
        registry:
          address: ${JSF_REGISTRY:127.0.0.1:2181}
        consumer:
          timeout: 3000
          retries: 2

      # JMQ配置
      jmq:
        nameserver: ${JMQ_NAMESERVER:127.0.0.1:9876}
        producer:
          group: order-service-producer
          send-timeout: 3000
        topic:
          order-status: order-status-topic

      # 缓存配置
      cache:
        jimdb:
          enabled: ${JIMDB_ENABLED:true}
          cluster: ${JIMDB_CLUSTER:default}
        local:
          enabled: true
          max-size: 10000
          expire-after-write: 1800

Maven依赖:
  - 更新pom.xml添加：
    - spring-boot-starter-web
    - spring-boot-starter-data-jpa
    - mysql-connector-java
    - jsf-client (企业版本)
    - jmq-client (企业版本)
    - jimdb-client (企业版本)
    - validation-api
    - lombok

数据库:
  - 创建订单数据库表
  - 添加索引优化查询
  - 配置数据库连接池
```

## 验证循环

### 第1级：语法和风格
```bash
# 首先运行这些 - 在继续之前修复任何错误
mvn clean compile                   # 编译检查
mvn checkstyle:check               # 代码风格检查
mvn spotbugs:check                 # 静态代码分析

# 预期：无编译错误，无风格违规。如有错误，阅读并修复。
```

### 第2级：单元测试
```java
// OrderServiceTest.java
@SpringBootTest
@Transactional
class OrderServiceTest {

    @Autowired
    private OrderService orderService;

    @MockBean
    private InventoryServiceClient inventoryServiceClient;

    @MockBean
    private NotificationService notificationService;

    @Test
    void testCreateOrderSuccess() {
        // 准备测试数据
        CreateOrderRequest request = new CreateOrderRequest();
        request.setCustomerId(1L);
        request.setItems(Arrays.asList(
            new OrderItemRequest(1L, "商品A", 2, new BigDecimal("100.00"))
        ));

        // Mock库存检查
        when(inventoryServiceClient.checkInventory(any()))
            .thenReturn(new InventoryCheckResult(true, 10));

        // 执行测试
        Order order = orderService.createOrder(request);

        // 验证结果
        assertThat(order.getId()).isNotNull();
        assertThat(order.getStatus()).isEqualTo(OrderStatus.CREATED);
        assertThat(order.getItems()).hasSize(1);

        // 验证交互
        verify(inventoryServiceClient).checkInventory(any());
        verify(notificationService).sendOrderStatusNotification(eq(order), eq(OrderStatus.CREATED));
    }

    @Test
    void testCreateOrderInsufficientInventory() {
        // 准备测试数据
        CreateOrderRequest request = new CreateOrderRequest();
        request.setCustomerId(1L);
        request.setItems(Arrays.asList(
            new OrderItemRequest(1L, "商品A", 100, new BigDecimal("100.00"))
        ));

        // Mock库存不足
        when(inventoryServiceClient.checkInventory(any()))
            .thenReturn(new InventoryCheckResult(false, 5));

        // 执行测试并验证异常
        assertThatThrownBy(() -> orderService.createOrder(request))
            .isInstanceOf(InsufficientInventoryException.class)
            .hasMessageContaining("库存不足");
    }
}

// InventoryServiceClientTest.java
@SpringBootTest
class InventoryServiceClientTest {

    @Autowired
    private InventoryServiceClient inventoryServiceClient;

    @MockBean
    private InventoryService inventoryService;

    @Test
    void testCheckInventoryWithCache() {
        // 测试缓存机制
        InventoryCheckRequest request = new InventoryCheckRequest(1L, 5);
        InventoryCheckResult expectedResult = new InventoryCheckResult(true, 10);

        when(inventoryService.checkInventory(request)).thenReturn(expectedResult);

        // 第一次调用
        InventoryCheckResult result1 = inventoryServiceClient.checkInventory(request);
        assertThat(result1.isAvailable()).isTrue();

        // 第二次调用应该使用缓存
        InventoryCheckResult result2 = inventoryServiceClient.checkInventory(request);
        assertThat(result2.isAvailable()).isTrue();

        // 验证只调用了一次远程服务
        verify(inventoryService, times(1)).checkInventory(request);
    }

    @Test
    void testInventoryServiceFailureHandling() {
        // 测试服务降级
        InventoryCheckRequest request = new InventoryCheckRequest(1L, 5);

        when(inventoryService.checkInventory(request))
            .thenThrow(new RuntimeException("网络异常"));

        // 验证降级处理
        assertThatThrownBy(() -> inventoryServiceClient.checkInventory(request))
            .isInstanceOf(InventoryServiceException.class)
            .hasMessageContaining("库存服务暂时不可用");
    }
}
```

```bash
# 迭代运行测试直到通过：
mvn test                           # 运行所有测试
mvn test -Dtest=OrderServiceTest   # 运行特定测试类

# 如果失败：调试特定测试，修复代码，重新运行
```

### 第3级：集成测试
```bash
# 启动本地开发环境
docker-compose up -d               # 启动依赖服务（数据库、注册中心等）

# 启动库存服务
cd inventory-service
mvn spring-boot:run

# 启动订单服务
cd order-service
mvn spring-boot:run

# 测试REST API
curl -X POST http://localhost:8080/order-service/api/orders \
  -H "Content-Type: application/json" \
  -d '{
    "customerId": 1,
    "items": [
      {
        "productId": 1,
        "productName": "测试商品",
        "quantity": 2,
        "unitPrice": 100.00
      }
    ]
  }'

# 预期响应：
# {
#   "id": 1,
#   "orderNo": "ORD20231201001",
#   "customerId": 1,
#   "totalAmount": 200.00,
#   "status": "CREATED",
#   "items": [...]
# }

# 验证JSF调用日志
# 验证JMQ消息发送日志
# 检查数据库订单记录
```

## 最终验证检查清单
- [ ] 所有测试通过：`mvn test`
- [ ] 无编译错误：`mvn clean compile`
- [ ] 代码风格检查通过：`mvn checkstyle:check`
- [ ] 静态代码分析通过：`mvn spotbugs:check`
- [ ] JSF RPC调用正常工作（服务注册发现正常）
- [ ] JMQ消息队列正常发送和接收
- [ ] JIMDB缓存集成工作正常
- [ ] REST API所有端点响应正确
- [ ] 数据库事务正确处理
- [ ] 异常情况优雅处理（服务降级）
- [ ] 配置外部化完成（支持环境变量）
- [ ] 日志记录完整且结构化
- [ ] 健康检查端点正常
- [ ] Docker容器构建成功
- [ ] 本地开发环境可正常启动
- [ ] API文档完整（Swagger/OpenAPI）
- [ ] README包含清晰的设置和运行说明

---

## 要避免的反模式
- ❌ 不要在代码中硬编码配置 - 使用外部配置
- ❌ 不要忽略JDK 1.8兼容性 - 避免使用新版本特性
- ❌ 不要在事务方法中进行长时间的外部调用
- ❌ 不要忽略JSF调用的超时和重试配置
- ❌ 不要让JMQ消息发送失败影响主业务流程
- ❌ 不要忽略缓存的一致性问题
- ❌ 不要在生产环境中使用默认的数据库配置
- ❌ 不要忽略服务间的循环依赖问题
- ❌ 不要在单个事务中处理过多的业务逻辑
- ❌ 不要忽略分布式事务的复杂性
- ❌ 不要在没有监控的情况下部署到生产环境

## 性能和扩展性考虑
```java
// 批量处理优化
@Service
public class OrderBatchService {

    @Async("orderTaskExecutor")
    public CompletableFuture<Void> processBatchOrders(List<CreateOrderRequest> requests) {
        // 批量处理订单，提高吞吐量
        return CompletableFuture.runAsync(() -> {
            requests.parallelStream()
                .forEach(this::processOrderAsync);
        });
    }
}

// 缓存预热策略
@Component
public class CacheWarmupService {

    @EventListener(ApplicationReadyEvent.class)
    public void warmupCache() {
        // 应用启动时预热热点数据
        preloadHotProducts();
        preloadCustomerData();
    }
}

// 数据库分页优化
@Repository
public class OrderRepository extends JpaRepository<Order, Long> {

    @Query("SELECT o FROM Order o WHERE o.customerId = :customerId ORDER BY o.createTime DESC")
    Page<Order> findByCustomerIdOrderByCreateTimeDesc(
        @Param("customerId") Long customerId,
        Pageable pageable);
}
```

## 监控和运维
```yaml
# 监控指标配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# UMP监控埋点示例
logging:
  level:
    com.company.order: INFO
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 信心分数：9/10

高信心度原因：
- 有完整的企业中间件文档可以参考
- Spring Boot微服务架构模式成熟
- 有详细的示例代码可以遵循
- 全面的测试策略和验证门控
- 考虑了性能、监控和运维需求

轻微不确定性：
- 企业中间件的具体版本兼容性需要验证
- 分布式事务的复杂场景处理需要进一步测试
