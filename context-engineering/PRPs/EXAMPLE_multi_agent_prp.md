name: "多智能体系统：研究智能体与邮件草稿子智能体"
description: |

## 目的
构建一个Pydantic AI多智能体系统，其中主要的研究智能体使用Brave搜索API，并将邮件草稿智能体（使用Gmail API）作为工具。这展示了智能体作为工具的模式以及外部API集成。

## 核心原则
1. **上下文为王**：包含所有必要的文档、示例和注意事项
2. **验证循环**：提供AI可以运行和修复的可执行测试/检查
3. **信息密集**：使用代码库中的关键词和模式
4. **渐进成功**：从简单开始，验证，然后增强

---

## 目标
创建一个生产就绪的多智能体系统，用户可以通过CLI研究主题，研究智能体可以将邮件草稿任务委托给邮件草稿智能体。系统应支持多个LLM提供商并安全处理API认证。

## 为什么
- **业务价值**：自动化研究和邮件草稿工作流程
- **集成**：展示高级Pydantic AI多智能体模式
- **解决的问题**：减少基于研究的邮件通信的手动工作

## 功能描述
一个基于CLI的应用程序，其中：
- 用户输入研究查询
- 研究智能体使用Brave API搜索
- 研究智能体可以调用邮件草稿智能体创建Gmail草稿
- 结果实时流式返回给用户

### 成功标准
- [ ] 研究智能体通过Brave API成功搜索
- [ ] 邮件智能体使用正确认证创建Gmail草稿
- [ ] 研究智能体可以将邮件智能体作为工具调用
- [ ] CLI提供带工具可见性的流式响应
- [ ] 所有测试通过且代码符合质量标准

## 所需上下文

### 文档与参考资料
```yaml
# 必读 - 在上下文窗口中包含这些内容
- url: https://ai.pydantic.dev/agents/
  why: 核心智能体创建模式

- url: https://ai.pydantic.dev/multi-agent-applications/
  why: 多智能体系统模式，特别是智能体作为工具

- url: https://developers.google.com/gmail/api/guides/sending
  why: Gmail API认证和草稿创建

- url: https://api-dashboard.search.brave.com/app/documentation
  why: Brave搜索API REST端点

- file: examples/agent/agent.py
  why: 智能体创建、工具注册、依赖的模式

- file: examples/agent/providers.py
  why: 多提供商LLM配置模式

- file: examples/cli.py
  why: 带流式响应和工具可见性的CLI结构

- url: https://github.com/googleworkspace/python-samples/blob/main/gmail/snippet/send%20mail/create_draft.py
  why: 官方Gmail草稿创建示例
```

### 当前代码库结构
```bash
.
├── examples/
│   ├── agent/
│   │   ├── agent.py
│   │   ├── providers.py
│   │   └── ...
│   └── cli.py
├── PRPs/
│   └── templates/
│       └── prp_base.md
├── INITIAL.md
├── CLAUDE.md
└── requirements.txt
```

### 期望的代码库结构（需要添加的文件）
```bash
.
├── agents/
│   ├── __init__.py               # 包初始化
│   ├── research_agent.py         # 带Brave搜索的主智能体
│   ├── email_agent.py           # 带Gmail功能的子智能体
│   ├── providers.py             # LLM提供商配置
│   └── models.py                # 数据验证的Pydantic模型
├── tools/
│   ├── __init__.py              # 包初始化
│   ├── brave_search.py          # Brave搜索API集成
│   └── gmail_tool.py            # Gmail API集成
├── config/
│   ├── __init__.py              # 包初始化
│   └── settings.py              # 环境和配置管理
├── tests/
│   ├── __init__.py              # 包初始化
│   ├── test_research_agent.py   # 研究智能体测试
│   ├── test_email_agent.py      # 邮件智能体测试
│   ├── test_brave_search.py     # Brave搜索工具测试
│   ├── test_gmail_tool.py       # Gmail工具测试
│   └── test_cli.py              # CLI测试
├── cli.py                       # CLI接口
├── .env.example                 # 环境变量模板
├── requirements.txt             # 更新的依赖
├── README.md                    # 综合文档
└── credentials/.gitkeep         # Gmail凭证目录
```

### 已知陷阱和库特性
```python
# 关键：Pydantic AI需要全程异步 - 在异步上下文中不使用同步函数
# 关键：Gmail API首次运行需要OAuth2流程 - 需要credentials.json
# 关键：Brave API有速率限制 - 免费层每月2000次请求
# 关键：智能体作为工具模式需要传递ctx.usage进行令牌跟踪
# 关键：Gmail草稿需要正确MIME格式的base64编码
# 关键：始终使用绝对导入以获得更清晰的代码
# 关键：在.env中存储敏感凭证，永远不要提交它们
```

## 实现蓝图

### 数据模型和结构

```python
# models.py - 核心数据结构
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class ResearchQuery(BaseModel):
    query: str = Field(..., description="要调查的研究主题")
    max_results: int = Field(10, ge=1, le=50)
    include_summary: bool = Field(True)

class BraveSearchResult(BaseModel):
    title: str
    url: str
    description: str
    score: float = Field(0.0, ge=0.0, le=1.0)

class EmailDraft(BaseModel):
    to: List[str] = Field(..., min_items=1)
    subject: str = Field(..., min_length=1)
    body: str = Field(..., min_length=1)
    cc: Optional[List[str]] = None
    bcc: Optional[List[str]] = None

class ResearchEmailRequest(BaseModel):
    research_query: str
    email_context: str = Field(..., description="邮件生成的上下文")
    recipient_email: str
```

### 待完成任务列表

```yaml
任务1：设置配置和环境
创建 config/settings.py:
  - 模式：像示例使用os.getenv一样使用pydantic-settings
  - 加载带默认值的环境变量
  - 验证所需API密钥存在

创建 .env.example:
  - 包含所有必需的环境变量及其描述
  - 遵循examples/README.md的模式

任务2：实现Brave搜索工具
创建 tools/brave_search.py:
  - 模式：像examples/agent/tools.py一样的异步函数
  - 使用httpx的简单REST客户端（已在requirements中）
  - 优雅处理速率限制和错误
  - 返回结构化的BraveSearchResult模型

任务3：实现Gmail工具
创建 tools/gmail_tool.py:
  - 模式：遵循Gmail快速入门的OAuth2流程
  - 在credentials/目录中存储token.json
  - 使用正确的MIME编码创建草稿
  - 自动处理认证刷新

任务4：创建邮件草稿智能体
创建 agents/email_agent.py:
  - 模式：遵循examples/agent/agent.py结构
  - 使用带deps_type模式的Agent
  - 将gmail_tool注册为@agent.tool
  - 返回EmailDraft模型

任务5：创建研究智能体
创建 agents/research_agent.py:
  - 模式：来自Pydantic AI文档的多智能体模式
  - 将brave_search注册为工具
  - 将email_agent.run()注册为工具
  - 使用RunContext进行依赖注入

任务6：实现CLI接口
创建 cli.py:
  - 模式：遵循examples/cli.py流式模式
  - 带工具可见性的彩色输出
  - 使用asyncio.run()正确处理异步
  - 对话上下文的会话管理

任务7：添加综合测试
创建 tests/:
  - 模式：镜像示例测试结构
  - 模拟外部API调用
  - 测试正常路径、边界情况、错误
  - 确保80%+覆盖率

任务8：创建文档
创建 README.md:
  - 模式：遵循examples/README.md结构
  - 包含设置、安装、使用
  - API密钥配置步骤
  - 架构图
```

### 每个任务的伪代码

```python
# 任务2：Brave搜索工具
async def search_brave(query: str, api_key: str, count: int = 10) -> List[BraveSearchResult]:
    # 模式：像示例使用aiohttp一样使用httpx
    async with httpx.AsyncClient() as client:
        headers = {"X-Subscription-Token": api_key}
        params = {"q": query, "count": count}

        # 陷阱：如果API密钥无效，Brave API返回401
        response = await client.get(
            "https://api.search.brave.com/res/v1/web/search",
            headers=headers,
            params=params,
            timeout=30.0  # 关键：设置超时以避免挂起
        )

        # 模式：结构化错误处理
        if response.status_code != 200:
            raise BraveAPIError(f"API返回 {response.status_code}")

        # 使用Pydantic解析和验证
        data = response.json()
        return [BraveSearchResult(**result) for result in data.get("web", {}).get("results", [])]

# 任务5：带邮件智能体作为工具的研究智能体
@research_agent.tool
async def create_email_draft(
    ctx: RunContext[AgentDependencies],
    recipient: str,
    subject: str,
    context: str
) -> str:
    """基于研究上下文创建邮件草稿。"""
    # 关键：传递usage进行令牌跟踪
    result = await email_agent.run(
        f"创建一封给{recipient}关于：{context}的邮件",
        deps=EmailAgentDeps(subject=subject),
        usage=ctx.usage  # 来自多智能体文档的模式
    )

    return f"草稿已创建，ID：{result.data}"
```

### 集成点
```yaml
环境:
  - 添加到: .env
  - 变量: |
      # LLM配置
      LLM_PROVIDER=openai
      LLM_API_KEY=sk-...
      LLM_MODEL=gpt-4

      # Brave搜索
      BRAVE_API_KEY=BSA...

      # Gmail（credentials.json的路径）
      GMAIL_CREDENTIALS_PATH=./credentials/credentials.json

配置:
  - Gmail OAuth：首次运行打开浏览器进行授权
  - 令牌存储：./credentials/token.json（自动创建）

依赖:
  - 更新requirements.txt，添加：
    - google-api-python-client
    - google-auth-httplib2
    - google-auth-oauthlib
```

## 验证循环

### 第1级：语法和风格
```bash
# 首先运行这些 - 在继续之前修复任何错误
ruff check . --fix              # 自动修复风格问题
mypy .                          # 类型检查

# 预期：无错误。如有错误，阅读并修复。
```

### 第2级：单元测试
```python
# test_research_agent.py
async def test_research_with_brave():
    """测试研究智能体正确搜索"""
    agent = create_research_agent()
    result = await agent.run("AI安全研究")
    assert result.data
    assert len(result.data) > 0

async def test_research_creates_email():
    """测试研究智能体可以调用邮件智能体"""
    agent = create_research_agent()
    result = await agent.run(
        "研究AI安全并给****************起草邮件"
    )
    assert "draft_id" in result.data

# test_email_agent.py
def test_gmail_authentication(monkeypatch):
    """测试Gmail OAuth流程处理"""
    monkeypatch.setenv("GMAIL_CREDENTIALS_PATH", "test_creds.json")
    tool = GmailTool()
    assert tool.service is not None

async def test_create_draft():
    """测试使用正确编码创建草稿"""
    agent = create_email_agent()
    result = await agent.run(
        "创建一封给****************关于AI研究的邮件"
    )
    assert result.data.get("draft_id")
```

```bash
# 迭代运行测试直到通过：
pytest tests/ -v --cov=agents --cov=tools --cov-report=term-missing

# 如果失败：调试特定测试，修复代码，重新运行
```

### 第3级：集成测试
```bash
# 测试CLI交互
python cli.py

# 预期交互：
# 您：研究最新的AI安全发展
# 🤖 助手：[流式输出研究结果]
# 🛠 使用的工具：
#   1. brave_search (query='AI安全发展', limit=10)
#
# 您：为此创建一封给****************的邮件草稿
# 🤖 助手：[创建草稿]
# 🛠 使用的工具：
#   1. create_email_draft (recipient='<EMAIL>', ...)

# 检查Gmail草稿文件夹中创建的草稿
```

## 最终验证检查清单
- [ ] 所有测试通过：`pytest tests/ -v`
- [ ] 无代码检查错误：`ruff check .`
- [ ] 无类型错误：`mypy .`
- [ ] Gmail OAuth流程工作（浏览器打开，令牌保存）
- [ ] Brave搜索返回结果
- [ ] 研究智能体成功调用邮件智能体
- [ ] CLI流式响应带工具可见性
- [ ] 错误情况得到优雅处理
- [ ] README包含清晰的设置说明
- [ ] .env.example包含所有必需变量

---

## 要避免的反模式
- ❌ 不要硬编码API密钥 - 使用环境变量
- ❌ 不要在异步智能体上下文中使用同步函数
- ❌ 不要跳过Gmail的OAuth流程设置
- ❌ 不要忽略API的速率限制
- ❌ 不要忘记在多智能体调用中传递ctx.usage
- ❌ 不要提交credentials.json或token.json文件

## 信心分数：9/10

高信心度原因：
- 有代码库中清晰的示例可以遵循
- 外部API文档完善
- 多智能体系统的既定模式
- 全面的验证门控

Gmail OAuth首次设置UX存在轻微不确定性，但文档提供了清晰的指导。