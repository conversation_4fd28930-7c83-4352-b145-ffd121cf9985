name: "Java PRP基础模板 v2 - 含企业中间件集成的上下文丰富模板"
description: |

## 目的
为AI代理优化的模板，通过充分的上下文和自我验证能力，通过迭代改进实现可工作的Java代码。

## 核心原则
1. **上下文为王**: 包含所有必要的文档、示例和注意事项
2. **验证循环**: 提供AI可以运行和修复的可执行测试/检查
3. **信息密集**: 使用代码库中的关键词和模式
4. **渐进成功**: 从简单开始，验证，然后增强
5. **全局规则**: 确保遵循CLAUDE.md中的所有规则
6. **企业中间件**: 优先使用企业内部中间件方案

---

## 目标
[要构建的功能 - 具体描述最终状态和期望]

## 为什么
- [业务价值和用户影响]
- [与现有功能的集成]
- [解决的问题和受益人群]

## 功能描述
[用户可见的行为和技术要求]

### 成功标准
- [ ] [具体可衡量的结果]

## 所需上下文

### 文档与参考资料
```yaml
# 必读 - 在上下文窗口中包含这些内容
- file: middleware-docs/middleware-list.md
  why: 了解企业中间件清单，进行关键词匹配

# AI两步查询逻辑：
# 第一步：总是先读取 middleware-list.md 了解可用中间件
# 第二步：根据需求关键词匹配，只读取匹配的中间件文档文件
# 禁止：读取所有中间件文档（浪费上下文）

# 示例：如果需求匹配到RPC调用
- file: middleware-docs/jsf.md
  why: 获取JSF RPC框架的完整使用指南（依赖、配置、示例）

# 示例：如果需求匹配到缓存
- file: middleware-docs/jimdb.md
  why: 获取企业Redis的完整使用指南（依赖、配置、示例）

# 示例：如果需求匹配到消息队列
- file: middleware-docs/jmq.md
  why: 获取企业消息队列的完整使用指南（依赖、配置、示例）

# 注意：只包含匹配的中间件文档，不包含无关文档

# 其他参考资料
- url: [官方API文档URL]
  why: [需要的具体章节/方法]
  
- file: [path/to/example.java]
  why: [要遵循的模式，要避免的陷阱]
```

### 当前代码库结构
```bash
# 在项目根目录运行 `tree` 获取代码库概览
```

### 期望的代码库结构
```bash
# 需要添加的文件及其职责
```

### 已知代码库注意事项和库特性
```java
// 关键: [库名称] 需要 [特定设置]
// 示例: Spring Boot需要@SpringBootApplication注解
// 示例: 企业中间件需要特定的版本配置
// 示例: 我们使用Spring Boot 2.7.0，不能升级
```

## 实现蓝图

### 第1步: 现有系统集成分析

**现有系统检查**:
```bash
# 1. 系统环境检查
java -version                        # 检查Java版本
mvn dependency:tree > deps.txt       # 检查现有依赖
find . -name "application*.yml"      # 检查配置文件

# 2. 备份关键配置
cp -r src/main/resources src/main/resources.backup.$(date +%Y%m%d)

# 3. 检查端口和服务
netstat -tlnp | grep -E "(8080|8081|8082)"
ps aux | grep java
```

**企业中间件需求分析（两步查询）**:

```
第一步 - 读取中间件清单:
  → 总是先读取 middleware-docs/middleware-list.md
  → 了解所有可用的企业中间件类型和关键词

第二步 - 关键词匹配:
  → 分析需求描述中的关键词
  → 在清单中匹配对应的中间件
  → 记录匹配的中间件文件名

第三步 - 按需读取具体文档:
  → 只读取匹配中间件的具体文档文件
  → 例如：如果匹配到RPC需求，只读取 middleware-docs/jsf.md
  → 例如：如果匹配到缓存需求，只读取 middleware-docs/jimdb.md
  → 禁止读取所有中间件文档

第四步 - 集成兼容性检查:
  → 检查新中间件与现有依赖的兼容性
  → 识别可能的版本冲突
  → 制定渐进式集成策略
```

### 第2步: 渐进式中间件集成

**集成策略**: 非侵入式集成，支持降级和回滚

**Maven依赖** (渐进式添加，避免冲突):
```xml
<!-- 检查现有依赖冲突 -->
<!-- mvn dependency:tree | grep -E "(spring-boot|redis|jsf)" -->

<!-- 示例：如果匹配到JSF，添加时排除冲突依赖 -->
<dependency>
    <groupId>com.company</groupId>
    <artifactId>jsf-spring-boot-starter</artifactId>
    <version>3.2.1</version>
    <exclusions>
        <!-- 排除与现有系统冲突的依赖 -->
        <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

**应用配置** (保护现有配置，支持开关控制):
```yaml
# 保留现有配置不变
spring:
  application:
    name: ${EXISTING_APP_NAME}  # 保持现有应用名

# 新增中间件配置（带开关和降级）
enterprise:
  # JSF配置（如果匹配）
  jsf:
    enabled: ${JSF_ENABLED:false}  # 默认关闭，测试通过后开启
    registry:
      address: ${JSF_REGISTRY_ADDRESS:127.0.0.1:2181}
```

### 第3步: 数据模型和结构

创建核心数据模型，确保类型安全和一致性。

```java
// 示例数据模型
@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String username;
    
    // 其他字段...
}
```

### 第4步: 任务清单

```yaml
任务1 - 项目配置:
  修改 pom.xml:
    - 添加匹配中间件的Maven依赖
    - 确保版本与文档中指定的一致
  
  修改 application.yml:
    - 添加匹配中间件的配置
    - 使用文档中的配置模板

任务2 - 中间件配置类:
  创建 config/MiddlewareConfig.java:
    - 基于中间件文档的配置模式
    - 配置企业中间件客户端
    - 添加必要的Bean定义

任务3 - 业务逻辑实现:
  创建 service/XxxService.java:
    - 注入企业中间件客户端
    - 实现业务逻辑
    - 遵循文档中的使用模式

任务4 - 测试实现:
  创建 test/service/XxxServiceTest.java:
    - Mock企业中间件依赖
    - 测试业务逻辑
    - 验证中间件调用
```

### 集成点
```yaml
数据库:
  - 迁移: "添加 'feature_enabled' 列到 users 表"
  - 索引: "CREATE INDEX idx_feature_lookup ON users(feature_id)"
  
配置:
  - 添加到: src/main/resources/application.yml
  - 模式: "feature.timeout: ${FEATURE_TIMEOUT:30}"
  
路由:
  - 添加到: src/main/java/controller/
  - 模式: "@RequestMapping('/api/feature')"
```

## 验证循环

### Level 1: 编译和配置检查
```bash
mvn clean compile                    # 编译检查
mvn dependency:tree                  # 依赖检查
mvn spring-boot:run --dry-run       # 配置检查

# 预期: 无编译错误，依赖正确，配置有效
# 如有错误: 检查企业中间件依赖配置，确认版本正确
```

### Level 2: 单元测试
```java
// 创建 test/service/NewFeatureServiceTest.java 包含这些测试用例:
@Test
public void testHappyPath() {
    // 基本功能正常工作
    Result result = newFeatureService.processFeature("valid_input");
    assertEquals("success", result.getStatus());
}

@Test
public void testValidationError() {
    // 无效输入抛出验证异常
    assertThrows(ValidationException.class, () -> {
        newFeatureService.processFeature("");
    });
}

@Test
public void testExternalApiTimeout() {
    // 优雅处理超时
    when(mockExternalApi.call(any())).thenThrow(new TimeoutException());
    Result result = newFeatureService.processFeature("valid");
    assertEquals("error", result.getStatus());
    assertTrue(result.getMessage().contains("timeout"));
}
```

```bash
# 运行并迭代直到通过:
mvn test
# 如果失败: 阅读错误，理解根本原因，修复代码，重新运行
```

### Level 3: 集成测试
```bash
# 启动应用
mvn spring-boot:run

# 测试功能端点
curl -X POST http://localhost:8080/api/feature \
  -H "Content-Type: application/json" \
  -d '{"param": "test_value"}'

# 预期: {"status": "success", "data": {...}}
# 如果错误: 检查日志文件获取堆栈跟踪
```

## 最终验证清单
- [ ] 所有测试通过: `mvn test`
- [ ] 无编译错误: `mvn compile`
- [ ] 应用正常启动: `mvn spring-boot:run`
- [ ] 手动测试成功: [具体的curl/命令]
- [ ] 错误情况得到优雅处理
- [ ] 日志信息丰富但不冗长
- [ ] 文档已更新（如需要）
- [ ] 企业中间件集成正常工作

---

## 要避免的反模式

### 中间件使用反模式
- ❌ 不要使用公开的开源中间件（如RabbitMQ、标准Redis）
- ❌ 不要根据公开知识推测企业中间件用法
- ❌ 不要跳过中间件文档查询直接编码
- ❌ 不要忽略企业中间件的版本要求

### 开发反模式  
- ❌ 不要在现有模式有效时创建新模式
- ❌ 不要因为"应该能工作"就跳过验证
- ❌ 不要忽略失败的测试 - 修复它们
- ❌ 不要硬编码应该配置的值
- ❌ 不要捕获所有异常 - 要具体
- ❌ 不要忘记在测试中Mock企业中间件依赖

---

## 企业中间件集成说明

本模板通过两步查询流程自动集成企业内部中间件：

1. **清单查询**: AI先读取middleware-list.md了解所有可用中间件
2. **关键词匹配**: 根据需求关键词匹配对应的中间件
3. **按需读取**: 只读取匹配中间件的具体文档文件
4. **代码生成**: 基于文档内容生成符合企业规范的配置和代码
5. **验证确保**: 通过验证循环确保中间件集成正确工作

这确保了生成的代码始终使用企业标准中间件，同时避免了上下文浪费。