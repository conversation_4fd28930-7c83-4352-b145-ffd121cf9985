## FEATURE:

基于当前JDL-Trade-OPS系统架构，设计并实现一个数据变更监控工具模块。具体要求如下：

### 功能需求

#### 1. 数据源管理
- 支持动态配置多种数据源类型（MySQL、TiDB）
- 提供数据源连接测试功能
- 支持数据源的增删改查操作
- 数据源配置需要加密存储敏感信息（如密码）

#### 2. 监控任务配置
- 用户可以创建监控任务，包含以下配置项：
    - 任务名称和描述
    - 关联的数据源
    - 监控SQL语句（用于查询当前数据）
    - 监控类型：数据总量监控 或 字段值变更监控
    - 执行频率（支持cron表达式）
    - 告警阈值配置（数据量变化阈值、字段差异阈值）
    - 历史快照保留数量（1-3次）

#### 3. 数据快照管理
- 定时执行监控SQL并保存快照数据
- 支持查看历史快照记录
- 自动清理超出保留数量的历史快照
- 快照数据需要包含执行时间、结果数据、执行状态等信息

#### 4. 变更检测与告警
- 实时比对当前数据与历史快照
- 支持两种告警触发条件：
    - 数据量变化超过配置阈值（绝对值或百分比）
    - 特定字段值变化超过配置阈值
- 集成现有的咚咚/京ME消息推送功能
- 告警消息需要包含详细的变更信息和对比数据

#### 5. 权限控制
- 利用现有权限注解和菜单系统
- 实现以下权限控制：
    - 数据源管理权限（查看、新增、编辑、删除）
    - 监控任务管理权限（查看、新增、编辑、删除、启停）
    - 告警记录查看权限
    - 系统配置管理权限

### 技术实现要求

#### 后端实现
- 基于现有Spring Boot架构
- 使用现有的数据库连接池和事务管理
- 集成Quartz或类似定时任务框架
- 遵循现有的代码规范和项目结构
- 实现RESTful API接口

#### 前端实现
- 基于现有Vue3 + Element Plus架构
- 复用现有的组件库（如BatchInput、SearchButtons等）
- 遵循现有的页面布局和样式规范
- 实现响应式设计

#### 数据库设计
- 设计相关数据表：数据源配置表、监控任务表、快照记录表、告警记录表
- 遵循现有的数据库命名规范
- 考虑数据安全和性能优化

### 页面功能设计

#### 1. 数据源管理页面
- 数据源列表展示（支持搜索、分页）
- 数据源新增/编辑表单
- 连接测试功能
- 数据源删除确认

#### 2. 监控任务管理页面
- 任务列表展示（支持搜索、筛选、分页）
- 任务新增/编辑表单（包含SQL编辑器）
- 任务启停控制
- 任务执行历史查看

#### 3. 告警记录页面
- 告警记录列表（支持时间范围筛选）
- 告警详情查看（包含变更对比）
- 告警处理状态管理

#### 4. 系统配置页面
- 全局告警配置
- 消息推送配置
- 系统参数设置

请提供详细的设计方案，包括数据库表结构设计、API接口设计、前端页面原型，以及具体的实现步骤规划。

## EXAMPLES:

参考以下示例代码模式：
- `examples/typical-scenarios/order-enhancement.java` - 现有订单系统功能增强模式
- `examples/middleware-integration/cache-fallback.java` - 缓存集成和降级处理模式
- `examples/code-patterns/service-layer.java` - 服务层设计模式
- `examples/code-patterns/testing-patterns.java` - 单元测试和Mock模式

## DOCUMENTATION:

开发过程中需要参考的文档：
- Spring Boot官方文档: https://docs.spring.io/spring-boot/docs/current/reference/html/
- JPA文档: https://docs.spring.io/spring-data/jpa/docs/current/reference/html/
- 企业中间件文档将根据需求自动匹配
- 当前项目规范和架构：docs/design/development-specification.md

## OTHER CONSIDERATIONS:

特殊要求和注意事项：
- 现有系统基于Spring Boot，不能升级版本
- 新功能必须支持功能开关，默认关闭
- 此新增功能不能影响原系统功能流程
- 需要支持现有系统的渐进式集成
- 所有数据库操作需要事务支持
- 需要完整的单元测试覆盖

## MIDDLEWARE HINTS:

功能涉及的中间件类型（AI会自动匹配企业方案）：
- 需要缓存记录和高频信息 → 会匹配Redis分布式缓存
- 需要记录操作日志 → donglog统一日志组件
- 需要存储配置和操作记录 → 会匹配项目当前Mysql/Tidb组件
- 如果需要调用RPC服务 → 会匹配JSF RPC框架