# JMQ - 消息队列

## 基本信息
- **版本**: 2.1.0 (dong-boot-starter)
- **关键词**: 消息, 队列, 异步, 事件, 通知, 解耦, MQ, 消息流处理
- **适用场景**: 低延迟、高并发的分布式消息流处理
- **依赖要求**: JDK 8+, Spring Boot 2.7.0+

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>jmq-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
spring:
  application:
    name: ${APP_NAME:my-app}  # 必填项

dong:
  jmq:
    enabled: ${JMQ_ENABLED:false}  # 功能开关
    
    # 生产者配置
    producers:
      producer1:
        enabled: true
        app: ${JMQ_PRODUCER_APP:myApp}
        address: ${JMQ_ADDRESS:test-nameserver.jmq.jd.local:50088}
        password: ${JMQ_PASSWORD:}
      
    # 消费者配置  
    consumers:
      consumer1:
        enabled: true
        app: ${JMQ_CONSUMER_APP:myApp}
        address: ${JMQ_ADDRESS:test-nameserver.jmq.jd.local:50088}
        password: ${JMQ_PASSWORD:}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `JMQ_ENABLED` | 是否启用JMQ | false | true |
| `JMQ_ADDRESS` | JMQ服务地址 | test-nameserver.jmq.jd.local:50088 | prod-nameserver.jmq.jd.local:50088 |
| `JMQ_PASSWORD` | JMQ访问密码 | - | your_password |
| `JMQ_PRODUCER_APP` | 生产者应用名 | - | order-producer |
| `JMQ_CONSUMER_APP` | 消费者应用名 | - | order-consumer |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| app | string | 是 | - | 应用标识，用于权限控制 |
| address | string | 是 | - | JMQ服务器地址 |
| password | string | 是 | - | 访问密码 |
| enabled | boolean | 否 | true | 是否启用该生产者/消费者 |

## 使用代码

### 基础用法
```java
// 1. 消息生产者
@Component
public class MessageProducer {
    
    @JmqProducer(name = "producer1")
    private Producer producer;
    
    @Value("${demo.topic:default-topic}")
    private String topic;
    
    public void sendMessage(String content) throws Exception {
        Message message = new Message(topic, content, "businessId");
        producer.send(message);
        log.info("消息发送成功: topic={}, content={}", topic, content);
    }
    
    public void sendMessageWithCallback(String content) throws Exception {
        Message message = new Message(topic, content, "businessId");
        
        producer.send(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult result) {
                log.info("消息发送成功: {}", result);
            }
            
            @Override
            public void onException(Throwable e) {
                log.error("消息发送失败: {}", e.getMessage());
            }
        });
    }
}

// 2. 消息消费者
@Component
public class MessageConsumer {
    
    @JmqListener(id = "consumer1", topics = {"${demo.topic:default-topic}"})
    public void handleMessage(List<Message> messages) {
        for (Message message : messages) {
            try {
                processMessage(message);
                log.info("消息处理成功: topic={}, content={}", 
                    message.getTopic(), message.getText());
            } catch (Exception e) {
                log.error("消息处理失败: topic={}, content={}, error={}", 
                    message.getTopic(), message.getText(), e.getMessage());
                // 根据业务需要决定是否重试或记录到死信队列
            }
        }
    }
    
    private void processMessage(Message message) {
        // 业务处理逻辑
        String content = message.getText();
        // 处理消息内容
    }
}
```

### 常见场景
```java
// 场景1: 订单事件处理
@Component
public class OrderEventProducer {
    
    @JmqProducer(name = "producer1")
    private Producer producer;
    
    private static final String ORDER_TOPIC = "order.events";
    
    public void publishOrderCreated(Order order) throws Exception {
        OrderEvent event = new OrderEvent("ORDER_CREATED", order.getId(), order);
        Message message = new Message(ORDER_TOPIC, JSON.toJSONString(event), order.getId().toString());
        producer.send(message);
    }
    
    public void publishOrderPaid(Order order) throws Exception {
        OrderEvent event = new OrderEvent("ORDER_PAID", order.getId(), order);
        Message message = new Message(ORDER_TOPIC, JSON.toJSONString(event), order.getId().toString());
        producer.send(message);
    }
}

@Component
public class OrderEventConsumer {
    
    @JmqListener(id = "consumer1", topics = {"order.events"})
    public void handleOrderEvent(List<Message> messages) {
        for (Message message : messages) {
            OrderEvent event = JSON.parseObject(message.getText(), OrderEvent.class);
            
            switch (event.getEventType()) {
                case "ORDER_CREATED":
                    handleOrderCreated(event);
                    break;
                case "ORDER_PAID":
                    handleOrderPaid(event);
                    break;
                default:
                    log.warn("未知事件类型: {}", event.getEventType());
            }
        }
    }
    
    private void handleOrderCreated(OrderEvent event) {
        // 处理订单创建事件
        log.info("处理订单创建事件: orderId={}", event.getOrderId());
    }
    
    private void handleOrderPaid(OrderEvent event) {
        // 处理订单支付事件
        log.info("处理订单支付事件: orderId={}", event.getOrderId());
    }
}

// 场景2: 延迟消息处理
@Component
public class DelayedMessageService {
    
    @JmqProducer(name = "producer1")
    private Producer producer;
    
    public void sendDelayedMessage(String content, int delaySeconds) throws Exception {
        Message message = new Message("delayed.topic", content, "delayId");
        // 设置延迟时间
        message.setDelayTime(System.currentTimeMillis() + delaySeconds * 1000);
        producer.send(message);
    }
}

// 场景3: 批量消息处理
@Component
public class BatchMessageConsumer {
    
    @JmqListener(id = "consumer1", topics = {"batch.topic"}, delayedStart = 10)
    public void handleBatchMessages(List<Message> messages) {
        log.info("批量处理消息: count={}", messages.size());
        
        // 批量处理消息
        List<String> contents = messages.stream()
            .map(Message::getText)
            .collect(Collectors.toList());
            
        processBatch(contents);
    }
    
    private void processBatch(List<String> contents) {
        // 批量业务处理逻辑
        for (String content : contents) {
            // 处理单个消息
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingOrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    // JMQ消息生产者（可选注入）
    @JmqProducer(name = "producer1")
    private Producer producer;
    
    @Value("${order.topic:order.events}")
    private String orderTopic;
    
    public Order createOrder(CreateOrderRequest request) {
        // 1. 创建订单（现有逻辑）
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setAmount(request.getAmount());
        order.setStatus(OrderStatus.CREATED);
        order = orderRepository.save(order);
        
        // 2. 发送订单创建事件（新增功能）
        publishOrderCreatedEvent(order);
        
        return order;
    }
    
    private void publishOrderCreatedEvent(Order order) {
        try {
            if (producer != null) {
                OrderEvent event = new OrderEvent("ORDER_CREATED", order.getId(), order);
                Message message = new Message(orderTopic, JSON.toJSONString(event), order.getId().toString());
                producer.send(message);
                log.info("订单创建事件发送成功: orderId={}", order.getId());
            }
        } catch (Exception e) {
            log.error("订单创建事件发送失败: orderId={}, error={}", order.getId(), e.getMessage());
            // 消息发送失败不影响主流程
        }
    }
}
```

## 功能特性

### 核心能力
- **低延迟**: 毫秒级消息传递延迟
- **高并发**: 支持百万级TPS消息处理
- **高可用**: 集群部署，自动故障转移
- **高可靠**: 消息持久化，确保消息不丢失

### 技术特点
- **多种消费模式**: 支持集群消费和广播消费
- **消息重试**: 自动重试机制和死信队列
- **延迟消息**: 支持任意时间的延迟消息
- **批量处理**: 支持批量发送和批量消费

### 适用场景
- **异步解耦**: 系统间异步通信和解耦
- **事件驱动**: 基于事件的业务流程处理
- **流量削峰**: 高并发场景下的流量缓冲
- **数据同步**: 跨系统的数据同步和一致性

## 注意事项

### 使用约束
- 消息内容建议使用JSON格式，便于序列化和反序列化
- 消费者逻辑必须保证幂等性，避免重复消费问题
- 单个消息大小不建议超过1MB
- Topic命名建议使用业务域.事件类型格式

### 性能考虑
- 合理设置批量消费大小，平衡延迟和吞吐量
- 避免在消费者中执行耗时操作，影响消费速度
- 使用异步发送提高生产者性能
- 监控消息积压情况，及时调整消费者数量

### 安全要求
- 生产环境必须配置访问密码
- 敏感消息内容考虑加密传输
- 定期轮换访问密码
- 监控异常消息模式和访问行为

## 最佳实践

1. **消息设计**: 消息内容包含足够的业务信息，避免额外查询
2. **幂等处理**: 消费者逻辑设计为幂等，使用业务ID去重
3. **异常处理**: 区分业务异常和系统异常，制定不同的重试策略
4. **监控告警**: 关注消息积压、消费延迟和异常率
5. **容量规划**: 根据业务量合理规划Topic分区和消费者数量
6. **版本兼容**: 消息格式变更时保持向后兼容

## 常见问题

### 配置问题
- **连接失败**: 检查JMQ服务地址和网络连通性
- **认证失败**: 确认应用名和密码配置正确
- **Topic不存在**: 确认Topic已在JMQ管理平台创建

### 运行时问题
- **消息积压**: 检查消费者处理能力，考虑增加消费者实例
- **重复消费**: 确保消费者逻辑幂等性，检查消费者组配置
- **消息丢失**: 检查生产者发送确认和消费者提交确认
- **消费延迟**: 优化消费者处理逻辑，调整批量消费参数

## 环境信息

| 环境 | 服务地址 | 说明 |
|------|----------|------|
| 测试 | test-nameserver.jmq.jd.local:50088 | 测试环境JMQ服务 |
| 预发 | pre-nameserver.jmq.jd.local:50088 | 预发环境JMQ服务 |
| 生产 | nameserver.jmq.jd.local:50088 | 生产环境JMQ服务 |