# 企业中间件清单

本文件列出所有可用的企业中间件，AI会根据需求关键词匹配对应的中间件。

## RPC调用类
- **文件**: `jsf.md`
- **名称**: JSF服务框架
- **关键词**: RPC, 远程调用, 服务调用, 接口调用, 微服务通信, 分布式服务
- **适用场景**: 分布式系统间的高性能RPC通信

## 消息队列类
- **文件**: `jmq.md`
- **名称**: JMQ消息队列
- **关键词**: 消息, 队列, 异步, 事件, 通知, 解耦, MQ, 消息流处理
- **适用场景**: 异步处理、事件驱动、系统解耦

- **文件**: `redis.md`
- **名称**: Redis分布式缓存
- **关键词**: 缓存, Redis, Redisson, 快速访问, 临时存储, 分布式缓存, 高性能存储, 分布式锁, 会话存储
- **适用场景**: 高吞吐、低延迟的分布式缓存服务，会话存储，分布式锁，消息发布订阅

- **文件**: `dongcache.md`
- **名称**: DongCache本地缓存管理
- **关键词**: 本地缓存, Caffeine, Guava, 内存缓存, 缓存管理
- **适用场景**: 本地缓存管理和监控

## 配置管理类
- **文件**: `ducc.md`
- **名称**: DUCC统一配置中心
- **关键词**: 配置, 参数, 开关, 环境变量, 配置中心, 动态配置
- **适用场景**: 集中化配置管理和动态配置更新

## 开发框架类
- **文件**: `dongboot.md`
- **名称**: DongBoot统一开发框架
- **关键词**: 开发框架, Spring Boot, 脚手架, 统一框架, 应用架构
- **适用场景**: 统一开发框架和应用架构

## HTTP客户端类
- **文件**: `donghttp.md`
- **名称**: DongHttp HTTP客户端
- **关键词**: HTTP客户端, REST调用, 远程调用, API调用, HTTP请求
- **适用场景**: HTTP/HTTPS远程服务调用

## 监控类
- **文件**: `ump.md`
- **名称**: UMP统一监控平台
- **关键词**: 监控, 告警, 性能监控, 业务监控, 指标收集, 日志监控
- **适用场景**: 应用性能监控和业务指标监控

## 日志类
- **文件**: `donglog.md`
- **名称**: DongLog统一日志组件
- **关键词**: 日志, 日志组件, Log4j2, SLF4J, 异步日志, 日志监控
- **适用场景**: 统一日志组件和日志管理

## 任务调度类
- **文件**: `dongschedule.md`
- **名称**: DongSchedule任务调度框架
- **关键词**: 任务调度, 定时任务, 分片任务, MapReduce, 任务管理
- **适用场景**: 集中化任务调度和监控管理

## 认证授权类
- **文件**: `sso.md`
- **名称**: SSO单点登录系统
- **关键词**: 单点登录, SSO, 用户认证, 权限控制, 登录拦截
- **适用场景**: 统一用户认证和单点登录

- **文件**: `usf.md`
- **名称**: USF权限系统
- **关键词**: 权限系统, 权限控制, 菜单权限, 数据权限, 权限拦截
- **适用场景**: 统一权限管理和访问控制

## AI使用说明

1. **第一步**: 总是先读取此文件，了解可用中间件
2. **第二步**: 根据需求关键词匹配对应的中间件
3. **第三步**: 只读取匹配中间件的具体文档文件
4. **禁止**: 读取所有中间件文档，避免上下文浪费

## 维护说明

- 新增中间件：添加到对应分类，创建对应的.md文件
- 更新中间件：直接修改对应的.md文件
- 删除中间件：从清单中移除，删除对应文件