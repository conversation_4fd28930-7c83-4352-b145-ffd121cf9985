# USF - 权限系统

## 基本信息
- **版本**: 1.2.2-SNAPSHOT
- **关键词**: 权限系统, 权限控制, 菜单权限, 数据权限, 权限拦截
- **适用场景**: 统一权限管理和访问控制
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.susf</groupId>
    <artifactId>susf-client</artifactId>
    <version>1.2.2-SNAPSHOT</version>
</dependency>
```

**版本说明**: 使用指定版本1.2.2-SNAPSHOT

## 配置

### 基础配置
```yaml
usf:
  enabled: ${USF_ENABLED:false}  # 功能开关
  tenementCode: ${USF_TENEMENT_CODE:susf-demo}  # 租户编码
  appCode: ${USF_APP_CODE:susf-demo}  # 应用编码
  userSystemType: ${USF_USER_SYSTEM_TYPE:1}  # 账号体系 1ERP 2PASSPORT 3FLP
  callUser: ${USF_CALL_USER:org.missusf}  # 调用人
  errorPage: ${USF_ERROR_PAGE:error}  # 错误页面
  # JSF配置
  jsf:
    consumer:
      alias:
        usf: ${JSF_CONSUMER_ALIAS_USF:susf-lcp-test}
      token:
        usf: ${JSF_CONSUMER_TOKEN_USF:doh96jpxka8nprjqf3d2ak}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `USF_ENABLED` | 是否启用USF | false | true |
| `USF_TENEMENT_CODE` | 租户编码 | susf-demo | your-tenant |
| `USF_APP_CODE` | 应用编码 | susf-demo | your-app |
| `USF_USER_SYSTEM_TYPE` | 账号体系类型 | 1 | 1 |
| `JSF_CONSUMER_ALIAS_USF` | JSF消费者别名 | susf-lcp-test | susf-lcp-prod |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| tenementCode | String | 是 | susf-demo | 租户编码，用于多租户隔离 |
| appCode | String | 是 | susf-demo | 应用编码，标识具体应用 |
| userSystemType | Integer | 是 | 1 | 账号体系：1ERP 2京东PIN 3FLP |
| callUser | String | 是 | org.missusf | 调用人标识 |
| errorPage | String | 否 | error | 权限验证失败时跳转的错误页面 |

## 使用代码

### 基础用法
```java
// 1. USF配置类
@Configuration
@ConditionalOnProperty(name = "usf.enabled", havingValue = "true")
public class UsfConfig {

    @Value("${usf.tenementCode}")
    private String tenementCode;
    
    @Value("${usf.appCode}")
    private String appCode;
    
    @Value("${usf.userSystemType}")
    private Integer userSystemType;

    @Bean
    public SusfContext susfContext() {
        SusfContext context = new SusfContext();
        context.setTenementCode(tenementCode);
        context.setAppCode(appCode);
        context.setUserSystemType(userSystemType);
        return context;
    }

    @Bean
    public UsfInterceptor usfInterceptor() {
        UsfInterceptor interceptor = new UsfInterceptor();
        interceptor.setSusfContext(susfContext());
        interceptor.setErrorPage("error");
        return interceptor;
    }
}

// 2. JSF服务配置
@Component
public class UsfServiceConfig {

    @BootReference(
        uniqueId = "susfPermissionService",
        binding = @BootReferenceBinding(
            alias = "${jsf.consumer.alias.usf}",
            bindingType = "jsf",
            timeout = 5000,
            retries = 2,
            cluster = "failfast",
            parameters = {
                @BootParameter(key = "token", value = "${jsf.consumer.token.usf}", hidden = true),
                @BootParameter(key = "serialization", value = "hessian"),
                @BootParameter(key = "protocol", value = "jsf"),
                @BootParameter(key = "check", value = "false"),
                @BootParameter(key = "lazy", value = "true")
            }
        )
    )
    private SusfPermissionService susfPermissionService;

    public SusfPermissionService getSusfPermissionService() {
        return susfPermissionService;
    }
}
```

### 常见场景
```java
// 场景1: 权限拦截注解
@RestController
@RequestMapping("/api/business")
public class BusinessController {

    @Permission(value = "queryBusiness")
    @GetMapping("/list")
    public Result<List<Business>> getBusinessList() {
        // 业务逻辑
        return Result.success(businessService.findAll());
    }

    @Permission(value = "createBusiness")
    @PostMapping("/create")
    public Result<Business> createBusiness(@RequestBody CreateBusinessRequest request) {
        // 业务逻辑
        return Result.success(businessService.create(request));
    }

    @Permission(value = "deleteBusiness")
    @DeleteMapping("/{id}")
    public Result<Void> deleteBusiness(@PathVariable Long id) {
        // 业务逻辑
        businessService.delete(id);
        return Result.success();
    }
}

// 场景2: 获取用户菜单权限
@Service
public class MenuService {

    @Autowired
    private SusfPermissionService susfPermissionService;
    
    @Value("${usf.tenementCode}")
    private String tenementCode;
    
    @Value("${usf.appCode}")
    private String appCode;
    
    @Value("${usf.callUser}")
    private String callUser;

    public List<Menu> getUserMenus(String userCode) {
        try {
            SusfDataDto dto = new SusfDataDto();
            dto.setTenementCode(tenementCode);
            dto.setAppCode(appCode);
            dto.setUserCode(userCode);
            dto.setUserSystemType(1); // ERP账号
            dto.setCallUser(callUser);

            List<Menu> menus = susfPermissionService.findAllResourceList(dto);
            
            log.info("获取用户菜单成功: userCode={}, menuCount={}", userCode, menus.size());
            return menus;
        } catch (Exception e) {
            log.error("获取用户菜单失败: userCode={}, error={}", userCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}

// 场景3: 获取用户数据权限
@Service
public class DataPermissionService {

    @Autowired
    private SusfPermissionService susfPermissionService;
    
    @Value("${usf.tenementCode}")
    private String tenementCode;
    
    @Value("${usf.callUser}")
    private String callUser;

    public List<DataInfo> getUserDataPermission(String userCode, String typeCode) {
        try {
            SusfDataDto dto = new SusfDataDto();
            dto.setTenementCode(tenementCode);
            dto.setTypeCode(typeCode);
            dto.setUserCode(userCode);
            dto.setUserSystemType(1); // ERP账号
            dto.setCallUser(callUser);

            List<DataInfo> dataInfos = susfPermissionService.indDataListWithDistinct(dto);
            
            log.info("获取用户数据权限成功: userCode={}, typeCode={}, dataCount={}", 
                userCode, typeCode, dataInfos.size());
            return dataInfos;
        } catch (Exception e) {
            log.error("获取用户数据权限失败: userCode={}, typeCode={}, error={}", 
                userCode, typeCode, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public boolean hasDataPermission(String userCode, String typeCode, String dataValue) {
        List<DataInfo> dataInfos = getUserDataPermission(userCode, typeCode);
        return dataInfos.stream()
            .anyMatch(dataInfo -> dataValue.equals(dataInfo.getDataValue()));
    }
}

// 场景4: 权限检查服务
@Service
public class PermissionCheckService {

    @Autowired
    private MenuService menuService;
    
    @Autowired
    private DataPermissionService dataPermissionService;

    public boolean hasMenuPermission(String userCode, String permissionCode) {
        try {
            List<Menu> menus = menuService.getUserMenus(userCode);
            return menus.stream()
                .anyMatch(menu -> permissionCode.equals(menu.getPermissionCode()));
        } catch (Exception e) {
            log.error("菜单权限检查失败: userCode={}, permissionCode={}", userCode, permissionCode, e);
            return false;
        }
    }

    public boolean hasDataPermission(String userCode, String typeCode, String dataValue) {
        try {
            return dataPermissionService.hasDataPermission(userCode, typeCode, dataValue);
        } catch (Exception e) {
            log.error("数据权限检查失败: userCode={}, typeCode={}, dataValue={}", 
                userCode, typeCode, dataValue, e);
            return false;
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // USF服务（可选注入，支持降级）
    @Autowired(required = false)
    private PermissionCheckService permissionCheckService;

    // 降级服务
    @Autowired
    private LocalPermissionService localPermissionService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 检查用户权限（新增，支持降级）
        if (!checkUserPermission(request.getUserCode(), request.getOperation())) {
            throw new PermissionDeniedException("用户无权限执行此操作");
        }
        
        // 2. 检查数据权限（新增，支持降级）
        if (!checkDataPermission(request.getUserCode(), request.getDataType(), request.getDataValue())) {
            throw new PermissionDeniedException("用户无权限访问此数据");
        }
        
        // 3. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        return result;
    }
    
    private boolean checkUserPermission(String userCode, String operation) {
        try {
            if (permissionCheckService != null) {
                // 使用USF权限检查
                return permissionCheckService.hasMenuPermission(userCode, operation);
            } else {
                // 降级到本地权限检查
                return localPermissionService.hasPermission(userCode, operation);
            }
        } catch (Exception e) {
            log.warn("权限检查失败，默认拒绝访问: userCode={}, operation={}, error={}", 
                userCode, operation, e.getMessage());
            return false;
        }
    }
    
    private boolean checkDataPermission(String userCode, String dataType, String dataValue) {
        try {
            if (permissionCheckService != null) {
                // 使用USF数据权限检查
                return permissionCheckService.hasDataPermission(userCode, dataType, dataValue);
            } else {
                // 降级到本地数据权限检查
                return localPermissionService.hasDataPermission(userCode, dataType, dataValue);
            }
        } catch (Exception e) {
            log.warn("数据权限检查失败，默认拒绝访问: userCode={}, dataType={}, dataValue={}, error={}", 
                userCode, dataType, dataValue, e.getMessage());
            return false;
        }
    }
}
```

## 功能特性

### 核心能力
- **菜单权限**: 控制用户可访问的菜单和功能
- **数据权限**: 控制用户可访问的数据范围
- **权限拦截**: 自动拦截无权限的请求

### 技术特点
- **多租户支持**: 支持多租户权限隔离
- **多账号体系**: 支持ERP、京东PIN等多种账号体系
- **JSF集成**: 通过JSF调用权限服务

### 适用场景
- **企业应用**: 企业内部系统的权限管理
- **多租户系统**: SaaS应用的权限隔离
- **数据安全**: 敏感数据的访问控制

## 注意事项

### 使用约束
- 必须配置JSF注册中心
- 需要在USF系统中预先配置权限数据
- 不同环境需要使用不同的配置参数

### 性能考虑
- 权限信息可以缓存提高性能
- 合理设置JSF超时和重试参数

### 安全要求
- JSF token需要安全存储
- 权限检查失败时要有明确的错误提示

## 最佳实践

1. **权限设计**: 合理设计权限粒度，避免过于复杂
2. **缓存策略**: 缓存用户权限信息提高性能
3. **异常处理**: 完善的权限验证失败处理机制
4. **日志记录**: 记录权限检查日志便于审计
5. **降级方案**: 提供本地权限检查的降级方案
