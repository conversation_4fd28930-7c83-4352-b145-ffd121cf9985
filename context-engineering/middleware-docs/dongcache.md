# DongCache - 本地缓存管理

## 基本信息
- **版本**: 
- **关键词**: 本地缓存, Caffeine, Guava, 内存缓存, 缓存管理
- **适用场景**: 本地缓存管理和监控
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>cache-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  cache:
    enabled: ${DONGCACHE_ENABLED:false}  # 功能开关
    caches:
      # Caffeine缓存配置示例
      caffeineCache1:
        initialCapacity: ${CACHE_INITIAL_CAPACITY:1024}
        maximumSize: ${CACHE_MAXIMUM_SIZE:10240}
        refreshAfterWrite: ${CACHE_REFRESH_AFTER_WRITE:60000}
      
      # Guava缓存配置示例
      guavaCache1:
        initialCapacity: ${CACHE_INITIAL_CAPACITY:1024}
        maximumSize: ${CACHE_MAXIMUM_SIZE:10240}
        expireAfterWrite: ${CACHE_EXPIRE_AFTER_WRITE:60000}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DONGCACHE_ENABLED` | 是否启用DongCache | false | true |
| `CACHE_INITIAL_CAPACITY` | 缓存初始容量 | 1024 | 2048 |
| `CACHE_MAXIMUM_SIZE` | 缓存最大大小 | 10240 | 20480 |
| `CACHE_REFRESH_AFTER_WRITE` | 写入后刷新时间(毫秒) | 60000 | 30000 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| initialCapacity | Integer | 否 | 16 | 指定缓存对象的初始容量 |
| maximumSize | Long | 否 | 用户指定 | 指定缓存内最大键值对数量 |
| expireAfterWrite | Long | 否 | 用户指定 | 指定缓存对象在被写入后的过期时间(毫秒) |
| expireAfterAccess | Long | 否 | 用户指定 | 指定缓存对象在最后一次访问后的过期时间(毫秒) |
| refreshAfterWrite | Long | 否 | 用户指定 | 指定缓存对象在被写入后的自动刷新时间(毫秒) |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.cache.enabled", havingValue = "true")
public class DongCacheConfig {

    @Bean
    public CacheManager cacheManager() {
        return new DongCacheManager();
    }

    @Bean
    public DongCacheHealthIndicator dongCacheHealthIndicator() {
        return new DongCacheHealthIndicator();
    }
}

// 2. 服务类
@Service
public class DongCacheService {

    @BootCache(name = "caffeineCache1")
    private LoadingCache<String, String> caffeineCache;

    @BootCacheLoader(cacheName = "caffeineCache1")
    CacheLoader<String, String> cacheLoader = this::loadFromRemote;

    @Autowired
    private LocalCacheService localCacheService; // 降级服务

    public String getCachedValue(String key) {
        try {
            if (caffeineCache != null) {
                return caffeineCache.get(key);
            }
        } catch (Exception e) {
            log.warn("DongCache调用失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        return localCacheService.getValue(key);
    }

    private String loadFromRemote(String key) {
        return "remote value for " + key;
    }

    public boolean isAvailable() {
        return caffeineCache != null;
    }
}
```

### 常见场景
```java
// 场景1: 热点数据缓存
@Service
public class HotDataCacheService {

    @BootCache(name = "hotDataCache")
    private LoadingCache<String, UserInfo> userCache;

    @BootCacheLoader(cacheName = "hotDataCache")
    CacheLoader<String, UserInfo> userLoader = userId -> {
        // 从数据库或远程服务加载用户信息
        if (userCache != null) {
            return userService.getUserById(userId);
        }
        return null;
    };

    public UserInfo getUserInfo(String userId) {
        try {
            if (userCache != null) {
                return userCache.get(userId);
            }
        } catch (Exception e) {
            log.error("用户信息缓存获取失败: {}", e.getMessage());
        }

        return userService.getUserById(userId);
    }
}

// 场景2: 计算结果缓存
@Service
public class ComputeCacheService {

    @BootCache(name = "computeCache")
    private LoadingCache<String, BigDecimal> computeCache;

    @BootCacheLoader(cacheName = "computeCache")
    CacheLoader<String, BigDecimal> computeLoader = key -> {
        return expensiveComputation(key);
    };

    public BigDecimal getComputeResult(String key) {
        try {
            if (computeCache != null) {
                return computeCache.get(key);
            }
        } catch (Exception e) {
            log.warn("计算结果缓存失败，直接计算: {}", e.getMessage());
        }

        return expensiveComputation(key);
    }

    private BigDecimal expensiveComputation(String key) {
        // 复杂计算逻辑
        return new BigDecimal("100.00");
    }
}

// 场景3: 配置信息缓存
@Service
public class ConfigCacheService {

    @BootCache(name = "configCache")
    private LoadingCache<String, String> configCache;

    @BootCacheLoader(cacheName = "configCache")
    CacheLoader<String, String> configLoader = configKey -> {
        return configService.getConfig(configKey);
    };

    public String getConfigValue(String configKey) {
        if (configCache != null) {
            try {
                return configCache.get(configKey);
            } catch (Exception e) {
                log.warn("配置缓存获取失败，但不影响主流程: {}", e.getMessage());
            }
        }
        
        return configService.getConfig(configKey);
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DongCache服务（可选注入，支持降级）
    @BootCache(name = "businessCache")
    private LoadingCache<String, BusinessData> businessCache;

    @BootCacheLoader(cacheName = "businessCache")
    CacheLoader<String, BusinessData> businessLoader = this::loadBusinessData;

    // 降级服务
    @Autowired
    private LocalCacheService localCacheService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 2. 使用DongCache增强功能（新增，支持降级）
        enhanceWithCache(result);
        
        return result;
    }
    
    private void enhanceWithCache(BusinessObject object) {
        try {
            if (businessCache != null) {
                // 使用DongCache缓存
                BusinessData cachedData = businessCache.get(object.getId().toString());
                object.setCachedData(cachedData);
            } else {
                // 降级到本地缓存
                BusinessData cachedData = localCacheService.get(object.getId().toString());
                object.setCachedData(cachedData);
            }
        } catch (Exception e) {
            log.warn("缓存处理失败，使用默认值: {}", e.getMessage());
            object.setCachedData(new BusinessData());
        }
    }

    private BusinessData loadBusinessData(String key) {
        return businessRepository.findDataById(key);
    }
}
```

## 功能特性

### 核心能力
- **统一管理**: 支持Guava和Caffeine两种主流本地缓存
- **实时监控**: 缓存命中率监控和性能指标实时展示
- **动态调参**: 支持运行时动态调整缓存参数

### 技术特点
- **健康检测**: 缓存健康度自动检测和异常告警
- **影子库支持**: 支持影子库模式，压测环境数据隔离
- **配置变更**: 配置变更无需重启应用

### 适用场景
- **热点数据缓存**: 频繁访问的数据本地缓存
- **计算结果缓存**: 复杂计算结果的缓存存储
- **配置信息缓存**: 配置信息的本地缓存

## 注意事项

### 使用约束
- 需要正确配置CacheLoader，否则refreshAfterWrite不生效
- maximumSize和maximumWeight不能同时设置
- 缓存大小设置需要考虑内存容量

### 性能考虑
- 合理设置缓存大小避免内存溢出
- 选择合适的过期策略提高命中率
- 监控缓存性能指标

### 安全要求
- 缓存数据不应包含敏感信息
- 影子库模式确保数据隔离

## 最佳实践

1. **缓存设计原则**: 根据内存容量和数据特点设置maximumSize
2. **过期策略选择**: 根据数据更新频率选择expireAfterWrite或expireAfterAccess
3. **刷新策略使用**: 对于重要数据使用refreshAfterWrite避免缓存穿透
4. **性能监控**: 定期检查缓存命中率和性能指标
5. **异常处理**: CacheLoader中要处理好异常情况
