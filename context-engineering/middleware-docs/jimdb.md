# JIMDB - 分布式缓存

## 基本信息
- **版本**: 1.5.2 (dong-boot-starter)
- **关键词**: 缓存, <PERSON><PERSON>, 快速访问, 临时存储, 分布式缓存, 高性能存储
- **适用场景**: 高吞吐、低延迟的分布式缓存服务
- **依赖要求**: JDK 8+, Spring Boot 2.7.0+

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>jim-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  jim:
    enabled: ${JIMDB_ENABLED:false}  # 功能开关
    jim-url: ${JIMDB_URL:jim://2914173422341158041/110000259}
    service-endpoint: ${JIMDB_ENDPOINT:http://test.cfs.jim.jd.local}
    config-id: ${JIMDB_CONFIG_ID:}  # 可选，使用默认配置
    io-thread-pool-size: ${JIMDB_IO_THREADS:}  # 可选，使用默认值
    computation-thread-pool-size: ${JIMDB_COMPUTE_THREADS:}  # 可选，使用默认值
    request-queue-size: ${JIMDB_QUEUE_SIZE:}  # 可选，使用默认值
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `JIMDB_ENABLED` | 是否启用JIMDB | false | true |
| `JIMDB_URL` | JIMDB集群标识和密码 | - | jim://cluster_id/password |
| `JIMDB_ENDPOINT` | 元数据服务域名 | http://test.cfs.jim.jd.local | http://cfs.jim.jd.local |
| `JIMDB_CONFIG_ID` | 客户端配置ID | - | custom_config_id |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| jimUrl | string | 是 | - | JIMDB集群的标识和密码，需妥善保管 |
| serviceEndpoint | string | 否 | 环境默认值 | 元数据服务域名，提供动态配置和拓扑信息 |
| configId | string | 否 | 默认配置 | JIMDB集群的ClientConfig的ID |
| ioThreadPoolSize | int | 否 | 默认值 | JIMDB SDK中IO线程数 |
| computationThreadPoolSize | int | 否 | 默认值 | 用于完成SDK内部任务的线程数 |
| requestQueueSize | int | 否 | 默认值 | 用于流量控制的请求队列大小 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.jim.enabled", havingValue = "true")
public class JimdbConfig {
    
    @Bean
    public JimdbHealthIndicator jimdbHealthIndicator() {
        return new JimdbHealthIndicator();
    }
}

// 2. 缓存服务
@Service
public class JimdbCacheService {
    
    @Autowired(required = false)
    private Cluster cluster;
    
    public void set(String key, String value) {
        if (cluster != null) {
            cluster.set(key, value);
        }
    }
    
    public void set(String key, String value, int expireSeconds) {
        if (cluster != null) {
            cluster.setex(key, expireSeconds, value);
        }
    }
    
    public String get(String key) {
        if (cluster != null) {
            return cluster.get(key);
        }
        return null;
    }
    
    public void delete(String key) {
        if (cluster != null) {
            cluster.del(key);
        }
    }
    
    public boolean exists(String key) {
        if (cluster != null) {
            return cluster.exists(key);
        }
        return false;
    }
    
    public void expire(String key, int seconds) {
        if (cluster != null) {
            cluster.expire(key, seconds);
        }
    }
}
```

### 常见场景
```java
// 场景1: 用户会话缓存
@Service
public class UserSessionService {
    
    @Autowired(required = false)
    private Cluster cluster;
    
    private static final String SESSION_PREFIX = "session:";
    private static final int SESSION_EXPIRE = 3600; // 1小时
    
    public void saveSession(String sessionId, UserSession session) {
        if (cluster != null) {
            String key = SESSION_PREFIX + sessionId;
            String value = JSON.toJSONString(session);
            cluster.setex(key, SESSION_EXPIRE, value);
        }
    }
    
    public UserSession getSession(String sessionId) {
        if (cluster != null) {
            String key = SESSION_PREFIX + sessionId;
            String value = cluster.get(key);
            if (value != null) {
                return JSON.parseObject(value, UserSession.class);
            }
        }
        return null;
    }
    
    public void removeSession(String sessionId) {
        if (cluster != null) {
            String key = SESSION_PREFIX + sessionId;
            cluster.del(key);
        }
    }
}

// 场景2: 分布式锁
@Service
public class DistributedLockService {
    
    @Autowired(required = false)
    private Cluster cluster;
    
    public boolean tryLock(String lockKey, String lockValue, int expireSeconds) {
        if (cluster == null) {
            return false;
        }
        
        try {
            // 使用SET NX EX命令实现分布式锁
            String result = cluster.set(lockKey, lockValue, "NX", "EX", expireSeconds);
            return "OK".equals(result);
        } catch (Exception e) {
            log.error("获取分布式锁失败: key={}, error={}", lockKey, e.getMessage());
            return false;
        }
    }
    
    public void releaseLock(String lockKey, String lockValue) {
        if (cluster == null) {
            return;
        }
        
        try {
            // 使用Lua脚本确保原子性释放锁
            String script = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                           "return redis.call('del', KEYS[1]) else return 0 end";
            cluster.eval(script, 1, lockKey, lockValue);
        } catch (Exception e) {
            log.error("释放分布式锁失败: key={}, error={}", lockKey, e.getMessage());
        }
    }
}

// 场景3: 计数器和限流
@Service
public class CounterService {
    
    @Autowired(required = false)
    private Cluster cluster;
    
    public long increment(String key) {
        if (cluster != null) {
            return cluster.incr(key);
        }
        return 0;
    }
    
    public long incrementBy(String key, long value) {
        if (cluster != null) {
            return cluster.incrBy(key, value);
        }
        return 0;
    }
    
    public boolean isRateLimited(String key, int limit, int windowSeconds) {
        if (cluster == null) {
            return false;
        }
        
        try {
            long current = cluster.incr(key);
            if (current == 1) {
                // 第一次访问，设置过期时间
                cluster.expire(key, windowSeconds);
            }
            return current > limit;
        } catch (Exception e) {
            log.error("限流检查失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingUserService {
    
    @Autowired
    private UserRepository userRepository;
    
    // JIMDB缓存服务（可选注入）
    @Autowired(required = false)
    private Cluster cluster;
    
    // 本地缓存作为降级方案
    @Autowired
    private LocalCacheService localCacheService;
    
    private static final String USER_CACHE_PREFIX = "user:";
    private static final int CACHE_EXPIRE = 1800; // 30分钟
    
    public User getUserById(Long userId) {
        String cacheKey = USER_CACHE_PREFIX + userId;
        
        // 1. 先从缓存获取
        User cachedUser = getCachedUser(cacheKey);
        if (cachedUser != null) {
            return cachedUser;
        }
        
        // 2. 缓存未命中，从数据库获取
        User user = userRepository.findById(userId);
        if (user != null) {
            // 3. 更新缓存
            setCachedUser(cacheKey, user);
        }
        
        return user;
    }
    
    public void updateUser(User user) {
        // 1. 更新数据库
        userRepository.save(user);
        
        // 2. 清除缓存
        String cacheKey = USER_CACHE_PREFIX + user.getId();
        deleteCachedUser(cacheKey);
    }
    
    private User getCachedUser(String key) {
        try {
            // 优先使用JIMDB
            if (cluster != null) {
                String value = cluster.get(key);
                if (value != null) {
                    return JSON.parseObject(value, User.class);
                }
            }
        } catch (Exception e) {
            log.warn("JIMDB获取缓存失败，尝试本地缓存: key={}, error={}", key, e.getMessage());
        }
        
        // 降级到本地缓存
        return localCacheService.get(key, User.class);
    }
    
    private void setCachedUser(String key, User user) {
        try {
            // 优先使用JIMDB
            if (cluster != null) {
                String value = JSON.toJSONString(user);
                cluster.setex(key, CACHE_EXPIRE, value);
                return;
            }
        } catch (Exception e) {
            log.warn("JIMDB设置缓存失败，使用本地缓存: key={}, error={}", key, e.getMessage());
        }
        
        // 降级到本地缓存
        localCacheService.put(key, user, Duration.ofSeconds(CACHE_EXPIRE));
    }
    
    private void deleteCachedUser(String key) {
        try {
            if (cluster != null) {
                cluster.del(key);
            }
        } catch (Exception e) {
            log.warn("JIMDB删除缓存失败: key={}, error={}", key, e.getMessage());
        }
        
        // 同时清除本地缓存
        localCacheService.evict(key);
    }
}
```

## 功能特性

### 核心能力
- **高性能**: 基于Redis优化，提供微秒级访问延迟
- **高可用**: 分布式架构，支持故障自动切换
- **在线伸缩**: 支持集群在线扩容和缩容
- **数据安全**: 多副本存储，保证数据可靠性

### 技术特点
- **兼容Redis**: 支持Redis协议和命令
- **动态配置**: 支持运行时配置调整
- **监控完善**: 提供详细的性能监控指标
- **安全可控**: 支持访问控制和数据加密

### 适用场景
- **热点数据缓存**: 频繁访问的数据缓存
- **会话存储**: 用户会话和状态存储
- **分布式锁**: 分布式系统的锁机制
- **计数器**: 访问计数和限流控制

## 注意事项

### 使用约束
- JimUrl包含集群密码，必须妥善保管，避免泄露
- 单个key的value大小建议不超过1MB
- 避免使用过长的key名称，影响性能
- 合理设置过期时间，避免内存溢出

### 性能考虑
- 批量操作优于单个操作，减少网络开销
- 合理使用pipeline提高批量操作性能
- 避免大key操作，可能阻塞其他请求
- 监控内存使用情况，及时清理过期数据

### 安全要求
- 生产环境JimUrl必须使用加密传输
- 定期轮换集群访问密码
- 敏感数据存储前考虑加密
- 监控异常访问模式和大量数据操作

## 最佳实践

1. **Key设计**: 使用有意义的前缀和层次结构，如 `业务:类型:ID`
2. **过期策略**: 为所有key设置合理的过期时间，避免内存泄漏
3. **数据序列化**: 使用JSON格式存储复杂对象，便于调试和兼容
4. **异常处理**: 缓存操作失败时有降级方案，不影响主业务流程
5. **监控告警**: 关注缓存命中率、内存使用率和响应时间
6. **容量规划**: 根据业务增长预估容量需求，提前扩容

## 常见问题

### 配置问题
- **连接失败**: 检查jimUrl格式和网络连通性
- **认证失败**: 确认jimUrl中的密码正确
- **配置不生效**: 检查serviceEndpoint地址是否正确

### 运行时问题
- **内存不足**: 检查key过期时间设置，清理无用数据
- **响应慢**: 检查是否有大key操作，优化数据结构
- **连接超时**: 调整网络超时配置，检查网络稳定性
- **数据丢失**: 检查过期时间设置，确认数据持久化策略

## 环境信息

| 环境 | 服务端点 | 说明 |
|------|----------|------|
| 测试 | http://test.cfs.jim.jd.local | 测试环境元数据服务 |
| 预发 | http://pre.cfs.jim.jd.local | 预发环境元数据服务 |
| 生产 | http://cfs.jim.jd.local | 生产环境元数据服务 |