# 中间件文档维护指南

## 🎯 目标

为企业中间件创建标准化、AI友好的使用文档，确保AI能够准确理解中间件功能并生成正确的集成代码。

## 📋 维护体系

### 文档标准
- **模板文件**: `middleware-template.md` - 标准化模板
- **验证脚本**: `../tools/validate-middleware.sh` - 自动验证工具
- **示例文档**: `jsf.md`, `jmq.md`, `jimdb.md` - 完整示例参考

### 质量要求
- [ ] 包含所有必需章节（基本信息、依赖、配置、代码、功能特性、注意事项）
- [ ] 关键词设置准确，涵盖所有可能的需求表达
- [ ] 代码示例完整可用，包含降级处理机制
- [ ] 现有系统集成友好（条件化注入、功能开关）
- [ ] 通过验证脚本100%验证

## 🚀 标准维护流程

### 新增中间件文档

#### 1. 复制模板文件
```bash
# 进入中间件文档目录
cd middleware-docs/

# 复制模板文件
cp middleware-template.md your-middleware.md
```

#### 2. 填写文档内容
按照以下优先级填写：

**必填项目（AI理解和生成代码必需）**
- [ ] **基本信息**：版本、关键词、适用场景、依赖要求
- [ ] **Maven依赖**：完整的依赖配置
- [ ] **配置**：基础配置、环境变量、参数详解
- [ ] **使用代码**：基础用法、常见场景、集成示例
- [ ] **功能特性**：核心能力、技术特点、适用场景
- [ ] **注意事项**：使用约束、性能考虑、安全要求

**可选项目（整理成本较高，可省略）**
- [ ] 最佳实践：如果整理成本高，可省略
- [ ] 常见问题：如果整理成本高，可省略
- [ ] 环境信息：如果没有环境差异，可省略

#### 3. 关键词设置要点
```markdown
# 关键词设置示例
- **关键词**: RPC, 远程调用, 服务调用, 接口调用, 微服务通信

# 要点：
- 包含所有可能的需求表达方式
- 至少3-5个关键词，覆盖不同的表达习惯
- 考虑技术术语和业务术语的差异
```

#### 4. 代码示例要求
```java
// 必须包含的元素：
@Configuration
@ConditionalOnProperty(name = "dong.middleware.enabled", havingValue = "true")
public class MiddlewareConfig {
    // 配置代码
}

@Service
public class MiddlewareService {
    @Autowired(required = false)  // 条件化注入
    private MiddlewareClient client;
    
    public ReturnType method(ParamType param) {
        try {
            if (client != null) {
                return client.call(param);
            }
        } catch (Exception e) {
            log.warn("中间件调用失败，执行降级: {}", e.getMessage());
        }
        
        // 降级处理
        return fallbackService.fallback(param);
    }
}

// 现有系统集成示例
@Service
public class ExistingBusinessService {
    // 展示如何在不影响现有功能的前提下集成
}
```

#### 5. 验证文档完整性
```bash
# 运行验证脚本
../tools/validate-middleware.sh your-middleware.md

# 确保100%通过验证
```

#### 6. 更新中间件清单
在 `middleware-list.md` 中添加新的中间件条目：
```markdown
## [分类]类
- **文件**: `your-middleware.md`
- **名称**: [中间件名称]
- **关键词**: [关键词列表]
- **适用场景**: [适用场景描述]
```

### 更新现有文档

#### 1. 直接编辑文档
```bash
# 编辑现有文档
vim middleware-name.md
```

#### 2. 验证更新
```bash
# 验证文档完整性
../tools/validate-middleware.sh middleware-name.md
```

#### 3. 测试AI匹配
确保关键词匹配功能正常工作

### 批量验证

#### 验证所有中间件文档
```bash
# 验证所有文档
for file in *.md; do
    if [[ "$file" != "middleware-list.md" && "$file" != "middleware-template.md" && "$file" != "MAINTENANCE_GUIDE.md" ]]; then
        echo "验证 $file..."
        ../tools/validate-middleware.sh "$file"
    fi
done
```

## 📊 质量检查清单

### 核心要求（必须满足）
- [ ] 包含所有必需章节
- [ ] 关键词设置准确，至少3-5个
- [ ] 基础用法包含完整的配置类和服务类
- [ ] 所有代码示例可以直接使用
- [ ] 包含现有系统集成示例
- [ ] 包含降级处理机制
- [ ] 通过验证脚本100%验证

### 集成安全性（重要）
- [ ] 使用条件化注入 `@Autowired(required = false)`
- [ ] 使用条件化配置 `@ConditionalOnProperty`
- [ ] 支持功能开关控制
- [ ] 配置使用环境变量
- [ ] 不影响现有功能

### AI友好性（关键）
- [ ] 结构标准化，便于AI解析
- [ ] 关键词精准，提高匹配准确性
- [ ] 代码完整，可直接使用
- [ ] 场景覆盖，涵盖常用需求

## 🚨 常见错误和避免方法

### 1. 关键词不准确
**错误**: 只设置技术术语，忽略业务表达
**正确**: 包含技术术语和业务表达，如"RPC, 远程调用, 服务调用"

### 2. 代码不完整
**错误**: 只提供接口定义，缺少实现细节
**正确**: 提供完整的配置类、服务类和使用示例

### 3. 缺少降级处理
**错误**: 只考虑中间件正常工作的情况
**正确**: 包含异常处理和降级方案

### 4. 配置硬编码
**错误**: 配置值写死在代码中
**正确**: 使用环境变量和配置开关

### 5. 章节结构不标准
**错误**: 自定义章节名称和结构
**正确**: 严格按照模板结构组织内容

## 🔧 工具使用

### 验证脚本功能
```bash
# 基本验证
./validate-middleware.sh middleware-file.md

# 验证脚本检查项目：
# - 必需章节完整性
# - 基本信息内容
# - 关键词数量和质量
# - Maven依赖配置
# - 配置内容结构
# - 代码示例数量和质量
# - 现有系统集成友好性
# - 功能特性描述
# - 注意事项说明
# - 文档结构正确性
```

### 模板文件使用
```bash
# 模板文件包含：
# - 完整的章节结构
# - 详细的填写说明
# - 代码示例模板
# - 占位符标记
```

## 📞 支持和帮助

### 参考资源
1. **模板文件**: `middleware-template.md` - 详细的填写模板
2. **示例文档**: `jsf.md`, `jmq.md`, `jimdb.md` - 完整示例参考
3. **验证脚本**: `../tools/validate-middleware.sh` - 自动验证工具

### 问题解决
1. **验证失败**: 参考示例文档，检查缺失项目
2. **关键词匹配**: 测试不同的需求表达方式
3. **代码问题**: 确保代码可以编译和运行
4. **集成问题**: 参考现有系统集成示例

通过遵循这个维护指南，可以确保创建出高质量、AI友好的中间件文档，为Java Context Engineering模板提供强大的企业中间件支持。
