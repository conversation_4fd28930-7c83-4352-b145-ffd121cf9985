# DUCC - 统一配置中心

## 基本信息
- **版本**: 
- **关键词**: 配置, 参数, 开关, 环境变量, 配置中心, 动态配置
- **适用场景**: 集中化配置管理和动态配置更新
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>ducc-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  ducc:
    enabled: ${DUCC_ENABLED:false}  # 功能开关
    manager:
      application: ${DUCC_APPLICATION:}  # 应用名称标识
      resources:
        - name: ${DUCC_RESOURCE_NAME:}  # 资源名称
          uri: ${DUCC_URI:}  # DUCC配置中心的连接URI
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DUCC_ENABLED` | 是否启用DUCC | false | true |
| `DUCC_APPLICATION` | 应用名称标识 |  | jdos_dongboot |
| `DUCC_RESOURCE_NAME` | 资源名称 |  | jdos_dongboot |
| `DUCC_URI` | DUCC配置中心连接URI |  | ucc://jdos_dongboot:<EMAIL>/v1/namespace/dongboot/config/config/profiles/test?longPolling=60000&necessary=true |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| application | String | 是 |  | 应用名称标识 |
| resources[].name | String | 是 |  | 资源名称 |
| resources[].uri | String | 是 |  | DUCC配置中心的连接URI，包含应用标识、密钥、API地址、命名空间等信息 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.ducc.enabled", havingValue = "true")
public class DuccConfig {

    @Bean
    public DuccClient duccClient() {
        return new DuccClient();
    }

    @Bean
    public DuccHealthIndicator duccHealthIndicator() {
        return new DuccHealthIndicator();
    }
}

// 2. 服务类
@Service
public class DuccService {

    @Autowired(required = false)
    private DuccClient duccClient;

    @Autowired
    private LocalConfigService localConfigService; // 降级服务

    public String getConfig(String key) {
        try {
            if (duccClient != null) {
                return duccClient.getConfig(key);
            }
        } catch (Exception e) {
            log.warn("DUCC调用失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        return localConfigService.getConfig(key);
    }

    public boolean isAvailable() {
        return duccClient != null;
    }
}
```

### 常见场景
```java
// 场景1: 配置值注入
@Service
public class ConfigInjectService {

    @LafValue("dongboot.ducc.test")
    private String configValue;

    @Autowired(required = false)
    private DuccClient duccClient;

    public String getConfigValue() {
        // 使用注解注入的配置值
        if (configValue != null) {
            return configValue;
        }

        // 降级逻辑
        return "default-value";
    }
}

// 场景2: 动态配置获取
@Service
public class DynamicConfigService {

    @Autowired(required = false)
    private DuccClient duccClient;

    public String getDynamicConfig(String key) {
        // 动态获取配置值
        try {
            if (duccClient != null) {
                return duccClient.getConfig(key);
            }
        } catch (Exception e) {
            log.error("动态配置获取失败: {}", e.getMessage());
        }

        return null;
    }
}

// 场景3: 配置变更监听
@Service
public class ConfigChangeService {

    @Autowired(required = false)
    private DuccClient duccClient;

    public void watchConfigChange(String key) {
        // 监听配置变更
        if (duccClient != null) {
            try {
                duccClient.watchConfig(key, newValue -> {
                    log.info("配置{}发生变更: {}", key, newValue);
                    // 处理配置变更逻辑
                });
            } catch (Exception e) {
                log.warn("配置变更监听失败，但不影响主流程: {}", e.getMessage());
            }
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DUCC服务（可选注入，支持降级）
    @Autowired(required = false)
    private DuccClient duccClient;

    // 降级服务
    @Autowired
    private LocalConfigService localConfigService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 2. 使用DUCC获取配置增强功能（新增，支持降级）
        enhanceWithConfig(result);
        
        return result;
    }
    
    private void enhanceWithConfig(BusinessObject object) {
        try {
            if (duccClient != null) {
                // 使用DUCC获取配置
                String configValue = duccClient.getConfig("business.config.key");
                object.setConfigValue(configValue);
            } else {
                // 降级到本地配置
                String configValue = localConfigService.getConfig("business.config.key");
                object.setConfigValue(configValue);
            }
        } catch (Exception e) {
            log.warn("配置获取失败，使用默认值: {}", e.getMessage());
            object.setConfigValue("default-value");
        }
    }
}
```

## 功能特性

### 核心能力
- **集中存储**: 统一管理应用配置信息
- **动态更新**: 支持配置的实时更新和推送
- **版本控制**: 提供配置版本管理和回滚功能

### 技术特点
- **长轮询**: 支持长轮询机制，实时获取配置变更
- **推送通知**: 配置变更时主动推送给客户端
- **缓存机制**: 本地缓存配置，提高访问性能

### 适用场景
- **环境隔离**: 支持多环境配置隔离管理
- **权限控制**: 支持细粒度的配置访问权限控制
- **审计日志**: 记录配置变更的完整审计信息

## 注意事项

### 使用约束
- 配置URI包含敏感信息，需要妥善保管
- 配置变更需要考虑对业务的影响

### 性能考虑
- 本地缓存机制减少网络调用
- 长轮询避免频繁的配置检查

### 安全要求
- 应用密钥需要安全存储，避免泄露
- 配置访问需要权限控制

## 最佳实践

1. **配置命名**: 使用有意义的配置key，便于管理和维护
2. **环境隔离**: 不同环境使用不同的配置文件和命名空间
3. **安全管理**: 妥善保管应用密钥，避免泄露
4. **监控告警**: 关注配置变更和应用状态，及时处理异常
5. **版本管理**: 重要配置变更前做好备份，支持快速回滚
