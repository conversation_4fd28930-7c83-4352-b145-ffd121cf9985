# JSF - 服务框架

## 基本信息
- **版本**: 3.2.1 (dong-boot-starter)
- **关键词**: R<PERSON>, 远程调用, 服务调用, 接口调用, 微服务通信, 分布式服务
- **适用场景**: 分布式系统间的高性能RPC通信和服务治理
- **依赖要求**: JDK 8+, Spring Boot 2.7.0+

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>jsf-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  jsf:
    enabled: ${JSF_ENABLED:false}  # 功能开关
    # 注册中心配置
    registry:
      index: ${JSF_REGISTRY_INDEX:test.i.jsf.jd.local}
    # 服务端配置
    servers:
      myserver:
        protocol: jsf
        host: 127.0.0.1
        port: ${JSF_SERVER_PORT:22000}
        threads: 200
    # 消费者配置
    consumer:
      timeout: 3000
      retries: 2
      loadbalance: random
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `JSF_ENABLED` | 是否启用JSF | false | true |
| `JSF_REGISTRY_INDEX` | 注册中心地址 | test.i.jsf.jd.local | test.i.jsf.jd.local |
| `JSF_SERVER_PORT` | 服务提供者端口 | 22000 | 22000 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| timeout | int | 否 | 5000 | 服务调用超时时间(毫秒) |
| serialization | string | 否 | msgpack | 序列化方式(hessian/msgpack) |
| retries | int | 否 | 2 | 重试次数 |
| loadbalance | string | 否 | random | 负载均衡策略 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.jsf.enabled", havingValue = "true")
public class JSFConfig {

    @Bean
    public JSFHealthIndicator jsfHealthIndicator() {
        return new JSFHealthIndicator();
    }
}

// 2. 服务提供者
@Service
@BootService(uniqueId = "userServiceImpl", bindings = {
    @BootServiceBinding(alias = "user-service", server = {"myserver"}, bindingType = "jsf")
})
public class UserServiceImpl implements UserService {

    @Override
    public User getUserById(Long userId) {
        return userRepository.findById(userId);
    }

    @Override
    public List<User> getUsersByIds(List<Long> userIds) {
        return userRepository.findByIdIn(userIds);
    }
}

// 3. 服务消费者
@Service
public class OrderService {

    @BootReference(
        uniqueId = "userService",
        binding = @BootReferenceBinding(
            alias = "user-service",
            bindingType = "jsf",
            timeout = 3000,
            retries = 2
        ),
        required = false
    )
    @Autowired(required = false)
    private UserService userService;
    
    public Order createOrder(CreateOrderRequest request) {
        // 远程调用用户服务
        User user = userService.getUserById(request.getUserId());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        return orderRepository.save(new Order(request, user));
    }
}
```

### 常见场景
```java
// 场景1: 批量调用优化
@Service
public class UserBatchService {
    
    @BootReference(uniqueId = "userService", binding = @BootReferenceBinding(alias = "user-service", bindingType = "jsf"))
    private UserService userService;
    
    public Map<Long, User> getUserMap(List<Long> userIds) {
        // 批量获取用户信息，减少RPC调用次数
        List<User> users = userService.getUsersByIds(userIds);
        return users.stream().collect(Collectors.toMap(User::getId, Function.identity()));
    }
}

// 场景2: 异步调用
@Service
public class AsyncUserService {
    
    @BootReference(uniqueId = "userService", binding = @BootReferenceBinding(alias = "user-service", bindingType = "jsf"))
    private UserService userService;
    
    @Async
    public CompletableFuture<User> getUserAsync(Long userId) {
        User user = userService.getUserById(userId);
        return CompletableFuture.completedFuture(user);
    }
}

// 场景3: 服务降级
@Service
public class UserServiceWithFallback {
    
    @BootReference(uniqueId = "userService", binding = @BootReferenceBinding(alias = "user-service", bindingType = "jsf"))
    private UserService userService;
    
    public User getUserWithFallback(Long userId) {
        try {
            return userService.getUserById(userId);
        } catch (Exception e) {
            log.warn("JSF调用失败，返回默认用户: {}", e.getMessage());
            return createDefaultUser(userId);
        }
    }
    
    private User createDefaultUser(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setName("默认用户");
        return user;
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    // JSF服务调用（可选注入，支持降级）
    @BootReference(
        uniqueId = "userService",
        binding = @BootReferenceBinding(alias = "user-service", bindingType = "jsf"),
        required = false
    )
    private UserService userService;
    
    // HTTP降级服务
    @Autowired
    private HttpUserService httpUserService;
    
    public Order createOrder(CreateOrderRequest request) {
        // 1. 用户验证（支持降级）
        User user = getUserWithFallback(request.getUserId());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 2. 创建订单（现有逻辑不变）
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setUserName(user.getName());
        order.setAmount(request.getAmount());
        
        return orderRepository.save(order);
    }
    
    private User getUserWithFallback(Long userId) {
        try {
            // 优先使用JSF调用
            if (userService != null) {
                return userService.getUserById(userId);
            }
        } catch (Exception e) {
            log.warn("JSF调用失败，降级到HTTP调用: {}", e.getMessage());
        }
        
        // 降级到HTTP调用
        return httpUserService.getUserById(userId);
    }
}
```

## 功能特性

### 核心能力
- **高性能RPC**: 基于NIO的高性能远程过程调用
- **服务治理**: 服务注册发现、负载均衡、故障转移
- **多种序列化**: 支持hessian、msgpack、json等序列化方式
- **协议支持**: 支持JSF、HTTP等多种通信协议

### 技术特点
- **高并发**: 支持大规模并发调用
- **低延迟**: 优化的网络通信和序列化
- **高可用**: 集群部署和自动故障检测
- **易扩展**: 支持自定义协议和序列化

### 适用场景
- **微服务架构**: 微服务间的通信调用
- **分布式系统**: 跨系统的服务调用
- **高并发场景**: 需要高性能RPC调用的业务
- **服务治理**: 需要统一服务管理的场景

## 注意事项

### 使用约束
- 接口参数和返回值必须可序列化
- 服务接口应保持向后兼容性
- 避免在接口中传递过大的对象
- 生产环境必须配置注册中心

### 性能考虑
- 合理设置超时时间，避免长时间阻塞
- 使用批量接口减少RPC调用次数
- 选择合适的序列化方式平衡性能和兼容性
- 合理配置线程池大小

### 安全要求
- 生产环境使用token进行服务认证
- 敏感数据传输时考虑加密
- 定期更新服务密钥
- 监控异常调用和访问模式

## 最佳实践

1. **接口设计**: 使用DTO对象，避免直接传递Entity，保持接口稳定性
2. **异常处理**: 定义业务异常类，在服务端统一处理，客户端做好降级
3. **版本管理**: 做好接口版本管理，新版本保持向后兼容
4. **监控告警**: 关注服务调用成功率、响应时间和异常情况
5. **连接管理**: 合理配置连接池，避免连接泄漏
6. **测试策略**: 做好单元测试和集成测试，使用Mock进行测试隔离

## 常见问题

### 配置问题
- **服务注册失败**: 检查注册中心地址和网络连通性
- **端口冲突**: 确保服务端口未被占用
- **配置不生效**: 检查配置文件格式和参数名称

### 运行时问题
- **调用超时**: 调整timeout配置，检查网络和服务性能
- **序列化异常**: 确保传输对象实现Serializable接口
- **负载均衡异常**: 检查服务提供者状态和负载均衡策略
- **连接异常**: 检查网络连接和防火墙设置

## 环境信息

| 环境 | 注册中心地址 | 说明 |
|------|-------------|------|
| 测试 | test.i.jsf.jd.local | 测试环境注册中心 |
| 预发 | pre.i.jsf.jd.local | 预发环境注册中心 |
| 生产 | i.jsf.jd.com | 生产环境注册中心 |