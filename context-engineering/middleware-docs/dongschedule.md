# DongSchedule - 任务调度框架

## 基本信息
- **版本**: 1.0.6+
- **关键词**: 任务调度, 定时任务, 分片任务, MapReduce, 任务管理
- **适用场景**: 集中化任务调度和监控管理
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>schedule-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  schedule:
    enabled: ${DONGSCHEDULE_ENABLED:false}  # 功能开关
    executor:
      system: ${SCHEDULE_SYSTEM:EMPTY}  # 应用通过行云部署则无需配置
      app: ${SCHEDULE_APP:EMPTY}        # 应用通过行云部署则无需配置
      group: ${SCHEDULE_GROUP:EMPTY}    # 应用通过行云部署则无需配置
      port: ${SCHEDULE_PORT:9527}       # 执行器端口
      accessToken: ${SCHEDULE_ACCESS_TOKEN:default_token}  # 访问令牌
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DONGSCHEDULE_ENABLED` | 是否启用DongSchedule | false | true |
| `SCHEDULE_SYSTEM` | 系统标识 | EMPTY | order-system |
| `SCHEDULE_APP` | 应用标识 | EMPTY | order-service |
| `SCHEDULE_GROUP` | 分组标识 | EMPTY | business-group |
| `SCHEDULE_PORT` | 执行器端口 | 9527 | 9527 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| executor.system | String | 否 | EMPTY | 系统标识，行云部署时自动获取 |
| executor.app | String | 否 | EMPTY | 应用标识，行云部署时自动获取 |
| executor.group | String | 否 | EMPTY | 分组标识，行云部署时自动获取 |
| executor.port | Integer | 否 | 9527 | 执行器监听端口 |
| executor.accessToken | String | 否 | default_token | 访问令牌 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.schedule.enabled", havingValue = "true")
public class DongScheduleConfig {

    @Bean
    public DongScheduleExecutor dongScheduleExecutor() {
        return new DongScheduleExecutor();
    }

    @Bean
    public DongScheduleHealthIndicator dongScheduleHealthIndicator() {
        return new DongScheduleHealthIndicator();
    }
}

// 2. 基础任务示例
@Component
@DongJob(name = "demoJob")
public class SampleJob extends AbstractJobHandler {
    
    @Override
    public JobResult execute(JobContext jobContext) {
        try {
            // 业务逻辑处理
            log.info("任务开始执行: jobId={}", jobContext.getJobId());
            
            // 执行具体业务
            processBusinessLogic();
            
            log.info("任务执行成功: jobId={}", jobContext.getJobId());
            return JobResult.SUCCESS;
        } catch (Exception e) {
            log.error("任务执行失败: jobId={}, error={}", jobContext.getJobId(), e.getMessage(), e);
            return new JobResult(500, "任务执行失败: " + e.getMessage());
        }
    }
    
    private void processBusinessLogic() {
        // 具体业务逻辑
    }
}
```

### 常见场景
```java
// 场景1: 分片任务
@Component
@DongJob(name = "shardJob")
public class ShardJob extends AbstractJobHandler {
    
    @Override
    public JobResult execute(JobContext jobContext) {
        // 获取分片信息
        int total = jobContext.getShardTotal();  // 总分片数
        int shard = jobContext.getShardIndex();  // 当前分片索引
        
        log.info("分片任务执行: total={}, shard={}", total, shard);
        
        try {
            // 根据分片信息处理数据
            processShardData(shard, total);
            
            return JobResult.SUCCESS;
        } catch (Exception e) {
            log.error("分片任务执行失败: shard={}, error={}", shard, e.getMessage(), e);
            return new JobResult(500, "分片任务执行失败");
        }
    }
    
    private void processShardData(int shard, int total) {
        // 根据分片处理数据
        // 例如：处理ID % total == shard 的数据
    }
}

// 场景2: MapReduce任务
@Component
@DongJob(name = "mapReduceJob")
public class MapReduceJob extends AbstractJobHandler {
    
    @Override
    public MapResult map(JobContext jobContext) throws Exception {
        List<MapResult.Task> taskList = new ArrayList<>();
        
        // 查询需要处理的数据范围
        Pair<Integer, Integer> idRange = queryDataRange();
        Integer minId = idRange.getFirst();
        Integer maxId = idRange.getSecond();
        
        // 根据执行器数量分割任务
        int step = 1000; // 每个子任务处理1000条数据
        
        for (int i = minId; i <= maxId; i += step) {
            int endId = Math.min(i + step - 1, maxId);
            
            // 创建子任务
            DataRange range = new DataRange(i, endId);
            MapResult.Task task = MapResult.Task.ParamBuilder.build()
                .data(JsonUtils.toJson(range))
                .create();
            taskList.add(task);
        }
        
        log.info("Map阶段完成，生成{}个子任务", taskList.size());
        return new MapResult(taskList);
    }
    
    @Override
    public JobResult execute(JobContext jobContext) {
        try {
            // 获取子任务参数
            String dynamicParam = jobContext.getDynamicParam();
            DataRange range = JsonUtils.fromJson(dynamicParam, DataRange.class);
            
            log.info("处理数据范围: startId={}, endId={}", range.getStartId(), range.getEndId());
            
            // 处理指定范围的数据
            processDataRange(range);
            
            return JobResult.SUCCESS;
        } catch (Exception e) {
            log.error("子任务执行失败: error={}", e.getMessage(), e);
            return new JobResult(500, "子任务执行失败");
        }
    }
    
    private Pair<Integer, Integer> queryDataRange() {
        // 查询数据库获取ID范围
        return new Pair<>(1, 10000);
    }
    
    private void processDataRange(DataRange range) {
        // 处理指定范围的数据
    }
    
    private static class DataRange {
        private int startId;
        private int endId;
        
        public DataRange(int startId, int endId) {
            this.startId = startId;
            this.endId = endId;
        }
        
        // getters and setters
    }
}

// 场景3: 定时数据同步任务
@Component
@DongJob(name = "dataSyncJob")
public class DataSyncJob extends AbstractJobHandler {
    
    @Autowired
    private DataSyncService dataSyncService;
    
    @Override
    public JobResult execute(JobContext jobContext) {
        try {
            // 获取任务参数
            String params = jobContext.getExecutorParams();
            SyncConfig config = JsonUtils.fromJson(params, SyncConfig.class);
            
            log.info("开始数据同步: source={}, target={}", config.getSource(), config.getTarget());
            
            // 执行数据同步
            SyncResult result = dataSyncService.syncData(config);
            
            log.info("数据同步完成: syncCount={}, errorCount={}", 
                result.getSyncCount(), result.getErrorCount());
            
            return JobResult.SUCCESS;
        } catch (Exception e) {
            log.error("数据同步失败: error={}", e.getMessage(), e);
            return new JobResult(500, "数据同步失败");
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DongSchedule任务（可选注入，支持降级）
    @Component
    @DongJob(name = "businessCleanupJob")
    public static class BusinessCleanupJob extends AbstractJobHandler {
        
        @Autowired
        private ExistingBusinessService businessService;
        
        @Override
        public JobResult execute(JobContext jobContext) {
            try {
                // 调用现有业务服务进行数据清理
                int cleanupCount = businessService.cleanupExpiredData();
                
                log.info("数据清理完成: cleanupCount={}", cleanupCount);
                return JobResult.SUCCESS;
            } catch (Exception e) {
                log.error("数据清理失败: error={}", e.getMessage(), e);
                return new JobResult(500, "数据清理失败");
            }
        }
    }

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 2. 业务逻辑处理
        return result;
    }
    
    // 3. 新增：定时清理过期数据的方法
    public int cleanupExpiredData() {
        // 清理过期数据的业务逻辑
        LocalDateTime expireTime = LocalDateTime.now().minusDays(30);
        return businessRepository.deleteExpiredData(expireTime);
    }
}
```

## 功能特性

### 核心能力
- **集中化管理**: 统一的任务调度平台，可视化任务配置和监控
- **高可用性**: 分布式部署支持，故障转移机制，任务重试机制
- **灵活调度**: 支持Cron表达式、固定频率调度、手动触发执行

### 技术特点
- **分片处理**: 大数据量任务分片处理，动态分片调整，负载均衡
- **MapReduce**: 支持MapReduce模式处理大数据量任务
- **监控告警**: 集中式日志管理，任务执行状态监控

### 适用场景
- **定时任务**: 定期执行的业务处理任务
- **数据处理**: 大数据量的批处理任务
- **系统维护**: 数据清理、备份等维护任务

## 注意事项

### 使用约束
- 任务需要保持幂等性，避免重复执行产生副作用
- JobResult必须设置正确的code，200表示成功，其他表示失败
- 行云部署时会自动获取system、app、group参数

### 性能考虑
- 合理设置超时时间和并发数量
- 大数据量处理使用分片或MapReduce模式
- 监控任务执行状态和性能指标

### 安全要求
- 访问令牌需要安全配置
- 任务执行权限控制

## 最佳实践

1. **任务设计**: 保持任务的幂等性，合理处理异常情况
2. **性能优化**: 对于大数据量处理，合理使用分片和MapReduce模式
3. **监控告警**: 关注任务执行状态、耗时和成功率
4. **资源管理**: 合理配置超时时间和并发数量
5. **日志记录**: 记录关键业务日志，便于问题排查
6. **异常处理**: 完善的异常处理和错误码返回
