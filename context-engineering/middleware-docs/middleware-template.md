# [中间件名称] - [简短描述]

## 基本信息
- **版本**: [当前版本号]
- **关键词**: [关键词1, 关键词2, 关键词3, 关键词4, 关键词5]
- **适用场景**: [一句话描述主要用途]
- **依赖要求**: [JDK版本要求, Spring Boot版本要求]

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>[artifact-id]</artifactId>
    <version>[version]</version>
</dependency>
```

**版本说明**: [版本兼容性说明，如：使用DongBoot统一管理版本，无需指定具体版本号]

## 配置

### 基础配置
```yaml
dong:
  [middleware-name]:
    enabled: ${[MIDDLEWARE_NAME]_ENABLED:false}  # 功能开关
    # 主要配置项
    [config-key1]: ${[ENV_VAR1]:[default-value1]}
    [config-key2]: ${[ENV_VAR2]:[default-value2]}

    # 子配置组（如适用）
    [sub-config]:
      [sub-key1]: [value1]
      [sub-key2]: [value2]
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `[MIDDLEWARE_NAME]_ENABLED` | 是否启用[中间件名称] | false | true |
| `[ENV_VAR1]` | [变量1说明] | [默认值1] | [示例值1] |
| `[ENV_VAR2]` | [变量2说明] | [默认值2] | [示例值2] |
| `[ENV_VAR3]` | [变量3说明] | [默认值3] | [示例值3] |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| [param1] | [type1] | [是/否] | [default1] | [详细说明1] |
| [param2] | [type2] | [是/否] | [default2] | [详细说明2] |
| [param3] | [type3] | [是/否] | [default3] | [详细说明3] |
| [param4] | [type4] | [是/否] | [default4] | [详细说明4] |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.[middleware-name].enabled", havingValue = "true")
public class [MiddlewareName]Config {

    @Bean
    public [MiddlewareClient] [middlewareClient]() {
        return new [MiddlewareClient]();
    }

    @Bean
    public [MiddlewareName]HealthIndicator [middlewareName]HealthIndicator() {
        return new [MiddlewareName]HealthIndicator();
    }
}

// 2. 服务类
@Service
public class [MiddlewareName]Service {

    @Autowired(required = false)
    private [MiddlewareClient] [middlewareClient];

    @Autowired
    private [FallbackService] fallbackService; // 降级服务

    public [ReturnType] [mainMethod]([ParamType] param) {
        try {
            if ([middlewareClient] != null) {
                return [middlewareClient].[clientMethod](param);
            }
        } catch (Exception e) {
            log.warn("[中间件名称]调用失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        return fallbackService.[fallbackMethod](param);
    }

    public boolean isAvailable() {
        return [middlewareClient] != null;
    }
}
```

### 常见场景
```java
// 场景1: [场景1描述]
@Service
public class [Scenario1]Service {

    @Autowired(required = false)
    private [MiddlewareClient] [middlewareClient];

    public [ReturnType1] [scenario1Method]([ParamType1] param) {
        // [场景1的具体实现代码]
        if ([middlewareClient] != null) {
            // 使用中间件的逻辑
            return [middlewareClient].[method1](param);
        }

        // 降级逻辑
        return [defaultValue1];
    }
}

// 场景2: [场景2描述]
@Service
public class [Scenario2]Service {

    @Autowired(required = false)
    private [MiddlewareClient] [middlewareClient];

    public [ReturnType2] [scenario2Method]([ParamType2] param) {
        // [场景2的具体实现代码]
        try {
            if ([middlewareClient] != null) {
                return [middlewareClient].[method2](param);
            }
        } catch (Exception e) {
            log.error("[场景2]执行失败: {}", e.getMessage());
        }

        return [defaultValue2];
    }
}

// 场景3: [场景3描述]
@Service
public class [Scenario3]Service {

    @Autowired(required = false)
    private [MiddlewareClient] [middlewareClient];

    public void [scenario3Method]([ParamType3] param) {
        // [场景3的具体实现代码]
        if ([middlewareClient] != null) {
            try {
                [middlewareClient].[method3](param);
                log.info("[场景3]执行成功");
            } catch (Exception e) {
                log.warn("[场景3]执行失败，但不影响主流程: {}", e.getMessage());
            }
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private [ExistingRepository] [existingRepository];

    // [中间件名称]服务（可选注入，支持降级）
    @Autowired(required = false)
    private [MiddlewareClient] [middlewareClient];

    // 降级服务
    @Autowired
    private [LocalService] localService;

    public [BusinessObject] [businessMethod]([BusinessRequest] request) {
        // 1. 执行现有业务逻辑（保持不变）
        [BusinessObject] result = [existingRepository].[existingMethod](request);

        // 2. 使用中间件增强功能（新增，支持降级）
        [enhanceWithMiddleware](result);

        return result;
    }

    private void [enhanceWithMiddleware]([BusinessObject] object) {
        try {
            if ([middlewareClient] != null) {
                // 使用中间件增强
                [middlewareClient].[enhanceMethod](object);
                log.info("[中间件名称]增强成功: {}", object.getId());
            } else {
                // 降级到本地处理
                localService.[localEnhanceMethod](object);
                log.info("使用本地服务增强: {}", object.getId());
            }
        } catch (Exception e) {
            log.warn("[中间件名称]增强失败，不影响主流程: {}", e.getMessage());
            // 增强失败不影响主业务流程
        }
    }
}
        [ExistingEntity] entity = existingRepository.findById(param.getId());
        
        // 2. 新增中间件功能（支持降级）
        [processWithMiddleware](entity);
        
        return entity;
    }
    
    private void [processWithMiddleware]([ExistingEntity] entity) {
        try {
            if ([middlewareClient] != null) {
                // 使用中间件处理
                [middlewareClient].[method](entity);
            } else {
                // 降级到备用方案
                [fallbackService].[method](entity);
            }
        } catch (Exception e) {
            log.warn("中间件处理失败，使用降级方案: {}", e.getMessage());
            [fallbackService].[method](entity);
        }
    }
}
```

## 功能特性

### 核心能力
- **[特性1]**: [详细说明]
- **[特性2]**: [详细说明]
- **[特性3]**: [详细说明]

### 技术特点
- **[技术点1]**: [说明]
- **[技术点2]**: [说明]

### 适用场景
- **[场景1]**: [说明]
- **[场景2]**: [说明]

## 注意事项

### 使用约束
- [约束1]: [说明]
- [约束2]: [说明]

### 性能考虑
- [性能点1]: [说明]
- [性能点2]: [说明]

### 安全要求
- [安全点1]: [说明]
- [安全点2]: [说明]

## 最佳实践

1. **[实践1]**: [详细说明]
2. **[实践2]**: [详细说明]
3. **[实践3]**: [详细说明]

## 常见问题

### 配置问题
- **[问题1]**: [解决方案]
- **[问题2]**: [解决方案]

### 运行时问题
- **[问题1]**: [解决方案]
- **[问题2]**: [解决方案]

## 环境信息

| 环境 | 地址/配置 | 说明 |
|------|-----------|------|
| 测试 | [test-config] | [说明] |
| 预发 | [pre-config] | [说明] |
| 生产 | [prod-config] | [说明] |

---

## 📝 填写说明

### 必填项目（AI理解和生成代码必需）
- [ ] 基本信息：版本、关键词、适用场景、依赖要求
- [ ] Maven依赖：完整的依赖配置
- [ ] 配置：基础配置、环境变量、参数详解
- [ ] 使用代码：基础用法、常见场景、集成示例
- [ ] 功能特性：核心能力、技术特点、适用场景
- [ ] 注意事项：使用约束、性能考虑、安全要求

### 可选项目（整理成本较高，可省略，建议尽量完善，提升AI生成质量）
- [ ] 最佳实践：如果整理成本高，可省略
- [ ] 常见问题：如果整理成本高，可省略
- [ ] 环境信息：如果没有环境差异，可省略

### 填写要点

#### 关键词设置（重要）
- 包含所有可能的需求表达方式
- 至少3-5个关键词，覆盖不同的表达习惯
- 示例：RPC → "RPC, 远程调用, 服务调用, 接口调用, 微服务通信"

#### 代码示例要求
- 所有代码必须可以直接使用
- 必须包含降级处理机制
- 使用 `@Autowired(required = false)` 条件化注入
- 包含异常处理和日志记录

#### 现有系统集成友好性
- 展示如何在不影响现有功能的前提下集成
- 提供完整的降级方案
- 使用功能开关控制

### 填写完成后
1. 删除所有 `[占位符]` 和本填写说明
2. 运行 `./tools/validate-middleware.sh [文件名].md` 验证
3. 参考已完成的示例文档进行对比检查
4. 确保关键词匹配测试正确