# SSO - 单点登录系统

## 基本信息
- **版本**: 1.0.9-SNAPSHOT
- **关键词**: 单点登录, SSO, 用户认证, 权限控制, 登录拦截
- **适用场景**: 统一用户认证和单点登录
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.ssa</groupId>
    <artifactId>oidc-client</artifactId>
    <version>1.0.9-SNAPSHOT</version>
</dependency>
```

**版本说明**: 使用指定版本1.0.9-SNAPSHOT

## 配置

### 基础配置
```yaml
sso:
  enabled: ${SSO_ENABLED:false}  # 功能开关
  clientId: ${SSO_CLIENT_ID:test3}  # 客户端ID
  clientSecret: ${SSO_CLIENT_SECRET:347c6161e79f4b6a8873202dd5fe7e8f}  # 客户端密钥
  endpoint: ${SSO_ENDPOINT:http://test.ssa.jd.com}  # SSO鉴权地址
  serviceIndex: ${SSO_SERVICE_INDEX:test.i.jsf.jd.local}  # JSF服务地址
  serviceAlias: ${SSO_SERVICE_ALIAS:bj-test}  # JSF别名
  excludePath: ${SSO_EXCLUDE_PATH:/static,/res,/login,/logout}  # 排除路径
  oauth2ParamPrefixPolicy: ${SSO_OAUTH2_PARAM_PREFIX_POLICY:Oidc}  # OAuth2参数前缀策略
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `SSO_ENABLED` | 是否启用SSO | false | true |
| `SSO_CLIENT_ID` | 客户端ID | test3 | your-app-id |
| `SSO_CLIENT_SECRET` | 客户端密钥 | 347c6161e79f4b6a8873202dd5fe7e8f | your-secret |
| `SSO_ENDPOINT` | SSO鉴权地址 | http://test.ssa.jd.com | https://ssa.jd.com |
| `SSO_SERVICE_INDEX` | JSF服务地址 | test.i.jsf.jd.local | i.jsf.jd.com |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| clientId | String | 是 | test3 | 应用在SSO系统中的客户端ID |
| clientSecret | String | 是 | 347c6161e79f4b6a8873202dd5fe7e8f | 客户端密钥 |
| endpoint | String | 是 | http://test.ssa.jd.com | SSO鉴权服务地址 |
| serviceIndex | String | 是 | test.i.jsf.jd.local | JSF注册中心地址 |
| serviceAlias | String | 是 | bj-test | JSF服务别名 |
| excludePath | String | 否 |  | 不需要登录验证的路径，多个路径用逗号分隔 |

## 使用代码

### 基础用法
```java
// 1. SSO拦截器配置
import com.jd.ssa.oidc.client.interceptor.ErpSsoInterceptor;

@Component
public class SpringSSOInterceptor extends ErpSsoInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果是OPTIONS请求，直接返回false，不进行拦截
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return false;
        }
        
        // 处理登出请求
        if (request.getRequestURI().startsWith("/logout") ||
                request.getRequestURI().startsWith("/api/logout")) {
            this.toLogout(request, response);
            return false;
        }
        
        // 检查是否为排除路径
        if (this.isExclude(request.getRequestURI())) {
            return true;
        }
        
        // 调用SSO拦截器的preHandle方法
        return super.preHandle(request, response, handler);
    }
}

// 2. 拦截器配置类
@Configuration
@ConditionalOnProperty(name = "sso.enabled", havingValue = "true")
public class SpringSSOInterceptorConfig implements WebMvcConfigurer {
    
    @Autowired
    private Environment env;
     
    @Bean
    public SpringSSOInterceptor springSSOInterceptor() {
        SpringSSOInterceptor interceptor = new SpringSSOInterceptor();
        interceptor.setEnvironment(this.env);
        try {
            interceptor.afterPropertiesSet();
        } catch (Exception e) {
            throw new RuntimeException("SSO拦截器初始化失败", e);
        }
        return interceptor;
    }
 
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(springSSOInterceptor()).addPathPatterns("/**");
    }
}
```

### 常见场景
```java
// 场景1: 获取登录用户信息
@RestController
@RequestMapping("/api/user")
public class UserController {

    @GetMapping("/info")
    public Result<UserInfo> getUserInfo() {
        try {
            // 获取当前登录用户信息
            LoginContext loginContext = LoginContext.getLoginContext();
            
            UserInfo userInfo = new UserInfo();
            userInfo.setPin(loginContext.getPin());
            userInfo.setNick(loginContext.getNick());
            userInfo.setPersonId(loginContext.getPersonId());
            userInfo.setOrgId(loginContext.getOrgId());
            userInfo.setOrgName(loginContext.getOrgName());
            userInfo.setEmail(loginContext.getEmail());
            userInfo.setMobile(loginContext.getMobile());
            
            return Result.success(userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage(), e);
            return Result.error("获取用户信息失败");
        }
    }

    @PostMapping("/logout")
    public Result<Void> logout(HttpServletResponse response) {
        try {
            // 执行登出操作
            String ssoDomainName = "your-domain.com";
            String logoutUrl = "http://ssa.jd.com/sso/logout?ReturnUrl=http://your-app-url";
            
            SSOHelper.logout(response, ssoDomainName);
            
            return Result.success();
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage(), e);
            return Result.error("登出失败");
        }
    }
}

// 场景2: 权限检查
@Service
public class PermissionService {

    public boolean hasPermission(String permission) {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext == null) {
                return false;
            }
            
            // 根据用户信息检查权限
            return checkUserPermission(loginContext.getPin(), permission);
        } catch (Exception e) {
            log.error("权限检查失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    private boolean checkUserPermission(String pin, String permission) {
        // 实现具体的权限检查逻辑
        return true;
    }
}

// 场景3: 用户信息缓存
@Service
public class UserCacheService {

    @Autowired(required = false)
    private CacheManager cacheManager;

    public UserInfo getCachedUserInfo() {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext == null) {
                return null;
            }
            
            String cacheKey = "user:info:" + loginContext.getPin();
            
            // 先从缓存获取
            if (cacheManager != null) {
                Cache cache = cacheManager.getCache("userInfo");
                if (cache != null) {
                    Cache.ValueWrapper wrapper = cache.get(cacheKey);
                    if (wrapper != null) {
                        return (UserInfo) wrapper.get();
                    }
                }
            }
            
            // 缓存未命中，构建用户信息并缓存
            UserInfo userInfo = buildUserInfo(loginContext);
            
            if (cacheManager != null) {
                Cache cache = cacheManager.getCache("userInfo");
                if (cache != null) {
                    cache.put(cacheKey, userInfo);
                }
            }
            
            return userInfo;
        } catch (Exception e) {
            log.error("获取缓存用户信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    private UserInfo buildUserInfo(LoginContext loginContext) {
        UserInfo userInfo = new UserInfo();
        userInfo.setPin(loginContext.getPin());
        userInfo.setNick(loginContext.getNick());
        userInfo.setPersonId(loginContext.getPersonId());
        userInfo.setOrgId(loginContext.getOrgId());
        userInfo.setOrgName(loginContext.getOrgName());
        userInfo.setEmail(loginContext.getEmail());
        userInfo.setMobile(loginContext.getMobile());
        return userInfo;
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // SSO服务（可选注入，支持降级）
    @Autowired(required = false)
    private PermissionService permissionService;

    // 降级服务
    @Autowired
    private LocalAuthService localAuthService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 检查用户权限（新增，支持降级）
        if (!checkUserPermission(request.getOperation())) {
            throw new UnauthorizedException("用户无权限执行此操作");
        }
        
        // 2. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 3. 记录用户操作日志（新增）
        logUserOperation(request, result);
        
        return result;
    }
    
    private boolean checkUserPermission(String operation) {
        try {
            if (permissionService != null) {
                // 使用SSO权限检查
                return permissionService.hasPermission(operation);
            } else {
                // 降级到本地权限检查
                return localAuthService.hasPermission(operation);
            }
        } catch (Exception e) {
            log.warn("权限检查失败，默认拒绝访问: {}", e.getMessage());
            return false;
        }
    }
    
    private void logUserOperation(BusinessRequest request, BusinessObject result) {
        try {
            LoginContext loginContext = LoginContext.getLoginContext();
            if (loginContext != null) {
                log.info("用户操作记录: pin={}, operation={}, resourceId={}", 
                    loginContext.getPin(), request.getOperation(), result.getId());
            }
        } catch (Exception e) {
            log.warn("用户操作日志记录失败: {}", e.getMessage());
        }
    }
}
```

## 功能特性

### 核心能力
- **单点登录**: 一次登录，多系统访问
- **统一认证**: 集中的用户认证管理
- **前后端分离**: 支持前后端分离架构

### 技术特点
- **自动拦截**: 自动拦截未认证请求
- **登录信息获取**: 便捷获取当前登录用户信息
- **路径排除**: 支持配置不需要认证的路径

### 适用场景
- **企业应用**: 企业内部系统的统一登录
- **微服务架构**: 微服务间的统一认证
- **前后端分离**: 支持SPA应用的认证

## 注意事项

### 使用约束
- 必须配置JSF注册中心
- 测试环境使用通用的appkey和appToken
- 前后端分离需要特殊配置

### 性能考虑
- 登录信息可以缓存提高性能
- 合理配置排除路径减少拦截

### 安全要求
- 客户端密钥需要安全存储
- 生产环境使用HTTPS
- 合理配置登出URL

## 最佳实践

1. **路径配置**: 合理配置excludePath，静态资源不需要认证
2. **异常处理**: 完善的认证失败处理机制
3. **用户信息缓存**: 缓存用户信息提高性能
4. **权限控制**: 结合业务权限进行细粒度控制
5. **日志记录**: 记录用户操作日志便于审计
