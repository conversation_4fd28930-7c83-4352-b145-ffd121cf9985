# DongHttp - HTTP客户端

## 基本信息
- **版本**: 
- **关键词**: HTTP客户端, REST调用, 远程调用, API调用, HTTP请求
- **适用场景**: HTTP/HTTPS远程服务调用
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>http-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  http:
    enabled: ${DONGHTTP_ENABLED:false}  # 功能开关
    clients:
      # HTTP客户端配置示例
      defaultClient:
        connectTimeout: ${HTTP_CONNECT_TIMEOUT:5000}
        readTimeout: ${HTTP_READ_TIMEOUT:10000}
        writeTimeout: ${HTTP_WRITE_TIMEOUT:10000}
        maxIdleConnections: ${HTTP_MAX_IDLE_CONNECTIONS:5}
        keepAliveDuration: ${HTTP_KEEP_ALIVE_DURATION:300000}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DONGHTTP_ENABLED` | 是否启用DongHttp | false | true |
| `HTTP_CONNECT_TIMEOUT` | 连接超时时间(毫秒) | 5000 | 3000 |
| `HTTP_READ_TIMEOUT` | 读取超时时间(毫秒) | 10000 | 15000 |
| `HTTP_WRITE_TIMEOUT` | 写入超时时间(毫秒) | 10000 | 15000 |
| `HTTP_MAX_IDLE_CONNECTIONS` | 最大空闲连接数 | 5 | 10 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| connectTimeout | Long | 否 | 5000 | 连接超时时间(毫秒) |
| readTimeout | Long | 否 | 10000 | 读取超时时间(毫秒) |
| writeTimeout | Long | 否 | 10000 | 写入超时时间(毫秒) |
| maxIdleConnections | Integer | 否 | 5 | 最大空闲连接数 |
| keepAliveDuration | Long | 否 | 300000 | 连接保持时间(毫秒) |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.http.enabled", havingValue = "true")
public class DongHttpConfig {

    @Bean
    public DongHttpClient dongHttpClient() {
        return new DongHttpClient();
    }

    @Bean
    public DongHttpHealthIndicator dongHttpHealthIndicator() {
        return new DongHttpHealthIndicator();
    }
}

// 2. 服务类
@Service
public class DongHttpService {

    @Autowired(required = false)
    private DongHttpClient dongHttpClient;

    @Autowired
    private RestTemplate restTemplate; // 降级服务

    public String httpGet(String url) {
        try {
            if (dongHttpClient != null) {
                return dongHttpClient.get(url);
            }
        } catch (Exception e) {
            log.warn("DongHttp调用失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        return restTemplate.getForObject(url, String.class);
    }

    public boolean isAvailable() {
        return dongHttpClient != null;
    }
}
```

### 常见场景
```java
// 场景1: GET请求
@Service
public class HttpGetService {

    @Autowired(required = false)
    private DongHttpClient dongHttpClient;

    public UserInfo getUserInfo(String userId) {
        String url = "https://api.example.com/users/" + userId;
        
        try {
            if (dongHttpClient != null) {
                String response = dongHttpClient.get(url);
                return JsonUtils.fromJson(response, UserInfo.class);
            }
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage());
        }

        return null;
    }
}

// 场景2: POST请求
@Service
public class HttpPostService {

    @Autowired(required = false)
    private DongHttpClient dongHttpClient;

    public OrderResult createOrder(CreateOrderRequest request) {
        String url = "https://api.example.com/orders";
        
        try {
            if (dongHttpClient != null) {
                String requestBody = JsonUtils.toJson(request);
                String response = dongHttpClient.post(url, requestBody);
                return JsonUtils.fromJson(response, OrderResult.class);
            }
        } catch (Exception e) {
            log.error("创建订单失败: {}", e.getMessage());
        }

        return null;
    }
}

// 场景3: 带请求头的请求
@Service
public class HttpHeaderService {

    @Autowired(required = false)
    private DongHttpClient dongHttpClient;

    public ApiResponse callApiWithAuth(String apiUrl, String token) {
        try {
            if (dongHttpClient != null) {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                headers.put("Content-Type", "application/json");
                
                String response = dongHttpClient.get(apiUrl, headers);
                return JsonUtils.fromJson(response, ApiResponse.class);
            }
        } catch (Exception e) {
            log.error("API调用失败: {}", e.getMessage());
        }

        return null;
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DongHttp服务（可选注入，支持降级）
    @Autowired(required = false)
    private DongHttpClient dongHttpClient;

    // 降级服务
    @Autowired
    private RestTemplate restTemplate;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 2. 使用DongHttp调用外部服务增强功能（新增，支持降级）
        enhanceWithExternalService(result);
        
        return result;
    }
    
    private void enhanceWithExternalService(BusinessObject object) {
        try {
            String externalApiUrl = "https://external-api.example.com/enhance/" + object.getId();
            
            if (dongHttpClient != null) {
                // 使用DongHttp调用外部服务
                String response = dongHttpClient.get(externalApiUrl);
                EnhancementData data = JsonUtils.fromJson(response, EnhancementData.class);
                object.setEnhancementData(data);
                log.info("外部服务增强成功: {}", object.getId());
            } else {
                // 降级到RestTemplate
                EnhancementData data = restTemplate.getForObject(externalApiUrl, EnhancementData.class);
                object.setEnhancementData(data);
                log.info("使用RestTemplate增强成功: {}", object.getId());
            }
        } catch (Exception e) {
            log.warn("外部服务调用失败，不影响主流程: {}", e.getMessage());
            // 外部服务调用失败不影响主业务流程
        }
    }
}
```

## 功能特性

### 核心能力
- **连接池管理**: 自动管理HTTP连接池，提高性能
- **超时控制**: 支持连接、读取、写入超时配置
- **重试机制**: 内置重试机制，提高调用成功率

### 技术特点
- **异步支持**: 支持异步HTTP调用
- **监控集成**: 集成监控指标，实时监控调用状态
- **负载均衡**: 支持多实例负载均衡

### 适用场景
- **微服务调用**: 微服务间的HTTP通信
- **第三方API集成**: 调用外部第三方API服务
- **数据同步**: 与外部系统进行数据同步

## 注意事项

### 使用约束
- 需要合理设置超时时间，避免长时间阻塞
- 连接池大小需要根据并发量调整
- 重试机制要考虑幂等性

### 性能考虑
- 合理配置连接池参数提高性能
- 使用异步调用避免阻塞
- 监控HTTP调用性能指标

### 安全要求
- HTTPS调用需要证书验证
- 敏感信息不要在URL中传递
- 请求头中的认证信息要安全处理

## 最佳实践

1. **超时设置**: 根据业务需求合理设置各种超时时间
2. **连接池配置**: 根据并发量和目标服务能力配置连接池
3. **异常处理**: 完善的异常处理和降级机制
4. **监控告警**: 关注HTTP调用的成功率和响应时间
5. **安全考虑**: 使用HTTPS，保护敏感数据传输
