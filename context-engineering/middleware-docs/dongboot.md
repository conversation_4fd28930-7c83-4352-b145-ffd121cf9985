# DongBoot - 统一开发框架

## 基本信息
- **版本**: 1.0.6-HOTFIX-T1
- **关键词**: 开发框架, Spring Boot, 脚手架, 统一框架, 应用架构
- **适用场景**: 统一开发框架和应用架构
- **依赖要求**: JDK 1.8+, 替代Spring Boot

## Maven依赖
```xml
<!-- 父POM配置 -->
<parent>
    <groupId>com.jd.framework</groupId>
    <artifactId>dong-boot-starter-parent</artifactId>
    <version>1.0.6-HOTFIX-T1</version>
</parent>

<!-- 核心依赖 -->
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>dong-boot-starter-core</artifactId>
</dependency>

<!-- 测试依赖 -->
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>dong-boot-starter-test</artifactId>
    <scope>test</scope>
</dependency>
```

**版本说明**: 推荐使用1.0.6-HOTFIX-T1版本，替代Spring Boot父POM

## 配置

### 基础配置
```yaml
dong:
  boot:
    enabled: ${DONGBOOT_ENABLED:true}  # 功能开关
    # DongBoot核心配置
    readiness-check:
      enabled: ${READINESS_CHECK_ENABLED:true}
    async-init:
      enabled: ${ASYNC_INIT_ENABLED:true}
    class-isolation:
      enabled: ${CLASS_ISOLATION_ENABLED:false}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DONGBOOT_ENABLED` | 是否启用DongBoot | true | true |
| `READINESS_CHECK_ENABLED` | 是否启用健康检查 | true | true |
| `ASYNC_INIT_ENABLED` | 是否启用Bean异步初始化 | true | true |
| `CLASS_ISOLATION_ENABLED` | 是否启用类隔离 | false | true |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| readiness-check.enabled | Boolean | 否 | true | 应用健康检查机制开关 |
| async-init.enabled | Boolean | 否 | true | Bean异步初始化开关 |
| class-isolation.enabled | Boolean | 否 | false | 类隔离功能开关 |

## 使用代码

### 基础用法
```java
// 1. 启动类配置
@DongBootApplication
public class DemoApplication {
    public static void main(String[] args) {
        DongApplication.run(DemoApplication.class, args);
    }
}

// 2. 服务类
@Service
public class DongBootService {

    @Autowired(required = false)
    private DongBootClient dongBootClient;

    @Autowired
    private StandardService standardService; // 降级服务

    public String processRequest(String request) {
        try {
            if (dongBootClient != null) {
                return dongBootClient.process(request);
            }
        } catch (Exception e) {
            log.warn("DongBoot调用失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        return standardService.process(request);
    }

    public boolean isAvailable() {
        return dongBootClient != null;
    }
}
```

### 常见场景
```java
// 场景1: 模块化架构
@Service
public class ModularService {

    @Autowired(required = false)
    private DongBootClient dongBootClient;

    public ModuleResult processModule(ModuleRequest request) {
        // 使用DongBoot模块化能力
        if (dongBootClient != null) {
            return dongBootClient.processModule(request);
        }

        // 降级逻辑
        return new ModuleResult("default");
    }
}

// 场景2: 异步初始化
@Service
public class AsyncInitService {

    @Autowired(required = false)
    private DongBootClient dongBootClient;

    @PostConstruct
    public void init() {
        // 使用DongBoot异步初始化
        try {
            if (dongBootClient != null) {
                dongBootClient.asyncInit(this::heavyInitialization);
                log.info("异步初始化启动成功");
            }
        } catch (Exception e) {
            log.warn("异步初始化失败，使用同步初始化: {}", e.getMessage());
            heavyInitialization();
        }
    }

    private void heavyInitialization() {
        // 重量级初始化逻辑
    }
}

// 场景3: 健康检查
@Service
public class HealthCheckService {

    @Autowired(required = false)
    private DongBootClient dongBootClient;

    public HealthStatus checkHealth() {
        // 使用DongBoot健康检查
        if (dongBootClient != null) {
            try {
                return dongBootClient.checkHealth();
            } catch (Exception e) {
                log.error("健康检查执行失败: {}", e.getMessage());
            }
        }

        return HealthStatus.DOWN;
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DongBoot服务（可选注入，支持降级）
    @Autowired(required = false)
    private DongBootClient dongBootClient;

    // 降级服务
    @Autowired
    private StandardFrameworkService standardService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 执行现有业务逻辑（保持不变）
        BusinessObject result = businessRepository.findById(request.getId());
        
        // 2. 使用DongBoot增强功能（新增，支持降级）
        enhanceWithDongBoot(result);
        
        return result;
    }
    
    private void enhanceWithDongBoot(BusinessObject object) {
        try {
            if (dongBootClient != null) {
                // 使用DongBoot增强
                dongBootClient.enhance(object);
                log.info("DongBoot增强成功: {}", object.getId());
            } else {
                // 降级到标准框架
                standardService.enhance(object);
                log.info("使用标准框架增强: {}", object.getId());
            }
        } catch (Exception e) {
            log.warn("DongBoot增强失败，不影响主流程: {}", e.getMessage());
            // 增强失败不影响主业务流程
        }
    }
}
```

## 功能特性

### 核心能力
- **Readiness Check**: 应用健康检查机制
- **关键日志标准化打印**: 统一日志格式和输出标准
- **Bean异步初始化**: 提升应用启动性能
- **类隔离**: 避免类冲突和版本冲突

### 技术特点
- **统一托管中间件SDK**: 集成企业中间件
- **完善公共组件能力矩阵**: 标准化组件
- **DDD分层架构**: 标准化应用架构

### 适用场景
- **企业级应用开发**: 替代Spring Boot的企业框架
- **微服务架构**: 支持微服务开发模式
- **中间件集成**: 统一管理企业中间件

## 注意事项

### 使用约束
- 需要完全替代Spring Boot父POM
- 启动类注解需要从@SpringBootApplication改为@DongBootApplication
- main方法需要使用DongApplication.run()

### 性能考虑
- Bean异步初始化可以提升启动性能
- 类隔离可能会增加内存开销

### 安全要求
- 遵循企业安全规范
- 中间件访问需要权限控制

## 最佳实践

1. **模块划分**: 严格按照DDD分层架构进行模块划分
2. **依赖管理**: Client模块保持轻量，减少三方依赖
3. **接口设计**: RPC接口与实现分离，保持接口稳定性
4. **测试覆盖**: 单元测试和集成测试并重
5. **配置管理**: 合理使用配置中心，避免硬编码
6. **监控告警**: 关注应用性能和业务指标
