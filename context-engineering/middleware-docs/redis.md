# Redis - 分布式缓存

## 基本信息
- **版本**: Redisson 3.x (Spring Boot Starter)
- **关键词**: 缓存, <PERSON>is, 快速访问, 临时存储, 分布式缓存, 高性能存储, Redisson
- **适用场景**: 高吞吐、低延迟的分布式缓存服务，会话存储，分布式锁
- **依赖要求**: JDK 8+, Spring Boot 2.7.0+

## Maven依赖
```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用Spring Boot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
# Redis连接配置
spring:
  redis:
    # 地址
    host: ${REDIS_HOST:localhost}
    # 端口，默认为6379
    port: ${REDIS_PORT:6379}
    # 数据库索引
    database: ${REDIS_DATABASE:0}
    # 密码
    password: ${REDIS_PASSWORD:}
    # 连接超时时间
    timeout: ${REDIS_TIMEOUT:10s}
    # 是否开启ssl
    ssl: ${REDIS_SSL:false}

# Redisson配置
redisson:
  # redis key前缀
  keyPrefix: ${REDIS_KEY_PREFIX:jdl-tops-}
  # 线程池数量
  threads: ${REDISSON_THREADS:4}
  # Netty线程池数量
  nettyThreads: ${REDISSON_NETTY_THREADS:8}
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${tops.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: ${REDIS_MIN_IDLE:8}
    # 连接池大小
    connectionPoolSize: ${REDIS_POOL_SIZE:32}
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: ${REDIS_IDLE_TIMEOUT:10000}
    # 命令等待超时，单位：毫秒
    timeout: ${REDIS_COMMAND_TIMEOUT:3000}
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: ${REDIS_SUB_POOL_SIZE:50}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `REDIS_HOST` | Redis服务器地址 | localhost | redis.example.com |
| `REDIS_PORT` | Redis端口 | 6379 | 6379 |
| `REDIS_DATABASE` | 数据库索引 | 0 | 2 |
| `REDIS_PASSWORD` | Redis密码 | - | your_password |
| `REDIS_KEY_PREFIX` | Key前缀 | jdl-tops- | myapp- |

### 集群配置示例
```yaml
# Redis集群配置
spring:
  redis:
    cluster:
      nodes:
        - ${REDIS_NODE1:*************:6379}
        - ${REDIS_NODE2:*************:6379}
        - ${REDIS_NODE3:*************:6379}
    password: ${REDIS_PASSWORD:}
    timeout: ${REDIS_TIMEOUT:10s}
    ssl: ${REDIS_SSL:false}

redisson:
  keyPrefix: ${REDIS_KEY_PREFIX:jdl-tops-}
  threads: ${REDISSON_THREADS:16}
  nettyThreads: ${REDISSON_NETTY_THREADS:32}
  # 集群配置
  clusterServersConfig:
    clientName: ${tops.name}
    masterConnectionMinimumIdleSize: ${REDIS_MASTER_MIN_IDLE:32}
    masterConnectionPoolSize: ${REDIS_MASTER_POOL_SIZE:64}
    slaveConnectionMinimumIdleSize: ${REDIS_SLAVE_MIN_IDLE:32}
    slaveConnectionPoolSize: ${REDIS_SLAVE_POOL_SIZE:64}
    idleConnectionTimeout: ${REDIS_IDLE_TIMEOUT:10000}
    timeout: ${REDIS_COMMAND_TIMEOUT:3000}
    subscriptionConnectionPoolSize: ${REDIS_SUB_POOL_SIZE:50}
```

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@EnableCaching
@EnableConfigurationProperties(RedissonProperties.class)
public class RedisConfig {
    
    @Autowired
    private RedissonProperties redissonProperties;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Bean
    public RedissonAutoConfigurationCustomizer redissonCustomizer() {
        return config -> {
            config.setThreads(redissonProperties.getThreads())
                .setNettyThreads(redissonProperties.getNettyThreads())
                .setCodec(new JsonJacksonCodec(objectMapper));
            // 配置单机或集群模式
            configureSingleServer(config);
        };
    }
    
    @Bean
    public CacheManager cacheManager() {
        return new PlusSpringCacheManager();
    }
}

// 2. Redis工具类使用
@Service
public class RedisCacheService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    public void set(String key, Object value) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value);
    }
    
    public void set(String key, Object value, Duration duration) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value, duration);
    }
    
    public <T> T get(String key, Class<T> clazz) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }
    
    public void delete(String key) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }
    
    public boolean exists(String key) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        return bucket.isExists();
    }
    
    public boolean expire(String key, Duration duration) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        return bucket.expire(duration);
    }
}
```

### 常见场景
```java
// 场景1: 用户会话缓存
@Service
public class UserSessionService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    private static final String SESSION_PREFIX = "session:";
    private static final Duration SESSION_EXPIRE = Duration.ofHours(1);
    
    public void saveSession(String sessionId, UserSession session) {
        String key = SESSION_PREFIX + sessionId;
        RBucket<UserSession> bucket = redissonClient.getBucket(key);
        bucket.set(session, SESSION_EXPIRE);
    }
    
    public UserSession getSession(String sessionId) {
        String key = SESSION_PREFIX + sessionId;
        RBucket<UserSession> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }
    
    public void removeSession(String sessionId) {
        String key = SESSION_PREFIX + sessionId;
        RBucket<UserSession> bucket = redissonClient.getBucket(key);
        bucket.delete();
    }
}

// 场景2: 分布式锁
@Service
public class DistributedLockService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
    
    public void releaseLock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }
    
    public <T> T executeWithLock(String lockKey, long waitTime, long leaseTime, 
                                 TimeUnit unit, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitTime, leaseTime, unit)) {
                return supplier.get();
            } else {
                throw new RuntimeException("获取锁失败: " + lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取锁被中断: " + lockKey, e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}

// 场景3: 计数器和限流
@Service
public class CounterService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    public long increment(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.incrementAndGet();
    }
    
    public long incrementBy(String key, long value) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        return atomicLong.addAndGet(value);
    }
    
    public boolean isRateLimited(String key, int limit, Duration window) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        rateLimiter.trySetRate(RateType.OVERALL, limit, window.getSeconds(), RateIntervalUnit.SECONDS);
        return !rateLimiter.tryAcquire();
    }
    
    // 滑动窗口限流
    public boolean slidingWindowRateLimit(String key, int limit, Duration window) {
        long now = System.currentTimeMillis();
        long windowStart = now - window.toMillis();
        
        RList<Long> requests = redissonClient.getList(key);
        
        // 清理过期请求
        requests.removeIf(timestamp -> timestamp < windowStart);
        
        if (requests.size() >= limit) {
            return true; // 限流
        }
        
        requests.add(now);
        requests.expire(window);
        return false;
    }
}

// 场景4: Spring Cache注解使用
@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    // 缓存查询结果，支持自定义过期时间和最大条目数
    @Cacheable(cacheNames = "user:cache#60s#10m#20", key = "#userId", condition = "#userId != null")
    public User getUserById(Long userId) {
        return userRepository.findById(userId).orElse(null);
    }

    // 更新缓存
    @CachePut(cacheNames = "user:cache#60s#10m#20", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }

    // 清除缓存
    @CacheEvict(cacheNames = "user:cache#60s#10m#20", key = "#userId")
    public void deleteUser(Long userId) {
        userRepository.deleteById(userId);
    }

    // 清除所有缓存
    @CacheEvict(cacheNames = "user:cache#60s#10m#20", allEntries = true)
    public void clearAllUserCache() {
        // 清除所有用户缓存
    }
}

// 场景5: 发布订阅
@Service
public class MessageService {

    @Autowired
    private RedissonClient redissonClient;

    public void publishMessage(String channel, Object message) {
        RTopic topic = redissonClient.getTopic(channel);
        topic.publish(message);
    }

    public void subscribeMessage(String channel, Class<?> messageType, Consumer<Object> handler) {
        RTopic topic = redissonClient.getTopic(channel);
        topic.addListener(messageType, (charSequence, message) -> handler.accept(message));
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingOrderService {

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RedissonClient redissonClient;

    private static final String ORDER_CACHE_PREFIX = "order:";
    private static final Duration CACHE_EXPIRE = Duration.ofMinutes(30);

    public Order getOrderById(Long orderId) {
        String cacheKey = ORDER_CACHE_PREFIX + orderId;

        // 1. 先从缓存获取
        Order cachedOrder = getCachedOrder(cacheKey);
        if (cachedOrder != null) {
            return cachedOrder;
        }

        // 2. 缓存未命中，从数据库获取
        Order order = orderRepository.findById(orderId).orElse(null);
        if (order != null) {
            // 3. 更新缓存
            setCachedOrder(cacheKey, order);
        }

        return order;
    }

    public void updateOrder(Order order) {
        // 1. 更新数据库
        orderRepository.save(order);

        // 2. 清除缓存
        String cacheKey = ORDER_CACHE_PREFIX + order.getId();
        deleteCachedOrder(cacheKey);
    }

    private Order getCachedOrder(String key) {
        try {
            RBucket<Order> bucket = redissonClient.getBucket(key);
            return bucket.get();
        } catch (Exception e) {
            log.warn("Redis获取缓存失败: key={}, error={}", key, e.getMessage());
            return null;
        }
    }

    private void setCachedOrder(String key, Order order) {
        try {
            RBucket<Order> bucket = redissonClient.getBucket(key);
            bucket.set(order, CACHE_EXPIRE);
        } catch (Exception e) {
            log.warn("Redis设置缓存失败: key={}, error={}", key, e.getMessage());
        }
    }

    private void deleteCachedOrder(String key) {
        try {
            RBucket<Order> bucket = redissonClient.getBucket(key);
            bucket.delete();
        } catch (Exception e) {
            log.warn("Redis删除缓存失败: key={}, error={}", key, e.getMessage());
        }
    }
}
```

## 功能特性

### 核心能力
- **高性能**: 基于内存存储，提供微秒级访问延迟
- **高可用**: 支持主从复制和哨兵模式，故障自动切换
- **可扩展**: 支持集群模式，水平扩展
- **数据持久化**: 支持RDB和AOF持久化机制

### Redisson特点
- **分布式锁**: 提供可重入锁、公平锁、读写锁等
- **分布式集合**: 支持分布式Map、Set、List、Queue等
- **发布订阅**: 支持消息发布订阅模式
- **限流器**: 内置令牌桶和漏桶限流算法
- **Spring集成**: 完美集成Spring Cache和Spring Boot

### 适用场景
- **热点数据缓存**: 频繁访问的数据缓存
- **会话存储**: 用户会话和状态存储
- **分布式锁**: 分布式系统的锁机制
- **计数器**: 访问计数和限流控制
- **消息队列**: 简单的消息发布订阅

## 注意事项

### 使用约束
- Redis密码必须妥善保管，避免泄露
- 单个key的value大小建议不超过512MB
- 避免使用过长的key名称，影响内存和性能
- 合理设置过期时间，避免内存溢出

### 性能考虑
- 批量操作优于单个操作，减少网络开销
- 合理使用pipeline提高批量操作性能
- 避免大key操作，可能阻塞其他请求
- 监控内存使用情况，及时清理过期数据

### 安全要求
- 生产环境必须设置密码
- 定期轮换Redis访问密码
- 敏感数据存储前考虑加密
- 监控异常访问模式和大量数据操作
- 配置防火墙规则，限制访问来源

## 最佳实践

1. **Key设计**: 使用有意义的前缀和层次结构，如 `业务:类型:ID`
2. **过期策略**: 为所有key设置合理的过期时间，避免内存泄漏
3. **数据序列化**: 使用JSON格式存储复杂对象，便于调试和兼容
4. **异常处理**: 缓存操作失败时有降级方案，不影响主业务流程
5. **监控告警**: 关注缓存命中率、内存使用率和响应时间
6. **容量规划**: 根据业务增长预估容量需求，提前扩容
7. **连接池配置**: 合理配置连接池大小，避免连接不足或浪费

## 常见问题

### 配置问题
- **连接失败**: 检查host、port配置和网络连通性
- **认证失败**: 确认password配置正确
- **超时问题**: 调整timeout配置，检查网络延迟

### 运行时问题
- **内存不足**: 检查key过期时间设置，清理无用数据
- **响应慢**: 检查是否有大key操作，优化数据结构
- **连接超时**: 调整连接池配置，检查网络稳定性
- **数据丢失**: 检查过期时间设置，确认持久化策略

### Spring Cache问题
- **缓存不生效**: 检查@EnableCaching注解和缓存名称配置
- **序列化错误**: 确认对象可序列化，配置正确的序列化方式
- **缓存穿透**: 使用condition条件避免缓存null值

## 环境配置示例

### 开发环境
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10s

redisson:
  keyPrefix: dev-
  threads: 4
  nettyThreads: 8
```

### 生产环境
```yaml
spring:
  redis:
    host: redis-cluster.example.com
    port: 6379
    database: 0
    password: ${REDIS_PASSWORD}
    timeout: 10s
    ssl: true

redisson:
  keyPrefix: prod-
  threads: 16
  nettyThreads: 32
  singleServerConfig:
    connectionPoolSize: 64
    connectionMinimumIdleSize: 32
```
