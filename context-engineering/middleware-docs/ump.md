# UMP - 统一监控平台

## 基本信息
- **版本**: 
- **关键词**: 监控, 告警, 性能监控, 业务监控, 指标收集, 日志监控
- **适用场景**: 应用性能监控和业务指标监控
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>ump-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  ump:
    enabled: ${UMP_ENABLED:false}  # 功能开关
    appName: ${UMP_APP_NAME:}  # 应用名称
    # UMP监控配置
    monitor:
      enabled: ${UMP_MONITOR_ENABLED:true}
      heartbeat:
        enabled: ${UMP_HEARTBEAT_ENABLED:true}
        interval: ${UMP_HEARTBEAT_INTERVAL:60000}
      alarm:
        enabled: ${UMP_ALARM_ENABLED:true}
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `UMP_ENABLED` | 是否启用UMP | false | true |
| `UMP_APP_NAME` | 应用名称标识 |  | order-service |
| `UMP_MONITOR_ENABLED` | 是否启用监控 | true | true |
| `UMP_HEARTBEAT_ENABLED` | 是否启用心跳检测 | true | true |
| `UMP_HEARTBEAT_INTERVAL` | 心跳间隔(毫秒) | 60000 | 30000 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| appName | String | 是 |  | 应用名称，用于监控标识 |
| monitor.enabled | Boolean | 否 | true | 是否启用监控功能 |
| heartbeat.enabled | Boolean | 否 | true | 是否启用心跳检测 |
| heartbeat.interval | Long | 否 | 60000 | 心跳检测间隔时间(毫秒) |
| alarm.enabled | Boolean | 否 | true | 是否启用告警功能 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.ump.enabled", havingValue = "true")
public class UmpConfig {

    @Bean
    public UmpClient umpClient() {
        return new UmpClient();
    }

    @Bean
    public UmpHealthIndicator umpHealthIndicator() {
        return new UmpHealthIndicator();
    }
}

// 2. 服务类
@Service
public class UmpService {

    @Autowired(required = false)
    private UmpClient umpClient;

    @Autowired
    private LocalMonitorService localMonitorService; // 降级服务

    public void recordMetric(String key, double value) {
        try {
            if (umpClient != null) {
                umpClient.recordMetric(key, value);
            }
        } catch (Exception e) {
            log.warn("UMP指标记录失败，执行降级: {}", e.getMessage());
        }

        // 降级处理
        localMonitorService.recordMetric(key, value);
    }

    public boolean isAvailable() {
        return umpClient != null;
    }
}
```

### 常见场景
```java
// 场景1: 业务指标监控
@Service
public class BusinessMetricService {

    @Autowired(required = false)
    private UmpClient umpClient;

    public void recordOrderMetric(Order order) {
        try {
            if (umpClient != null) {
                // 记录订单金额
                umpClient.recordMetric("order.amount", order.getAmount().doubleValue());
                
                // 记录订单数量
                umpClient.incrementCounter("order.count");
                
                // 记录订单状态分布
                umpClient.incrementCounter("order.status." + order.getStatus().name());
                
                log.info("订单指标记录成功: orderId={}", order.getId());
            }
        } catch (Exception e) {
            log.warn("订单指标记录失败，但不影响主流程: {}", e.getMessage());
        }
    }
}

// 场景2: 性能监控
@Service
public class PerformanceMonitorService {

    @Autowired(required = false)
    private UmpClient umpClient;

    public void monitorMethodPerformance(String methodName, long executionTime) {
        try {
            if (umpClient != null) {
                // 记录方法执行时间
                umpClient.recordTimer("method.execution.time." + methodName, executionTime);
                
                // 记录方法调用次数
                umpClient.incrementCounter("method.call.count." + methodName);
                
                // 如果执行时间过长，记录慢查询
                if (executionTime > 1000) {
                    umpClient.incrementCounter("method.slow.query." + methodName);
                }
            }
        } catch (Exception e) {
            log.warn("性能监控记录失败: {}", e.getMessage());
        }
    }
}

// 场景3: 异常监控
@Service
public class ExceptionMonitorService {

    @Autowired(required = false)
    private UmpClient umpClient;

    public void recordException(String operation, Exception exception) {
        try {
            if (umpClient != null) {
                // 记录异常次数
                umpClient.incrementCounter("exception.count." + operation);
                
                // 记录异常类型
                String exceptionType = exception.getClass().getSimpleName();
                umpClient.incrementCounter("exception.type." + exceptionType);
                
                // 记录异常详情
                umpClient.recordEvent("exception.detail", Map.of(
                    "operation", operation,
                    "exceptionType", exceptionType,
                    "message", exception.getMessage()
                ));
            }
        } catch (Exception e) {
            log.warn("异常监控记录失败: {}", e.getMessage());
        }
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // UMP服务（可选注入，支持降级）
    @Autowired(required = false)
    private UmpClient umpClient;

    // 降级服务
    @Autowired
    private LocalMonitorService localMonitorService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 执行现有业务逻辑（保持不变）
            BusinessObject result = businessRepository.findById(request.getId());
            
            // 2. 记录业务成功指标（新增，支持降级）
            recordSuccessMetrics(result, startTime);
            
            return result;
        } catch (Exception e) {
            // 3. 记录业务异常指标（新增，支持降级）
            recordExceptionMetrics(request, e, startTime);
            throw e;
        }
    }
    
    private void recordSuccessMetrics(BusinessObject result, long startTime) {
        try {
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (umpClient != null) {
                // 使用UMP记录指标
                umpClient.recordTimer("business.process.time", executionTime);
                umpClient.incrementCounter("business.process.success");
                umpClient.recordMetric("business.result.size", result.getSize());
            } else {
                // 降级到本地监控
                localMonitorService.recordTimer("business.process.time", executionTime);
                localMonitorService.incrementCounter("business.process.success");
            }
        } catch (Exception e) {
            log.warn("成功指标记录失败，不影响主流程: {}", e.getMessage());
        }
    }
    
    private void recordExceptionMetrics(BusinessRequest request, Exception exception, long startTime) {
        try {
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (umpClient != null) {
                // 使用UMP记录异常指标
                umpClient.recordTimer("business.process.error.time", executionTime);
                umpClient.incrementCounter("business.process.error");
                umpClient.incrementCounter("business.error.type." + exception.getClass().getSimpleName());
            } else {
                // 降级到本地监控
                localMonitorService.incrementCounter("business.process.error");
            }
        } catch (Exception e) {
            log.warn("异常指标记录失败: {}", e.getMessage());
        }
    }
}
```

## 功能特性

### 核心能力
- **指标收集**: 支持计数器、计时器、仪表盘等多种指标类型
- **实时监控**: 实时收集和展示应用性能指标
- **告警机制**: 支持阈值告警和异常告警

### 技术特点
- **心跳检测**: 应用健康状态实时监控
- **多维度监控**: 支持业务指标和技术指标监控
- **可视化展示**: 丰富的图表和仪表盘展示

### 适用场景
- **应用性能监控**: 监控应用的响应时间、吞吐量等
- **业务指标监控**: 监控订单量、用户活跃度等业务指标
- **异常监控**: 监控应用异常和错误率

## 注意事项

### 使用约束
- 指标名称需要规范化，便于管理和查询
- 监控数据量要控制，避免影响性能
- 告警阈值需要合理设置

### 性能考虑
- 监控代码要轻量化，避免影响主业务
- 异步发送监控数据，减少延迟
- 合理设置采样率

### 安全要求
- 监控数据不应包含敏感信息
- 访问监控平台需要权限控制

## 最佳实践

1. **指标设计**: 设计有意义的指标名称和标签
2. **告警策略**: 合理设置告警阈值，避免告警风暴
3. **性能优化**: 监控代码要高效，不影响主业务性能
4. **数据治理**: 定期清理无用的监控指标
5. **可视化**: 创建有意义的监控仪表盘
