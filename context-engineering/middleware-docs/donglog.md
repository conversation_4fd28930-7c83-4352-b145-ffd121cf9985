# DongLog - 统一日志组件

## 基本信息
- **版本**: 
- **关键词**: 日志, 日志组件, Log4j2, SLF4J, 异步日志, 日志监控
- **适用场景**: 统一日志组件和日志管理
- **依赖要求**: JDK 1.8+, Spring Boot 2.x

## Maven依赖
```xml
<dependency>
    <groupId>com.jd.framework</groupId>
    <artifactId>log-dong-boot-starter</artifactId>
</dependency>
```

**版本说明**: 使用DongBoot统一管理版本，无需指定具体版本号

## 配置

### 基础配置
```yaml
dong:
  log:
    enabled: ${DONGLOG_ENABLED:false}  # 功能开关
    # 异步日志配置
    async:
      enabled: ${DONGLOG_ASYNC_ENABLED:true}
      ringBufferSize: ${DONGLOG_RING_BUFFER_SIZE:8192}
      waitStrategy: ${DONGLOG_WAIT_STRATEGY:TIMEOUT}
      timeout: ${DONGLOG_TIMEOUT:100}
    # 日志级别配置
    level:
      root: ${DONGLOG_ROOT_LEVEL:INFO}
```

### JVM启动参数
```properties
# 设置异步selector
-DLog4jContextSelector=org.apache.logging.log4j.core.async.BasicAsyncLoggerContextSelector

# 设置异步日志队列缓冲区大小
-DAsyncLogger.RingBufferSize=8192

# 设置异步日志的空闲等待策略
-DAsyncLogger.WaitStrategy=TIMEOUT

# 设置异步日志的等待策略超时时间(ms)
-DAsyncLogger.Timeout=100

# 设置异步日志缓冲区满的执行策略为丢弃
-Dlog4j2.AsyncQueueFullPolicy=Discard

# 设置异步日志缓冲区满的丢弃策略阈值
-Dlog4j2.DiscardThreshold=ERROR
```

### 环境变量
| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DONGLOG_ENABLED` | 是否启用DongLog | false | true |
| `DONGLOG_ASYNC_ENABLED` | 是否启用异步日志 | true | true |
| `DONGLOG_RING_BUFFER_SIZE` | 异步日志队列缓冲区大小 | 8192 | 16384 |
| `DONGLOG_WAIT_STRATEGY` | 空闲等待策略 | TIMEOUT | TIMEOUT |
| `DONGLOG_TIMEOUT` | 等待策略超时时间(ms) | 100 | 200 |

### 配置参数详解
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| async.enabled | Boolean | 否 | true | 是否启用异步日志 |
| async.ringBufferSize | Integer | 否 | 8192 | 异步日志队列缓冲区大小 |
| async.waitStrategy | String | 否 | TIMEOUT | 空闲等待策略 |
| async.timeout | Integer | 否 | 100 | 等待策略超时时间(毫秒) |
| level.root | String | 否 | INFO | 根日志级别 |

## 使用代码

### 基础用法
```java
// 1. 配置类
@Configuration
@ConditionalOnProperty(name = "dong.log.enabled", havingValue = "true")
public class DongLogConfig {

    @Bean
    public DongLogManager dongLogManager() {
        return new DongLogManager();
    }

    @Bean
    public DongLogHealthIndicator dongLogHealthIndicator() {
        return new DongLogHealthIndicator();
    }
}

// 2. 使用DongLogger接口
import com.jd.component.donglog.log.DongLogger;

@Service
public class DongLogService {

    // 方式一：手动初始化Logger
    private static final DongLogger logger = DongLogger.getLogger(DongLogService.class.getName());

    @Autowired
    private StandardLogService standardLogService; // 降级服务

    public void logInfo(String message) {
        try {
            if (logger != null) {
                logger.info(message);
            }
        } catch (Exception e) {
            // 降级处理
            standardLogService.info(message);
        }
    }

    public boolean isAvailable() {
        return logger != null;
    }
}
```

### 常见场景
```java
// 场景1: 使用Lombok自动生成Logger
import com.jd.component.donglog.annotation.CustomLog;

@CustomLog
@Service
public class BusinessLogService {

    public void processOrder(Order order) {
        // 记录业务日志
        log.info("接收到订单请求,orderId={},customerId={},amount={}", 
            order.getId(), order.getCustomerId(), order.getAmount());
        
        try {
            // 业务处理逻辑
            processOrderLogic(order);
            
            log.info("订单处理成功,orderId={}", order.getId());
        } catch (Exception e) {
            log.error("订单处理失败,orderId={},error={}", order.getId(), e.getMessage(), e);
            throw e;
        }
    }

    private void processOrderLogic(Order order) {
        // 具体业务逻辑
    }
}

// 场景2: 结构化日志记录
@Service
public class StructuredLogService {

    private static final DongLogger logger = DongLogger.getLogger(StructuredLogService.class);

    public void recordUserAction(String userId, String action, Map<String, Object> params) {
        // 结构化日志格式
        logger.info("用户操作记录,userId={},action={},params={},timestamp={}", 
            userId, action, JsonUtils.toJson(params), System.currentTimeMillis());
    }

    public void recordPerformanceMetric(String methodName, long executionTime) {
        // 性能指标日志
        logger.info("性能指标,method={},executionTime={}ms,timestamp={}", 
            methodName, executionTime, System.currentTimeMillis());
    }

    public void recordSecurityEvent(String event, String userId, String ip) {
        // 安全事件日志
        logger.warn("安全事件,event={},userId={},ip={},timestamp={}", 
            event, userId, ip, System.currentTimeMillis());
    }
}

// 场景3: 链路追踪日志
@Service
public class TraceLogService {

    private static final DongLogger logger = DongLogger.getLogger(TraceLogService.class);

    public void processWithTrace(String traceId, String operation) {
        // 设置链路追踪ID
        MDC.put("traceId", traceId);
        
        try {
            logger.info("开始处理操作,operation={}", operation);
            
            // 业务处理
            doProcess(operation);
            
            logger.info("操作处理完成,operation={}", operation);
        } finally {
            // 清理MDC
            MDC.remove("traceId");
        }
    }

    private void doProcess(String operation) {
        // 具体处理逻辑
    }
}
```

### 集成示例
```java
// 在现有业务服务中的集成示例
@Service
public class ExistingBusinessService {

    @Autowired
    private BusinessRepository businessRepository;

    // DongLog服务（可选注入，支持降级）
    private static final DongLogger logger = DongLogger.getLogger(ExistingBusinessService.class);

    // 降级服务
    @Autowired
    private StandardLogService standardLogService;

    public BusinessObject processBusinessLogic(BusinessRequest request) {
        // 1. 记录请求开始日志
        logRequestStart(request);
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 2. 执行现有业务逻辑（保持不变）
            BusinessObject result = businessRepository.findById(request.getId());
            
            // 3. 记录成功日志（新增，支持降级）
            logRequestSuccess(request, result, startTime);
            
            return result;
        } catch (Exception e) {
            // 4. 记录异常日志（新增，支持降级）
            logRequestError(request, e, startTime);
            throw e;
        }
    }
    
    private void logRequestStart(BusinessRequest request) {
        try {
            if (logger != null) {
                logger.info("业务请求开始,requestId={},type={},timestamp={}", 
                    request.getId(), request.getType(), System.currentTimeMillis());
            } else {
                standardLogService.info("业务请求开始: " + request.getId());
            }
        } catch (Exception e) {
            // 日志记录失败不影响主流程
        }
    }
    
    private void logRequestSuccess(BusinessRequest request, BusinessObject result, long startTime) {
        try {
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (logger != null) {
                logger.info("业务请求成功,requestId={},resultSize={},executionTime={}ms", 
                    request.getId(), result.getSize(), executionTime);
            } else {
                standardLogService.info("业务请求成功: " + request.getId());
            }
        } catch (Exception e) {
            // 日志记录失败不影响主流程
        }
    }
    
    private void logRequestError(BusinessRequest request, Exception exception, long startTime) {
        try {
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (logger != null) {
                logger.error("业务请求失败,requestId={},error={},executionTime={}ms", 
                    request.getId(), exception.getMessage(), executionTime, exception);
            } else {
                standardLogService.error("业务请求失败: " + request.getId(), exception);
            }
        } catch (Exception e) {
            // 日志记录失败不影响主流程
        }
    }
}
```

## 功能特性

### 核心能力
- **统一接口**: 遵循SLF4J接口标准，提供DongLogger统一接口
- **动态配置**: 支持运行时动态调整日志级别，配置变更无需重启
- **异步日志**: 默认异步日志输出，高性能日志处理

### 技术特点
- **标准化格式**: 标准化日志格式，链路追踪支持
- **业务上下文**: 业务上下文自动注入
- **安全可靠**: 日志脱敏处理，敏感信息保护

### 适用场景
- **业务日志**: 记录关键业务操作和状态变更
- **性能监控**: 记录方法执行时间和性能指标
- **问题排查**: 提供详细的错误信息和堆栈跟踪

## 注意事项

### 使用约束
- 需要正确配置JVM启动参数
- Lombok注解@CustomLog不支持DongBoot主入口类
- 异步日志缓冲区满时会丢弃日志

### 性能考虑
- 合理设置缓冲区大小避免日志丢失
- 异步日志提升性能但可能丢失部分日志
- 避免过度日志记录影响性能

### 安全要求
- 不要记录密码、身份证等敏感信息
- 使用日志脱敏功能保护隐私数据

## 最佳实践

1. **日志级别使用**: 生产环境使用INFO级别，测试环境可使用DEBUG
2. **结构化日志**: 使用键值对格式，便于日志分析和检索
3. **关键信息记录**: 包含订单号、用户ID等关键业务标识
4. **性能优化**: 使用异步日志，合理设置缓冲区大小
5. **链路追踪**: 利用MDC设置链路ID进行问题定位
