- 项目日志路径：/Users/<USER>/work/export/Logs/jdl-trade-ops/
- 项目日志文件名：jdl-trade-ops.log
- 项目日志文件路径：/Users/<USER>/work/export/Logs/jdl-trade-ops/jdl-trade-ops.log

项目启动步骤

1. 先进行编译再运行，运行前需要kill掉后台运行的相关进程。

``` bash
ps -ef | grep TopsApplication | grep -v grep
```

2. 清空项目日志文件 /Users/<USER>/work/export/Logs/jdl-trade-ops/jdl-trade-ops.log
3. 编译命令：cd /Users/<USER>/work/source/jdl-trade-ops/ && mvn clean -U install -Dmaven.test.skip=true -s
   /Users/<USER>/work/config/maven/setting.xml
4. 运行命令：cd /Users/<USER>/work/source/jdl-trade-ops/tops-admin/target/tops-admin-package && chmod +x bin/start.sh
   bin/stop.sh && ./bin/start.sh

5. 验证： 日志地址：/Users/<USER>/work/export/Logs/jdl-trade-ops/jdl-trade-ops.log
   5.1 首先通过日志关键词启动成功，来验证。 若Application run failed 代表启动失败。
   5.2 通过访问：http://localhost:8989/sse/mcp-server/order来验证mcp服务是否启动成功
   5.3 通过访问：http://localhost/duty/dutyInfo/list验证程序是否启动成功。