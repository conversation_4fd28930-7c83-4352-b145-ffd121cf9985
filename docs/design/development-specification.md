# JDL-Trade-OPS 开发规范文档

## 1. 项目概述

### 1.1 项目简介

JDL-Trade-OPS运营生产力系统是一个基于Spring Boot + Vue 3的企业级运营管理平台，采用前后端分离架构，支持多模块、多环境部署。

### 1.2 技术栈

**后端技术栈：**

- Spring Boot 2.7.18
- Java 1.8
- MyBatis-Plus 3.5.4
- Sa-Token 1.37.0 (权限认证)
- Redisson 3.20.1 (分布式缓存)
- Hutool 5.8.22 (工具库)
- Maven 多模块管理

**前端技术栈：**

- Vue 3.2.45
- Vite 3.2.3
- Element Plus 2.7.8
- Pinia 2.0.22 (状态管理)
- Vue Router 4.1.4
- Axios 0.27.2
- SCSS (样式预处理)

### 1.3 项目架构

```
jdl-trade-ops/
├── tops-admin/          # 管理后台模块
├── tops-framework/      # 核心框架模块
├── tops-system/         # 系统管理模块
├── tops-common/         # 通用工具模块
├── tops-generator/      # 代码生成模块
├── tops-job/           # 定时任务模块
├── tops-oss/           # 对象存储模块
├── tops-sms/           # 短信服务模块
├── tops-api/           # API接口模块
├── tops-main/          # 主业务模块
├── tops-extend/        # 扩展模块
└── tops-ui-vue3/       # 前端Vue3项目
```

## 2. 后端开发规范

### 2.1 项目结构规范

```
com.tops.{module}/
├── controller/         # 控制器层
├── service/           # 服务层
│   └── impl/         # 服务实现
├── mapper/           # 数据访问层
├── domain/           # 领域对象
│   ├── bo/          # 业务对象
│   └── vo/          # 视图对象
├── dto/             # 数据传输对象
├── enums/           # 枚举类
├── constants/       # 常量类
├── config/          # 配置类
├── adapter/         # 适配器
├── translator/      # 转换器
└── utils/           # 工具类
```

### 2.2 命名规范

**类命名：**

- Controller: `{Business}Controller`
- Service: `I{Business}Service` (接口), `{Business}ServiceImpl` (实现)
- Mapper: `{Business}Mapper`
- Entity: `{Business}`
- BO: `{Business}Bo`
- VO: `{Business}Vo`
- DTO: `{Business}Dto`

**方法命名：**

- 查询: `query*`, `get*`, `find*`
- 新增: `insert*`, `add*`, `create*`
- 修改: `update*`, `edit*`, `modify*`
- 删除: `delete*`, `remove*`
- 校验: `validate*`, `check*`

### 2.3 Controller层规范

```java
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/{module}/{business}")
public class {Business}Controller extends

BaseController {

    private final I {
        Business
    } Service i {
        Business
    } Service;

    /**
     * 查询列表
     */
    @SaCheckPermission("{module}:{business}:list")
    @GetMapping("/list")
    public TableDataInfo < {Business} Vo > list({Business}Bo bo, PageQuery pageQuery) {
        return i {
            Business
        } Service.queryPageList(bo, pageQuery);
    }

    /**
     * 新增
     */
    @SaCheckPermission("{module}:{business}:add")
    @Log(title = "{功能名称}", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping()
    public R<Void> add ( @Validated(AddGroup.class) @RequestBody {
        Business
    } Bo bo){
        return toAjax(i {
            Business
        } Service.insertByBo(bo));
    }
}
```

### 2.4 Service层规范

```java
public interface I {
    Business
}

Service {
    /**
     * 查询单个
     */
    {
        Business
    } Vo queryById (Long id);

    /**
     * 分页查询
     */
    TableDataInfo < {Business} Vo > queryPageList({Business}Bo bo, PageQuery pageQuery);

    /**
     * 新增
     */
    Boolean insertByBo ({Business} Bo bo);

    /**
     * 修改
     */
    Boolean updateByBo ({Business} Bo bo);

    /**
     * 删除
     */
    Boolean deleteWithValidByIds (Collection < Long > ids, Boolean isValid);
}
```

### 2.5 实体类规范

```java
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("{table_name}")
public class {Business}extends

BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务字段
     */
    private String businessField;

    /**
     * 版本号(乐观锁)
     */
    @Version
    private Long version;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;
}
```

### 2.6 异常处理规范

- 使用自定义异常: `DependencyFailureException`, `InternalFailureException`, `InvalidRequestException`
- 统一返回格式: `R<T>`
- 异常信息要明确具体，便于定位问题

### 2.7 数据库规范

- 表名使用下划线命名: `user_info`
- 字段名使用下划线命名: `create_time`
- 必须包含: `id`, `create_time`, `update_time`, `create_by`, `update_by`
- 软删除使用: `del_flag`
- 乐观锁使用: `version`

## 3. 前端开发规范

### 3.1 项目结构规范

```
src/
├── api/              # API接口
├── assets/           # 静态资源
│   ├── images/      # 图片
│   ├── icons/       # 图标
│   └── styles/      # 样式文件
├── components/       # 公共组件
├── directive/        # 自定义指令
├── layout/          # 布局组件
├── plugins/         # 插件
├── router/          # 路由配置
├── store/           # 状态管理
├── utils/           # 工具函数
└── views/           # 页面组件
```

### 3.2 组件命名规范

- 组件文件名: PascalCase (`UserList.vue`)
- 组件name: PascalCase (`UserList`)
- 路由name: camelCase (`userList`)
- 文件夹名: kebab-case (`user-management`)

### 3.3 Vue组件规范

```vue
<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item label="关键字" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入关键字" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="序号" type="index" width="50" />
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="BusinessList">
import { listBusiness, delBusiness } from "@/api/business";

// 响应式数据
const loading = ref(true);
const dataList = ref([]);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  keyword: undefined
});

// 查询列表
function getList() {
  loading.value = true;
  listBusiness(queryParams.value).then(response => {
    dataList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

// 重置按钮操作
function resetQuery() {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    keyword: undefined
  };
  handleQuery();
}

// 初始化
onMounted(() => {
  getList();
});
</script>
```

### 3.4 API接口规范

```javascript
import request from '@/utils/request'

// 查询列表
export function listBusiness(query) {
  return request({
    url: '/business/list',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getBusiness(id) {
  return request({
    url: '/business/' + id,
    method: 'get'
  })
}

// 新增
export function addBusiness(data) {
  return request({
    url: '/business',
    method: 'post',
    data: data
  })
}

// 修改
export function updateBusiness(data) {
  return request({
    url: '/business',
    method: 'put',
    data: data
  })
}

// 删除
export function delBusiness(id) {
  return request({
    url: '/business/' + id,
    method: 'delete'
  })
}
```

### 3.5 样式规范

- 使用SCSS预处理器
- 采用BEM命名规范
- 使用CSS变量进行主题配置
- 响应式设计，支持移动端

## 4. 代码质量规范

### 4.1 注释规范

- 类和方法必须添加JavaDoc注释
- 复杂业务逻辑必须添加行内注释
- 前端组件和方法添加JSDoc注释

### 4.2 日志规范

- 使用Slf4j进行日志记录
- 日志级别: ERROR > WARN > INFO > DEBUG
- 关键业务操作必须记录日志

### 4.3 安全规范

- 使用Sa-Token进行权限控制
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护

### 4.4 性能规范

- 数据库查询优化
- 缓存合理使用
- 分页查询必须限制条数
- 前端组件懒加载

## 5. 部署与环境

### 5.1 环境配置

- local: 本地开发环境
- dev: 开发测试环境
- test: 测试环境
- yfb: 预发布环境
- prod: 生产环境

### 5.2 配置管理

- 使用Spring Profile进行环境区分
- 敏感配置使用环境变量
- 前端使用.env文件管理环境变量

## 6. 监控与运维

### 6.1 健康检查

- 应用健康检查: `/health`
- 数据库连接检查
- 缓存连接检查

### 6.2 监控指标

- 应用性能监控
- 业务指标监控
- 错误日志监控

## 7. 测试规范

### 7.1 单元测试

```java

@SpringBootTest
class BusinessServiceTest {

    @Autowired
    private IBusinessService businessService;

    @Test
    void testQueryById() {
        // Given
        Long id = 1L;

        // When
        BusinessVo result = businessService.queryById(id);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
    }
}
```

### 7.2 前端测试

- 使用Vitest进行单元测试
- 组件测试使用Vue Test Utils
- E2E测试使用Cypress

## 8. Git规范

### 8.1 分支管理

- master: 主分支，用于生产环境
- develop: 开发分支
- feature/*: 功能分支
- hotfix/*: 热修复分支
- release/*: 发布分支

### 8.2 提交规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型：**

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 9. 代码生成规范

### 9.1 使用代码生成器

项目提供了代码生成器模块(`tops-generator`)，可以快速生成标准的CRUD代码：

1. 配置数据表信息
2. 选择生成模板
3. 生成Controller、Service、Mapper、Entity等文件
4. 生成前端页面和API接口

### 9.2 生成后的代码规范

- 生成的代码必须符合项目规范
- 根据业务需求进行适当调整
- 添加必要的业务逻辑和校验

## 10. 最佳实践

### 10.1 后端最佳实践

1. **依赖注入**: 使用`@RequiredArgsConstructor`替代`@Autowired`
2. **异常处理**: 统一异常处理，避免在Controller中处理业务异常
3. **事务管理**: 在Service层使用`@Transactional`
4. **缓存使用**: 合理使用Redis缓存，注意缓存一致性
5. **分页查询**: 大数据量查询必须分页
6. **SQL优化**: 避免N+1查询，合理使用索引

### 10.2 前端最佳实践

1. **组件复用**: 提取公共组件，避免重复代码
2. **状态管理**: 合理使用Pinia管理全局状态
3. **路由懒加载**: 大型应用使用路由懒加载
4. **图片优化**: 使用适当的图片格式和大小
5. **SEO优化**: 合理设置页面标题和meta信息
6. **无障碍访问**: 遵循WCAG规范

### 10.3 性能优化

1. **数据库优化**:
    - 合理设计索引
    - 避免全表扫描
    - 使用连接池
2. **缓存策略**:
    - 热点数据缓存
    - 缓存预热
    - 缓存穿透防护
3. **前端优化**:
    - 代码分割
    - 资源压缩
    - CDN加速
    - 图片懒加载

## 11. 安全规范详细

### 11.1 认证授权

- 使用Sa-Token进行统一认证
- JWT Token管理
- 权限粒度控制到按钮级别
- 支持单点登录(SSO)

### 11.2 数据安全

- 敏感数据加密存储
- 数据传输HTTPS加密
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

### 11.3 接口安全

- 接口限流
- 参数校验
- 签名验证
- IP白名单

## 12. 监控告警

### 12.1 应用监控

- Spring Boot Actuator健康检查
- JVM监控
- 接口响应时间监控
- 错误率监控

### 12.2 业务监控

- 关键业务指标监控
- 用户行为分析
- 系统资源使用情况

### 12.3 日志管理

- 统一日志格式
- 日志分级存储
- 日志检索和分析
- 异常日志告警

---

**注意事项：**

1. 严格遵循本规范进行开发
2. 代码提交前必须进行自测
3. 重要功能变更需要编写单元测试
4. 遵循代码审查流程
5. 定期更新和完善开发规范
6. 新团队成员必须熟读本规范
