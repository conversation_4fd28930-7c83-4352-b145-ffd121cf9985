# JDL-Trade-OPS 后端项目打包和启动分析

## 📦 项目打包配置

### Maven Assembly Plugin 配置

**位置**: `tops-admin/pom.xml`

```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-assembly-plugin</artifactId>
    <version>3.3.0</version>
    <configuration>
        <descriptors>
            <descriptor>src/main/assembly/assembly.xml</descriptor>
        </descriptors>
    </configuration>
    <executions>
        <execution>
            <id>make-assembly</id>
            <phase>package</phase>
            <goals>
                <goal>single</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

### Assembly 配置文件

**位置**: `tops-admin/src/main/assembly/assembly.xml`

**主要功能**:
- 定义打包格式为ZIP
- 配置文件结构和依赖包含规则
- 设置可执行脚本权限

**打包结构**:
```
tops-admin-package/
├── bin/           # 启动和停止脚本
│   ├── start.sh   # 启动脚本
│   └── stop.sh    # 停止脚本
├── conf/          # 配置文件
│   ├── application.yml
│   ├── application-dev.yml
│   ├── application-prod.yml
│   ├── application-test.yml
│   ├── application-yfb.yml
│   ├── applicationContext.xml
│   ├── important.properties
│   ├── logback-plus.xml
│   └── 其他配置文件...
└── lib/           # 依赖jar包
    ├── tops-admin-4.8.2.jar
    ├── tops-system-4.8.2.jar
    ├── tops-common-4.8.2.jar
    └── 第三方依赖jar包...
```

## 🚀 启动脚本分析

### start.sh 脚本详解

**位置**: `tops-admin/target/tops-admin-package/bin/start.sh`

#### 1. 性能监控集成
```bash
curl -s "http://pfinder-master.jd.com/access/script" -o /tmp/pfinder.sh ; source /tmp/pfinder.sh || :
```
- 集成京东内部性能监控工具 PFinder
- 自动下载并加载监控脚本

#### 2. 基础目录设置
```bash
BASEDIR=`dirname $0`/..
BASEDIR=`(cd "$BASEDIR"; pwd)`
```
- 获取应用根目录的绝对路径
- 确保脚本在任何位置执行都能正确找到应用目录

#### 3. Java 环境检测
```bash
if [ -z "$JAVACMD" ] ; then
  if [ -n "$JAVA_HOME"  ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
      JAVACMD="$JAVA_HOME/jre/sh/java"  # IBM JDK on AIX
    else
      JAVACMD="$JAVA_HOME/bin/java"     # 标准JDK
    fi
  else
    JAVACMD=`which java`                # 系统PATH中的java
  fi
fi
```
- 支持多种Java环境配置
- 兼容IBM JDK和标准JDK
- 自动检测系统Java环境

#### 4. 类路径配置
```bash
CLASSPATH="$BASEDIR"/conf:"$BASEDIR"/lib/*
```
- 配置文件目录优先级最高
- 包含所有lib目录下的jar包

#### 5. JVM 参数配置
```bash
if [ -z "$OPTS_MEMORY" ] ; then
    OPTS_MEMORY="-Xss1m -Xms2G -Xmx2G -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./java_pid<pid>.hprof"
fi
```
- **堆栈大小**: 1MB
- **初始堆内存**: 2GB
- **最大堆内存**: 2GB
- **OOM处理**: 自动生成堆转储文件

#### 6. 日志目录创建
```bash
LogPath="/export/Logs/jdl-trade-ops/"
/bin/mkdir -p $LogPath
```
- 统一的日志存储路径
- 自动创建日志目录

#### 7. 应用启动
```bash
nohup "$JAVACMD" ${PFINDER_AGENT:-} $JAVA_OPTS \
  $JAVA_SPEC \
  $OPTS_MEMORY \
  -classpath "$CLASSPATH" \
  -Dbasedir="$BASEDIR" \
  -Dfile.encoding="UTF-8" \
  com.tops.TopsApplication \
  >${LogPath}jdl-trade-ops.log 2>&1 &
```
- **nohup**: 后台运行，不受终端关闭影响
- **PFinder集成**: 性能监控代理
- **主类**: `com.tops.TopsApplication`
- **日志重定向**: 标准输出和错误输出都重定向到日志文件

#### 8. Nginx 启动
```bash
sudo /export/servers/nginx/sbin/nginx
```
- 同时启动Nginx服务器
- 可能用于反向代理或静态资源服务

### stop.sh 脚本详解

**位置**: `tops-admin/target/tops-admin-package/bin/stop.sh`

#### 1. 进程查找
```bash
PIDPROC=`ps -ef | grep 'TopsApplication' | grep -v 'grep'| awk '{print $2}'`
```
- 查找所有包含'TopsApplication'的进程
- 排除grep进程本身
- 提取进程ID

#### 2. 进程停止
```bash
for PID in $PIDPROC
do
if kill -9 $PID
   then echo "process TopsApplication(Pid:$PID) was force stopped at " `date`
fi
done
```
- 强制杀死所有相关进程
- 使用`kill -9`确保进程被终止
- 记录停止时间

## 🏗️ 主应用类分析

### TopsApplication.java

**位置**: `tops-admin/src/main/java/com/tops/TopsApplication.java`

#### 1. 应用配置
```java
@SpringBootApplication(scanBasePackages = {"com.tops"})
@ImportResource(value = {"classpath:applicationContext.xml"})
@PropertySource(value = {"classpath:important.properties"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
```
- **组件扫描**: 扫描com.tops包下所有组件
- **XML配置**: 导入传统Spring XML配置
- **属性文件**: 加载重要配置属性
- **AOP代理**: 启用AspectJ代理

#### 2. 启动优化
```java
System.setProperty("spring.devtools.restart.enabled", "false");
SpringApplication application = new SpringApplication(TopsApplication.class);
application.setApplicationStartup(new BufferingApplicationStartup(2048));
```
- 禁用开发工具自动重启
- 启用应用启动缓冲，提高启动性能

#### 3. 启动信息展示
```java
@Component
public static class StartupListener implements ApplicationListener<ApplicationReadyEvent> {
    // 应用启动完成后显示访问链接和服务信息
}
```
- 显示本地和外部访问链接
- 展示健康检查端点
- 显示MCP服务和值班接口信息

## 📋 打包和部署流程

### 1. 构建命令
```bash
# 在项目根目录执行
mvn clean package -pl tops-admin -am -DskipTests

# 或者只构建tops-admin模块
cd tops-admin
mvn clean package -DskipTests
```

### 2. 打包产物
- **主jar包**: `tops-admin/target/tops-admin.jar`
- **Assembly包**: `tops-admin/target/tops-admin-package.zip`
- **解压目录**: `tops-admin/target/tops-admin-package/`

### 3. 部署步骤
```bash
# 1. 解压打包文件
unzip tops-admin-package.zip

# 2. 进入解压目录
cd tops-admin-package

# 3. 给脚本添加执行权限
chmod +x bin/*.sh

# 4. 启动应用
./bin/start.sh

# 5. 停止应用
./bin/stop.sh
```

### 4. 验证部署
```bash
# 检查进程
ps -ef | grep TopsApplication

# 检查日志
tail -f /export/Logs/jdl-trade-ops/jdl-trade-ops.log

# 健康检查
curl http://localhost:8080/health
```

## 🔧 配置管理

### 环境配置文件
- **application.yml**: 主配置文件
- **application-dev.yml**: 开发环境
- **application-test.yml**: 测试环境
- **application-prod.yml**: 生产环境
- **application-yfb.yml**: 预发布环境

### 配置优先级
1. 命令行参数
2. 系统属性
3. 环境变量
4. application-{profile}.yml
5. application.yml
6. important.properties

## 🚨 监控和日志

### 日志配置
- **日志文件**: `/export/Logs/jdl-trade-ops/jdl-trade-ops.log`
- **日志配置**: `conf/logback-plus.xml`
- **日志级别**: 可通过配置文件调整

### 性能监控
- **PFinder**: 京东内部性能监控工具
- **健康检查**: `/health` 端点
- **JVM监控**: 堆转储文件自动生成

### 服务端点
- **主服务**: `http://localhost:8080/`
- **健康检查**: `http://localhost:8080/health`
- **MCP服务**: `http://localhost:8989/sse/mcp-server/order`
- **值班接口**: `http://localhost:8080/duty/dutyInfo/list`

## 📝 总结

JDL-Trade-OPS后端项目采用了标准的Spring Boot + Maven Assembly的打包部署方案：

1. **模块化设计**: 多模块Maven项目结构
2. **标准化打包**: 使用Assembly插件生成标准部署包
3. **自动化脚本**: 提供启动和停止脚本
4. **环境隔离**: 支持多环境配置
5. **监控集成**: 集成性能监控和健康检查
6. **日志管理**: 统一的日志存储和管理

这种部署方式适合传统的服务器部署环境，提供了完整的应用生命周期管理功能。
