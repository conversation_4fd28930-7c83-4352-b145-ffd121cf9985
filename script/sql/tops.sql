-- ----------------------------
-- 1、部门表
-- ----------------------------
drop table if exists tops_sys_dept;
create table tops_sys_dept (
  dept_id           bigint(20)      not null                   comment '部门id',
  parent_id         bigint(20)      default 0                  comment '父部门id',
  ancestors         varchar(500)    default ''                 comment '祖级列表',
  dept_name         varchar(30)     default ''                 comment '部门名称',
  order_num         int(4)          default 0                  comment '显示顺序',
  leader            varchar(20)     default null               comment '负责人',
  phone             varchar(11)     default null               comment '联系电话',
  email             varchar(50)     default null               comment '邮箱',
  status            char(1)         default '0'                comment '部门状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (dept_id)
) engine=innodb comment = '部门表';

-- ----------------------------
-- 初始化-部门表数据
-- ----------------------------
insert into tops_sys_dept values(100,  0,   '0',          '交易科技',   0, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(101,  100, '0,100',      '深圳总公司', 1, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(102,  100, '0,100',      '长沙分公司', 2, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(103,  101, '0,100,101',  '研发部门',   1, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(104,  101, '0,100,101',  '市场部门',   2, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(105,  101, '0,100,101',  '测试部门',   3, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(106,  101, '0,100,101',  '财务部门',   4, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(107,  101, '0,100,101',  '运维部门',   5, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(108,  102, '0,100,102',  '市场部门',   1, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);
insert into tops_sys_dept values(109,  102, '0,100,102',  '财务部门',   2, '交易', '15888888888', '<EMAIL>', '0', '0', 'admin', sysdate(), '', null);


-- ----------------------------
-- 2、用户信息表
-- ----------------------------
drop table if exists tops_sys_user;
create table tops_sys_user (
  user_id           bigint(20)      not null     comment '用户ID',
  dept_id           bigint(20)      default null               comment '部门ID',
  user_name         varchar(30)     not null                   comment '用户账号',
  nick_name         varchar(30)     not null                   comment '用户昵称',
  user_type         varchar(10)     default 'sys_user'         comment '用户类型（sys_user系统用户）',
  email             varchar(50)     default ''                 comment '用户邮箱',
  phonenumber       varchar(11)     default ''                 comment '手机号码',
  sex               char(1)         default '0'                comment '用户性别（0男 1女 2未知）',
  avatar            varchar(100)    default ''                 comment '头像地址',
  password          varchar(100)    default ''                 comment '密码',
  status            char(1)         default '0'                comment '帐号状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  login_ip          varchar(128)    default ''                 comment '最后登录IP',
  login_date        datetime                                   comment '最后登录时间',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (user_id)
) engine=innodb comment = '用户信息表';

-- ----------------------------
-- 初始化-用户信息表数据
-- ----------------------------
insert into tops_sys_user values(1,  103, 'admin', '交易平台', 'sys_user', '<EMAIL>', '15888888888', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', sysdate(), 'admin', sysdate(), '', null, '管理员');
insert into tops_sys_user values(2,  105, 'test', '交易平台', 'sys_user', '<EMAIL>',  '15666666666', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', sysdate(), 'admin', sysdate(), '', null, '测试员');


-- ----------------------------
-- 3、岗位信息表
-- ----------------------------
drop table if exists tops_sys_post;
create table tops_sys_post
(
  post_id       bigint(20)      not null                   comment '岗位ID',
  post_code     varchar(64)     not null                   comment '岗位编码',
  post_name     varchar(50)     not null                   comment '岗位名称',
  post_sort     int(4)          not null                   comment '显示顺序',
  status        char(1)         not null                   comment '状态（0正常 1停用）',
  create_by     varchar(64)     default ''                 comment '创建者',
  create_time   datetime                                   comment '创建时间',
  update_by     varchar(64)     default ''                 comment '更新者',
  update_time   datetime                                   comment '更新时间',
  remark        varchar(500)    default null               comment '备注',
  primary key (post_id)
) engine=innodb comment = '岗位信息表';

-- ----------------------------
-- 初始化-岗位信息表数据
-- ----------------------------
insert into tops_sys_post values(1, 'ceo',  '董事长',    1, '0', 'admin', sysdate(), '', null, '');
insert into tops_sys_post values(2, 'se',   '项目经理',  2, '0', 'admin', sysdate(), '', null, '');
insert into tops_sys_post values(3, 'hr',   '人力资源',  3, '0', 'admin', sysdate(), '', null, '');
insert into tops_sys_post values(4, 'user', '普通员工',  4, '0', 'admin', sysdate(), '', null, '');


-- ----------------------------
-- 4、角色信息表
-- ----------------------------
drop table if exists tops_sys_role;
create table tops_sys_role (
  role_id              bigint(20)      not null                   comment '角色ID',
  role_name            varchar(30)     not null                   comment '角色名称',
  role_key             varchar(100)    not null                   comment '角色权限字符串',
  role_sort            int(4)          not null                   comment '显示顺序',
  data_scope           char(1)         default '1'                comment '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  menu_check_strictly  tinyint(1)      default 1                  comment '菜单树选择项是否关联显示',
  dept_check_strictly  tinyint(1)      default 1                  comment '部门树选择项是否关联显示',
  status               char(1)         not null                   comment '角色状态（0正常 1停用）',
  del_flag             char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by            varchar(64)     default ''                 comment '创建者',
  create_time          datetime                                   comment '创建时间',
  update_by            varchar(64)     default ''                 comment '更新者',
  update_time          datetime                                   comment '更新时间',
  remark               varchar(500)    default null               comment '备注',
  primary key (role_id)
) engine=innodb comment = '角色信息表';

-- ----------------------------
-- 初始化-角色信息表数据
-- ----------------------------
insert into tops_sys_role values('1', '超级管理员',  'admin',  1, 1, 1, 1, '0', '0', 'admin', sysdate(), '', null, '超级管理员');
insert into tops_sys_role values('2', '普通角色',    'common', 2, 2, 1, 1, '0', '0', 'admin', sysdate(), '', null, '普通角色');


-- ----------------------------
-- 5、菜单权限表
-- ----------------------------
drop table if exists tops_sys_menu;
create table tops_sys_menu (
  menu_id           bigint(20)      not null                   comment '菜单ID',
  menu_name         varchar(50)     not null                   comment '菜单名称',
  parent_id         bigint(20)      default 0                  comment '父菜单ID',
  order_num         int(4)          default 0                  comment '显示顺序',
  path              varchar(200)    default ''                 comment '路由地址',
  component         varchar(255)    default null               comment '组件路径',
  query_param       varchar(255)    default null               comment '路由参数',
  is_frame          int(1)          default 1                  comment '是否为外链（0是 1否）',
  is_cache          int(1)          default 0                  comment '是否缓存（0缓存 1不缓存）',
  menu_type         char(1)         default ''                 comment '菜单类型（M目录 C菜单 F按钮）',
  visible           char(1)         default 0                  comment '显示状态（0显示 1隐藏）',
  status            char(1)         default 0                  comment '菜单状态（0正常 1停用）',
  perms             varchar(100)    default null               comment '权限标识',
  icon              varchar(100)    default '#'                comment '菜单图标',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default ''                 comment '备注',
  primary key (menu_id)
) engine=innodb comment = '菜单权限表';

-- ----------------------------
-- 初始化-菜单信息表数据
-- ----------------------------
-- 一级菜单
insert into tops_sys_menu values('1', '系统管理', '0', '1', 'system',           null, '', 1, 0, 'M', '0', '0', '', 'system',   'admin', sysdate(), '', null, '系统管理目录');
insert into tops_sys_menu values('2', '系统监控', '0', '2', 'monitor',          null, '', 1, 0, 'M', '0', '0', '', 'monitor',  'admin', sysdate(), '', null, '系统监控目录');
insert into tops_sys_menu values('3', '系统工具', '0', '3', 'tool',             null, '', 1, 0, 'M', '0', '0', '', 'tool',     'admin', sysdate(), '', null, '系统工具目录');

-- 二级菜单
insert into tops_sys_menu values('100',  '用户管理', '1',   '1', 'user',       'system/user/index',        '', 1, 0, 'C', '0', '0', 'system:user:list',        'user',          'admin', sysdate(), '', null, '用户管理菜单');
insert into tops_sys_menu values('101',  '角色管理', '1',   '2', 'role',       'system/role/index',        '', 1, 0, 'C', '0', '0', 'system:role:list',        'peoples',       'admin', sysdate(), '', null, '角色管理菜单');
insert into tops_sys_menu values('102',  '菜单管理', '1',   '3', 'menu',       'system/menu/index',        '', 1, 0, 'C', '0', '0', 'system:menu:list',        'tree-table',    'admin', sysdate(), '', null, '菜单管理菜单');
insert into tops_sys_menu values('103',  '部门管理', '1',   '4', 'dept',       'system/dept/index',        '', 1, 0, 'C', '0', '0', 'system:dept:list',        'tree',          'admin', sysdate(), '', null, '部门管理菜单');
insert into tops_sys_menu values('104',  '岗位管理', '1',   '5', 'post',       'system/post/index',        '', 1, 0, 'C', '0', '0', 'system:post:list',        'post',          'admin', sysdate(), '', null, '岗位管理菜单');
insert into tops_sys_menu values('105',  '字典管理', '1',   '6', 'dict',       'system/dict/index',        '', 1, 0, 'C', '0', '0', 'system:dict:list',        'dict',          'admin', sysdate(), '', null, '字典管理菜单');
insert into tops_sys_menu values('106',  '参数设置', '1',   '7', 'config',     'system/config/index',      '', 1, 0, 'C', '0', '0', 'system:config:list',      'edit',          'admin', sysdate(), '', null, '参数设置菜单');
insert into tops_sys_menu values('107',  '通知公告', '1',   '8', 'notice',     'system/notice/index',      '', 1, 0, 'C', '0', '0', 'system:notice:list',      'message',       'admin', sysdate(), '', null, '通知公告菜单');
insert into tops_sys_menu values('108',  '日志管理', '1',   '9', 'log',        '',                         '', 1, 0, 'M', '0', '0', '',                        'log',           'admin', sysdate(), '', null, '日志管理菜单');
insert into tops_sys_menu values('109',  '在线用户', '2',   '1', 'online',     'monitor/online/index',     '', 1, 0, 'C', '0', '0', 'monitor:online:list',     'online',        'admin', sysdate(), '', null, '在线用户菜单');
insert into tops_sys_menu values('112',  '缓存列表', '2',   '6', 'cacheList',  'monitor/cache/list',       '', 1, 0, 'C', '0', '0', 'monitor:cache:list',      'redis-list',    'admin', sysdate(), '', null, '缓存列表菜单');
insert into tops_sys_menu values('113',  '缓存监控', '2',   '5', 'cache',      'monitor/cache/index',      '', 1, 0, 'C', '0', '0', 'monitor:cache:list',      'redis',         'admin', sysdate(), '', null, '缓存监控菜单');
insert into tops_sys_menu values('114',  '表单构建', '3',   '1', 'build',      'tool/build/index',         '', 1, 0, 'C', '0', '0', 'tool:build:list',         'build',         'admin', sysdate(), '', null, '表单构建菜单');
insert into tops_sys_menu values('115',  '代码生成', '3',   '2', 'gen',        'tool/gen/index',           '', 1, 0, 'C', '0', '0', 'tool:gen:list',           'code',          'admin', sysdate(), '', null, '代码生成菜单');
-- springboot-admin监控
insert into tops_sys_menu values('117',  'Admin监控', '2',  '5', 'Admin',      'monitor/admin/index',      '', 1, 0, 'C', '0', '0', 'monitor:admin:list',      'dashboard',     'admin', sysdate(), '', null, 'Admin监控菜单');
-- oss菜单
insert into tops_sys_menu values('118',  '文件管理', '1', '10', 'oss', 'system/oss/index', '', 1, 0, 'C', '0', '0', 'system:oss:list', 'upload', 'admin', sysdate(), '', null, '文件管理菜单');
-- xxl-job-admin控制台
insert into tops_sys_menu values('120',  '任务调度中心', '2',  '5', 'XxlJob',      'monitor/xxljob/index',      '', 1, 0, 'C', '0', '0', 'monitor:xxljob:list',      'job',     'admin', sysdate(), '', null, 'Xxl-Job控制台菜单');

-- 三级菜单
insert into tops_sys_menu values('500',  '操作日志', '108', '1', 'operlog',    'monitor/operlog/index',    '', 1, 0, 'C', '0', '0', 'monitor:operlog:list',    'form',          'admin', sysdate(), '', null, '操作日志菜单');
insert into tops_sys_menu values('501',  '登录日志', '108', '2', 'logininfor', 'monitor/logininfor/index', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor',    'admin', sysdate(), '', null, '登录日志菜单');
-- 用户管理按钮
insert into tops_sys_menu values('1001', '用户查询', '100', '1',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1002', '用户新增', '100', '2',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1003', '用户修改', '100', '3',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1004', '用户删除', '100', '4',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1005', '用户导出', '100', '5',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:export',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1006', '用户导入', '100', '6',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:import',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1007', '重置密码', '100', '7',  '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd',       '#', 'admin', sysdate(), '', null, '');
-- 角色管理按钮
insert into tops_sys_menu values('1008', '角色查询', '101', '1',  '', '', '', 1, 0, 'F', '0', '0', 'system:role:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1009', '角色新增', '101', '2',  '', '', '', 1, 0, 'F', '0', '0', 'system:role:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1010', '角色修改', '101', '3',  '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1011', '角色删除', '101', '4',  '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1012', '角色导出', '101', '5',  '', '', '', 1, 0, 'F', '0', '0', 'system:role:export',         '#', 'admin', sysdate(), '', null, '');
-- 菜单管理按钮
insert into tops_sys_menu values('1013', '菜单查询', '102', '1',  '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1014', '菜单新增', '102', '2',  '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1015', '菜单修改', '102', '3',  '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1016', '菜单删除', '102', '4',  '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove',         '#', 'admin', sysdate(), '', null, '');
-- 部门管理按钮
insert into tops_sys_menu values('1017', '部门查询', '103', '1',  '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1018', '部门新增', '103', '2',  '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1019', '部门修改', '103', '3',  '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1020', '部门删除', '103', '4',  '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove',         '#', 'admin', sysdate(), '', null, '');
-- 岗位管理按钮
insert into tops_sys_menu values('1021', '岗位查询', '104', '1',  '', '', '', 1, 0, 'F', '0', '0', 'system:post:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1022', '岗位新增', '104', '2',  '', '', '', 1, 0, 'F', '0', '0', 'system:post:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1023', '岗位修改', '104', '3',  '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1024', '岗位删除', '104', '4',  '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1025', '岗位导出', '104', '5',  '', '', '', 1, 0, 'F', '0', '0', 'system:post:export',         '#', 'admin', sysdate(), '', null, '');
-- 字典管理按钮
insert into tops_sys_menu values('1026', '字典查询', '105', '1', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:query',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1027', '字典新增', '105', '2', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:add',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1028', '字典修改', '105', '3', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1029', '字典删除', '105', '4', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1030', '字典导出', '105', '5', '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:export',         '#', 'admin', sysdate(), '', null, '');
-- 参数设置按钮
insert into tops_sys_menu values('1031', '参数查询', '106', '1', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:query',        '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1032', '参数新增', '106', '2', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:add',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1033', '参数修改', '106', '3', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:edit',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1034', '参数删除', '106', '4', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:remove',       '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1035', '参数导出', '106', '5', '#', '', '', 1, 0, 'F', '0', '0', 'system:config:export',       '#', 'admin', sysdate(), '', null, '');
-- 通知公告按钮
insert into tops_sys_menu values('1036', '公告查询', '107', '1', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:query',        '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1037', '公告新增', '107', '2', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:add',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1038', '公告修改', '107', '3', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit',         '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1039', '公告删除', '107', '4', '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove',       '#', 'admin', sysdate(), '', null, '');
-- 操作日志按钮
insert into tops_sys_menu values('1040', '操作查询', '500', '1', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query',      '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1041', '操作删除', '500', '2', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove',     '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1042', '日志导出', '500', '4', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export',     '#', 'admin', sysdate(), '', null, '');
-- 登录日志按钮
insert into tops_sys_menu values('1043', '登录查询', '501', '1', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query',   '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1044', '登录删除', '501', '2', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove',  '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1045', '日志导出', '501', '3', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export',  '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1050', '账户解锁', '501', '4', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock',  '#', 'admin', sysdate(), '', null, '');
-- 在线用户按钮
insert into tops_sys_menu values('1046', '在线查询', '109', '1', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query',       '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1047', '批量强退', '109', '2', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1048', '单条强退', '109', '3', '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', sysdate(), '', null, '');
-- 代码生成按钮
insert into tops_sys_menu values('1055', '生成查询', '115', '1', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query',             '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1056', '生成修改', '115', '2', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit',              '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1057', '生成删除', '115', '3', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1058', '导入代码', '115', '2', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import',            '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1059', '预览代码', '115', '4', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview',           '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1060', '生成代码', '115', '5', '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code',              '#', 'admin', sysdate(), '', null, '');
-- oss相关按钮
insert into tops_sys_menu values('1600', '文件查询', '118', '1', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:query',        '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1601', '文件上传', '118', '2', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:upload',       '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1602', '文件下载', '118', '3', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:download',     '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1603', '文件删除', '118', '4', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:remove',       '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1604', '配置添加', '118', '5', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:add',          '#', 'admin', sysdate(), '', null, '');
insert into tops_sys_menu values('1605', '配置编辑', '118', '6', '#', '', '', 1, 0, 'F', '0', '0', 'system:oss:edit',         '#', 'admin', sysdate(), '', null, '');


-- ----------------------------
-- 6、用户和角色关联表  用户N-1角色
-- ----------------------------
drop table if exists tops_sys_user_role;
create table tops_sys_user_role (
  user_id   bigint(20) not null comment '用户ID',
  role_id   bigint(20) not null comment '角色ID',
  primary key(user_id, role_id)
) engine=innodb comment = '用户和角色关联表';

-- ----------------------------
-- 初始化-用户和角色关联表数据
-- ----------------------------
insert into tops_sys_user_role values ('1', '1');
insert into tops_sys_user_role values ('2', '2');


-- ----------------------------
-- 7、角色和菜单关联表  角色1-N菜单
-- ----------------------------
drop table if exists tops_sys_role_menu;
create table tops_sys_role_menu (
  role_id   bigint(20) not null comment '角色ID',
  menu_id   bigint(20) not null comment '菜单ID',
  primary key(role_id, menu_id)
) engine=innodb comment = '角色和菜单关联表';

-- ----------------------------
-- 初始化-角色和菜单关联表数据
-- ----------------------------
insert into tops_sys_role_menu values ('2', '1');
insert into tops_sys_role_menu values ('2', '2');
insert into tops_sys_role_menu values ('2', '3');
insert into tops_sys_role_menu values ('2', '4');
insert into tops_sys_role_menu values ('2', '100');
insert into tops_sys_role_menu values ('2', '101');
insert into tops_sys_role_menu values ('2', '102');
insert into tops_sys_role_menu values ('2', '103');
insert into tops_sys_role_menu values ('2', '104');
insert into tops_sys_role_menu values ('2', '105');
insert into tops_sys_role_menu values ('2', '106');
insert into tops_sys_role_menu values ('2', '107');
insert into tops_sys_role_menu values ('2', '108');
insert into tops_sys_role_menu values ('2', '109');
insert into tops_sys_role_menu values ('2', '110');
insert into tops_sys_role_menu values ('2', '111');
insert into tops_sys_role_menu values ('2', '112');
insert into tops_sys_role_menu values ('2', '113');
insert into tops_sys_role_menu values ('2', '114');
insert into tops_sys_role_menu values ('2', '115');
insert into tops_sys_role_menu values ('2', '116');
insert into tops_sys_role_menu values ('2', '500');
insert into tops_sys_role_menu values ('2', '501');
insert into tops_sys_role_menu values ('2', '1000');
insert into tops_sys_role_menu values ('2', '1001');
insert into tops_sys_role_menu values ('2', '1002');
insert into tops_sys_role_menu values ('2', '1003');
insert into tops_sys_role_menu values ('2', '1004');
insert into tops_sys_role_menu values ('2', '1005');
insert into tops_sys_role_menu values ('2', '1006');
insert into tops_sys_role_menu values ('2', '1007');
insert into tops_sys_role_menu values ('2', '1008');
insert into tops_sys_role_menu values ('2', '1009');
insert into tops_sys_role_menu values ('2', '1010');
insert into tops_sys_role_menu values ('2', '1011');
insert into tops_sys_role_menu values ('2', '1012');
insert into tops_sys_role_menu values ('2', '1013');
insert into tops_sys_role_menu values ('2', '1014');
insert into tops_sys_role_menu values ('2', '1015');
insert into tops_sys_role_menu values ('2', '1016');
insert into tops_sys_role_menu values ('2', '1017');
insert into tops_sys_role_menu values ('2', '1018');
insert into tops_sys_role_menu values ('2', '1019');
insert into tops_sys_role_menu values ('2', '1020');
insert into tops_sys_role_menu values ('2', '1021');
insert into tops_sys_role_menu values ('2', '1022');
insert into tops_sys_role_menu values ('2', '1023');
insert into tops_sys_role_menu values ('2', '1024');
insert into tops_sys_role_menu values ('2', '1025');
insert into tops_sys_role_menu values ('2', '1026');
insert into tops_sys_role_menu values ('2', '1027');
insert into tops_sys_role_menu values ('2', '1028');
insert into tops_sys_role_menu values ('2', '1029');
insert into tops_sys_role_menu values ('2', '1030');
insert into tops_sys_role_menu values ('2', '1031');
insert into tops_sys_role_menu values ('2', '1032');
insert into tops_sys_role_menu values ('2', '1033');
insert into tops_sys_role_menu values ('2', '1034');
insert into tops_sys_role_menu values ('2', '1035');
insert into tops_sys_role_menu values ('2', '1036');
insert into tops_sys_role_menu values ('2', '1037');
insert into tops_sys_role_menu values ('2', '1038');
insert into tops_sys_role_menu values ('2', '1039');
insert into tops_sys_role_menu values ('2', '1040');
insert into tops_sys_role_menu values ('2', '1041');
insert into tops_sys_role_menu values ('2', '1042');
insert into tops_sys_role_menu values ('2', '1043');
insert into tops_sys_role_menu values ('2', '1044');
insert into tops_sys_role_menu values ('2', '1045');
insert into tops_sys_role_menu values ('2', '1050');
insert into tops_sys_role_menu values ('2', '1046');
insert into tops_sys_role_menu values ('2', '1047');
insert into tops_sys_role_menu values ('2', '1048');
insert into tops_sys_role_menu values ('2', '1055');
insert into tops_sys_role_menu values ('2', '1056');
insert into tops_sys_role_menu values ('2', '1057');
insert into tops_sys_role_menu values ('2', '1058');
insert into tops_sys_role_menu values ('2', '1059');
insert into tops_sys_role_menu values ('2', '1060');

-- ----------------------------
-- 8、角色和部门关联表  角色1-N部门
-- ----------------------------
drop table if exists tops_sys_role_dept;
create table tops_sys_role_dept (
  role_id   bigint(20) not null comment '角色ID',
  dept_id   bigint(20) not null comment '部门ID',
  primary key(role_id, dept_id)
) engine=innodb comment = '角色和部门关联表';

-- ----------------------------
-- 初始化-角色和部门关联表数据
-- ----------------------------
insert into tops_sys_role_dept values ('2', '100');
insert into tops_sys_role_dept values ('2', '101');
insert into tops_sys_role_dept values ('2', '105');


-- ----------------------------
-- 9、用户与岗位关联表  用户1-N岗位
-- ----------------------------
drop table if exists tops_sys_user_post;
create table tops_sys_user_post
(
  user_id   bigint(20) not null comment '用户ID',
  post_id   bigint(20) not null comment '岗位ID',
  primary key (user_id, post_id)
) engine=innodb comment = '用户与岗位关联表';

-- ----------------------------
-- 初始化-用户与岗位关联表数据
-- ----------------------------
insert into tops_sys_user_post values ('1', '1');
insert into tops_sys_user_post values ('2', '2');


-- ----------------------------
-- 10、操作日志记录
-- ----------------------------
drop table if exists tops_sys_oper_log;
create table tops_sys_oper_log (
  oper_id           bigint(20)      not null                   comment '日志主键',
  title             varchar(50)     default ''                 comment '模块标题',
  business_type     int(2)          default 0                  comment '业务类型（0其它 1新增 2修改 3删除）',
  method            varchar(100)    default ''                 comment '方法名称',
  request_method    varchar(10)     default ''                 comment '请求方式',
  operator_type     int(1)          default 0                  comment '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name         varchar(50)     default ''                 comment '操作人员',
  dept_name         varchar(50)     default ''                 comment '部门名称',
  oper_url          varchar(255)    default ''                 comment '请求URL',
  oper_ip           varchar(128)    default ''                 comment '主机地址',
  oper_location     varchar(255)    default ''                 comment '操作地点',
  oper_param        varchar(2000)   default ''                 comment '请求参数',
  json_result       varchar(2000)   default ''                 comment '返回参数',
  status            int(1)          default 0                  comment '操作状态（0正常 1异常）',
  error_msg         varchar(2000)   default ''                 comment '错误消息',
  oper_time         datetime                                   comment '操作时间',
  primary key (oper_id),
  key idx_sys_oper_log_bt (business_type),
  key idx_sys_oper_log_s  (status),
  key idx_sys_oper_log_ot (oper_time)
) engine=innodb comment = '操作日志记录';


-- ----------------------------
-- 11、字典类型表
-- ----------------------------
drop table if exists tops_sys_dict_type;
create table tops_sys_dict_type
(
  dict_id          bigint(20)      not null                   comment '字典主键',
  dict_name        varchar(100)    default ''                 comment '字典名称',
  dict_type        varchar(100)    default ''                 comment '字典类型',
  status           char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by        varchar(64)     default ''                 comment '创建者',
  create_time      datetime                                   comment '创建时间',
  update_by        varchar(64)     default ''                 comment '更新者',
  update_time      datetime                                   comment '更新时间',
  remark           varchar(500)    default null               comment '备注',
  primary key (dict_id),
  unique (dict_type)
) engine=innodb comment = '字典类型表';

insert into tops_sys_dict_type values(1,  '用户性别', 'sys_user_sex',        '0', 'admin', sysdate(), '', null, '用户性别列表');
insert into tops_sys_dict_type values(2,  '菜单状态', 'sys_show_hide',       '0', 'admin', sysdate(), '', null, '菜单状态列表');
insert into tops_sys_dict_type values(3,  '系统开关', 'sys_normal_disable',  '0', 'admin', sysdate(), '', null, '系统开关列表');
insert into tops_sys_dict_type values(6,  '系统是否', 'sys_yes_no',          '0', 'admin', sysdate(), '', null, '系统是否列表');
insert into tops_sys_dict_type values(7,  '通知类型', 'sys_notice_type',     '0', 'admin', sysdate(), '', null, '通知类型列表');
insert into tops_sys_dict_type values(8,  '通知状态', 'sys_notice_status',   '0', 'admin', sysdate(), '', null, '通知状态列表');
insert into tops_sys_dict_type values(9,  '操作类型', 'sys_oper_type',       '0', 'admin', sysdate(), '', null, '操作类型列表');
insert into tops_sys_dict_type values(10, '系统状态', 'sys_common_status',   '0', 'admin', sysdate(), '', null, '登录状态列表');


-- ----------------------------
-- 12、字典数据表
-- ----------------------------
drop table if exists tops_sys_dict_data;
create table tops_sys_dict_data
(
  dict_code        bigint(20)      not null                   comment '字典编码',
  dict_sort        int(4)          default 0                  comment '字典排序',
  dict_label       varchar(100)    default ''                 comment '字典标签',
  dict_value       varchar(100)    default ''                 comment '字典键值',
  dict_type        varchar(100)    default ''                 comment '字典类型',
  css_class        varchar(100)    default null               comment '样式属性（其他样式扩展）',
  list_class       varchar(100)    default null               comment '表格回显样式',
  is_default       char(1)         default 'N'                comment '是否默认（Y是 N否）',
  status           char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by        varchar(64)     default ''                 comment '创建者',
  create_time      datetime                                   comment '创建时间',
  update_by        varchar(64)     default ''                 comment '更新者',
  update_time      datetime                                   comment '更新时间',
  remark           varchar(500)    default null               comment '备注',
  primary key (dict_code)
) engine=innodb comment = '字典数据表';

insert into tops_sys_dict_data values(1,  1,  '男',       '0',       'sys_user_sex',        '',   '',        'Y', '0', 'admin', sysdate(), '', null, '性别男');
insert into tops_sys_dict_data values(2,  2,  '女',       '1',       'sys_user_sex',        '',   '',        'N', '0', 'admin', sysdate(), '', null, '性别女');
insert into tops_sys_dict_data values(3,  3,  '未知',     '2',       'sys_user_sex',        '',   '',        'N', '0', 'admin', sysdate(), '', null, '性别未知');
insert into tops_sys_dict_data values(4,  1,  '显示',     '0',       'sys_show_hide',       '',   'primary', 'Y', '0', 'admin', sysdate(), '', null, '显示菜单');
insert into tops_sys_dict_data values(5,  2,  '隐藏',     '1',       'sys_show_hide',       '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '隐藏菜单');
insert into tops_sys_dict_data values(6,  1,  '正常',     '0',       'sys_normal_disable',  '',   'primary', 'Y', '0', 'admin', sysdate(), '', null, '正常状态');
insert into tops_sys_dict_data values(7,  2,  '停用',     '1',       'sys_normal_disable',  '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '停用状态');
insert into tops_sys_dict_data values(12, 1,  '是',       'Y',       'sys_yes_no',          '',   'primary', 'Y', '0', 'admin', sysdate(), '', null, '系统默认是');
insert into tops_sys_dict_data values(13, 2,  '否',       'N',       'sys_yes_no',          '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '系统默认否');
insert into tops_sys_dict_data values(14, 1,  '通知',     '1',       'sys_notice_type',     '',   'warning', 'Y', '0', 'admin', sysdate(), '', null, '通知');
insert into tops_sys_dict_data values(15, 2,  '公告',     '2',       'sys_notice_type',     '',   'success', 'N', '0', 'admin', sysdate(), '', null, '公告');
insert into tops_sys_dict_data values(16, 1,  '正常',     '0',       'sys_notice_status',   '',   'primary', 'Y', '0', 'admin', sysdate(), '', null, '正常状态');
insert into tops_sys_dict_data values(17, 2,  '关闭',     '1',       'sys_notice_status',   '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '关闭状态');
insert into tops_sys_dict_data values(29, 99, '其他',     '0',       'sys_oper_type',       '',   'info',    'N', '0', 'admin', sysdate(), '', null, '其他操作');
insert into tops_sys_dict_data values(18, 1,  '新增',     '1',       'sys_oper_type',       '',   'info',    'N', '0', 'admin', sysdate(), '', null, '新增操作');
insert into tops_sys_dict_data values(19, 2,  '修改',     '2',       'sys_oper_type',       '',   'info',    'N', '0', 'admin', sysdate(), '', null, '修改操作');
insert into tops_sys_dict_data values(20, 3,  '删除',     '3',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '删除操作');
insert into tops_sys_dict_data values(21, 4,  '授权',     '4',       'sys_oper_type',       '',   'primary', 'N', '0', 'admin', sysdate(), '', null, '授权操作');
insert into tops_sys_dict_data values(22, 5,  '导出',     '5',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', sysdate(), '', null, '导出操作');
insert into tops_sys_dict_data values(23, 6,  '导入',     '6',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', sysdate(), '', null, '导入操作');
insert into tops_sys_dict_data values(24, 7,  '强退',     '7',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '强退操作');
insert into tops_sys_dict_data values(25, 8,  '生成代码', '8',       'sys_oper_type',       '',   'warning', 'N', '0', 'admin', sysdate(), '', null, '生成操作');
insert into tops_sys_dict_data values(26, 9,  '清空数据', '9',       'sys_oper_type',       '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '清空操作');
insert into tops_sys_dict_data values(27, 1,  '成功',     '0',       'sys_common_status',   '',   'primary', 'N', '0', 'admin', sysdate(), '', null, '正常状态');
insert into tops_sys_dict_data values(28, 2,  '失败',     '1',       'sys_common_status',   '',   'danger',  'N', '0', 'admin', sysdate(), '', null, '停用状态');


-- ----------------------------
-- 13、参数配置表
-- ----------------------------
drop table if exists tops_sys_config;
create table tops_sys_config (
  config_id         bigint(20)      not null                   comment '参数主键',
  config_name       varchar(100)    default ''                 comment '参数名称',
  config_key        varchar(100)    default ''                 comment '参数键名',
  config_value      varchar(500)    default ''                 comment '参数键值',
  config_type       char(1)         default 'N'                comment '系统内置（Y是 N否）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (config_id)
) engine=innodb comment = '参数配置表';

insert into tops_sys_config values(1, '主框架页-默认皮肤样式名称',     'sys.index.skinName',            'skin-blue',     'Y', 'admin', sysdate(), '', null, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow' );
insert into tops_sys_config values(2, '用户管理-账号初始密码',        'sys.user.initPassword',         '123456',        'Y', 'admin', sysdate(), '', null, '初始化密码 123456' );
insert into tops_sys_config values(3, '主框架页-侧边栏主题',          'sys.index.sideTheme',           'theme-dark',    'Y', 'admin', sysdate(), '', null, '深色主题theme-dark，浅色主题theme-light' );
insert into tops_sys_config values(4, '账号自助-验证码开关',          'sys.account.captchaEnabled',    'true',          'Y', 'admin', sysdate(), '', null, '是否开启验证码功能（true开启，false关闭）');
insert into tops_sys_config values(5, '账号自助-是否开启用户注册功能',  'sys.account.registerUser',      'false',         'Y', 'admin', sysdate(), '', null, '是否开启注册用户功能（true开启，false关闭）');
insert into tops_sys_config values(11, 'OSS预览列表资源开关',         'sys.oss.previewListResource',   'true',          'Y', 'admin', sysdate(), '', null, 'true:开启, false:关闭');


-- ----------------------------
-- 14、系统访问记录
-- ----------------------------
drop table if exists tops_sys_logininfor;
create table tops_sys_logininfor (
  info_id        bigint(20)     not null                  comment '访问ID',
  user_name      varchar(50)    default ''                comment '用户账号',
  ipaddr         varchar(128)   default ''                comment '登录IP地址',
  login_location varchar(255)   default ''                comment '登录地点',
  browser        varchar(50)    default ''                comment '浏览器类型',
  os             varchar(50)    default ''                comment '操作系统',
  status         char(1)        default '0'               comment '登录状态（0成功 1失败）',
  msg            varchar(255)   default ''                comment '提示消息',
  login_time     datetime                                 comment '访问时间',
  primary key (info_id),
  key idx_sys_logininfor_s  (status),
  key idx_sys_logininfor_lt (login_time)
) engine=innodb comment = '系统访问记录';


-- ----------------------------
-- 17、通知公告表
-- ----------------------------
drop table if exists tops_sys_notice;
create table tops_sys_notice (
  notice_id         bigint(20)      not null                   comment '公告ID',
  notice_title      varchar(50)     not null                   comment '公告标题',
  notice_type       char(1)         not null                   comment '公告类型（1通知 2公告）',
  notice_content    longblob        default null               comment '公告内容',
  status            char(1)         default '0'                comment '公告状态（0正常 1关闭）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(255)    default null               comment '备注',
  primary key (notice_id)
) engine=innodb comment = '通知公告表';

-- ----------------------------
-- 初始化-公告信息表数据
-- ----------------------------
insert into tops_sys_notice values('1', '温馨提醒：2018-07-01 新版本发布啦', '2', '新版本内容', '0', 'admin', sysdate(), '', null, '管理员');
insert into tops_sys_notice values('2', '维护通知：2018-07-01 系统凌晨维护', '1', '维护内容',   '0', 'admin', sysdate(), '', null, '管理员');


-- ----------------------------
-- 18、代码生成业务表
-- ----------------------------
drop table if exists tops_gen_table;
create table tops_gen_table (
  table_id          bigint(20)      not null                   comment '编号',
  table_name        varchar(200)    default ''                 comment '表名称',
  table_comment     varchar(500)    default ''                 comment '表描述',
  sub_table_name    varchar(64)     default null               comment '关联子表的表名',
  sub_table_fk_name varchar(64)     default null               comment '子表关联的外键名',
  class_name        varchar(100)    default ''                 comment '实体类名称',
  tpl_category      varchar(200)    default 'crud'             comment '使用的模板（crud单表操作 tree树表操作）',
  package_name      varchar(100)                               comment '生成包路径',
  module_name       varchar(30)                                comment '生成模块名',
  business_name     varchar(30)                                comment '生成业务名',
  function_name     varchar(50)                                comment '生成功能名',
  function_author   varchar(50)                                comment '生成功能作者',
  gen_type          char(1)         default '0'                comment '生成代码方式（0zip压缩包 1自定义路径）',
  gen_path          varchar(200)    default '/'                comment '生成路径（不填默认项目路径）',
  options           varchar(1000)                              comment '其它生成选项',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (table_id)
) engine=innodb comment = '代码生成业务表';


-- ----------------------------
-- 19、代码生成业务表字段
-- ----------------------------
drop table if exists tops_gen_table_column;
create table tops_gen_table_column (
  column_id         bigint(20)      not null                   comment '编号',
  table_id          bigint(20)                                 comment '归属表编号',
  column_name       varchar(200)                               comment '列名称',
  column_comment    varchar(500)                               comment '列描述',
  column_type       varchar(100)                               comment '列类型',
  java_type         varchar(500)                               comment 'JAVA类型',
  java_field        varchar(200)                               comment 'JAVA字段名',
  is_pk             char(1)                                    comment '是否主键（1是）',
  is_increment      char(1)                                    comment '是否自增（1是）',
  is_required       char(1)                                    comment '是否必填（1是）',
  is_insert         char(1)                                    comment '是否为插入字段（1是）',
  is_edit           char(1)                                    comment '是否编辑字段（1是）',
  is_list           char(1)                                    comment '是否列表字段（1是）',
  is_query          char(1)                                    comment '是否查询字段（1是）',
  query_type        varchar(200)    default 'EQ'               comment '查询方式（等于、不等于、大于、小于、范围）',
  html_type         varchar(200)                               comment '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  dict_type         varchar(200)    default ''                 comment '字典类型',
  sort              int                                        comment '排序',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  primary key (column_id)
) engine=innodb comment = '代码生成业务表字段';

-- ----------------------------
-- OSS对象存储表
-- ----------------------------
drop table if exists tops_sys_oss;
create table tops_sys_oss (
  oss_id          bigint(20)   not null                   comment '对象存储主键',
  file_name       varchar(255) not null default ''        comment '文件名',
  original_name   varchar(255) not null default ''        comment '原名',
  file_suffix     varchar(10)  not null default ''        comment '文件后缀名',
  url             varchar(500) not null                   comment 'URL地址',
  create_time     datetime              default null      comment '创建时间',
  create_by       varchar(64)           default ''        comment '上传人',
  update_time     datetime              default null      comment '更新时间',
  update_by       varchar(64)           default ''        comment '更新人',
  service         varchar(20)  not null default 'minio'   comment '服务商',
  primary key (oss_id)
) engine=innodb comment ='OSS对象存储表';

-- ----------------------------
-- OSS对象存储动态配置表
-- ----------------------------
drop table if exists tops_sys_oss_config;
create table tops_sys_oss_config (
  oss_config_id   bigint(20)   not null                   comment '主建',
  config_key      varchar(20)  not null   default ''      comment '配置key',
  access_key      varchar(255)            default ''      comment 'accessKey',
  secret_key      varchar(255)            default ''      comment '秘钥',
  bucket_name     varchar(255)            default ''      comment '桶名称',
  prefix           varchar(255)           default ''      comment '前缀',
  endpoint         varchar(255)           default ''      comment '访问站点',
  domain           varchar(255)           default ''      comment '自定义域名',
  is_https         char(1)                default 'N'     comment '是否https（Y=是,N=否）',
  region           varchar(255)           default ''      comment '域',
  access_policy    char(1)     not null   default '1'     comment '桶权限类型(0=private 1=public 2=custom)',
  status           char(1)                default '1'     comment '是否默认（0=是,1=否）',
  ext1             varchar(255)           default ''      comment '扩展字段',
  create_by       varchar(64)             default ''      comment '创建者',
  create_time     datetime                default null    comment '创建时间',
  update_by       varchar(64)             default ''      comment '更新者',
  update_time     datetime                default null    comment '更新时间',
  remark           varchar(500)           default null    comment '备注',
  primary key (oss_config_id)
) engine=innodb comment='对象存储配置表';

insert into tops_sys_oss_config values (1, 'minio',  'tops',            'tops123',        'tops',             '', '127.0.0.1:9000',                '','N', '',             '1' ,'0', '', 'admin', sysdate(), 'admin', sysdate(), NULL);
insert into tops_sys_oss_config values (2, 'qiniu',  'XXXXXXXXXXXXXXX',  'XXXXXXXXXXXXXXX', 'tops',             '', 's3-cn-north-1.qiniucs.com',     '','N', '',             '1' ,'1', '', 'admin', sysdate(), 'admin', sysdate(), NULL);
insert into tops_sys_oss_config values (3, 'aliyun', 'XXXXXXXXXXXXXXX',  'XXXXXXXXXXXXXXX', 'tops',             '', 'oss-cn-beijing.aliyuncs.com',   '','N', '',             '1' ,'1', '', 'admin', sysdate(), 'admin', sysdate(), NULL);
insert into tops_sys_oss_config values (4, 'qcloud', 'XXXXXXXXXXXXXXX',  'XXXXXXXXXXXXXXX', 'tops-1250000000',  '', 'cos.ap-beijing.myqcloud.com',   '','N', 'ap-beijing',   '1' ,'1', '', 'admin', sysdate(), 'admin', sysdate(), NULL);
insert into tops_sys_oss_config values (5, 'image',  'tops',            'tops123',        'tops',             'image', '127.0.0.1:9000',           '','N', '',             '1' ,'1', '', 'admin', sysdate(), 'admin', sysdate(), NULL);

-- ----------------------------
-- 审批信息表
-- ----------------------------
CREATE TABLE `tops_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1000' COMMENT '租户信息',
  `process_id` varchar(50) NOT NULL COMMENT '流程ID',
  `process_desc` varchar(50) NOT NULL COMMENT '流程描述',
  `process_instance_id` varchar(50) NOT NULL COMMENT '流程实例ID',
  `instance_status` varchar(50) NOT NULL COMMENT '流程实例状态：XBP平台：驳回（-1），进行中（0），结束（1），撤回（2）',
  `platform` varchar(50) NOT NULL COMMENT '流程所属平台',
  `instance_content` text NOT NULL COMMENT '流程实例内容',
  `instance_content_key` varchar(250) DEFAULT NULL COMMENT '流程实例内容唯一键，用于防止重复操作',
  `create_user` varchar(50) NOT NULL COMMENT '流程提交用户',
  `udpate_user` varchar(50) NOT NULL COMMENT '流程提交用户',
  `create_time` varchar(50) NOT NULL COMMENT '流程提交时间',
  `update_time` varchar(50) NOT NULL COMMENT '流程提交时间',
  `yn` int(11) NOT NULL DEFAULT '1' COMMENT '逻辑删除标志：1：未删除 0：已删除',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_yn_tenant_process_instance` (`yn`,`tenant_id`,`process_id`,`process_instance_id`),
  KEY `idx_yn_tenant_process_platform_status` (`yn`,`tenant_id`,`process_id`,`platform`,`instance_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批信息表';

-- ----------------------------
-- 值班信息表
-- ----------------------------
CREATE TABLE `tops_duty_info` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `dept_id` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '小组ID',
  `dept_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '小组名称',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `start_time` varchar(32) COLLATE utf8mb4_bin NOT NULL COMMENT '开始时间',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `end_time` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '结束时间',
  `start_week` varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '星期',
  `erp` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT 'erp',
  `name` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '姓名',
  `phone` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '电话',
  `email` varchar(200) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- 订单请求记录表
-- ----------------------------
CREATE TABLE `tops_order_request` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_no` varchar(100) DEFAULT NULL COMMENT '商家账号',
  `agent_sales` varchar(100) DEFAULT NULL COMMENT '代理销售',
  `business_scene` varchar(100) DEFAULT NULL COMMENT '业务场景',
  `business_type` varchar(100) DEFAULT NULL COMMENT '业务类型',
  `business_unit` varchar(100) DEFAULT NULL COMMENT '业务身份',
  `create_pin` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `customer_order_no` varchar(100) DEFAULT NULL COMMENT '客户订单号',
  `message_id` int(11) DEFAULT NULL COMMENT '消息ID',
  `mq_retry_message` tinyint(1) DEFAULT NULL COMMENT 'MQ重试消息',
  `operator` varchar(100) DEFAULT NULL COMMENT '操作人',
  `operator_time` datetime DEFAULT NULL COMMENT '操作时间',
  `order_mark` varchar(255) DEFAULT NULL COMMENT '订单标记',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `order_standard_status` int(11) DEFAULT NULL COMMENT '订单标准状态',
  `order_status` int(11) DEFAULT NULL COMMENT '订单状态',
  `pdq_retry_message` tinyint(1) DEFAULT NULL COMMENT 'PDQ重试消息',
  `request` text COMMENT '请求报文',
  `response` text COMMENT '响应报文',
  `response_code` varchar(100) DEFAULT NULL COMMENT '响应代码',
  `response_msg` varchar(255) DEFAULT NULL COMMENT '响应消息',
  `sku_size` int(11) DEFAULT NULL COMMENT 'SKU大小',
  `so_source` varchar(100) DEFAULT NULL COMMENT '订单来源',
  `system_caller` varchar(100) DEFAULT NULL COMMENT '系统调用者',
  `custom_order_type` varchar(50) DEFAULT NULL COMMENT '订单类型(寻源、越库、合单等)',
  `system_id` int(11) DEFAULT NULL COMMENT '系统ID',
  `tenant_id` varchar(20) DEFAULT '1000' COMMENT '租户编号',
  `topic` varchar(255) DEFAULT NULL COMMENT '主题',
  `trace_id` varchar(255) DEFAULT NULL COMMENT '追踪ID',
  `update_pin` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `yn` int(11) DEFAULT NULL COMMENT '是否有效',
  PRIMARY KEY (`id`),
  KEY `idx_trace` (`trace_id`),
  KEY `idx_tenant_querykey` (`tenant_id`,`trace_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单请求记录表';

-- ----------------------------
-- 订单信息表
-- ----------------------------
CREATE TABLE `tops_order_info` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `tenant_id` varchar(20) DEFAULT '000000' COMMENT '租户编号',
  `query_key` varchar(100) DEFAULT NULL COMMENT '查询标识',
  `order_no` varchar(100) DEFAULT NULL COMMENT '订单号',
  `parent_order_no` varchar(100) DEFAULT NULL COMMENT '父单单号',
  `customer_order_no` varchar(100) DEFAULT NULL COMMENT '客户订单号',
  `custom_order_no` varchar(100) DEFAULT NULL COMMENT '自定义单号',
  `channel_order_no` varchar(100) DEFAULT NULL COMMENT '渠道单号',
  `waybill_no` varchar(100) DEFAULT NULL COMMENT '运单号',
  `channel_source` varchar(50) DEFAULT NULL COMMENT '渠道来源',
  `account_no` varchar(100) DEFAULT NULL COMMENT '商家账号',
  `customer_name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `business_unit` varchar(50) DEFAULT NULL COMMENT '业务身份',
  `order_status` varchar(32) DEFAULT NULL COMMENT '订单状态',
  `order_custom_status` varchar(32) DEFAULT NULL COMMENT '订单自定义状态',
  `channel_order_status` varchar(32) DEFAULT NULL COMMENT '渠道订单状态',
  `channel_order_cancel_status` varchar(32) DEFAULT NULL COMMENT '渠道取消状态',
  `waybill_status` varchar(32) DEFAULT NULL COMMENT '运单状态',
  `receive_time` datetime DEFAULT NULL COMMENT '订单创建时间',
  `last_operation_time` datetime DEFAULT NULL COMMENT '最后操作时间',
  `custom_order_type` varchar(50) DEFAULT NULL COMMENT '订单类型(寻源、越库、合单等)',
  `baichuan_flag` varchar(32) DEFAULT NULL COMMENT '百川标识',
  `create_dept` bigint(20) DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_query_key` (`query_key`),
  KEY `idx_tenant_querykey` (`tenant_id`,`query_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单信息表';

-- ----------------------------
-- 订单统计数据表
-- ----------------------------
CREATE TABLE `tops_stat_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `type` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计类型：订单单量、异常记录',
  `category` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '统计分类:时间天或小时',
  `category_value` datetime DEFAULT NULL COMMENT '统计分类值(默认支持时间)',
  `dimension` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '维度',
  `dimension_value` varchar(100) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '维度值(POP、ISV)',
  `stat_count` bigint(20) DEFAULT '0' COMMENT '数量',
  `create_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建日期',
  `update_by` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新日期',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_type_category_dimension` (`type`,`category`,`category_value`,`dimension`,`dimension_value`) COMMENT '联合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;



INSERT INTO `tops_sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES
	(1793648070504763393, '订单中心', 0, 1, 'order', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'skill', 'admin', '2024-05-23 22:19:59', 'admin', '2024-05-23 22:19:59', ''),
	(1793648510441115650, '订单信息', 1793648070504763393, 2, 'orderInfo', 'order/orderInfo/index', NULL, 1, 0, 'C', '0', '0', 'order:orderInfo:list', '#', 'admin', '2024-05-23 22:22:01', 'admin', '2024-06-14 15:07:13', '订单信息菜单'),
	(1793648510441115651, '订单信息查询', 1793648510441115650, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderInfo:query', '#', 'admin', '2024-05-23 22:22:01', '', NULL, ''),
	(1793648510441115652, '订单信息新增', 1793648510441115650, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderInfo:add', '#', 'admin', '2024-05-23 22:22:01', '', NULL, ''),
	(1793648510441115653, '订单信息修改', 1793648510441115650, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderInfo:edit', '#', 'admin', '2024-05-23 22:22:01', '', NULL, ''),
	(1793648510441115654, '订单信息删除', 1793648510441115650, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderInfo:remove', '#', 'admin', '2024-05-23 22:22:01', '', NULL, ''),
	(1793648510441115655, '订单信息导出', 1793648510441115650, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderInfo:export', '#', 'admin', '2024-05-23 22:22:01', '', NULL, ''),
	(1795083462810386434, '请求分析', 1793648070504763393, 3, 'orderRequest', 'order/orderRequest/index', NULL, 1, 0, 'C', '0', '0', 'order:orderRequest:list', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:53:23', '订单请求信息菜单'),
	(1795083462810386435, '请求信息查询', 1795083462810386434, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderRequest:query', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:54:43', ''),
	(1795083462810386436, '请求信息新增', 1795083462810386434, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderRequest:add', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:54:37', ''),
	(1795083462810386437, '请求信息修改', 1795083462810386434, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderRequest:edit', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:54:51', ''),
	(1795083462810386438, '请求信息删除', 1795083462810386434, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderRequest:remove', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:54:59', ''),
	(1795083462810386439, '请求信息导出', 1795083462810386434, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:orderRequest:export', '#', 'admin', '2024-05-27 21:25:24', 'admin', '2024-06-25 20:55:08', ''),
	(1798374391231324161, '其它工具', 0, 6, 'other', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'peoples', 'admin', '2024-06-05 23:20:42', 'admin', '2024-06-05 23:20:42', ''),
	(1798608416000557058, '值班信息', 1798374391231324161, 1, 'dutyInfo', 'duty/dutyInfo/index', NULL, 1, 0, 'C', '0', '0', 'duty:dutyInfo:list', '#', 'admin', '2024-06-06 14:52:56', '', NULL, '值班信息菜单'),
	(1798608416000557059, '值班信息查询', 1798608416000557058, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'duty:dutyInfo:query', '#', 'admin', '2024-06-06 14:52:56', '', NULL, ''),
	(1798608416000557060, '值班信息新增', 1798608416000557058, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'duty:dutyInfo:add', '#', 'admin', '2024-06-06 14:52:56', '', NULL, ''),
	(1798608416000557061, '值班信息修改', 1798608416000557058, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'duty:dutyInfo:edit', '#', 'admin', '2024-06-06 14:52:56', '', NULL, ''),
	(1798608416000557062, '值班信息删除', 1798608416000557058, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'duty:dutyInfo:remove', '#', 'admin', '2024-06-06 14:52:56', '', NULL, ''),
	(1798608416000557063, '值班信息导出', 1798608416000557058, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'duty:dutyInfo:export', '#', 'admin', '2024-06-06 14:52:56', '', NULL, ''),
	(1800723644024737794, '订单统计', 1793648070504763393, 1, 'statRecord', 'order/statRecord/index', NULL, 1, 0, 'C', '0', '0', 'order:statRecord:list', '#', 'admin', '2024-06-12 10:57:25', '', NULL, '订单统计菜单'),
	(1800723644024737795, '订单统计查询', 1800723644024737794, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:statRecord:query', '#', 'admin', '2024-06-12 10:57:25', '', NULL, ''),
	(1800723644024737796, '订单统计新增', 1800723644024737794, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:statRecord:add', '#', 'admin', '2024-06-12 10:57:25', '', NULL, ''),
	(1800723644024737797, '订单统计修改', 1800723644024737794, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:statRecord:edit', '#', 'admin', '2024-06-12 10:57:25', '', NULL, ''),
	(1800723644024737798, '订单统计删除', 1800723644024737794, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:statRecord:remove', '#', 'admin', '2024-06-12 10:57:25', '', NULL, ''),
	(1800723644024737799, '订单统计导出', 1800723644024737794, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'order:statRecord:export', '#', 'admin', '2024-06-12 10:57:25', '', NULL, '');


INSERT INTO `tops_sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES
	(11, '订单-渠道来源', 'ops_order_channel_source', '0', 'trade', '2024-05-21 16:32:28', 'trade', '2024-05-21 16:32:28', ''),
	(12, '订单-标准状态', 'ops_order_standard_status', '0', 'trade', '2024-05-21 16:32:48', 'trade', '2024-05-21 16:32:48', ''),
	(13, '交易-业务身份', 'ops_business_units', '0', 'trade', '2024-05-21 16:33:11', 'trade', '2024-05-21 16:33:11', ''),
	(14, '订单-是否百川', 'ops_order_baichuan_flag', '0', 'trade', '2024-05-21 16:33:33', 'trade', '2024-05-21 16:33:33', ''),
	(15, '订单-自定义状态', 'ops_order_custom_status', '0', 'trade', '2024-05-21 16:30:47', 'trade', '2024-05-21 16:30:47', '订单自定义状态'),
	(16, '订单-渠道取消状态', 'ops_order_channel_cancel', '0', 'trade', '2024-05-21 16:34:34', 'trade', '2024-05-21 16:34:34', '渠道取消状态'),
	(17, '订单-渠道订单状态	', 'ops_order_channel_status', '0', 'trade', '2024-05-21 16:35:10', 'trade', '2024-05-21 16:35:10', '目前仅适配零售'),
	(18, '订单-运单状态', 'ops_order_waybill_status', '0', 'trade', '2024-05-21 16:35:53', 'trade', '2024-05-21 16:35:53', '运单状态'),
	(19, '订单-自定义订单状态', 'ops_custom_order_type', '0', 'trade', '2024-05-21 17:04:22', 'trade', '2024-05-21 17:04:22', '由订单各属性聚合而来，方便运维'),
	(20, '订单-抽象类型', 'ops_order_custom_type', '0', 'trade', '2024-05-21 20:47:57', 'trade', '2024-05-21 20:47:57', '多维度抽象订单类型(寻源拆分、合单、越库等)'),
	(1795077812244037634, '通用状态(成功/失败)', 'ops_common_status', '0', 'admin', '2024-05-27 21:01:16', 'admin', '2024-05-27 21:01:16', '1成功\n0失败'),
	(1805433284167831553, '订单-返回码', 'ops_order_response_msg', '0', 'admin', '2024-06-25 10:50:13', 'admin', '2024-06-25 17:31:23', NULL);
