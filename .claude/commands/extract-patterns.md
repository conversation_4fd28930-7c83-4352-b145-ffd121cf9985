# 提取项目代码模式

从现有Java项目中自动识别和提取编程模式，维护到examples/code-patterns/中。

## 执行流程

1. **项目代码分析**
   - 扫描当前项目的Java源代码文件
   - 识别项目使用的技术栈和框架
   - 分析代码结构和组织模式
   - 检测常见的设计模式和最佳实践

2. **模式识别**
   - **测试模式**: 识别JUnit测试类、Mock使用方式、测试数据准备模式
   - **配置模式**: 识别Spring配置类、Bean定义、属性配置模式
   - **服务层模式**: 识别Service类、事务处理、业务逻辑组织模式
   - **数据访问模式**: 识别Repository类、JPA使用、数据库操作模式
   - **异常处理模式**: 识别异常处理策略、错误响应模式
   - **日志模式**: 识别日志记录方式、监控埋点模式
   - **中间件集成模式**: 识别企业中间件使用方式、降级处理模式

3. **模式提取**
   - 提取代表性的代码片段
   - 去除业务特定的细节，保留通用模式
   - 添加必要的注释和说明
   - 组织成可复用的模式文档

4. **文档更新**
   - 更新examples/code-patterns/目录中的模式文档
   - 创建或更新以下文件：
     - `testing-patterns.md` - 测试模式和最佳实践
     - `configuration-patterns.md` - Spring配置模式
     - `service-patterns.md` - 服务层设计模式
     - `data-access-patterns.md` - 数据访问模式
     - `error-handling-patterns.md` - 异常处理模式
     - `logging-patterns.md` - 日志记录模式
     - `middleware-integration-patterns.md` - 中间件集成模式

5. **质量检查**
   - 验证提取的模式是否具有通用性
   - 确保代码示例语法正确
   - 检查模式文档的完整性和可读性

## 输出格式

每个模式文档包含：

```markdown
# [模式名称]

## 概述
[模式的简要描述和适用场景]

## 代码示例

### 基础用法
```java
// 提取的代码示例
```

### 高级用法
```java
// 更复杂的使用场景
```

## 最佳实践
- [实践要点1]
- [实践要点2]

## 注意事项
- [需要注意的问题]
- [常见陷阱]
```

## 使用方法

在Claude Code中运行：
```bash
/extract-patterns
```

该命令会：
1. 自动分析当前项目的Java代码
2. 识别和提取常见编程模式
3. 更新examples/code-patterns/中的文档
4. 减少手动维护examples的工作量

## 支持的模式类型

- **Spring Boot应用模式**: 应用启动、配置管理
- **REST API模式**: Controller设计、请求响应处理
- **业务服务模式**: Service层设计、事务管理
- **数据持久化模式**: JPA/MyBatis使用、数据库操作
- **测试模式**: 单元测试、集成测试、Mock使用
- **异常处理模式**: 全局异常处理、错误响应
- **安全模式**: 认证授权、输入验证
- **监控模式**: 日志记录、性能监控、健康检查
- **中间件集成模式**: 企业中间件使用、降级处理