# 创建Java PRP

## 功能文件: $ARGUMENTS

为Java功能实现生成完整的PRP，包含深入研究和企业中间件集成。确保将上下文传递给AI代理，以实现自我验证和迭代改进。首先阅读功能文件，了解需要创建什么、提供的示例如何帮助以及其他考虑因素。

AI代理只能获得你附加到PRP的上下文和训练数据。假设AI代理可以访问代码库并具有与你相同的知识截止点，因此将你的研究发现包含或引用在PRP中很重要。代理具有网络搜索功能，因此请传递文档和示例的URL。

## 研究流程

1. **企业中间件发现**
   - **始终先读取middleware-docs/middleware-list.md**以了解可用的企业中间件
   - 分析功能需求中的中间件关键词（RPC、消息、缓存、配置等）
   - 将关键词匹配到企业中间件，只读取匹配的中间件文档
   - **绝不读取所有中间件文档** - 只读取匹配的文档以避免上下文浪费

2. **代码库分析**
   - 在代码库中搜索类似的Java功能/模式
   - 识别要引用的Spring Boot模式和Maven配置
   - 注意现有的Java约定和企业中间件使用方式
   - 检查JUnit测试模式以确定验证方法

3. **外部研究**
   - 搜索Java/Spring Boot实现模式
   - 官方Java/Spring文档（包含具体URL）
   - Maven依赖文档
   - 企业中间件最佳实践和常见陷阱

4. **用户澄清**（如需要）
   - 要模仿的特定Java模式以及在哪里找到它们？
   - 企业中间件集成要求？

## PRP生成

使用PRPs/templates/prp_base.md作为模板：

### 要包含并传递给AI代理的关键上下文
- **企业中间件**: 仅来自middleware-docs/的匹配中间件文档
- **文档**: 带有具体章节的Java/Spring Boot URL
- **代码示例**: 来自代码库和examples/的真实Java代码片段
- **注意事项**: Java/Maven特性、版本问题、企业中间件约束
- **模式**: 要遵循的现有Java方法和Spring Boot模式

### 实现蓝图
- 从Java类结构和Spring Boot配置开始
- 引用真实的Java文件作为模式
- 包含Maven依赖管理
- 包含带有降级机制的企业中间件集成
- 按完成顺序列出要完成的任务以实现PRP

### 验证门控（必须可执行的Java命令）
```bash
# 编译检查
mvn clean compile

# 单元测试
mvn test

# 应用启动检查
mvn spring-boot:run --dry-run

# 集成测试（如适用）
mvn spring-boot:run &
sleep 10
curl -f http://localhost:8080/actuator/health
pkill -f spring-boot
```

*** 关键：在完成研究和探索代码库后，开始编写PRP之前 ***

*** 深入思考PRP并规划你的Java方法，然后开始编写PRP ***

### 企业中间件集成逻辑
在编写PRP之前，确保你已经：
1. 阅读middleware-docs/middleware-list.md以了解匹配系统
2. 识别哪些企业中间件（如果有）匹配功能需求
3. 只读取匹配的中间件文档文件
4. 规划带有适当降级机制的集成方法

## 输出
保存为：`PRPs/{feature-name}.md`

## 质量检查清单
- [ ] 包含所有必要上下文
- [ ] 验证门控可由AI执行
- [ ] 引用现有模式
- [ ] 清晰的实现路径
- [ ] 记录错误处理

在1-10的范围内为PRP评分（使用Claude代码一次性实现成功的信心水平）

记住：目标是通过全面的上下文实现一次性实现成功。