# 执行Java PRP

使用PRP文件实现Java功能。

## PRP文件: $ARGUMENTS

## 执行流程

1. **加载PRP**
   - 读取指定的PRP文件
   - 理解所有上下文和需求
   - 遵循PRP中的所有指令，必要时扩展研究
   - 确保拥有完全实现PRP所需的所有上下文
   - 根据需要进行更多网络搜索和代码库探索

2. **深度思考**
   - 在执行计划前深入思考。创建解决所有需求的综合计划。
   - 将复杂任务分解为更小、可管理的步骤。
   - 使用TodoWrite工具创建和跟踪实现计划。
   - 从现有代码中识别要遵循的实现模式。

3. **执行计划**
   - 执行PRP
   - 实现所有Java代码
   - 确保使用正确的Maven依赖和Spring Boot配置
   - 集成企业中间件（如果PRP中指定）

4. **验证**
   - 运行每个验证命令
   - 修复任何失败
   - 重新运行直到全部通过
   - Java验证包括：编译检查、单元测试、应用启动

5. **完成**
   - 确保所有检查清单项目完成
   - 运行最终验证套件
   - 报告完成状态
   - 重新阅读PRP确保已实现所有内容

6. **参考PRP**
   - 如果需要，可以随时重新参考PRP

注意：如果验证失败，使用PRP中的错误模式进行修复和重试。

## Java特定注意事项

- 确保Maven依赖正确配置
- 验证Spring Boot应用能够正常启动
- 检查企业中间件集成是否正常工作
- 确保JUnit测试覆盖主要功能
- 验证代码符合Java编码规范