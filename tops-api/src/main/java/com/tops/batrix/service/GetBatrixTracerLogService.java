package com.tops.batrix.service;


import com.tops.batrix.dto.BatrixTracerLog;
import com.tops.batrix.dto.BatrixTracerModel;
import com.tops.batrix.dto.BatrixTracerQueryCondition;

import java.util.List;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
public interface GetBatrixTracerLogService {

    /**
     * 获取指定请求出入参数
     * @param condition
     * @return
     */
    BatrixTracerModel getRequestContent(BatrixTracerQueryCondition condition);


    List<BatrixTracerModel> getRequestContentList(BatrixTracerQueryCondition condition);

    BatrixTracerLog getDependencyRequestContent(BatrixTracerQueryCondition condition, String method);



    BatrixTracerLog getDependencyRequestContentByNodeCode(BatrixTracerQueryCondition condition, String nodeCode, String nodeName);

    List<BatrixTracerLog> getDependencyRequestContentList(BatrixTracerQueryCondition condition);

    /**
     * 获取指定请求出入参数
     * @param condition
     * @return
     */
//    List<BatrixTracerModel> getRequestContents(BatrixTracerQueryCondition condition);
}
