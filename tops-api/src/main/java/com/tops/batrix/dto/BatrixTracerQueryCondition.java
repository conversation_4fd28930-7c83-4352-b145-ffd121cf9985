package com.tops.batrix.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Data
public class BatrixTracerQueryCondition implements Serializable {

    private static final long serialVersionUID = 9131549363111249414L;

    private String appCode;
    private String uri;
    private String traceId;
    private String bizId;
    private Long startTime;
    private Long endTime;
    private Date startTimeDate;
    private Date endTimeDate;

}
