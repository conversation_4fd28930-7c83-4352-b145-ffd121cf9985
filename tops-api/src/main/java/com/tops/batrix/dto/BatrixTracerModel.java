package com.tops.batrix.dto;


import lombok.Data;

import java.util.Date;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Data
public class BatrixTracerModel {

    /**
     * 链路ID
     */
    private String linkId;
    /**
     * 应用名
     */
    private String appName;
    /**
     * 上游应用名
     */
    private String upstreamAppName;
    /**
     * 上游机器IP
     */
    private String userIp;
    /**
     * 运行机器IP
     */
    private String serverIp;
    /**
     * 接口名
     */
    private String uri;
    /**
     * 请求正文
     */
    private String requestContent;
    /**
     * 响应正文
     */
    private String responseContent;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 业务traceId
     */
    private String traceId;
    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务场景
     */
    private String businessScene;
    /**
     * 链路状态
     */
    private String linkStatus;
    /**
     * 调用时间
     */
    private Date createTime;
    /**
     * 调用时间戳
     */
    private long createTimestamp;
    /**
     * 耗时（毫秒）
     */
    private int costTime;

}
