package com.tops.batrix.dto;


import lombok.Data;

import java.util.Date;

/**
 * @description  归因日志信息
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Data
public class BatrixTracerLog {

    /**
     * 链路ID
     */
    private String linkId;

    /**
     * appName
     */
    private String appName;

    /**
     * uri
     */
    private String uri;

    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点状态
     */
    private String nodeStatus;

    /**
     * 节点分组
     */
    private String nodeGroup;

    /**
     * 节点入参
     */
    private String nodeInParam;

    /**
     * 节点出参
     */
    private String nodeOutParam;

    /**
     * 节点扩展字段
     */
    private String nodeExt;

    /**
     * 节点顺序号
     */
    private Integer nodeOrder;

    /**
     * 调用栈-类名
     */
    private String className;

    /**
     * 调用栈-线程名
     */
    private String threadName;

    /**
     * 调用栈-方法名
     */
    private String methodName;

    /**
     * 日志级别
     */
    private String logLevel;

    /**
     * 节点内容
     */
    private String content;

    /**
     * 调用时间
     */
    private Date createTime;

    /**
     * 调用时间戳
     */
    private long createTimestamp;

    /**
     * 时间
     */
    private long costTime;

}
