<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jdl-trade-ops</artifactId>
        <groupId>com.tops</groupId>
        <version>4.8.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>tops-admin</artifactId>

    <description>
        web服务入口
    </description>

    <properties>
        <cglib.version>3.3.0</cglib.version>
        <kryo-shaded.version>4.0.2</kryo-shaded.version>
        <guava.version>31.0.1-jre</guava.version>
        <ump.version>20230930</ump.version>
        <okhttp3.version>4.9.0</okhttp3.version>
        <protostuff.version>1.2.2</protostuff.version>
        <jackson.version>2.12.7</jackson.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <jsf.version>1.7.7-HOTFIX-T2</jsf.version>
        <jsf.open.api.version>2.2.54</jsf.open.api.version>
        <jmq.version>2.3.3-RC2</jmq.version>
        <xbp.version>1.2.5</xbp.version>
        <!--零售订单基础信息查询-->
        <ioms.export.cbd.sdk.version>3.0.9-SNAPSHOT</ioms.export.cbd.sdk.version>
        <!--零售厂直订单基础信息查询-->
        <com.jd.dropship.version>1.0.0-SNAPSHOT</com.jd.dropship.version>
        <jdorders.export.dict.version>3.0.20-SNAPSHOT</jdorders.export.dict.version>
        <!--零售订单查询返回解析-->
        <purchase.sdk.domain.version>6.5.22-SNAPSHOT</purchase.sdk.domain.version>
        <purchase.serializer.utils>1.1-SNAPSHOT</purchase.serializer.utils>
        <!--关联关系版本-->
        <jdl.oms.relation.version>1.0.0-SNAPSHOT</jdl.oms.relation.version>
        <ducc.client.version>1.4.1-HOTFIX-T1</ducc.client.version>
        <dd-open-gw-api.version>4.1.0-SNAPSHOT</dd-open-gw-api.version>
        <orc-client.version>0.0.1-SNAPSHOT</orc-client.version>
        <op-client.version>0.0.2-SNAPSHOT</op-client.version>
        <eclp.master.version>0.0.2-SNAPSHOT</eclp.master.version>
        <eclp.exception.api.version>0.0.1-TEST-SNAPSHOT</eclp.exception.api.version>
        <clickhouse.version>0.2.6</clickhouse.version>
    </properties>


    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!-- Oracle -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!-- PostgreSql -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- SqlServer -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-framework</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jakarta.servlet-api</artifactId>
                    <groupId>jakarta.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-oss</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-generator</artifactId>
        </dependency>

        <!--  demo模块  -->
        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-main</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- skywalking 整合 logback -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.16.1</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>${cglib.version}</version>
        </dependency>
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo-shaded</artifactId>
            <version>${kryo-shaded.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp3.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dyuproject.protostuff</groupId>
            <artifactId>protostuff-api</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dyuproject.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dyuproject.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
            <scope>compile</scope>
        </dependency>
        <!--Jackson包-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>eclp-master-api</artifactId>
            <version>${eclp.master.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.13.graal</version>
        </dependency>

        <!--        <dependency>
                    <groupId>org.mapstruct</groupId>
                    <artifactId>mapstruct</artifactId>
                    <version>${mapstruct.version}</version>
                </dependency>
                <dependency>
                    <groupId>org.mapstruct</groupId>
                    <artifactId>mapstruct-processor</artifactId>
                    <version>${mapstruct.version}</version>
                </dependency>-->

        <!-- 京东内部工具包开始 -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <version>${jsf.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.ump</groupId>
            <artifactId>profiler</artifactId>
            <version>${ump.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 引用 PFinder SDK 库 -->
        <dependency>
            <groupId>com.jd.pfinder</groupId>
            <artifactId>pfinder-profiler-sdk</artifactId>
            <version>1.2.3-FINAL</version>
        </dependency>
        <dependency>
            <groupId>com.jd.laf.config</groupId>
            <artifactId>laf-config-client-jd-spring</artifactId>
            <version>${ducc.client.version}</version>
            <type>pom</type>
        </dependency>

        <!-- 京东内部工具包结束 -->

        <!-- 京东内部API包开始 -->
        <dependency>
            <groupId>com.jdl.cp</groupId>
            <artifactId>cp-oms-pk-client</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.jone</groupId>
            <artifactId>jone-api-sdk</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.jdl.oms</groupId>
            <artifactId>jdl-oms-client</artifactId>
            <version>2.0.9-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jmq-client-spring</artifactId>
                    <groupId>com.jd.jmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jmq-client-ump</artifactId>
                    <groupId>com.jd.jmq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jdl.cp.osc</groupId>
            <artifactId>cp-osc-client</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>

        <!-- 订单中心复合查询，底层依赖ES，最大支持400/s -->
        <dependency>
            <groupId>cn.jdl.oms</groupId>
            <artifactId>osc-client</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.pfinder</groupId>
                    <artifactId>pfinder-profiler-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 测试：0.0.1-SNAPSHOT 正式：1.0.0-RELEASE 待数据组重构rs-client后可以不单独引入间接依赖 -->
        <dependency>
            <groupId>cn.jdl.oms</groupId>
            <artifactId>rs-client</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.cp.orc</groupId>
            <artifactId>cp-orc-client</artifactId>
            <version>${orc-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jdl.cp.op</groupId>
            <artifactId>cp-op-client</artifactId>
            <version>${op-client.version}</version>
        </dependency>

        <!--关联关系-->
        <dependency>
            <groupId>cn.jdl.oms</groupId>
            <artifactId>jdl-oms-relation-client-service</artifactId>
            <version>${jdl.oms.relation.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 运单 -->
        <dependency>
            <groupId>com.jd.etms.waybill</groupId>
            <artifactId>etms-waybill-agent</artifactId>
            <version>4.2.302</version>
        </dependency>
        <!-- 零售订单查询 -->
        <dependency>
            <groupId>com.jd.order</groupId>
            <artifactId>order-sdk-component-export</artifactId>
            <version>2.2.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 天网敏感词服务 接口文档 https://joyspace.jd.com/pages/SgoMEuXn8DETRtPefQcN-->
        <dependency>
            <groupId>com.jd.risk.sensitive_word</groupId>
            <artifactId>sensitive_word_api</artifactId>
            <version>2.3.9</version>
        </dependency>

        <!-- eclp 异常中心 -->
        <dependency>
            <groupId>com.jd.eclp.exception</groupId>
            <artifactId>eclp-exception-api</artifactId>
            <version>${eclp.exception.api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.pagehelper</groupId>
                    <artifactId>pagehelper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jd.ldop.center</groupId>
                    <artifactId>ldop-center-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--es client-->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>7.17.21</version>
        </dependency>
        <!-- 京东内部工具包开始 -->
        <dependency>
            <groupId>com.jd.dd</groupId>
            <artifactId>dd-open-gw-api</artifactId>
            <version>${dd-open-gw-api.version}</version>
        </dependency>
        <!-- 京东加密工具 -->
        <dependency>
            <groupId>com.jd.security</groupId>
            <artifactId>aces-springclient</artifactId>
            <version>3.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>jmq-client-core</artifactId>
                    <groupId>com.jd.jmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.ei</groupId>
            <artifactId>mcp-server</artifactId>
            <version>1.3-SNAPSHOT</version>
        </dependency>
        <!-- 京东内部工具包结束 -->
        <!--sso begin-->
        <dependency>
            <groupId>com.jd.ssa</groupId>
            <artifactId>oidc-client</artifactId>
            <version>1.0.7-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.jd.common</groupId>
            <artifactId>jd-common-util</artifactId>
            <version>1.1.2</version>
            <exclusions>
                <exclusion>
                    <groupId>aopalliance</groupId>
                    <artifactId>aopalliance</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jmagick</groupId>
                    <artifactId>jmagick</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ice</groupId>
                    <artifactId>ice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-httpclient</groupId>
                    <artifactId>commons-httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-digester</groupId>
                    <artifactId>commons-digester</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>commons-codec</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-chain</groupId>
                    <artifactId>commons-chain</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-dbcp</groupId>
                    <artifactId>commons-dbcp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--sso end-->
        <!--XBP start-->
        <dependency>
            <groupId>com.jd.xbp</groupId>
            <artifactId>jsf-api</artifactId>
            <version>${xbp.version}</version>
        </dependency>
        <!--XBP end-->
        <!-- jmq Start -->
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-spring</artifactId>
            <version>${jmq.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-core</artifactId>
            <version>${jmq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.jmq</groupId>
            <artifactId>jmq-client-ump</artifactId>
            <version>${jmq.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- jmq End -->
        <!--jsf open api start-->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf-api</artifactId>
            <version>${jsf.open.api.version}</version>
        </dependency>
        <!--jsf open api end-->
        <!--easy excel start-->
        <!--        <dependency>-->
        <!--            <groupId>com.alibaba</groupId>-->
        <!--            <artifactId>easyexcel</artifactId>-->
        <!--            <version>${easyexcel.version}</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.poi</groupId>-->
        <!--                    <artifactId>poi-ooxml-schemas</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!--easy excel end-->
        <!--jdq start-->
        <dependency>
            <groupId>com.jd.jdq</groupId>
            <artifactId>jdq4-clients</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jd.bdp.jdq</groupId>
            <artifactId>jdwdata</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--jdq end-->
        <!-- tops-api start-->
        <dependency>
            <groupId>com.tops</groupId>
            <artifactId>tops-api</artifactId>
            <version>4.8.2</version>
        </dependency>
        <!-- tops-api end-->
        <!-- auto bots start-->
        <dependency>
            <groupId>com.jd.autobots</groupId>
            <artifactId>autobots-client</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>
        <!-- auto bots end-->


        <!--大数据创建任务-->
        <dependency>
            <groupId>com.jd.bdp</groupId>
            <artifactId>gw-sdk</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.jbdp</groupId>
            <artifactId>jbdp-edc-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--cron-->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.jsqlparser</groupId>
                    <artifactId>jsqlparser</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- ClickHouse JDBC driver -->
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>${clickhouse.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
                <configuration>
                    <mainClass>com.tops.TopsApplication</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptors>
                        <!--assembly配置文件地址，根据实际情况替换-->
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
