package com.tops.test;


import com.tops.batrix.dto.BatrixTracerQueryCondition;
import com.tops.batrix.service.GetBatrixTracerLogService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class BatrixTracerTest {

    @Resource
    GetBatrixTracerLogService getBatrixTracerLogService;

    @Test
    public void test() {
        BatrixTracerQueryCondition condition = new BatrixTracerQueryCondition();
        condition.setAppCode("express.oms.jdl.cn");
        condition.setUri("cn.jdl.oms.express.service.CallBackExpressOrderService#callBackOrder");
        condition.setStartTime(System.currentTimeMillis() - 1000);
        condition.setEndTime(System.currentTimeMillis() + 1000);
        condition.setBizId("EO620000202311222403");
        getBatrixTracerLogService.getRequestContentList(condition);
    }

    @Test
    public void test2() {
        BatrixTracerQueryCondition condition = new BatrixTracerQueryCondition();
        condition.setAppCode("jdos_jdl-pms-service");
        condition.setUri("cn.jdl.pms.api.ProductRecommendationService#checkProduct");
        String nodeCode = "cn.jdl.pms.api.ProductRecommendationService#checkProduct";
        String nodeName = "获取产品中心调用路由/商家扩展点请求参数";

            nodeCode = "com.jd.etms.vrs.api.cityaging.CityAgingNewApi#queryMixtureProductAging";
            nodeName = "获取纯配时效";
        condition.setTraceId("00c6ce9d-8152-4669-afc2-196cb50d4a2b");
        condition.setStartTime(System.currentTimeMillis() - 100000);
        condition.setEndTime(System.currentTimeMillis() + 100000);
        getBatrixTracerLogService.getDependencyRequestContentByNodeCode(condition, nodeCode, nodeName);
    }


}
