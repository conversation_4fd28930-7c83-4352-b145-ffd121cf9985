package com.tops.mcp.server;

import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.order.domain.bo.OpsOrderDetailBo;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.bo.TopsOrderStatusBo;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.domain.vo.TopsOrderStatusVo;
import com.tops.order.domain.vo.OrderModifyRecordsVO;
import com.tops.order.service.IOpsOrderInfoService;
import com.tops.order.service.ITopsOrderStatusService;
import lombok.extern.slf4j.Slf4j;
import org.jd.agent.mcp.Element.McpServerGroup;
import org.noear.solon.ai.annotation.ToolMapping;
import org.noear.solon.annotation.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

@Service("mcpOrderServer")
@Slf4j
public class McpOrderServer implements Serializable {
    private static final long serialVersionUID = 1L;
    private final static String BAICHUAN_FLAG = "1";

    @Autowired
    private IOpsOrderInfoService iOpsOrderInfoService;
    @Resource(name = "topsOrderStatusServiceImpl")
    private ITopsOrderStatusService topsOrderStatusService;


    @McpServerGroup(name = "order")
    @ToolMapping(name = "queryOrder", description = "根据订单号查询订单信息")
    public String queryOrder(@Param(name = "orderNo", description = "订单号，指的是京东物流订单号") String orderNo) {
        log.info("queryOrder:{}", orderNo);
        OpsOrderInfoBo bo = new OpsOrderInfoBo();
        bo.setBaichuanFlag(BAICHUAN_FLAG);
        bo.setOrderNo(orderNo);
        OpsOrderInfoVo vo = iOpsOrderInfoService.queryOrderDetail(bo);
        if (vo == null) {
            return "未查到对应单据信息，请检查订单号是否正确";
        }
        log.info("queryOrder-result:{}", TopsJsonUtils.toJSONString(vo));
        return TopsJsonUtils.toJSONString(vo);
    }

    @McpServerGroup(name = "order")
    @ToolMapping(name = "queryOrderStatusFlow", description = "根据订单号查询订单状态流水")
    public String queryOrderStatusFlow(@Param(name = "orderNo", description = "订单号，指的是京东物流订单号") String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return "订单号不能为空";
        }
        TopsOrderStatusBo orderStatusBo = new TopsOrderStatusBo();
        orderStatusBo.setOrderNo(orderNo);
        List<TopsOrderStatusVo> vos = topsOrderStatusService.queryOrderStatusList(orderStatusBo);
        if (CollectionUtils.isEmpty(vos)) {
            return "未查到对应单据信息，请检查订单号是否正确";
        }
        return TopsJsonUtils.toJSONString(vos);
    }

    @McpServerGroup(name = "order")
    @ToolMapping(name = "queryOrderModifyRecord", description = "根据订单号查询订单修改记录")
    public String queryOrderModifyRecord(@Param(name = "orderNo", description = "订单号，指的是京东物流订单号，纯配订单以E开头,供应链订单以ESL开头") String orderNo) {
        log.info("queryOrderModifyRecord:{}", orderNo);
        OpsOrderDetailBo bo = new OpsOrderDetailBo();
        bo.setBaichuanFlag(BAICHUAN_FLAG);
        bo.setOrderNo(orderNo);

        List<OrderModifyRecordsVO> orderModifyRecordsVOS = iOpsOrderInfoService.orderModifyRecords(bo);
        if (CollectionUtils.isEmpty(orderModifyRecordsVOS)) {
            return "未查到订单修改记录";
        }
        log.info("queryOrder-result:{}", TopsJsonUtils.toJSONString(orderModifyRecordsVOS));
        return TopsJsonUtils.toJSONString(orderModifyRecordsVOS);
    }


}
