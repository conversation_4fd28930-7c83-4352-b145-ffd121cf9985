package com.tops.mcp.server;

import com.tops.common.utils.TopsJsonUtils;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.service.IOpsOrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.jd.agent.mcp.Element.McpServerGroup;
import org.noear.solon.ai.annotation.ToolMapping;
import org.noear.solon.annotation.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;

@Service("mcpOrderServer")
@Slf4j
public class McpOrderServer implements Serializable {
    private static final long serialVersionUID = 1L;
    private final static String BAICHUAN_FLAG = "1";

    @Autowired
    private IOpsOrderInfoService iOpsOrderInfoService;

    @McpServerGroup(name = "order")
    @ToolMapping(name = "queryOrder", description = "根据订单号查询订单信息")
    public String queryOrder(@Param(name = "orderNo", description = "订单号，指的是京东物流订单号，通常供应链以ESL开头") String orderNo) {
        log.info("queryOrder:{}", orderNo);
        OpsOrderInfoBo bo = new OpsOrderInfoBo();
        bo.setBaichuanFlag(BAICHUAN_FLAG);
        bo.setOrderNo(orderNo);
        OpsOrderInfoVo vo = iOpsOrderInfoService.queryOrderDetail(bo);
        if (vo == null) {
            return "未查到对应单据信息，请检查订单号是否正确";
        }
        log.info("queryOrder-result:{}", TopsJsonUtils.toJSONString(vo));
        return TopsJsonUtils.toJSONString(vo);
    }
}
