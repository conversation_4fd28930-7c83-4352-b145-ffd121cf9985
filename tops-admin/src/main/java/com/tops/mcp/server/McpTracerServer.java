package com.tops.mcp.server;

import cn.hutool.core.date.DateTime;
import com.tops.batrix.dto.BatrixTracerLog;
import com.tops.batrix.dto.BatrixTracerModel;
import com.tops.batrix.dto.BatrixTracerQueryCondition;
import com.tops.batrix.service.GetBatrixTracerLogService;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.pl.REGON;
import org.jd.agent.mcp.Element.McpServerGroup;
import org.noear.solon.ai.annotation.ToolMapping;
import org.noear.solon.annotation.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Service("mcpTracerServer")
@Slf4j
public class McpTracerServer {
    @Resource
    GetBatrixTracerLogService getBatrixTracerLogService;

    public static final long DAY_TIME = 1000*60*60*24;

    @McpServerGroup(name = "tracer")
    @ToolMapping(name = "getProductRequestContent", description = "根据tracerId和时间获取产品中心调用路由/商家扩展点请求参数")
    public BatrixTracerLog getProductRequestContent(
        @Param(name = "tracerId", description = "调用请求唯一标识") String tracerId,
        @Param(name = "date", description = "调用时间") Date date,
        @Param(name = "isRouting", description = "标识调用路由") boolean isRouting,
        @Param(name = "isMerchantExtensionPoints", description = "商家扩展点请求参数") boolean isMerchantExtensionPoints) {
        BatrixTracerQueryCondition condition = new BatrixTracerQueryCondition();
        condition.setAppCode("jdos_jdl-pms-service");
        condition.setUri("cn.jdl.pms.api.ProductRecommendationService#checkProduct");
        String nodeCode = "cn.jdl.pms.api.ProductRecommendationService#checkProduct";
        String nodeName = "获取产品中心调用路由/商家扩展点请求参数";
        if (isRouting) {
            nodeCode = "com.jd.etms.vrs.api.cityaging.CityAgingNewApi#queryMixtureProductAging";
            nodeName = "获取纯配时效";
        } else if (isMerchantExtensionPoints) {
            //nodeCode = "cn.jdl.pms.service.ProductService#getProductByMerchant";
        }
        condition.setTraceId(tracerId);
        condition.setStartTime(date.getTime()- DAY_TIME);
        condition.setEndTime(date.getTime()+ DAY_TIME);
        return getBatrixTracerLogService.getDependencyRequestContentByNodeCode(condition, nodeCode, nodeName);
    }


    @McpServerGroup(name = "tracer")
    @ToolMapping(name = "getDependencyRequestContentList", description = "根据tracerId和时间获取调用外部系统的请求出入参")
    public List<BatrixTracerLog> getDependencyRequestContentList(
        @Param(name = "tracerId", description = "调用请求唯一标识") String tracerId,
        @Param(name = "date", description = "调用时间") String date,
        @Param(name = "appCode", description = "应用标识") String appCode,
        @Param(name = "uri", description = "调用uri") String uri
       ) throws ParseException {
        BatrixTracerQueryCondition condition = new BatrixTracerQueryCondition();
        condition.setAppCode(appCode);
        condition.setUri(uri);
        condition.setTraceId(tracerId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateTime = sdf.parse(date);
        condition.setStartTime(dateTime.getTime()- DAY_TIME);
        condition.setEndTime(dateTime.getTime()+ DAY_TIME);
        return getBatrixTracerLogService.getDependencyRequestContentList(condition);
    }

    @McpServerGroup(name = "tracer")
    @ToolMapping(name = "getRequestContentList", description = "根据tracerId和时间获取调用本系统的请求出入参")
    public List<BatrixTracerModel> getRequestContentList(
        @Param(name = "tracerId", description = "调用请求唯一标识") String tracerId,
        @Param(name = "date", description = "调用时间") String date,
        @Param(name = "appCode", description = "应用标识") String appCode,
        @Param(name = "uri", description = "调用uri") String uri,
         @Param(name = "bizId", description = "调用业务ID") String bizId) throws ParseException {
        BatrixTracerQueryCondition condition = new BatrixTracerQueryCondition();
        condition.setAppCode(appCode);
        condition.setUri(uri);
        condition.setBizId(bizId);
        condition.setTraceId(tracerId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateTime = sdf.parse(date);
        condition.setStartTime(dateTime.getTime()- DAY_TIME);
        condition.setEndTime(dateTime.getTime()+ DAY_TIME);
        return getBatrixTracerLogService.getRequestContentList(condition);
    }





}
