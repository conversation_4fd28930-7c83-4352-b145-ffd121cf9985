package com.tops.jdos.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.utils.StringUtils;
import com.tops.jdos.bean.dto.HistoryMonitorInfoRequest;
import com.tops.jdos.bean.dto.HistoryMonitorInfoResponse;
import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import com.tops.jdos.constant.EasyOpsMonitorConstant;
import com.tops.jdos.constant.EasyOpsMonitorInfraMetricKey;
import com.tops.jdos.enums.EasyOpsResourceTypeEnum;
import com.tops.jdos.utils.HttpUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName EasyOneAdapter
 * @Description 用途
 * @date 2024年09月13日 11:28 AM
 */
@Slf4j
@Service
public class EasyOpsAdapter {
    @Value("${easyops.host}")
    private String host;

    @Value("${easyops.token}")
    private String token;

    private static final String PATH_HISTORY_METRIC = "/historyMetric/getHisMetric";

    private static final String PATH_APP_EVENT_LIST = "/api/event/external/queryAppEvents";

    /**
     * 应用上下游信息查询URI
     */
    private static final String PATH_APP_UP_DOWN_STREAM = "api/dashboard/external/queryAppUpDownStream";

    /**
     * 获取监控详情
     * @param appName
     * @param type 类型 参考 {@link EasyOpsResourceTypeEnum},
     * @param startTimestamp 开始时间
     * @param endTimestamp 结束时间
     * @param resourceIds 机器ip地址
     * @return
     * @throws DependencyFailureException
     */
    public List<InfrastructureInfoDTO> queryInfrastructureInfo(String appName, EasyOpsResourceTypeEnum type, Long startTimestamp,
                                                               Long endTimestamp, List<String> resourceIds) throws DependencyFailureException {
        HistoryMonitorInfoRequest historyMonitorInfoRequest = toInfrastructureQueryRequest(appName, type, startTimestamp, endTimestamp, resourceIds);
        Map<String, String> parametersMap = new HashMap<>();
        parametersMap.put("Content-Type", "application/json");
        parametersMap.put("token", "jdl-ops");
        String responseBody = HttpUtil.post(host, PATH_HISTORY_METRIC, JSON.toJSONString(historyMonitorInfoRequest), parametersMap);
        HistoryMonitorInfoResponse responseDTO = JSON.parseObject(responseBody, new TypeReference<HistoryMonitorInfoResponse>(){});
        if (responseDTO == null) {
            log.error("Parse monitor info response failed, response body {}", responseBody);
            throw new DependencyFailureException("Parse monitor info response failed, response body: " + responseBody);
        }
        if (!HistoryMonitorInfoResponse.CODE_SUCCESS.equals(responseDTO.getCode())) {
            log.error("Query monitor info failed, response body {}", responseBody);
            throw new DependencyFailureException("Query monitor info failed, code: " + responseDTO.getCode()
                + ", message: " + responseDTO.getMsg());
        }
        if (responseDTO.getData() == null) {
            log.info("Query monitor info success, result size: 0");
            return new ArrayList<>(0);
        }

        return toInfrastructureInfoDTOList(responseDTO.getData());
    }


    /**
     * 转换请求
     * @param appName 应用名称
     * @param resourceType 资源类型
     * @param startTimestamp 开始时间
     * @param endTimestamp 结束时间
     * @return 请求
     */
    private HistoryMonitorInfoRequest toInfrastructureQueryRequest(String appName, EasyOpsResourceTypeEnum resourceType,
                                                    Long startTimestamp, Long endTimestamp, List<String> resourceIds) {
        HistoryMonitorInfoRequest request = new HistoryMonitorInfoRequest();
        request.setAppNames(Lists.newArrayList(appName));
        request.setMetrics(Lists.newArrayList(
            EasyOpsMonitorInfraMetricKey.HOST_ALIVE,
            EasyOpsMonitorInfraMetricKey.HOST_CPU_UTIL,
            EasyOpsMonitorInfraMetricKey.HOST_DISK_IO_UTIL,
            EasyOpsMonitorInfraMetricKey.HOST_LOAD_LOAD1,
            EasyOpsMonitorInfraMetricKey.HOST_NETWORK_BYTES_INCOMING,
            EasyOpsMonitorInfraMetricKey.HOST_NETWORK_BYTES_OUTGOING,
            EasyOpsMonitorInfraMetricKey.HOST_DISK_USAGE,
            EasyOpsMonitorInfraMetricKey.HOST_MEMORY_USAGE,
            EasyOpsMonitorInfraMetricKey.HOST_TCP_COUNT,
            EasyOpsMonitorInfraMetricKey.HOST_TCP_RETRANSMIT,
            EasyOpsMonitorInfraMetricKey.HOST_MIN_NTP_CLK_OFFSET_MS,
            EasyOpsMonitorInfraMetricKey.HOST_DISK_INODE_UTIL,
            EasyOpsMonitorInfraMetricKey.HOST_NETWORK_TCP_ESTABLISHED));
        request.setResType(resourceType.getType());
        request.setFrom(startTimestamp);
        request.setResourceIds(resourceIds);
        request.setTo(endTimestamp);
        return request;
    }

    private List<InfrastructureInfoDTO> toInfrastructureInfoDTOList(List<Map<String, String>> monitorInfo) {
        if (CollectionUtils.isEmpty(monitorInfo)) {
            return new ArrayList<>();
        }
        List<InfrastructureInfoDTO> infrastructureInfoDTOList = new ArrayList<>(monitorInfo.size());
        monitorInfo.forEach(map -> {
            InfrastructureInfoDTO infrastructureInfoDTO = new InfrastructureInfoDTO();
            infrastructureInfoDTO.setResourceId(map.get(EasyOpsMonitorConstant.RESOURCE_ID));
            String cpuUsageStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_CPU_UTIL);
            if (StringUtils.isNotEmpty(cpuUsageStr)) {
                infrastructureInfoDTO.setCpuUsage(formatDouble(cpuUsageStr));
            }

            String cpuLoad1minStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_LOAD_LOAD1);
            if (StringUtils.isNotEmpty(cpuLoad1minStr)) {
                infrastructureInfoDTO.setCpuLoad1min(formatDouble(cpuLoad1minStr));
            }

//            String diskUsageStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_DISK_USAGE);
//            if (StringUtils.isNotEmpty(diskUsageStr)) {
//                infrastructureInfoDTO.setDiskUsage(Double.valueOf(diskUsageStr));
//            }

            String memoryUsageStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_MEMORY_USAGE);
            if (StringUtils.isNotEmpty(memoryUsageStr)) {
                infrastructureInfoDTO.setMemoryUsage(formatDouble(memoryUsageStr));
            }

//            String networkIncomingStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_NETWORK_BYTES_INCOMING);
//            if (StringUtils.isNotEmpty(networkIncomingStr)) {
//                infrastructureInfoDTO.setNetworkIncoming(Double.valueOf(networkIncomingStr));
//            }
//
//            String networkOutgoingStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_NETWORK_BYTES_OUTGOING);
//            if (StringUtils.isNotEmpty(networkOutgoingStr)) {
//                infrastructureInfoDTO.setNetworkOutgoing(Double.valueOf(networkOutgoingStr));
//            }
//
//            String networkConnectionStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_TCP_COUNT);
//            if (StringUtils.isNotEmpty(networkConnectionStr)) {
//                infrastructureInfoDTO.setNetworkConnection(Double.valueOf(networkConnectionStr));
//            }

            String networkRetransmitStr =  map.get(EasyOpsMonitorInfraMetricKey.HOST_TCP_RETRANSMIT);
            if (StringUtils.isNotEmpty(networkRetransmitStr)) {
                infrastructureInfoDTO.setNetworkRetransmit(formatDouble(networkRetransmitStr));
            }

//            String ntpOffsetStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_MIN_NTP_CLK_OFFSET_MS);
//            if (StringUtils.isNotEmpty(ntpOffsetStr)) {
//                infrastructureInfoDTO.setNtpOffset(Double.valueOf(ntpOffsetStr));
//            }
//
//            String inodeUsageStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_DISK_INODE_UTIL);
//            if (StringUtils.isNotEmpty(inodeUsageStr)) {
//                infrastructureInfoDTO.setInodeUsage(Double.valueOf(inodeUsageStr));
//            }

            String aliveStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_ALIVE);
            if (StringUtils.isNotEmpty(aliveStr)) {
                infrastructureInfoDTO.setAlive(formatDouble(aliveStr));
            }

            String timestampStr = map.get(EasyOpsMonitorConstant.TIMESTAMP);
            if (StringUtils.isNotEmpty(timestampStr)) {
                infrastructureInfoDTO.setTimestamp(Long.valueOf(timestampStr));
            }

            String diskIOStr = map.get(EasyOpsMonitorInfraMetricKey.HOST_DISK_IO_UTIL);
            if (StringUtils.isNotEmpty(diskIOStr)) {
                infrastructureInfoDTO.setDiskIO(formatDouble(diskIOStr));
            }
            infrastructureInfoDTOList.add(infrastructureInfoDTO);
        });

        return infrastructureInfoDTOList;
    }

    private Double formatDouble(String numStr) {
        Double num = Double.valueOf(numStr);
        BigDecimal bigDecimal = new BigDecimal(num);
        return bigDecimal.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
}
