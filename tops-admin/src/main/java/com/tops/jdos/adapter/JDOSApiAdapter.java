package com.tops.jdos.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.jdos.bean.dto.*;
import com.tops.jdos.domain.*;
import com.tops.jdos.enums.JDOSApiEnum;
import com.tops.jdos.enums.JDOSMemberRoleEnum;
import com.tops.jdos.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class JDOSApiAdapter {
    @Value("${jdos.api.host}")
    private String host;
    @Value("${jdos.api.tenant}")
    private String tenant;

    @Value("${jdos.api.erp}")
    private String erp;

    @Value("${jdos.api.token}")
    private String token;

    /**
     * 查询应用负责人、应用分组、分组下容器信息
     *
     * @param jdosContext
     * @throws InvalidRequestException
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    public void queryMembers(JDOSContext jdosContext) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", tenant);
        headers.put("erp", erp);
        headers.put("token", token);
        HttpUtil httpUtil = new HttpUtil();
        JDOSResponse<ApplicationMember> response = new JDOSResponse<>();
        try {
            String responseJson = httpUtil.getRequest(host
                , String.format(JDOSApiEnum.QUEYR_APPLICATION_USERINFO.getApi(), jdosContext.getAppName())
                , parameters, headers);
            response = JSON.parseObject(responseJson, new TypeReference<JDOSResponse<ApplicationMember>>() {
            });
            log.info("JDOSApiAdapter.queryMembers appName:{},response:{}", jdosContext.getAppName(), JSON.toJSONString(response));
            List<MemberInfo> members = resolveMembers(response);
            jdosContext.withMembers(members);
        } catch (URISyntaxException e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用成员信息失败：URI语法异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new InternalFailureException("查询应用成员信息失败：URI语法异常");
        } catch (IOException e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用成员信息失败：IO异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new InternalFailureException("查询应用成员信息失败：IO异常");
        } catch (InternalFailureException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用成员信息失败：未知异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new DependencyFailureException("查询应用成员信息失败：未知异常");
        }
    }

    public void queryGroups(JDOSContext jdosContext) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("pageNum", "1");
        parameters.put("pageSize", "100");
        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", tenant);
        headers.put("erp", erp);
        headers.put("token", token);
        HttpUtil httpUtil = new HttpUtil();
        JDOSResponse<Page<Group>> response = new JDOSResponse<>();
        try {
            String responseJson = httpUtil.getRequest(host
                , String.format(JDOSApiEnum.QUEYR_APPLICATION_GROUPS.getApi(), jdosContext.getAppName())
                , parameters, headers);
            response = JSON.parseObject(responseJson, new TypeReference<JDOSResponse<Page<Group>>>() {
            });
            log.info("JDOSApiAdapter.queryGroups appName:{},response:{}", jdosContext.getAppName(), JSON.toJSONString(response));
            List<GroupInfo> groups = resolveGroups(response);
            jdosContext.withGroups(groups);
        } catch (URISyntaxException e) {
            log.error("JDOSApiAdapter.queryGroups 查询应用分组信息失败：URI语法异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new InternalFailureException("查询应用分组信息失败：URI语法异常");
        } catch (IOException e) {
            log.error("JDOSApiAdapter.queryGroups 查询应用分组信息失败：IO异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new InternalFailureException("查询应用分组信息失败：IO异常");
        } catch (InternalFailureException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryGroups 查询应用分组信息失败：未知异常,appName:{},exception:", jdosContext.getAppName(), e);
            throw new DependencyFailureException("查询应用分组信息失败：未知异常");
        }
    }


    public void queryContainers(JDOSContext jdosContext) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("pageNum", "1");
        parameters.put("pageSize", "100");
        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", tenant);
        headers.put("erp", erp);
        headers.put("token", token);
        HttpUtil httpUtil = new HttpUtil();
        Map<String, List<ContainerInfo>> containerMap = new HashMap<>();
        List<String> groupNames = jdosContext.getGroupNames();
        //TODO wangqin83 待观测串行执行实际表现后，决定是否并行
        for (String groupName : groupNames) {
            JDOSResponse<Page<Container>> response = new JDOSResponse<>();
            try {
                String responseJson = httpUtil.getRequest(host
                    , String.format(JDOSApiEnum.QUEYR_APPLICATION_GROUP_PODS.getApi(), jdosContext.getAppName(), groupName)
                    , parameters, headers);
                response = JSON.parseObject(responseJson, new TypeReference<JDOSResponse<Page<Container>>>() {
                });
                log.info("JDOSApiAdapter.queryContainers appName:{},groupName:{},response:{}", jdosContext.getAppName(), groupName, JSON.toJSONString(response));
                List<ContainerInfo> containers = resolveContainers(response);
                log.info("JDOSApiAdapter.queryContainers appName:{},groupName:{},containers:{}", jdosContext.getAppName(), groupName, JSON.toJSONString(containers));
                containerMap.put(groupName, containers);
            } catch (URISyntaxException e) {
                log.error("JDOSApiAdapter.queryContainers 查询应用分组容器信息失败：URI语法异常,appName:{},groupName:{},exception:", jdosContext.getAppName(), groupName, e);
                throw new InternalFailureException("查询应用分组容器信息失败：URI语法异常");
            } catch (IOException e) {
                log.error("JDOSApiAdapter.queryContainers 查询应用分组容器信息失败：IO异常,appName:{},groupName:{},exception:", jdosContext.getAppName(), groupName, e);
                throw new InternalFailureException("查询应用分组容器信息失败：IO异常");
            } catch (InternalFailureException e) {
                throw e;
            } catch (DependencyFailureException e) {
                throw e;
            } catch (InvalidRequestException e) {
                throw e;
            } catch (Exception e) {
                log.error("JDOSApiAdapter.queryContainers 查询应用分组容器信息失败：未知异常,appName:{},groupName:{},exception:", jdosContext.getAppName(), groupName, e);
                throw new DependencyFailureException("查询应用分组容器信息失败：未知异常");
            }
        }
        jdosContext.withContainers(containerMap);
    }

    /**
     * 关停指定机器
     *
     * @throws InvalidRequestException
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    public Map<String, String> shutdownContainer(ApplicationInfo applicationInfo, GroupInfo groupInfo, List<ContainerInfo> containers) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        JSONObject parameters =new JSONObject();
        List<Pod> pods = containers.stream().map(container -> {
            Pod pod = new Pod();
            pod.setIp(container.getIp());
            pod.setName(container.getName());
            return pod;
        }).collect(Collectors.toList());
        parameters.put("pods", pods);
        parameters.put("type", "Stop");
        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", tenant);
        headers.put("erp", erp);
        headers.put("token", token);
        headers.put("Content-Type", "application/json");
        JDOSResponse<JDOSShutdownResponse> response = new JDOSResponse<>();
        try {
            String responseJson = HttpUtil.post(host
                , String.format(JDOSApiEnum.START_OR_STOP_POD.getApi(), applicationInfo.getName(), groupInfo.getName())
                , JSON.toJSONString(parameters), headers);
            response = JSON.parseObject(responseJson, new TypeReference<JDOSResponse<JDOSShutdownResponse>>() {
            });
            log.info("JDOSApiAdapter.shutdownContainer appName:{},groupName:{},parameters:{},response:{}", applicationInfo.getName(), groupInfo.getName(), JSON.toJSONString(parameters), JSON.toJSONString(response));
            Map<String, String> result = resolveShutdown(response);
            return result;
        } catch (InternalFailureException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryGroups 关停失败：未知异常,appName:{},groupName:{},parameters:{},exception:", applicationInfo.getName(), groupInfo.getName(), JSON.toJSONString(parameters), e);
            throw new DependencyFailureException("关停失败：未知异常");
        }
    }

    /**
     * 扩容指定分组（受配额限制，后续如果迁移到serverless平台可以跳过配额限制）
     *
     * @param group       分组信息
     * @param scaleUpSize 要扩容的数量，举例：当前分组有3台机器，要扩容成5台机器，则该字段传入2.
     * @throws InvalidRequestException
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    public void scaleUpGroup(GroupInfo group, int scaleUpSize, String appName) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("groupConfigUuid", group.getGroupConfigUUID());
        jsonObject.put("replicas", scaleUpSize);

        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", tenant);
        headers.put("erp", erp);
        headers.put("token", token);
        headers.put("Content-Type", "application/json");

        String responseStr = HttpUtil.post(host, String.format(JDOSApiEnum.SCALE_UP_POD.getApi(), appName, group.getName()),
            JSON.toJSONString(jsonObject), headers);
        log.info("扩容结果 response:{}", responseStr);
        JSONObject response = JSONObject.parseObject(responseStr);
        Boolean success = response.getBoolean("success");
        if (success == null || !success) {
            log.error("扩容失败, message={}", response.getString("message"));
            throw new DependencyFailureException("扩容失败");
        }
    }


    private List<MemberInfo> resolveMembers(JDOSResponse<ApplicationMember> response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("JDOSApiAdapter.queryMembers 查询应用成员信息失败：调用接口返回失败");
            throw new DependencyFailureException("查询应用成员信息失败：调用接口返回失败");
        }
        List<MemberInfo> members = new ArrayList<>();
        try {
            members.addAll(toMemberInfo(response.getData().getAppOwner(), JDOSMemberRoleEnum.APPLICATION_OWNER.getCode()));
            members.addAll(toMemberInfo(response.getData().getAppAdmin(), JDOSMemberRoleEnum.APPLICATION_DEVELOPER.getCode()));
            members.addAll(toMemberInfo(response.getData().getAppTester(), JDOSMemberRoleEnum.APPLICATION_TESTER.getCode()));
            members.addAll(toMemberInfo(response.getData().getSystemAdmin(), JDOSMemberRoleEnum.SYSTEM_OWNER.getCode()));
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用成员信息失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询应用成员信息失败：解析返回值失败");
        }
        return members;
    }

    private List<MemberInfo> toMemberInfo(List<String> erps, String role) {
        List<MemberInfo> members = new ArrayList<>();
        if (erps != null) {
            erps.stream().map(erp -> {
                MemberInfo memberInfo = new MemberInfo();
                memberInfo.setErp(erp);
                memberInfo.setRole(role);
                return memberInfo;
            }).forEach(members::add);
        }
        return members;
    }

    private List<GroupInfo> resolveGroups(JDOSResponse<Page<Group>> response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("JDOSApiAdapter.queryMembers 查询应用分组信息失败：调用接口返回失败");
            throw new DependencyFailureException("查询应用分组信息失败：调用接口返回失败");
        }
        List<GroupInfo> groups = new ArrayList<>();
        try {
            if (response.getData().getList() != null) {
                for (Group group : response.getData().getList()) {
                    GroupInfo groupInfo = new GroupInfo();
                    groupInfo.setName(group.getGroupName());
                    groupInfo.setNickName(group.getNickname());
                    groupInfo.setGroupConfigUUID(group.getGroupConfigUUID());
                    groups.add(groupInfo);
                }
            }
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用分组信息失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询应用分组信息失败：解析返回值失败");
        }
        return groups;
    }

    private List<ContainerInfo> resolveContainers(JDOSResponse<Page<Container>> response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("JDOSApiAdapter.queryMembers 查询应用分组容器信息失败：调用接口返回失败");
            throw new DependencyFailureException("查询应用分组容器信息失败：调用接口返回失败");
        }
        List<ContainerInfo> containerInfos = new ArrayList<>();
        try {
            if (response.getData().getList() != null) {
                for (Container container : response.getData().getList()) {
                    ContainerInfo containerInfo = new ContainerInfo();
                    containerInfo.setId(container.getContainerID());
                    containerInfo.setName(container.getPodName());
                    containerInfo.setIp(container.getPodIP());
                    containerInfo.setHostIp(container.getHostIP());
                    containerInfos.add(containerInfo);
                    containerInfo.setGroupConfigUUID(container.getGroupConfigUUID());
                }
            }
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用分组容器信息失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询应用分组容器信息失败：解析返回值失败");
        }
        return containerInfos;
    }

    private Map<String, String> resolveShutdown(JDOSResponse<JDOSShutdownResponse> response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null || !response.isSuccess() || response.getData() == null) {
            log.error("JDOSApiAdapter.queryMembers 关停失败：调用接口返回失败");
            throw new DependencyFailureException("关停失败：调用接口返回失败");
        }
        Map<String, String> result = new HashMap<>();
        try {
            if (response.getData().getData() != null) {
                Set<String> ips = response.getData().getData().keySet();
                for (String ip : ips) {
                    Map<String, IpShutdownResponse> ipShutdownResponse = response.getData().getData().get(ip);
                    IpShutdownResponse shutdownResponse = ipShutdownResponse.get("do_command");
                    if (shutdownResponse != null && Objects.equals(shutdownResponse.getStatus(), "success")) {
                        result.put(ip, "success");
                    } else {
                        result.put(ip, "fail");
                    }
                }
            }
        } catch (Exception e) {
            log.error("JDOSApiAdapter.queryMembers 查询应用分组容器信息失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询应用分组容器信息失败：解析返回值失败");
        }
        return result;
    }
}
