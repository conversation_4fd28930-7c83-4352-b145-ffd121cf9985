package com.tops.jdos.constant;


/**
 * 监控指标
 */
public class EasyOpsMonitorInfraMetricKey {

    /**
     * host.cpu.util
     */
    public static final String HOST_CPU_UTIL = "host.cpu.util";

    /**
     * host.disk.io.util
     */
    public static final String HOST_DISK_IO_UTIL = "host.disk.io.util";

    /**
     * host.load.load1
     */
    public static final String HOST_LOAD_LOAD1 = "host.load.load1";

    /**
     * host.network.bytes.incoming
     */
    public static final String HOST_NETWORK_BYTES_INCOMING = "host.network.bytes.incoming";

    /**
     * host.network.bytes.outgoing
     */
    public static final String HOST_NETWORK_BYTES_OUTGOING = "host.network.bytes.outgoing";

    /**
     * host.disk.usage
     */
    public static final String HOST_DISK_USAGE = "host.disk.usage";

    /**
     * host.alive
     */
    public static final String HOST_ALIVE = "host.alive";

    /**
     * host.memory.usage
     */
    public static final String HOST_MEMORY_USAGE = "host.memory.usage";

    /**
     * host.tcp.count
     */
    public static final String HOST_TCP_COUNT = "host.tcp.count";

    /**
     * host.tcp.retransmit
     */
    public static final String HOST_TCP_RETRANSMIT = "host.tcp.retransmit";

    /**
     * host.min_ntp_clk_offset_ms
     */
    public static final String HOST_MIN_NTP_CLK_OFFSET_MS = "host.min_ntp_clk_offset_ms";

    /**
     * host.disk.inode.util
     */
    public static final String HOST_DISK_INODE_UTIL = "host.disk.inode.util";

    /**
     * host.network.tcp.established
     */
    public static final String HOST_NETWORK_TCP_ESTABLISHED = "host.network.tcp.established";
}
