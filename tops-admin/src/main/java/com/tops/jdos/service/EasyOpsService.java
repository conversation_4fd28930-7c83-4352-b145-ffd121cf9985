package com.tops.jdos.service;

import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName EasyoneService
 * @Description 用途
 * @date 2024年09月13日 4:16 PM
 */
public interface EasyOpsService {

    /**
     * 获取易维机器信息
     * @param appName
     * @param resourceIds
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public List<InfrastructureInfoDTO> getInfrastructureInfo(String appName, List<String> resourceIds, Date startTime, Date endTime) throws Exception;
}
