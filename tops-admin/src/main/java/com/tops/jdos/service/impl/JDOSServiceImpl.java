package com.tops.jdos.service.impl;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.StringUtils;
import com.tops.jdos.adapter.JDOSApiAdapter;
import com.tops.jdos.bean.dto.Application;
import com.tops.jdos.bean.po.JdosApplicationPO;
import com.tops.jdos.domain.*;
import com.tops.jdos.enums.JDOSMemberRoleEnum;
import com.tops.jdos.repository.JdosRepository;
import com.tops.jdos.service.JDOSService;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class JDOSServiceImpl implements JDOSService {
    @Autowired
    private JDOSApiAdapter jdosApiAdapter;
    @Autowired
    private JdosRepository jdosRepository;

    @Override
    public String queryAppOwnerFromJdos(String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        if (StringUtils.isEmpty(appName)) {
            return null;
        }
        //TODO wangqin83 添加异常处理
        Map<String, String> appOwners = new HashMap<>();
        JDOSContext jdosContext = new JDOSContext();
        jdosContext.withAppName(appName);
        jdosApiAdapter.queryMembers(jdosContext);
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        String ownerErp = jdosContext
            .getApplication()
            .getMembers()
            .stream()
            .filter(memberInfo -> Objects.equals(memberInfo.getRole(), JDOSMemberRoleEnum.APPLICATION_OWNER.getCode()))
            .map(MemberInfo::getErp)
            .findFirst()
            .orElse(null);

        return ownerErp;
    }

    @Override
    public Map<String, Application> queryAppsFromLocal() throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        List<JdosApplicationPO> jdosApplicationPOS = jdosRepository.queryApplications();
        Map<String, Application> result=new HashMap<>();
        for (JdosApplicationPO jdosApplicationPO : jdosApplicationPOS) {
            Application application = new Application();
            application.setName(jdosApplicationPO.getName());
            application.setOwner(jdosApplicationPO.getOwner());
            result.put(application.getName(),application);
        }
        return result;
    }

    /**
     * 查询行云部署平台指定应用的信息
     *
     * @param appName
     * @return
     * @throws InvalidRequestException
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    @Override
    public ApplicationInfo queryApplication(String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        JDOSContext jdosContext = new JDOSContext();
        jdosContext.withAppName(appName);
        jdosApiAdapter.queryMembers(jdosContext);
        jdosApiAdapter.queryGroups(jdosContext);
        jdosApiAdapter.queryContainers(jdosContext);
        return jdosContext.getApplication();
    }

    /**
     * 查询行云部署平台指定应用指定分组的信息
     *
     * @param appName
     * @param groupNames
     * @return
     * @throws InvalidRequestException
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    @Override
    public ApplicationInfo queryGroups(String appName, List<String> groupNames) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        JDOSContext jdosContext = new JDOSContext();
        jdosContext.withAppName(appName);
        List<GroupInfo> groupInfos = groupNames.stream().map(name -> {
            GroupInfo groupInfo = new GroupInfo();
            groupInfo.setName(name);
            return groupInfo;
        }).collect(Collectors.toList());
        jdosContext.withGroups(groupInfos);
        jdosApiAdapter.queryContainers(jdosContext);
        return jdosContext.getApplication();
    }

    /**
     * 关停指定应用-指定分组-指定容器
     *
     * @param container
     * @return
     * @throws InvalidRequestException
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    @Override
    public Map<String,String> shutdownContainer(ApplicationInfo application, GroupInfo group, List<ContainerInfo> containers) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
       return jdosApiAdapter.shutdownContainer(application,group,containers);
    }

    /**
     * 扩容指定分组（受配额限制，后续如果迁移到serverless平台可以跳过配额限制）
     *
     * @param group       分组信息
     * @param scaleUpSize 要扩容的数量，举例：当前分组有3台机器，要扩容成5台机器，则该字段传入2.
     * @throws InvalidRequestException
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    @Override
    public void scaleUpGroup(GroupInfo group, Integer scaleUpSize, String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        //查询容器信息
        ApplicationInfo applicationInfo = queryGroups(appName, Arrays.asList(group.getName()));
        String groupConfigUuid = applicationInfo.getGroups().get(0).getContainers().get(0).getGroupConfigUUID();
        log.info("扩容机器查询到的配置id为:{}", groupConfigUuid);
        group.setGroupConfigUUID(groupConfigUuid);
        //使用扩容
        jdosApiAdapter.scaleUpGroup(group, scaleUpSize, appName);
    }
}
