package com.tops.jdos.service.impl;

import com.jd.fastjson.JSON;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.utils.ClassPropertyUtil;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsBeanCopyUtils;
import com.tops.jdos.adapter.EasyOpsAdapter;
import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import com.tops.jdos.enums.EasyOpsResourceTypeEnum;
import com.tops.jdos.service.EasyOpsService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @ClassName EasyOneServiceImpl
 * @Description 用途
 * @date 2024年09月13日 8:45 PM
 */
@Service("easyOpsService")
@Slf4j
public class EasyOpsServiceImpl implements EasyOpsService {
    @Resource
    EasyOpsAdapter easyOneAdapter;

    @Override
    public List<InfrastructureInfoDTO> getInfrastructureInfo(String appName, List<String> resourceIds, Date startTime, Date endTime) throws Exception {
        if (StringUtils.isEmpty(appName)) {
            throw new InternalFailureException("appName不能为空");
        }

        if (CollectionUtils.isEmpty(resourceIds)) {
            throw new InternalFailureException("ip信息不能为空");
        }

        if (startTime == null || endTime == null) {
            throw new InternalFailureException("开始/结束时间不能为空");
        }

        if (DateUtils.differentDaysByMillisecond(startTime, endTime) > 600000) {
            throw new InternalFailureException("查询时间不能超过十分钟");
        }

        List<InfrastructureInfoDTO> infrastructureInfoDTOList = easyOneAdapter.queryInfrastructureInfo(appName, EasyOpsResourceTypeEnum.DOCKER,
            startTime.getTime(), endTime.getTime(), resourceIds);
        log.info("EasyOpsServiceImpl.getInfrastructureInfo appName:{},startTime:{},endTime:{},resourceIds:{},response:{}", appName, startTime.getTime(), endTime.getTime(), JSON.toJSONString(resourceIds), JSON.toJSONString(infrastructureInfoDTOList));
        //@link EasyOpsResponse.txt
        Map<String, List<InfrastructureInfoDTO>> infrastructureInfoDTOMap = infrastructureInfoDTOList.stream()
            .collect(Collectors.groupingBy(infrastructureInfoDTO -> infrastructureInfoDTO.getResourceId() + "_" + infrastructureInfoDTO.getTimestamp()));
        List<InfrastructureInfoDTO> resultList = new ArrayList<>();
        for (List<InfrastructureInfoDTO> sublist : infrastructureInfoDTOMap.values()) {
            InfrastructureInfoDTO infrastructureInfoDTO = new InfrastructureInfoDTO();
            for (InfrastructureInfoDTO item : sublist) {
                BeanUtils.copyProperties(item, infrastructureInfoDTO, ClassPropertyUtil.getNullPropertyNames(item));
            }

            resultList.add(infrastructureInfoDTO);
        }

        return resultList;
    }
}
