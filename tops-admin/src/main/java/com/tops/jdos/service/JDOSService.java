package com.tops.jdos.service;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.jdos.bean.dto.Application;
import com.tops.jdos.domain.ApplicationInfo;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;

import java.util.List;
import java.util.Map;

/**
 * 行云服务
 */
public interface JDOSService {

    String queryAppOwnerFromJdos(String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    Map<String, Application> queryAppsFromLocal() throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 查询行云部署平台指定应用的信息
     *
     * @param appName
     * @return
     * @throws InvalidRequestException
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    ApplicationInfo queryApplication(String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 查询行云部署平台指定应用指定分组的信息
     *
     * @param appName
     * @param groupNames
     * @return
     * @throws InvalidRequestException
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    ApplicationInfo queryGroups(String appName, List<String> groupNames) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 关停指定应用-指定分组-指定容器
     *
     * @param container
     * @return
     * @throws InvalidRequestException    关停失败会通过异常抛出，具体形式待定
     * @throws InternalFailureException
     * @throws DependencyFailureException
     */
    Map<String,String> shutdownContainer(ApplicationInfo application, GroupInfo group, List<ContainerInfo> container) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 扩容指定分组（受配额限制，后续如果迁移到serverless平台可以跳过配额限制）
     *
     * @param group       分组信息
     * @param scaleUpSize 要扩容的数量，举例：当前分组有3台机器，要扩容成5台机器，则该字段传入2.
     * @throws InvalidRequestException    扩容失败会通过异常抛出，具体形式待定
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    void scaleUpGroup(GroupInfo group, Integer scaleUpSize, String appName) throws InvalidRequestException, InternalFailureException, DependencyFailureException;
}
