package com.tops.jdos.domain;

import com.tops.jdos.domain.ContainerInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class GroupInfo implements Serializable {
    private static final long serialVersionUID = 7705939163461964788L;

    private String name;
    private String nickName;
    private String groupConfigUUID;
    private List<ContainerInfo> containers;
}
