package com.tops.jdos.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@Setter
public class JDOSContext implements Serializable {
    private static final long serialVersionUID = 1337985437765317346L;
    private ApplicationInfo application;

    public JDOSContext withMemberErps(List<String> memberErps) {
        if (application == null) {
            application = new ApplicationInfo();
        }
        if (application.getMembers() == null) {
            application.setMembers(new ArrayList<>());
        }
        List<MemberInfo> members = application.getMembers();
        for (String memberErp : memberErps) {
            MemberInfo memberInfo = new MemberInfo();
            members.add(memberInfo);
        }
        return this;
    }

    public JDOSContext withMembers(List<MemberInfo> members) {
        if (application == null) {
            application = new ApplicationInfo();
        }
        if (application.getMembers() == null) {
            application.setMembers(new ArrayList<>());
        }
        List<MemberInfo> nowMembers = application.getMembers();
        nowMembers.addAll(members);
        return this;
    }
    public JDOSContext withGroups(List<GroupInfo> groups) {
        if (application == null) {
            application = new ApplicationInfo();
        }
        if (application.getGroups() == null) {
            application.setGroups(new ArrayList<>());
        }
        List<GroupInfo> nowGroups = application.getGroups();
        nowGroups.addAll(groups);
        return this;
    }

    public JDOSContext withContainers(Map<String,List<ContainerInfo>> containers) {
        if (application == null) {
            application = new ApplicationInfo();
        }
        if (application.getGroups() == null) {
            application.setGroups(new ArrayList<>());
        }
        for (GroupInfo group : application.getGroups()) {
            if (group.getContainers()==null){
                group.setContainers(new ArrayList<>());
            }
            List<ContainerInfo> nowContainers = group.getContainers();
            nowContainers.addAll(containers.get(group.getName()));
        }
        return this;
    }

    public JDOSContext withAppName(String appName) {
        if (application == null) {
            application = new ApplicationInfo();
        }
        application.setName(appName);
        return this;
    }

    public String getAppName() {
        if (application == null) {
            return null;
        }
        return application.getName();
    }

    public List<String> getGroupNames() {
        if (application == null) {
            return new ArrayList<>();
        }
        if (application.getGroups()==null){
            return new ArrayList<>();
        }
        return application.getGroups().stream().map(GroupInfo::getName).collect(Collectors.toList());
    }


}
