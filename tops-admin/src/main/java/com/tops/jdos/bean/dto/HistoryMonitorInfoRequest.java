package com.tops.jdos.bean.dto;

import java.util.List;
import lombok.Data;

/**
 * 历史监控信息请求
 */
@Data
public class HistoryMonitorInfoRequest {

    /**
     * 机构全码
     */
    private String orgFullCode;

    /**
     * 系统名称
     */
    private List<String> appNames;

    /**
     * 资源id
     */
    private List<String> resourceIds;

    /**
     * 资源类型
     */
    private String resType;

    /**
     * 指标
     */
    private List<String> metrics;

    /**
     * 开始时间
     */
    private Long from;

    /**
     * 结束时间
     */
    private Long to;
}
