package com.tops.jdos.bean.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName InfrastructureInfoDto
 * @Description 用途
 * @date 2024年09月13日 2:06 PM
 */
@Data
public class InfrastructureInfoDTO {

    /**
     * 被查询资源ID
     */
    private String resourceId;

    /**
     * CPU使用率，百分比
     */
    private Double cpuUsage;

    /**
     * CPU负载，1分钟统计周期
     */
    private Double cpuLoad1min;

//    /**
//     * 磁盘使用率，百分比
//     */
//    private Double diskUsage;

    /**
     * 内存使用率，百分比
     */
    private Double memoryUsage;

//    /**
//     * 网络流入流量，单位byte/min
//     */
//    private Double networkIncoming;
//
//    /**
//     * 网络流出流量，单位byte/min
//     */
//    private Double networkOutgoing;

//    /**
//     * 网络连接数
//     */
//    private Double networkConnection;

    /**
     * 网络重传次数
     */
    private Double networkRetransmit;

//    /**
//     * NTP偏移量
//     */
//    private Double ntpOffset;
//
//    /**
//     * 磁盘inode使用率，百分比
//     */
//    private Double inodeUsage;

    /**
     * 容器是否存活
     */
    private Double alive;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 磁盘满忙
     */
    private Double diskIO;
}

