package com.tops.jdos.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ApplicationMember {
    /**
     * 系统负责人
     */
    List<String> systemOwner;
    /**
     * 系统研发
     */
    List<String> systemAdmin;
    /**
     * 系统测试
     */
    List<String> systemTester;
    /**
     * 系统运维
     */
    List<String> systemOp;
    /**
     * 应用负责人
     */
    List<String> appOwner;
    /**
     * 应用研发
     */
    List<String> appAdmin;
    /**
     * 应用测试
     */
    List<String> appTester;
    /**
     * 应用运维
     */
    List<String> appOp;
    /**
     * 应用访客
     */
    List<String> user;

}
