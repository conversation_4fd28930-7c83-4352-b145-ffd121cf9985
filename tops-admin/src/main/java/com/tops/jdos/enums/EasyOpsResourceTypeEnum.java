package com.tops.jdos.enums;

/**
 * EasyOps资源类型
 */
public enum EasyOpsResourceTypeEnum {

    /**
     * 京东云主机
     */
    VM("VM", "京东云主机"),

    /**
     * 京东云POD
     */
    POD("POD", "京东云POD"),

    /**
     * 京东云-mysql
     */
    MYSQL("MYSQL", "京东云-mysql"),

    /**
     * 京东云PG-sql
     */
    PG("POSTGRE_SQL", "京东云PG-sql"),

    /**
     * 京东云SQL-server
     */
    SQLSERVER("SQLSERVER", "京东云SQL-server"),

    /**
     * 京东云-percona
     */
    PERCONA("PERCONA", "京东云-percona"),

    /**
     * 京东云TIDB
     */
    TIDB("TIDB", "京东云TIDB"),

    /**
     * 京东云ClickHouse
     */
    CLICKHOUSE("CLICKHOUSE", "京东云ClickHouse"),

    /**
     * 京东云ES
     */
    ES("ES", "京东云ES"),

    /**
     * 京东云-redis
     */
    REDIS( "REDIS", "京东云-redis"),

    /**
     * 行云主机
     */
    DOCKER("DOCKER", "行云主机"),

    /**
     * 物理机
     */
    PHYSICAL_MACHINE( "PHYSICAL_MACHINE", "物理机"),

    /**
     * UMP方法可用率
     */
    UMP_METHOD( "UMP.method", "UMP方法可用率"),

    /**
     * mysql数据库
     */

    MYSQL_HOST("MYSQL_HOST", "mysql数据库");

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String desc;

    EasyOpsResourceTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
