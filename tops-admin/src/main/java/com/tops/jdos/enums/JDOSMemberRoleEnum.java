package com.tops.jdos.enums;

import lombok.Getter;

@Getter
public enum JDOSMemberRoleEnum {
    SYSTEM_OWNER("systemOwner","系统负责人"),
    APPLICATION_OWNER("applicationOwner","应用负责人"),
    APPLICATION_DEVELOPER("applicationOwner","应用开发人员"),
    APPLICATION_TESTER("applicationOwner","应用测试人员"),
    ;

    private String code;
    private String desc;

    JDOSMemberRoleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
