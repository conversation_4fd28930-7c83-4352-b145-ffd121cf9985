package com.tops.jdos.enums;

import lombok.Getter;

@Getter
public enum JDOSApiEnum {
    QUEYR_APPLICATION_USERINFO("/api/v2/apps/%s/members","GET","获取应用成员列表。占位符依次为：应用名"),
    QUEYR_APPLICATION_GROUPS("/api/v2/apps/%s/groups","GET","查询应用下所有分组。占位符依次为：应用名"),
    QUEYR_APPLICATION_GROUP_PODS("/api/v2/apps/%s/groups/%s/cluster/pods","GET","查询容器。占位符依次为：应用名-分组名"),
    START_OR_STOP_POD("/api/v2/apps/%s/groups/%s/startOstopApp","POST","启停容器。占位符依次为：应用名-分组名"),
    SCALE_UP_POD("/api/v2/apps/%s/groups/%s/scaleUp","POST","扩容分组。占位符依次为：应用名-分组名"),
    ;
    private String api;
    private String method;
    private String desc;

    JDOSApiEnum(String api,String method, String desc) {
        this.api = api;
        this.method = method;
        this.desc = desc;
    }
}
