package com.tops.jdos.utils;

import com.alibaba.fastjson.JSON;
import com.tops.common.utils.TopsHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.util.EntityUtils;

import java.nio.charset.StandardCharsets;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

public class HttpUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    public String getRequest(String host, String path, Map<String, String> parameters, Map<String, String> headers) throws URISyntaxException, IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        URIBuilder uriBuilder = new URIBuilder()
            .setScheme("http")
            .setHost(host)
            .setPath(path);
        if (parameters != null) {
            parameters.forEach(uriBuilder::setParameter);
        }
        HttpGet request = new HttpGet(uriBuilder.build());
        if (headers != null) {
            headers.forEach(request::addHeader);
        }
        //TODO wangqin83 增加重试
        CloseableHttpResponse response = httpClient.execute(request);

        return getContent(response);
    }

    private String getContent(CloseableHttpResponse response) throws IOException {
        String result = "";
        try {
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity);
        } finally {
            response.close();
        }
        return result;
    }

    /**
     * post请求
     *
     * @param host
     * @param path
     * @param json
     * @return
     */
    public static String post(String host, String path, String json, Map<String, String> parametersMap) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建一个HttpPost对象
            URIBuilder uriBuilder = new URIBuilder().setScheme("http").setHost(host).setPath(path);
            HttpPost postRequest = new HttpPost(uriBuilder.build());
            return post0(httpClient, postRequest, json, parametersMap);
        } catch (Exception e) {
            LOGGER.error("post请求失败:", e);
        }

        return null;
    }
    public static String httpsPost(String host, String path, String json, Map<String, String> parametersMap) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建一个HttpPost对象
            URIBuilder uriBuilder = new URIBuilder().setScheme("https").setHost(host).setPath(path);
            HttpPost postRequest = new HttpPost(uriBuilder.build());
           return post0(httpClient, postRequest, json, parametersMap);
        } catch (Exception e) {
            LOGGER.error("post请求失败:", e);
        }

        return null;
    }
    public static String post(String url, String json, Map<String, String> parametersMap) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建一个HttpPost对象
            HttpPost postRequest = new HttpPost(url);
            post0(httpClient, postRequest, json, parametersMap);
        } catch (Exception e) {
            LOGGER.error("post请求失败:", e);
        }

        return null;
    }

    private static String post0(CloseableHttpClient httpClient, HttpPost postRequest, String json, Map<String, String> parametersMap) throws IOException {
        for (Map.Entry<String, String> entry : parametersMap.entrySet()) {
            // 设置请求头
            postRequest.setHeader(entry.getKey(), entry.getValue());
        }

        // 设置请求体
        StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
        postRequest.setEntity(entity);
        // 发送请求并获取响应
        try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
            // 获取响应体
            if (response.getStatusLine().getStatusCode() == HttpStatus.OK.value()) {
                HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    String charset = EntityUtils.getContentCharSet(responseEntity);
                    if (charset == null) {
                        charset = StandardCharsets.UTF_8.name(); // 默认使用 UTF-8
                    }

                    return EntityUtils.toString(responseEntity, charset);
                }
            } else {
                LOGGER.error("post请求失败: response:{}", JSON.toJSONString(response));
            }
        }

        return null;
    }

    public static void main(String[] args) throws URISyntaxException, IOException {
        String PATH_TEMPLATE_QUERY_MEMBERS = "/api/v2/apps/%s/members";

        Map<String, String> parameters = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("tenant", "JDDTEST");
        headers.put("erp", "org.wljy1");
        headers.put("token", "0dc91b27-ff6b-44e6-8785-cc919047f0d8");

        HttpUtil httpUtil = new HttpUtil();

        httpUtil.getRequest("api.jcd-gateway.jd.com"
            , String.format(PATH_TEMPLATE_QUERY_MEMBERS, "jdl-ops")
            , parameters, headers);
    }
}
