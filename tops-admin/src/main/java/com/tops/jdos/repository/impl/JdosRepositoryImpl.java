package com.tops.jdos.repository.impl;

import com.alibaba.fastjson.JSON;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.redis.RedisUtils;
import com.tops.jdos.bean.po.JdosApplicationPO;
import com.tops.jdos.repository.JdosRepository;
import com.tops.jdos.repository.dao.JdosApplicationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;

@Component
@Slf4j
public class JdosRepositoryImpl implements JdosRepository {
    private final static int REDIS_EXPIRED_DAYS = 30;
    private final static String DATASOURCE_MODE_REDIS = "redis";
    private final static String DATASOURCE_MODE_MYSQL = "mysql";

    private final static String REDIS_KEY_TEMPLATE = "jdl-tops:jdos:application:%s";
    private final static String REDIS_KEY_SET_TEMPLATE = "jdl-tops:jdos:applications:keySet";

    @Value("${tops.jdos.datasource.mode}")
    private String datasourceMode;

    @Autowired
    private JdosApplicationDao jdosApplicationDao;


    @Override
    public List<JdosApplicationPO> queryApplications() throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (Objects.equals(datasourceMode, DATASOURCE_MODE_REDIS)) {
            return queryApplicationFromRedis();
        } else {
            return queryApplicationFromMysql();
        }
    }

    /**
     * 更新无负责人的应用
     *
     * @param pos
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 7)
    public void updateApplications(List<JdosApplicationPO> pos) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (Objects.equals(datasourceMode, DATASOURCE_MODE_REDIS)) {
            updateApplicationsFromRedis(pos);
        } else {
            updateApplicationsFromMysql(pos);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, timeout = 7)
    public void insertApps(List<String> appNames) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (Objects.equals(datasourceMode, DATASOURCE_MODE_REDIS)) {
            insertAppsFromRedis(appNames);
        } else {
            insertAppsFromMysql(appNames);
        }
    }

    private List<JdosApplicationPO> queryApplicationFromRedis() throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Set<String> keySets = RedisUtils.getCacheObject(REDIS_KEY_SET_TEMPLATE);
        List<JdosApplicationPO> pos = new ArrayList<>();
        if (keySets!=null){
            for (String cacheKey : keySets) {
                JdosApplicationPO po = RedisUtils.getCacheObject(cacheKey);
                if (po != null) {
                    pos.add(po);
                }
            }
        }

        return pos;
    }

    /**
     * 更新无负责人的应用
     *
     * @param pos
     */
    private void updateApplicationsFromRedis(List<JdosApplicationPO> pos) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        for (JdosApplicationPO po : pos) {
            String cacheKey = String.format(REDIS_KEY_TEMPLATE, po.getName());
            RedisUtils.setCacheObject(cacheKey, JSON.toJSONString(po), Duration.ofDays(REDIS_EXPIRED_DAYS));
        }
    }

    private void insertAppsFromRedis(List<String> appNames) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Set<String> keySets = RedisUtils.getCacheObject(REDIS_KEY_SET_TEMPLATE);
        if (keySets == null) {
            keySets = new HashSet<>();
        }
        for (String appName : appNames) {
            JdosApplicationPO po = new JdosApplicationPO();
            po.setName(appName);
            String cacheKey = String.format(REDIS_KEY_TEMPLATE, po.getName());
            RedisUtils.setCacheObject(cacheKey, JSON.toJSONString(po), Duration.ofDays(REDIS_EXPIRED_DAYS));
            keySets.add(cacheKey);
        }
        RedisUtils.setCacheObject(REDIS_KEY_SET_TEMPLATE, JSON.toJSONString(keySets), Duration.ofDays(REDIS_EXPIRED_DAYS));
    }

    private List<JdosApplicationPO> queryApplicationFromMysql() throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        return jdosApplicationDao.selectList();
    }

    /**
     * 更新无负责人的应用
     *
     * @param pos
     */
    private void updateApplicationsFromMysql(List<JdosApplicationPO> pos) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        for (JdosApplicationPO po : pos) {
            jdosApplicationDao.update(po, po.getName());
        }
    }

    private void insertAppsFromMysql(List<String> appNames) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        for (String appName : appNames) {
            JdosApplicationPO po = new JdosApplicationPO();
            po.setName(appName);
            jdosApplicationDao.insert(po);
        }
    }
}
