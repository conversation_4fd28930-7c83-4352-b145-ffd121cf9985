package com.tops.jdos.repository;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.jdos.bean.po.JdosApplicationPO;

import java.util.List;


public interface JdosRepository {

     List<JdosApplicationPO> queryApplications() throws InvalidRequestException, DependencyFailureException, InternalFailureException;

    /**
     * 更新无负责人的应用
     */
     void updateApplications(List<JdosApplicationPO> pos) throws InvalidRequestException, DependencyFailureException, InternalFailureException;

    /**
     * 插入应用
     */
     void insertApps(List<String> appNames) throws InvalidRequestException, DependencyFailureException, InternalFailureException;
}
