package com.tops.common.config;

import com.tops.audit.interceptor.ErpSSOInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

/**
 * web配置
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@Configuration
public class TopsWebMvcConfig implements WebMvcConfigurer {
    /**
     * erp sso 拦截器
     */
    @Autowired
    private ErpSSOInterceptor erpSSOInterceptor;

    /**
     * sso拦截器path patterns
     */
    private List<String> ssoPathPatterns = new ArrayList<>();

    /**
     * sso拦截器exclude path patterns
     */
    private List<String> ssoExcludePathPatterns = new ArrayList<>();
    {
        ssoPathPatterns.add("/**");
        ssoExcludePathPatterns.add("/tops/alarm/**");
        ssoExcludePathPatterns.add("/tops/autoBots/**");
        ssoExcludePathPatterns.add("/tops/trace/**");
        ssoExcludePathPatterns.add("/tops/ext/monitor/**");
        ssoExcludePathPatterns.add("/tops/tools/**");
        ssoExcludePathPatterns.add("/test/**");
        ssoExcludePathPatterns.add("/sse/**");
        ssoExcludePathPatterns.add("/mcp-service/**");
    }

    /**
     * Add Spring MVC lifecycle interceptors for pre- and post-processing of
     * controller method invocations and resource handler requests.
     * Interceptors can be registered to apply to all requests or be limited
     * to a subset of URL patterns.
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(erpSSOInterceptor).addPathPatterns(ssoPathPatterns).excludePathPatterns(ssoExcludePathPatterns);
    }
}
