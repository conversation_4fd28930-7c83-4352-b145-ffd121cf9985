package com.tops.common.config.ducc;

import com.alibaba.fastjson.JSON;
import com.jd.laf.config.Configuration;
import com.jd.laf.config.Property;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OrderStatusMonitorConfig
 * @Description 用途
 * @date 2024年01月22日 9:32 PM
 */
@Data
@Slf4j
@Component
public class OrderStatusMonitorConfig {
    /**
     * 符号：逗号
     */
    public static final String COMMA = ",";
    /**
     * 超时时间配置
     */
    private Map<String, OrderStatusMonitorConfigItem> orderStatusTimeoutConfig = Collections.emptyMap();

    /**
     * 超时时间配置
     */
    private Map<String, OrderStatusMonitorConfigItem> orderCustomStatusTimeoutConfig = Collections.emptyMap();

    /**
     * 记录的业务身份
     */
    private Set<String> recordBusinessUnitSet = Collections.emptySet();

    /**
     * 记录的展示的状态
     */
    private Set<String> recordCustomOrderStatusSet = Collections.emptySet();

    /**
     * 兜底超时时间
     */
    private int defaultTimeout = 30;

    /**
     * 黄灯阈值
     */
    private RateConfig rateConfig = new RateConfig();


    @LafValue(name = "order_status_monitor")
    public void onConfigureChange(Configuration configuration) {
        Property timeoutConfigProperty = configuration.getProperty("timeoutConfig");
        if (timeoutConfigProperty != null) {
            List<OrderStatusMonitorConfigItem> configList = JSON.parseArray(timeoutConfigProperty.getString(), OrderStatusMonitorConfigItem.class);
            orderCustomStatusTimeoutConfig = configList.stream().collect(Collectors.toMap(OrderStatusMonitorConfigItem::getOrderStatusCustom, Function.identity()));
            log.info("timeoutConfigProperty change, orderStatusTimeoutConfig:{}, orderCustomStatusTimeoutConfig:{}",
                JSON.toJSONString(orderStatusTimeoutConfig), JSON.toJSONString(orderCustomStatusTimeoutConfig));
        }

        Property recordBusinessUnitProperty = configuration.getProperty("recordBusinessUnit");
        if (recordBusinessUnitProperty != null) {
            recordBusinessUnitSet = Arrays.stream(recordBusinessUnitProperty.getString().split(COMMA)).collect(Collectors.toSet());
            log.info("recordBusinessUnitProperty change, {}", JSON.toJSONString(recordBusinessUnitSet));
        }

        Property recordCustomOrderStatusSetProperty = configuration.getProperty("recordCustomOrderStatusSet");
        if (recordBusinessUnitProperty != null) {
            recordCustomOrderStatusSet = Arrays.stream(recordCustomOrderStatusSetProperty.getString().split(COMMA)).collect(Collectors.toSet());
            log.info("recordOrderStatusSetProperty change, {}", JSON.toJSONString(recordCustomOrderStatusSet));
        }

        Property defaultTimeoutProperty = configuration.getProperty("defaultTimeout");
        if (defaultTimeoutProperty != null) {
            defaultTimeout = defaultTimeoutProperty.getInteger();
            log.info("defaultTimeoutProperty change, {}", defaultTimeout);
        }

        Property rateConfigProperty = configuration.getProperty("rateConfig");
        if (rateConfigProperty != null) {
            rateConfig = JSON.parseObject(rateConfigProperty.getString(), RateConfig.class);
            log.info("rateConfigProperty change, {}", rateConfig);
        }
    }
}
