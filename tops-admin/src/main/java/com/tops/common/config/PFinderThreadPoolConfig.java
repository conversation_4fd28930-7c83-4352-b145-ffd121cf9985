package com.tops.common.config;

import com.jd.pfinder.profiler.sdk.PfinderContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Configuration
public class PFinderThreadPoolConfig {
    @Value("${threadPoolTaskExecutor.corePoolSize}")
    private int corePoolSize;
    @Value("${threadPoolTaskExecutor.keepAliveSeconds}")
    private int keepAliveSeconds;
    @Value("${threadPoolTaskExecutor.maxPoolSize}")
    private int maxPoolSize;
    @Value("${threadPoolTaskExecutor.queueCapacity}")
    private int queueCapacity;

    @Bean("pFinderPoolTaskExecutor")
    public ExecutorService pFinderPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
        threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
        threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
        threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
        //对拒绝task的处理策略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.setThreadNamePrefix("sync-task-exec-");
        threadPoolTaskExecutor.initialize();
        log.info("ThreadPoolTaskExecutorConfig.threadPoolTaskExecutor init success,corePoolSize:{},keepAliveSeconds:{},maxPoolSize:{},queueCapacity:{}",
            corePoolSize, keepAliveSeconds, maxPoolSize, queueCapacity);

        return PfinderContext.executorServiceWrapper(threadPoolTaskExecutor.getThreadPoolExecutor());
    }


    @Value("${threadPoolTaskExecutor4PFinder.corePoolSize}")
    private int corePoolSize4PFinder;
    @Value("${threadPoolTaskExecutor4PFinder.keepAliveSeconds}")
    private int keepAliveSeconds4PFinder;
    @Value("${threadPoolTaskExecutor4PFinder.maxPoolSize}")
    private int maxPoolSize4PFinder;
    @Value("${threadPoolTaskExecutor4PFinder.queueCapacity}")
    private int queueCapacity4PFinder;
    @Bean("pFinderPoolTaskExecutor4PFinder")
    public ExecutorService pFinderPoolTaskExecutor4PFinder() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(corePoolSize4PFinder);
        threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds4PFinder);
        threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize4PFinder);
        threadPoolTaskExecutor.setQueueCapacity(queueCapacity4PFinder);
        //对拒绝task的处理策略
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.setThreadNamePrefix("sync-task-exec-pFinder-");
        threadPoolTaskExecutor.initialize();
        log.info("ThreadPoolTaskExecutorConfig.pFinderPoolTaskExecutor4PFinder init success,corePoolSize:{},keepAliveSeconds:{},maxPoolSize:{},queueCapacity:{}",
            corePoolSize4PFinder, keepAliveSeconds4PFinder, maxPoolSize4PFinder, queueCapacity4PFinder);

        return PfinderContext.executorServiceWrapper(threadPoolTaskExecutor.getThreadPoolExecutor());
    }

}
