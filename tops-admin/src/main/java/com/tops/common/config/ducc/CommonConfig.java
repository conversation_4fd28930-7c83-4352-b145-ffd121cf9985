package com.tops.common.config.ducc;

import com.jd.laf.config.Configuration;
import com.jd.laf.config.Property;
import com.jd.laf.config.spring.annotation.LafValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName XbpTopicRetransmitConfig
 * @Description 用途
 * @date 2024年09月25日 3:26 PM
 */
@Data
@Slf4j
@Component
public class CommonConfig {
    /**
     * 预发环境xbp ids
     */
    private Set<String> preProcessIds = new HashSet<>();

    /**
     * 预发环境xbp ids
     */
    private Set<String> prodProcessIds = new HashSet<>();

    @LafValue(name = "jdl_ops_common_config")
    public void onCommonConfigureChange(Configuration configuration) {
        Property prodProcessIdsProperty = configuration.getProperty("prodProcessIds");
        if (prodProcessIdsProperty != null) {
            prodProcessIds = new HashSet<>(Arrays.asList(prodProcessIdsProperty.getString().split(",")));
            log.info("生产环境流程id prodProcessIds:{}", prodProcessIds);
        }

        Property preProcessIdsProperty = configuration.getProperty("preProcessIds");
        if (preProcessIdsProperty != null) {
            preProcessIds = new HashSet<>(Arrays.asList(preProcessIdsProperty.getString().split(",")));
            log.info("预发环境流程id preProcessIds:{}", preProcessIds);
        }
    }
}
