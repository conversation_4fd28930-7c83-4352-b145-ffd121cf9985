package com.tops.common.config.ducc;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName TimeoutConfig
 * @Description 用途
 * @date 2024年01月22日 9:34 PM
 */
@Data
public class OrderStatusMonitorConfigItem {
    /**
     * 标准状态
     */
    private String orderStatus;

    /**
     * 自定义状态
     */
    private String orderStatusCustom;

    /**
     * 超时时间(黄灯)
     */
    private Integer timeoutYellow;

    /**
     * 超时时间(红灯)
     */
    private Integer timeoutRed;

    /**
     * 滞留单数报警阈值
     */
    private Integer retentionYellowUpperLimit = Integer.MAX_VALUE;

    /**
     * 滞留单数报警阈值
     */
    private Integer retentionRedUpperLimit = Integer.MAX_VALUE;
}
