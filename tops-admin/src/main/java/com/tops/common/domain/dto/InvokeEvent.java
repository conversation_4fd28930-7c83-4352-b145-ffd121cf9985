package com.tops.common.domain.dto;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 方法调用事件
 */
@Getter
@Setter
@Slf4j
public class InvokeEvent {

    private Object[] args;
    private String pathName;
    private ProceedingJoinPoint joinPoint;

    public InvokeEvent(ProceedingJoinPoint joinPoint) {
        this.joinPoint = joinPoint;
        this.args = joinPoint.getArgs();
        this.pathName = joinPoint.getTarget().getClass().getName() + "#" + joinPoint.getSignature().getName();
    }

    /**
     * 方法调用
     */
    public Object process() throws Throwable {
        try {
            return joinPoint.proceed(args);
        } catch (Throwable e) {
            throw e;
        }
    }
}
