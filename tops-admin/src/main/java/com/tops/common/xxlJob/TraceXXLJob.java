package com.tops.common.xxlJob;

import com.tops.common.config.ducc.OrderStatusMonitorConfig;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.service.OrderReportService;
import com.tops.pfinder.bean.dto.UpdateTracesRequest;
import com.tops.pfinder.enums.CoreApiEnum;
import com.tops.pfinder.service.TraceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 调用链路刷新任务
 */
@Component
@Slf4j
public class TraceXXLJob {
    @Autowired
    private TraceService traceService;

    @XxlJob(value = "traceXXLJob")
    public ReturnT<String> recordTrace(String param) {
        log.info("==========traceXXLJob===========");
        try {
            {
                traceService.updateTraces(getRequest(CoreApiEnum.PRODUCT_SUPPLY));
                Thread.sleep(1000);
            }
            {
                traceService.updateTraces(getRequest(CoreApiEnum.EXPRESS_CREATE_ORDER));
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("TraceXXLJob.recordTrace exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    private UpdateTracesRequest getRequest(CoreApiEnum coreApi) {
        UpdateTracesRequest request = new UpdateTracesRequest();
        request.setAppName(coreApi.getApplicationName());
        request.setAppCoord(coreApi.getApplicationCoord());
        request.setComponentName(coreApi.getApi());
        return request;
    }

}
