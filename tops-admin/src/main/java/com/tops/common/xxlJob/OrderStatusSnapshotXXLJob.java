package com.tops.common.xxlJob;

import com.google.common.collect.Lists;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.config.ducc.OrderStatusMonitorConfig;
import com.tops.common.constant.MDCTraceConstants;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.order.constant.OrderStatusConstant;
import com.tops.order.domain.vo.AggOrderStatusRequest;
import com.tops.order.service.OrderReportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @ClassName OrderStatusSnapshotXXLJob
 * @Description 用途
 * @date 2024年01月22日 8:21 PM
 */
@Component
@Slf4j
public class OrderStatusSnapshotXXLJob {
    @Resource
    OrderReportService orderReportService;

    @Resource
    OrderStatusMonitorConfig orderStatusMonitorConfig;

    @XxlJob(value = "orderStatusSnapshotJob")
    public ReturnT<String> recordOrderStatusSnapshot(String param) {
        MDC.put(MDCTraceConstants.TRACEID, UUID.randomUUID().toString());
        log.info("==========orderStatusSnapshotJob===========");
        CallerInfo callerInfo = Profiler.registerInfo("recordStatusSnapshot");
        try {
            Date date = new Date();
            AggOrderStatusRequest aggOrderStatusRequest = new AggOrderStatusRequest();
            aggOrderStatusRequest.setBusinessUnitList(orderStatusMonitorConfig.getRecordBusinessUnitSet().stream().collect(Collectors.toList()));
            aggOrderStatusRequest.setOrderStatusCustomList(orderStatusMonitorConfig.getRecordCustomOrderStatusSet().stream().collect(Collectors.toList()));
            aggOrderStatusRequest.setQueryTime(date);
            orderReportService.recordSnapshot(aggOrderStatusRequest);
            return ReturnT.SUCCESS;
        } catch (Exception ex) {
            log.error("record snapshot error", ex);
            Profiler.functionError(callerInfo);
            throw ex;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            MDC.remove(MDCTraceConstants.TRACEID);
        }
    }

    /**
     * 数据结转任务
     * @param param
     * @return
     */
    @XxlJob(value = "orderStatusCleanJob")
    public ReturnT<String> backupOrderStatus(String param) throws InternalFailureException {
        MDC.put(MDCTraceConstants.TRACEID, UUID.randomUUID().toString());
        log.info("==========backupOrderStatus===========");
        CallerInfo callerInfo = Profiler.registerInfo("backupOrderStatus");
        try {
            Date date = DateUtils.queryDayByDiffSource(new Date(), -15);
            orderReportService.backupOrderStatus(date);
            return ReturnT.SUCCESS;
        } catch (Exception ex) {
            log.error("clean order status flow error", ex);
            Profiler.functionError(callerInfo);
            throw ex;
        } finally {
            MDC.remove(MDCTraceConstants.TRACEID);
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
