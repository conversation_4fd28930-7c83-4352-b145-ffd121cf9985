package com.tops.common.xxlJob;

import com.jd.fastjson.JSON;
import com.jd.fastjson.TypeReference;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.pfinder.bean.dto.UpdatePanelDataRequest;
import com.tops.pfinder.service.PanelService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * 调用链路刷新任务
 */
@Component
@Slf4j
public class PanelDataXXLJob {
    @Autowired
    private PanelService panelService;

    @XxlJob(value = "panelDataXXLJob")
    public ReturnT<String> recordPanelData(String param) {
        log.info("==========panelDataXXLJob===========param:{}", param);

        try {
            UpdatePanelDataRequest request = getRequest(param);
            log.info("PanelDataXXLJob.recordPanelData request:{}", JSON.toJSONString(request));
            panelService.updateMetric(request);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("PanelDataXXLJob.recordPanelData exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    private UpdatePanelDataRequest getRequest(String param) throws ParseException {
        if (StringUtils.isBlank(param)) {
            Date beginTime = DateUtils.queryDayByDiffSource(new Date(), -1);
            Date endTime = DateUtils.queryDayLastMinByDiffSource(new Date(), -1);
            UpdatePanelDataRequest updatePanelDataRequest = new UpdatePanelDataRequest();
            updatePanelDataRequest.setBeginTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, beginTime));
            updatePanelDataRequest.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
            return updatePanelDataRequest;
        } else {
            TimeRange timeRange = JSON.parseObject(param, new TypeReference<TimeRange>() {
            });
            UpdatePanelDataRequest updatePanelDataRequest = new UpdatePanelDataRequest();
            updatePanelDataRequest.setBeginTime(timeRange.beginTime);
            updatePanelDataRequest.setEndTime(timeRange.endTime);
            return updatePanelDataRequest;
        }

    }

    @Getter
    @Setter
    public static class TimeRange {
        private String beginTime;
        private String endTime;
    }


    public static void main(String[] args) throws ParseException {
        Date time1 = DateUtils.parseDate("2024-09-18 00:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date time2 = DateUtils.parseDate("2024-09-18 01:00:00", DateUtils.YYYY_MM_DD_HH_MM_SS);
        System.out.println("一小时："+(time2.getTime()-time1.getTime()));
        TimeRange timeRange = JSON.parseObject("{\n" +
            "    \"beginTime\": \"2024-09-18 00:00:00\",\n" +
            "    \"endTime\": \"2024-10-06 23:59:59\"\n" +
            "}", new TypeReference<TimeRange>() {
        });
        Date beginTime = DateUtils.parseDate(timeRange.getBeginTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date endTime = DateUtils.parseDate(timeRange.getEndTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        List<DateUtils.DateDuration> dateDurations = DateUtils.queryHourSlice(beginTime, endTime);
        System.out.println("时间范围："+"2024-09-18 00:00:00"+"       "+"2024-10-06 23:59:59");
        for (DateUtils.DateDuration dateDuration : dateDurations) {
            System.out.println("时间分片:"+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,dateDuration.getBeginTime())+"       "+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,dateDuration.getEndTime()));
        }
    }
}
