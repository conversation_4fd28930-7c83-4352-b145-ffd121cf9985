package com.tops.common.xxlJob;

import com.alibaba.fastjson.JSON;
import com.tops.common.utils.StringUtils;
import com.tops.jdos.bean.po.JdosApplicationPO;
import com.tops.jdos.repository.JdosRepository;
import com.tops.jdos.service.JDOSService;
import com.tops.pfinder.repository.TraceRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * jdos应用元数据任务
 */
@Component
@Slf4j
public class JdosApplicationJob {
    @Autowired
    private TraceRepository traceRepository;
    @Autowired
    private JdosRepository jdosRepository;
    @Autowired
    private JDOSService jdosService;

    @XxlJob(value = "jdosApplicationJob")
    public ReturnT<String> recordApplicationInfo(String param) {
        log.info("==========jdosApplicationJob===========");
        try {
            Set<String> appNames = traceRepository.queryTraceAppNames();
            Set<String> appNamesExist = jdosRepository.queryApplications()
                .stream()
                .map(JdosApplicationPO::getName)
                .collect(Collectors.toSet());
            XxlJobLogger.log("JdosApplicationJob.recordApplicationInfo appNames:{}", JSON.toJSONString(appNames));
            XxlJobLogger.log("JdosApplicationJob.recordApplicationInfo appNamesExist:{}", JSON.toJSONString(appNamesExist));
            List<String> inserts = new ArrayList<>();
            for (String appName : appNames) {
                if (!appNamesExist.contains(appName)) {
                    inserts.add(appName);
                }
            }
            jdosRepository.insertApps(inserts);
            XxlJobLogger.log("JdosApplicationJob.recordApplicationInfo 新增应用:{}", JSON.toJSONString(inserts));
            List<JdosApplicationPO> jdosApplicationPOS = jdosRepository.queryApplications()
                .stream()
                .filter(application -> StringUtils.isBlank(application.getOwner()))
                .collect(Collectors.toList());
            for (JdosApplicationPO jdosApplicationPO : jdosApplicationPOS) {
                String owner = null;
                try {
                    owner = jdosService.queryAppOwnerFromJdos(jdosApplicationPO.getName());
                } catch (Exception e) {
                    XxlJobLogger.log("JdosApplicationJob.recordApplicationInfo 查询应用{}负责人失败", jdosApplicationPO.getName());
                    log.error("JdosApplicationJob.recordApplicationInfo 查询应用{}负责人失败,exception:", jdosApplicationPO.getName(), e);
                }
                jdosApplicationPO.setOwner(owner);
                Thread.sleep(1000);
            }
            jdosRepository.updateApplications(jdosApplicationPOS);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("JdosApplicationJob.recordApplicationInfo exception:", e);
        }
        return ReturnT.SUCCESS;
    }

}
