package com.tops.common.xxlJob;

import com.tops.common.utils.DateUtils;
import com.tops.pfinder.bean.dto.UpdateTracesInvokeTimesRequest;
import com.tops.pfinder.bean.dto.UpdateTracesRequest;
import com.tops.pfinder.enums.CoreApiEnum;
import com.tops.pfinder.service.TraceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * 调用链路刷新任务
 */
@Component
@Slf4j
public class TraceInvokeTimesXXLJob {
    @Autowired
    private TraceService traceService;

    @XxlJob(value = "traceInvokeTimesXXLJob")
    public ReturnT<String> recordTraceInvokeTimes(String param) {
        log.info("==========traceInvokeTimesXXLJob===========");

        try {
            UpdateTracesInvokeTimesRequest updateTracesInvokeTimesRequest = new UpdateTracesInvokeTimesRequest();
            updateTracesInvokeTimesRequest.setUpdateTracesRequests(new ArrayList<>());
            if (StringUtils.isNotBlank(param)){
                updateTracesInvokeTimesRequest.setRecordDate(DateUtils.parseDate(param,DateUtils.YYYY_MM_DD));
            }
            {
                UpdateTracesRequest request = getRequest(CoreApiEnum.SC_CREATE_ORDER);
                updateTracesInvokeTimesRequest.getUpdateTracesRequests().add(request);
            }
            {
                UpdateTracesRequest request = getRequest(CoreApiEnum.EXPRESS_CREATE_ORDER);
                updateTracesInvokeTimesRequest.getUpdateTracesRequests().add(request);
            }
            traceService.updateTracesInvokeTimes(updateTracesInvokeTimesRequest);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("TraceXXLJob.recordTraceInvokeTimes exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    private UpdateTracesRequest getRequest(CoreApiEnum coreApi) {
        UpdateTracesRequest request = new UpdateTracesRequest();
        request.setAppName(coreApi.getApplicationName());
        request.setAppCoord(coreApi.getApplicationCoord());
        request.setComponentName(coreApi.getApi());
        return request;
    }

}
