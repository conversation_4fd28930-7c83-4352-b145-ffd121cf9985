package com.tops.common.xxlJob;

import com.tops.common.utils.StringUtils;
import com.tops.pfinder.adapter.PFinderAdapter;
import com.tops.pfinder.bean.dto.*;
import com.tops.pfinder.bean.po.JsfConsumerMetricPO;
import com.tops.pfinder.enums.ComponentEnum;
import com.tops.pfinder.repository.TraceRepository;
import com.tops.pfinder.repository.dao.JsfConsumerMetricDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * jsf consumer配置
 */
@Component
@Slf4j
public class JsfConsumerMetricJob {
    @Autowired
    private TraceRepository traceRepository;
    @Autowired
    private PFinderAdapter pFinderAdapter;
    @Autowired
    private JsfConsumerMetricDao jsfConsumerMetricDao;

    private static Set<String> tooManyConsumerComponent = new HashSet<>();

    static {
        tooManyConsumerComponent.add("com.jd.gms.crs.rpc.ProductRpc#querySku");
    }

    @XxlJob(value = "jsfConsumerMetricJob")
    public ReturnT<String> recordJsfConsumerMetric(String param) {
        log.info("==========JsfConsumerMetricJob===========");
        Set<JsfConsumerMetricPO> consumerMetrics = new HashSet<>();
        Set<JsfConsumerMetricPO> hasMetrics = new HashSet<>();


        try {
            List<TraceInfo> traceInfos = traceRepository.queryTraces(new QueryTraceRequest(), new LinkedHashMap<>());
            Map<String, List<String>> componentParentMapping = new HashMap<>();
            //收集所有需要查询PFinder的接口
            for (TraceInfo traceInfo : traceInfos) {
                toParentMapping(traceInfo.getRoot(), componentParentMapping);
            }
            //查询PFinder 并 计算统计信息
            List<JsfConsumerMetricDTO> consumerMetricsDTO = new ArrayList<>();
            long start = System.currentTimeMillis();
            for (String componentName : componentParentMapping.keySet()) {
                Map<String, JsfConsumerMetricDTO> consumerMetric = pFinderAdapter.queryJsfConsumerMetric(componentName, componentParentMapping.get(componentName), tooManyConsumerComponent.contains(componentName));
                consumerMetricsDTO.addAll(new ArrayList<>(consumerMetric.values()));
            }
            log.info("JsfConsumerMetricJob.recordJsfConsumerMetric 统计数据耗时cost:{}", System.currentTimeMillis() - start);
            //将PFinder 返回的接口转换为 PO格式
            consumerMetrics = toConsumerMetrics(consumerMetricsDTO);
            for (TraceInfo traceInfo : traceInfos) {
                hasConfig(traceInfo.getRoot(), hasMetrics);
            }
            for (JsfConsumerMetricPO consumerMetric : consumerMetrics) {
                if (hasMetrics.contains(consumerMetric)) {
                    jsfConsumerMetricDao.update(consumerMetric);
                } else {
                    jsfConsumerMetricDao.insert(consumerMetric);
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("JsfConsumerMetricJob.recordJsfConsumerMetric exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    private void hasConfig(SpanInfo root, Set<JsfConsumerMetricPO> consumerMetrics) {
        if (root == null) {
            return;
        }
        if (root.getConsumerMetric() != null) {
            consumerMetrics.add(JsfConsumerMetricDTO.toPO(root.getConsumerMetric()));
        }
        if (CollectionUtils.isNotEmpty(root.getChildren())) {
            for (SpanInfo child : root.getChildren()) {
                hasConfig(child, consumerMetrics);
            }
        }
    }

    private Set<JsfConsumerMetricPO> toConsumerMetrics(List<JsfConsumerMetricDTO> consumerMetricDTOS) {
        return new HashSet<>(JsfConsumerMetricDTO.toPO(consumerMetricDTOS));
    }

    private void toParentMapping(SpanInfo spanInfo, Map<String, List<String>> componentParentMapping) {
        if (spanInfo == null) {
            return;
        }
        List<SpanInfo> children = spanInfo.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (SpanInfo child : children) {
                if (Objects.equals(child.getComponent().getComponentType(), ComponentEnum.JSF_CONSUMER.getComponentType())) {
                    List<String> parentAppName = componentParentMapping.getOrDefault(child.getComponent().getComponentName(), new ArrayList<>());
                    parentAppName.add(spanInfo.getApplication().getName());
                    componentParentMapping.put(child.getComponent().getComponentName(), parentAppName);
                }
                toParentMapping(child, componentParentMapping);
            }
        }
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = new BigDecimal("99.1829");
        System.out.println(String.format("bigDecimal:%.2s", bigDecimal));
        System.out.println(String.format("bigDecimal:%.2f", bigDecimal));
    }
}
