package com.tops.common.xxlJob;

import com.alibaba.fastjson.JSON;
import com.tops.pfinder.adapter.JsfAdapter;
import com.tops.pfinder.bean.dto.*;
import com.tops.pfinder.bean.po.JsfConsumerConfigPO;
import com.tops.pfinder.enums.ComponentEnum;
import com.tops.pfinder.repository.TraceRepository;
import com.tops.pfinder.repository.dao.JsfConsumerConfigDao;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * jsf consumer配置
 */
@Component
@Slf4j
public class JsfConsumerConfigJob {
    @Autowired
    private TraceRepository traceRepository;
    @Autowired
    private JsfAdapter jsfAdapter;
    @Autowired
    private JsfConsumerConfigDao jsfConsumerConfigDao;

    @XxlJob(value = "jsfConsumerConfigJob")
    public ReturnT<String> recordJsfConsumerConfig(String param) {
        log.info("==========jsfConsumerConfigJob===========");
        JsfConsumerConfigJobParam paramDTO = null;
        String componentName = null;
        String alias = null;
        try {
            paramDTO = JSON.parseObject(param, JsfConsumerConfigJobParam.class);
            componentName = paramDTO.getComponentName();
            alias = paramDTO.getAlias();
        } catch (Exception e) {
            log.error("JsfConsumerConfigJob.recordApplicationInfo 参数转换失败 exception:", e);
        }
        Set<JsfConsumerConfigPO> consumerConfigs = new HashSet<>();
        Set<JsfConsumerConfigPO> hasConfigs = new HashSet<>();
        try {
            List<TraceInfo> traceInfos = traceRepository.queryTraces(new QueryTraceRequest(), new LinkedHashMap<>());
            for (TraceInfo traceInfo : traceInfos) {
                queryJsfConsumerConfig(traceInfo.getRoot(), consumerConfigs, componentName, alias);
            }
            for (TraceInfo traceInfo : traceInfos) {
                hasConfig(traceInfo.getRoot(), hasConfigs);
            }
            for (JsfConsumerConfigPO consumerConfig : consumerConfigs) {
                if (hasConfigs.contains(consumerConfig)) {
                    jsfConsumerConfigDao.update(consumerConfig, consumerConfig.getSpanNo(), consumerConfig.getParentSpanNo(), consumerConfig.getAlias());
                } else {
                    jsfConsumerConfigDao.insert(consumerConfig);
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("JsfConsumerConfigJob.recordApplicationInfo exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    private void hasConfig(SpanInfo root, Set<JsfConsumerConfigPO> consumerConfigs) {
        if (root == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(root.getConsumerConfigs())) {
            consumerConfigs.addAll(JsfConsumerConfigDTO.toPO(root.getConsumerConfigs(), root.getSpanNo(), root.getParentNo()));
        }
        if (CollectionUtils.isNotEmpty(root.getChildren())) {
            for (SpanInfo child : root.getChildren()) {
                hasConfig(child, consumerConfigs);
            }
        }
    }

    private void queryJsfConsumerConfig(SpanInfo root, Set<JsfConsumerConfigPO> consumerConfigs, String componentName, String alias) {
        if (root == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(root.getChildren())) {
            for (SpanInfo child : root.getChildren()) {
                try {
                    if (CollectionUtils.isNotEmpty(child.getConsumerConfigs())) {
                        continue;
                    }
                    if (!Objects.equals(child.getComponent().getComponentType(), ComponentEnum.JSF_CONSUMER.getComponentType())) {
                        continue;
                    }
                    String argAlias = null;
                    if (Objects.equals(child.getComponent().getComponentName(), componentName)) {
                        argAlias = alias;
                    }
                    List<JsfConsumerConfigDTO> jsfConsumerConfigDTOS = jsfAdapter.queryJsfConsumerConfig(root.getApplication().getName(), child.getComponent().getComponentName(), argAlias);
                    Thread.sleep(10);
                    List<JsfConsumerConfigPO> po = JsfConsumerConfigDTO.toPO(jsfConsumerConfigDTOS, child.getSpanNo(), child.getParentNo());
                    consumerConfigs.addAll(po);
                    queryJsfConsumerConfig(child, consumerConfigs, componentName, alias);
                } catch (Exception e) {
                    log.error("JsfConsumerConfigJob.queryJsfConsumerConfig 查询JSF配置失败");
                }
            }
        }
    }
}
