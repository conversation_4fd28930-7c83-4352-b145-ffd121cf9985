package com.tops.common.utils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @ClassName ObjectUtils
 * @Description 查看一个对象是否有空属性
 * @date 2024年09月26日 4:00 PM
 */
public class ObjectUtils {

    public static boolean hasNullProperties(Object obj) {
        if (obj == null) {
            return true;
        }

        Field[] fields = obj.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                if (field.get(obj) == null) {
                    return true;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return false;
    }
}
