package com.tops.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAccessor;
import java.util.*;

/**
 * 日期转换
 * 包含Date、LocalDate、LocalDateTime、LocalTime、Instant和ZonedDateTime的互相转换
 * <p>
 * 注意，ZonedDateTime相关的转换，尤其是其他时间转ZonedDateTime，要注意时间和对应时区一致。
 *
 * <AUTHOR>
 * @ClassName: DateTimeConverterUtil
 * @Description: DateTime Converter
 * @date 2019年12月1日
 */
@Slf4j
public class TopsDateUtils {
    /**
     * 以毫秒表示的时间
     */
    private static final long DAY_IN_MILLIS = 24 * 3600 * 1000;
    private static final long HOUR_IN_MILLIS = 3600 * 1000;
    private static final long MINUTE_IN_MILLIS = 60 * 1000;
    private static final long SECOND_IN_MILLIS = 1000;

    public static ThreadLocal<SimpleDateFormat> datetimeFormat = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    private TopsDateUtils() {
    }

    /**
     * 计算时间之间的日期差，相差几日，单位：天
     *
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public static long calculateDaysDifference(String startDateTime, String endDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(startDateTime, formatter);
        LocalDateTime end = LocalDateTime.parse(endDateTime, formatter);
        return ChronoUnit.DAYS.between(start, end) + 1L;
    }

    /**
     * 日期拆分，将时间区间拆分为每天的日期起止时间
     * 例如：2020-01-01 00:00:00~2020-01-02 01:00:00 拆分为：2020-01-01 00:00:00~2020-01-02 00:00:00和2020-01-02 00:00:00~2020-01-02 01:00:00
     *
     * @param startDateTimeStr
     * @param endDateTimeStr
     * @return
     */
    public static List<String[]> splitDateTimeRange(String startDateTimeStr, String endDateTimeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse(startDateTimeStr, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endDateTimeStr, formatter);
        List<String[]> dateTimeRanges = new ArrayList<>();
        // 如果开始时间和结束时间在同一天，直接返回
        if (startDateTime.toLocalDate().equals(endDateTime.toLocalDate())) {
            dateTimeRanges.add(new String[]{startDateTimeStr, endDateTimeStr});
            return dateTimeRanges;
        }
        // 第一个时间段
        LocalDateTime nextDayMidnight = startDateTime.toLocalDate().atStartOfDay().plusDays(1);
        dateTimeRanges.add(new String[]{startDateTime.format(formatter), nextDayMidnight.format(formatter)});
        // 中间的每一天
        LocalDateTime currentStart = nextDayMidnight;
        while (currentStart.plusDays(1).isBefore(endDateTime)) {
            LocalDateTime currentEnd = currentStart.plusDays(1);
            dateTimeRanges.add(new String[]{currentStart.format(formatter), currentEnd.format(formatter)});
            currentStart = currentEnd;
        }
        // 最后一个时间段
        dateTimeRanges.add(new String[]{currentStart.format(formatter), endDateTime.format(formatter)});
        return dateTimeRanges;
    }


    /**
     * 计算时间之间的覆盖天数，单位：天
     *
     * @param startDateTime
     * @param endDateTime
     * @return
     */
    public static long calculateDaysScop(String startDateTime, String endDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start = LocalDateTime.parse(startDateTime, formatter);
        LocalDateTime end = LocalDateTime.parse(endDateTime, formatter);
        long daysDifference = ChronoUnit.DAYS.between(start.toLocalDate(), end.toLocalDate());
        // 如果 endDateTime 的时间部分不是 00:00:00，则需要额外加上一天
        if (!end.toLocalTime().equals(LocalDateTime.of(end.toLocalDate(), LocalDateTime.MIN.toLocalTime()).toLocalTime())) {
            daysDifference += 1;
        }

        return daysDifference;
    }

    /**
     * dateTime转String
     *
     * @param dateTime
     * @return
     */
    public static String toDateTimeStr(Date dateTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = simpleDateFormat.format(dateTime);
        return time;
    }

    public static String toDateStr(Date dateTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String time = simpleDateFormat.format(dateTime);
        return time;
    }

    /**
     * dateTime转String
     *
     * @param dateTime
     * @return
     */
    public static String toDateTimeStr(LocalDateTime dateTime) {
        String time = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return time;
    }

    /**
     * dateTime转String
     *
     * @param localDate
     * @return
     */
    public static String toDateStr(LocalDate localDate) {
        String dateStr = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return dateStr;
    }


    /**
     * dateTime转String
     *
     * @param inputDate
     * @return
     */
    public static String tranlateDateStr(String inputDate, String formatStr) {
        // 定义日期时间格式化器
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(formatStr);
        try {
            // 尝试解析为 LocalDateTime
            LocalDateTime dateTime = LocalDateTime.parse(inputDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            return dateTime.format(dateTimeFormatter);
        } catch (DateTimeParseException e) {
            // 如果解析失败，尝试解析为 LocalDate
            try {
                LocalDate date = LocalDate.parse(inputDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                return date.format(dateTimeFormatter);
            } catch (DateTimeParseException ex) {
                // 如果都解析失败，抛出异常
                throw new IllegalArgumentException("Invalid date format: " + inputDate);
            }
        }
    }

    /**
     * dateTime转String
     *
     * @param localDate
     * @return
     */
    public static String toDateTimeStr(LocalDate localDate) {
        String dateTime = toDateStr(localDate) + " 00:00:00";
        return dateTime;
    }

    /**
     * LocalDateTime转Date
     *
     * @param localDateTime
     * @return
     */
    public static Date toDate(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(String s) {
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
        } catch (ParseException e) {
            log.error("时间转换异常", e);
        }
        return date;
    }

    public static Date toDate(String s, String format) {
        Date date = null;
        try {
            date = new SimpleDateFormat(format).parse(s);
        } catch (ParseException e) {
            log.error("时间转换异常", e);
        }
        return date;
    }

    public static Date util(String s) {
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(s);
        } catch (ParseException e) {
            log.error("时间转换异常", e);
        }
        return date;
    }

    /**
     * LocalDate转Date
     *
     * @param localDate
     * @return
     */
    public static Date toDate(LocalDate localDate) {
        Objects.requireNonNull(localDate, "localDate");
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalTime转Date
     * 以当天的日期+LocalTime组成新的LocalDateTime转换为Date
     *
     * @param localTime
     * @return
     */
    public static Date toDate(LocalTime localTime) {
        Objects.requireNonNull(localTime, "localTime");
        return Date.from(LocalDate.now().atTime(localTime).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Instant转Date
     *
     * @param instant
     * @return
     */
    public static Date toDate(Instant instant) {
        return Date.from(instant);
    }

    /**
     * epochMilli毫秒转Date
     *
     * @param epochMilli
     * @return
     */
    public static Date toDate(long epochMilli) {
        Objects.requireNonNull(epochMilli, "epochMilli");
        return new Date(epochMilli);
    }

    /**
     * ZonedDateTime转Date
     * 注意时间对应的时区和默认时区差异
     *
     * @param zonedDateTime
     * @return
     */
    public static Date toDate(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * Date转LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        Objects.requireNonNull(date, "date");
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime toLocalDateTime(String dateTimeStr) {
        dateTimeStr = dateTimeStr.replace("T", " ");
        LocalDateTime beginDateTime = LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return beginDateTime;
    }

    /**
     * LocalDate转LocalDateTime
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime toLocalDateTime(LocalDate localDate) {
        Objects.requireNonNull(localDate, "localDate");
        return localDate.atStartOfDay();
    }

    /**
     * LocalTime转LocalDateTime
     * 以当天的日期+LocalTime组成新的LocalDateTime
     *
     * @param localTime
     * @return
     */
    public static LocalDateTime toLocalDateTime(LocalTime localTime) {
        Objects.requireNonNull(localTime, "localTime");
        return LocalDate.now().atTime(localTime);
    }

    /**
     * Instant转LocalDateTime
     *
     * @param instant
     * @return
     */
    public static LocalDateTime toLocalDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    /**
     * epochMilli毫秒转LocalDateTime
     *
     * @param epochMilli
     * @return
     */
    public static LocalDateTime toLocalDateTime(long epochMilli) {
        Objects.requireNonNull(epochMilli, "epochMilli");
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneId.systemDefault());
    }

    /**
     * temporal转LocalDateTime
     *
     * @param temporal
     * @return
     */
    public static LocalDateTime toLocalDateTime(TemporalAccessor temporal) {
        return LocalDateTime.from(temporal);
    }

    /**
     * ZonedDateTime转LocalDateTime
     * 注意时间对应的时区和默认时区差异
     *
     * @param zonedDateTime
     * @return
     */
    public static LocalDateTime toLocalDateTime(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return zonedDateTime.toLocalDateTime();
    }

    public static LocalDate toLocalDate(String dateStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            return LocalDate.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * Date转LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate toLocalDate(Date date) {
        return toLocalDateTime(date).toLocalDate();
    }

    /**
     * LocalDateTime转LocalDate
     *
     * @param localDateTime
     * @return
     */
    public static LocalDate toLocalDate(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        return localDateTime.toLocalDate();
    }

    /**
     * Instant转LocalDate
     *
     * @param instant
     * @return
     */
    public static LocalDate toLocalDate(Instant instant) {
        return toLocalDateTime(instant).toLocalDate();
    }

    /**
     * temporal转LocalDate
     *
     * @param temporal
     * @return
     */
    public static LocalDate toLocalDate(TemporalAccessor temporal) {
        return LocalDate.from(temporal);
    }

    /**
     * ZonedDateTime转LocalDate
     * 注意时间对应的时区和默认时区差异
     *
     * @param zonedDateTime
     * @return
     */
    public static LocalDate toLocalDate(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return zonedDateTime.toLocalDate();
    }

    /**
     * Date转LocalTime
     *
     * @param date
     * @return
     */
    public static LocalTime toLocalTime(Date date) {
        return toLocalDateTime(date).toLocalTime();
    }

    /**
     * LocalDateTime转LocalTime
     *
     * @param localDateTime
     * @return
     */
    public static LocalTime toLocalTime(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        return localDateTime.toLocalTime();
    }

    /**
     * Instant转LocalTime
     *
     * @param instant
     * @return
     */
    public static LocalTime toLocalTime(Instant instant) {
        return toLocalDateTime(instant).toLocalTime();
    }

    /**
     * temporal转LocalTime
     *
     * @param temporal
     * @return
     */
    public static LocalTime toLocalTime(TemporalAccessor temporal) {
        return LocalTime.from(temporal);
    }

    /**
     * ZonedDateTime转LocalTime
     * 注意时间对应的时区和默认时区差异
     *
     * @param zonedDateTime
     * @return
     */
    public static LocalTime toLocalTime(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return zonedDateTime.toLocalTime();
    }

    /**
     * Date转Instant
     *
     * @param date
     * @return
     */
    public static Instant toInstant(Date date) {
        Objects.requireNonNull(date, "date");
        return date.toInstant();
    }

    /**
     * LocalDateTime转Instant
     *
     * @param localDateTime
     * @return
     */
    public static Instant toInstant(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    }

    /**
     * LocalDate转Instant
     *
     * @param localDate
     * @return
     */
    public static Instant toInstant(LocalDate localDate) {
        return toLocalDateTime(localDate).atZone(ZoneId.systemDefault()).toInstant();
    }

    /**
     * LocalTime转Instant
     * 以当天的日期+LocalTime组成新的LocalDateTime转换为Instant
     *
     * @param localTime
     * @return
     */
    public static Instant toInstant(LocalTime localTime) {
        return toLocalDateTime(localTime).atZone(ZoneId.systemDefault()).toInstant();
    }

    /**
     * epochMilli毫秒转Instant
     *
     * @param epochMilli
     * @return
     */
    public static Instant toInstant(long epochMilli) {
        Objects.requireNonNull(epochMilli, "epochMilli");
        return Instant.ofEpochMilli(epochMilli);
    }

    /**
     * temporal转Instant
     *
     * @param temporal
     * @return
     */
    public static Instant toInstant(TemporalAccessor temporal) {
        return Instant.from(temporal);
    }

    /**
     * ZonedDateTime转Instant
     * 注意时间对应的时区和默认时区差异
     *
     * @param zonedDateTime
     * @return
     */
    public static Instant toInstant(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return zonedDateTime.toInstant();
    }

    /**
     * Date转毫秒值
     * 从1970-01-01T00:00:00Z开始的毫秒值
     *
     * @param date
     * @return
     */
    public static long toEpochMilli(Date date) {
        Objects.requireNonNull(date, "date");
        return date.getTime();
    }

    /**
     * LocalDateTime转毫秒值
     * 从1970-01-01T00:00:00Z开始的毫秒值
     *
     * @param localDateTime
     * @return
     */
    public static long toEpochMilli(LocalDateTime localDateTime) {
        return toInstant(localDateTime).toEpochMilli();
    }

    /**
     * LocalDate转毫秒值
     * 从1970-01-01T00:00:00Z开始的毫秒值
     *
     * @param localDate
     * @return
     */
    public static long toEpochMilli(LocalDate localDate) {
        return toInstant(localDate).toEpochMilli();
    }

    /**
     * Instant转毫秒值
     * 从1970-01-01T00:00:00Z开始的毫秒值
     *
     * @param instant
     * @return
     */
    public static long toEpochMilli(Instant instant) {
        Objects.requireNonNull(instant, "instant");
        return instant.toEpochMilli();
    }

    /**
     * ZonedDateTime转毫秒值，注意时间对应的时区和默认时区差异
     * 从1970-01-01T00:00:00Z开始的毫秒值
     *
     * @param zonedDateTime
     * @return
     */
    public static long toEpochMilli(ZonedDateTime zonedDateTime) {
        Objects.requireNonNull(zonedDateTime, "zonedDateTime");
        return zonedDateTime.toInstant().toEpochMilli();
    }

    /**
     * Date转ZonedDateTime，时区为系统默认时区
     *
     * @param date
     * @return
     */
    public static ZonedDateTime toZonedDateTime(Date date) {
        Objects.requireNonNull(date, "date");
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime().atZone(ZoneId.systemDefault());
    }

    /**
     * LocalDateTime转ZonedDateTime，时区为系统默认时区
     *
     * @param localDateTime
     * @return
     */
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        return localDateTime.atZone(ZoneId.systemDefault());
    }

    /**
     * LocalDateTime转ZonedDateTime，时区为zoneId对应时区
     * 注意，需要保证localDateTime和zoneId是对应的，不然会出现错误
     *
     * @param localDateTime
     * @param zoneId
     * @return
     */
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime, String zoneId) {
        Objects.requireNonNull(localDateTime, "localDateTime");
        Objects.requireNonNull(zoneId, "zoneId");
        return localDateTime.atZone(ZoneId.of(zoneId));
    }

    /**
     * LocalDate转ZonedDateTime，时区为系统默认时区
     *
     * @param localDate
     * @return such as 2020-02-19T00:00+08:00[Asia/Shanghai]
     */
    public static ZonedDateTime toZonedDateTime(LocalDate localDate) {
        Objects.requireNonNull(localDate, "localDate");
        return localDate.atStartOfDay().atZone(ZoneId.systemDefault());
    }

    /**
     * LocalTime转ZonedDateTime
     * 以当天的日期+LocalTime组成新的ZonedDateTime，时区为系统默认时区
     *
     * @param localTime
     * @return
     */
    public static ZonedDateTime toZonedDateTime(LocalTime localTime) {
        Objects.requireNonNull(localTime, "localTime");
        return LocalDate.now().atTime(localTime).atZone(ZoneId.systemDefault());
    }

    /**
     * Instant转ZonedDateTime，时区为系统默认时区
     *
     * @param instant
     * @return
     */
    public static ZonedDateTime toZonedDateTime(Instant instant) {
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault()).atZone(ZoneId.systemDefault());
    }

    /**
     * epochMilli毫秒转ZonedDateTime，时区为系统默认时区
     *
     * @param epochMilli
     * @return
     */
    public static ZonedDateTime toZonedDateTime(long epochMilli) {
        Objects.requireNonNull(epochMilli, "epochMilli");
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneId.systemDefault()).atZone(ZoneId.systemDefault());
    }

    /**
     * temporal转ZonedDateTime，时区为系统默认时区
     *
     * @param temporal
     * @return
     */
    public static ZonedDateTime toZonedDateTime(TemporalAccessor temporal) {
        return LocalDateTime.from(temporal).atZone(ZoneId.systemDefault());
    }

    public static boolean isToday(String timeString) {
        try {
            // 定义日期时间格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // 解析时间字符串
            LocalDateTime dateTime = LocalDateTime.parse(timeString, formatter);

            // 比较日期部分
            return dateTime.toLocalDate().equals(LocalDate.now());
        } catch (DateTimeParseException e) {
            // 时间字符串格式不正确
            return false;
        }
    }

    /**
     * 获取一天的凌晨时间
     *
     * @param date
     * @return
     */
    public static Date getStartDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMinimum(Calendar.MILLISECOND));
        return calendar.getTime();
    }

    public static Date add(Date time, int amount, int type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(type, amount);
        return calendar.getTime();
    }

    /**
     * 将字符串转成时间
     *
     * @param str
     * @return
     */
    public static Date parseDatetime(String str) {
        try {
            return datetimeFormat.get().parse(str);
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 计算两个时间之间的差值，根据标志的不同而不同
     *
     * @param flag   计算标志，表示按照年/月/日/时/分/秒等计算
     * @param calSrc 减数
     * @param calDes 被减数
     * @return 两个日期之间的差值
     */
    public static int dateDiff(char flag, Calendar calSrc, Calendar calDes) {

        long millisDiff = getMillis(calSrc) - getMillis(calDes);
        char year = 'y';
        char day = 'd';
        char hour = 'h';
        char minute = 'm';
        char second = 's';

        if (flag == year) {
            return (calSrc.get(Calendar.YEAR) - calDes.get(Calendar.YEAR));
        }

        if (flag == day) {
            return (int) (millisDiff / DAY_IN_MILLIS);
        }

        if (flag == hour) {
            return (int) (millisDiff / HOUR_IN_MILLIS);
        }

        if (flag == minute) {
            return (int) (millisDiff / MINUTE_IN_MILLIS);
        }

        if (flag == second) {
            return (int) (millisDiff / SECOND_IN_MILLIS);
        }

        return 0;
    }

    public static int dateDiff(Integer calendarType, Date date1, Date date2) {

        long millisDiff = date1.getTime() - date2.getTime();

        if (calendarType == Calendar.HOUR) {
            return (int) (millisDiff / HOUR_IN_MILLIS);
        }

        if (calendarType == Calendar.MINUTE) {
            return (int) (millisDiff / MINUTE_IN_MILLIS);
        }

        if (calendarType == Calendar.SECOND) {
            return (int) (millisDiff / SECOND_IN_MILLIS);
        }

        return 0;
    }

    /**
     * 系统时间的毫秒数
     *
     * @return 系统时间的毫秒数
     */
    public static long getMillis() {
        return System.currentTimeMillis();
    }

    /**
     * 指定日历的毫秒数
     *
     * @param cal 指定日历
     * @return 指定日历的毫秒数
     */
    public static long getMillis(Calendar cal) {
        // --------------------return cal.getTimeInMillis();
        return cal.getTime().getTime();
    }


    /**
     * Date增加天
     */
    public static Date dateAddDay(Date date, int day) {
        // 使用Calendar类来增加一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        date = calendar.getTime();
        return date;
    }

    /**
     * 计算两个日期之前有哪些年月，格式yyyyMM 202506
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static List<String> getYearMonthsBetween(Date startDate, Date endDate) {
        return getYearMonthsBetween(TopsDateUtils.toLocalDate(startDate), TopsDateUtils.toLocalDate(endDate));
    }

    public static List<String> getYearMonthsBetween(String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (startDate.length() > 10) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }
        LocalDate startLocalDate = LocalDate.parse(startDate, formatter);
        LocalDate endLocalDate = LocalDate.parse(endDate, formatter);
        return getYearMonthsBetween(startLocalDate, endLocalDate);
    }

    public static List<String> getYearMonthsBetween(LocalDate startDate, LocalDate endDate) {
        List<String> yearMonths = new ArrayList<>();
        YearMonth start = YearMonth.from(startDate);
        YearMonth end = YearMonth.from(endDate);
        DateTimeFormatter yearMonthFormatter = DateTimeFormatter.ofPattern("yyyyMM");
        while (!start.isAfter(end)) {
            yearMonths.add(start.format(yearMonthFormatter));
            start = start.plusMonths(1);
        }
        return yearMonths;
    }
}
