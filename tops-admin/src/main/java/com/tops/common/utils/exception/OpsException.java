package com.tops.common.utils.exception;

/**
 * ClassName:PucuException
 * Package:com.jdl.pu.common.util.exception
 * Description:异常类
 *
 * @date:2022/1/10 6:54 PM
 * @author:WeiLiming
 */
public class OpsException extends RuntimeException {
    public OpsException() {
    }

    public OpsException(String message) {
        super(message);
    }

    public OpsException(String message, Throwable cause) {
        super(message, cause);
    }
}
