package com.tops.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@Component
@Slf4j
public class ThreadUtil {

    @Autowired
    @Qualifier("pFinderPoolTaskExecutor")
    private ExecutorService pFinderPoolTaskExecutor;

    /**
     * 提交线程任务
     */
    public <T> Future<T> submitThread(Callable<T> thread) {
        return pFinderPoolTaskExecutor.submit(thread);
    }

    /**
     * 提交线程任务
     */
    public void submitThread(Runnable thread) {
         pFinderPoolTaskExecutor.submit(thread);
    }


}
