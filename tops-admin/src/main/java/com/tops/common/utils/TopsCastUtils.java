package com.tops.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClassName:PucuCastUtils
 * Package:com.jdl.pu.common.util
 * Description:集合转换
 *
 * @date:2022/1/10 6:51 PM
 * @author:WeiLiming
 */
public class TopsCastUtils {
    public static Integer[] arrayToIntArr(String[] b) {
        if (b == null) {
            return null;
        }
        Integer[] intArr = new Integer[b.length];
        for (int i = 0; i < b.length; i++) {
            intArr[i] = Integer.valueOf(b[i]);
        }
        return intArr;
    }

    public static int byteArrayToInt(byte[] b) {
        return b[3] & 0xFF |
                (b[2] & 0xFF) << 8 |
                (b[1] & 0xFF) << 16 |
                (b[0] & 0xFF) << 24;
    }

    public static byte[] intToByteArray(int a) {
        return new byte[]{
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    public static List<Integer> stringToIntList(Set<String> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Integer> resultList = Lists.newArrayList();
        for (String str : list) {
            resultList.add(Integer.valueOf(str));
        }
        return resultList;
    }

    /**
     * Map转换类
     * @param originalMap
     * @return
     */
    public static Map<String, String[]> objToStrArrMap(Map<String, Object> originalMap) {
        if (originalMap == null) {
            return null;
        }
        return originalMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> {
                    Object value = e.getValue();
                    if (value instanceof String) {
                        return new String[]{(String) value};
                    } else if (value instanceof String[]) {
                        return (String[]) value;
                    } else if (value instanceof Collection) {
                        return ((Collection<?>) value).stream()
                            .map(Object::toString)
                            .toArray(String[]::new);
                    } else {
                        return new String[]{value.toString()};
                    }
                }
            ));
    }
}
