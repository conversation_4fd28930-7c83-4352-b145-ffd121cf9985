package com.tops.common.utils;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TopsHtmlUtils {
    @Getter
    @Setter
    public static class HtmlStringBuilder{
        private StringBuilder htmlString;

        public HtmlStringBuilder append(String str){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append(str);
            return this;
        }
        public HtmlStringBuilder startTable(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("<table  border=\"1px\" cellspacing=\"0\"  width=\"700px\">");
            return this;
        }
        public HtmlStringBuilder endTable(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("</table>");
            return this;
        }
        public HtmlStringBuilder startRow(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("<tr>");
            return this;
        }
        public HtmlStringBuilder startRowError(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("<tr style=\"background-color: coral;\">");
            return this;
        }
        public HtmlStringBuilder endRow(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("</tr>");
            return this;
        }
        public HtmlStringBuilder startCol(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("<td>");
            return this;
        }
        public HtmlStringBuilder endCol(){
            if (htmlString==null){
                htmlString=new StringBuilder();
            }
            htmlString.append("</td>");
            return this;
        }
        @Override
        public String toString(){
            return htmlString.toString();
        }
    }
}
