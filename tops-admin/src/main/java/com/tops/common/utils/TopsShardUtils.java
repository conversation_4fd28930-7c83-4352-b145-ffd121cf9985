package com.tops.common.utils;


import com.google.common.collect.Maps;

import java.util.Map;

/**
 * ClassName:PucuShardUtils
 * Package:com.jdl.pu.common.util
 * Description:hash分片算法
 *
 * @date:2022/1/10 7:11 PM
 * @author:WeiLiming
 */
public class TopsShardUtils {
    public static void main(String[] args) {
        Map<Integer, Integer> map = Maps.newHashMap();
        for (int i = 0; i < 200000; i++) {
            long rad = Math.round(Math.random() * 10000000);
            int key = murmur3Hash(String.valueOf(rad),150);
            if (map.get(key) == null) {
                map.put(key, 1);
            } else {
                map.put(key, map.get(key) + 1);
            }


        }
    }

    /**
     * 哈希keyword, 返回一个落在0~totalShard-1之内的整数
     *
     * @param keyword
     * @param totalShard
     * @return
     */
    public static int murmur3Hash(String keyword, int totalShard) {
        byte[] bytesToHash = new byte[keyword.length() * 2];

        for (int i = 0; i < keyword.length(); ++i) {
            char c = keyword.charAt(i);
            byte b1 = (byte) c;
            byte b2 = (byte) (c >>> 8);

            assert (b1 & 255 | (b2 & 255) << 8) == c;

            bytesToHash[i * 2] = b1;
            bytesToHash[i * 2 + 1] = b2;
        }
        final int hash = hash(bytesToHash, 0, bytesToHash.length, 0);
        return Math.floorMod(hash, totalShard);
    }

    private static int hash(byte[] data, int offset, int len, int seed) {
        final int c1 = 0xcc9e2d51;
        final int c2 = 0x1b873593;

        int h1 = seed;
        // round down to 4 byte block
        int roundedEnd = offset + (len & 0xfffffffc);

        for (int i = offset; i < roundedEnd; i += 4) {
            // little endian load order
            int k1 = (data[i] & 0xff) | ((data[i + 1] & 0xff) << 8) | ((data[i + 2] & 0xff) << 16) | (data[i + 3] << 24);
            k1 *= c1;
            k1 = Integer.rotateLeft(k1, 15);
            k1 *= c2;

            h1 ^= k1;
            h1 = Integer.rotateLeft(h1, 13);
            h1 = h1 * 5 + 0xe6546b64;
        }
        // tail
        int k1 = 0;

        switch (len & 0x03) {
            case 3:
                k1 = (data[roundedEnd + 2] & 0xff) << 16;
                // fallthrough
            case 2:
                k1 |= (data[roundedEnd + 1] & 0xff) << 8;
                // fallthrough
            case 1:
                k1 |= (data[roundedEnd] & 0xff);
                k1 *= c1;
                k1 = Integer.rotateLeft(k1, 15);
                k1 *= c2;
                h1 ^= k1;
        }
        // finalization
        h1 ^= len;
        // fmix(h1);
        h1 ^= h1 >>> 16;
        h1 *= 0x85ebca6b;
        h1 ^= h1 >>> 13;
        h1 *= 0xc2b2ae35;
        h1 ^= h1 >>> 16;

        return h1;
    }
}
