package com.tops.common.utils;

import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * ClassName:TopsProfilerUtils
 * Package:com.jdl.pu.common.util
 * Description:UMP打点工具类
 *
 * @date:2022/1/10 6:57 PM
 * @author:WeiLiming
 */
public class TopsProfilerUtils {
    private final static boolean METHOD_ENABLE_HEART = false;
    private final static boolean METHOD_ENABLE_TP = true;

    public static CallerInfo registerInfo(String umpKey, String appName) {
        CallerInfo info = Profiler.registerInfo(umpKey, appName, METHOD_ENABLE_HEART, METHOD_ENABLE_TP);
        return info;
    }

    /**
     * 注册 CallerInfo
     *
     * @param baseKey
     * @param sources
     * @return
     */
    public static CallerInfo[] registerCallerInfo(String baseKey, String appName, String... sources) {
        CallerInfo[] callerInfoArray = new CallerInfo[sources.length + 1];
        CallerInfo caller = Profiler.registerInfo(baseKey, appName, METHOD_ENABLE_HEART, METHOD_ENABLE_TP);
        callerInfoArray[0] = caller;
        for (int i = 0; i < sources.length; i++) {
            String source = sources[i];
            if ("null".equals(source) || StringUtils.isEmpty(source)) {
                source = "unKnown";
            }
            StringBuilder sb = new StringBuilder(baseKey);
            sb.append("_");
            sb.append(source);
            CallerInfo sourceCaller = Profiler.registerInfo(sb.toString(), appName, METHOD_ENABLE_HEART, METHOD_ENABLE_TP);
            callerInfoArray[1 + i] = sourceCaller;
        }
        return callerInfoArray;
    }

    /**
     * 打可用率点
     *
     * @param callerInfos
     */
    public static void functionError(CallerInfo... callerInfos) {
        if (ArrayUtils.isNotEmpty(callerInfos)) {
            for (CallerInfo callerInfo : callerInfos) {
                Profiler.functionError(callerInfo);
            }
        }
    }

    /**
     * 结束Ump打点
     *
     * @param callerInfos
     */
    public static void registerInfoEnd(CallerInfo... callerInfos) {
        if (ArrayUtils.isNotEmpty(callerInfos)) {
            for (CallerInfo callerInfo : callerInfos) {
                Profiler.registerInfoEnd(callerInfo);
            }
        }
    }
}
