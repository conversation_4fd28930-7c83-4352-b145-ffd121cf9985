package com.tops.common.utils;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class TopsEnvUtils {
    @Getter
    static enum EnvEnums {
        DEV("dev", "开发环境"),
        YFB("yfb", "预发环境"),
        PROD("prod", "生产环境"),
        ;

        private String code;
        private String desc;

        EnvEnums(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        static EnvEnums getEnumByCode(String code) {
            for (EnvEnums value : EnvEnums.values()) {
                if (Objects.equals(value.getCode(), code)) {
                    return value;
                }
            }
            return null;
        }
    }


    @Value("${application.env}")
    private String env;

    public boolean isProd() {
        return Objects.equals(env, EnvEnums.PROD.getCode());
    }

    public boolean isYfb() {
        return Objects.equals(env, EnvEnums.YFB.getCode());
    }

    public boolean isDev() {
        return Objects.equals(env, EnvEnums.DEV.getCode());
    }

    public String getEnvDesc() {
        EnvEnums envEnum = EnvEnums.getEnumByCode(env);
        if (envEnum == null) {
            return "未知环境";
        }
        return envEnum.getDesc();
    }
}
