package com.tops.common.utils;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import java.util.List;
import java.util.Map;

/**
 * ClassName:TopsJsonUtils
 * Package:com.jdl.pu.common.util
 * Description:Json工具类
 *
 * @date:2022/1/10 7:03 PM
 * @author:WeiLiming
 */
public class TopsJsonUtils {
    public static String toJSONString(Object o) {
        return JSONObject.toJSONString(o);
       /* ObjectMapper mapper = new ObjectMapper();
        String jsonString = null;
        try {
            jsonString = mapper.writerWithDefaultPrettyPrinter()
                    .writeValueAsString(o);
        } catch (JsonProcessingException e) {
            throw new OpsException(e.getMessage(), e);
        }
        return jsonString;*/
    }

    public static <T> T parseObject(String jsonString, Class<T> valueType) {
        return JSON.parseObject(jsonString, valueType);
       /* ObjectMapper mapper = new ObjectMapper();
        T obj = null;
        try {
            obj = mapper.readValue(jsonString, valueType);
        } catch (IOException e) {
            throw new OpsException(e.getMessage(), e);
        }
        return obj;*/
    }

    public static <T> Map<String, T> parseMapObject(String jsonString) {
        return JSON.parseObject(jsonString, new TypeReference<Map<String, T>>() {
        });
       /* ObjectMapper mapper = new ObjectMapper();
        T obj = null;
        try {
            obj = mapper.readValue(jsonString, valueType);
        } catch (IOException e) {
            throw new OpsException(e.getMessage(), e);
        }
        return obj;*/
    }

    public static <T> List<T> parseListObject(String jsonString, Class<T> valueType) {
        return JSON.parseArray(jsonString, valueType);
        /*ObjectMapper mapper = new ObjectMapper();
        CollectionType listType = mapper.getTypeFactory().constructCollectionType(ArrayList.class, valueType);
        List<T> obj = null;
        try {
            obj = mapper.readValue(jsonString, listType);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return obj;*/
    }
}
