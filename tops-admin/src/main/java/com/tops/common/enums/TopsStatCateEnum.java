package com.tops.common.enums;

import lombok.Getter;

/**
 * ClassName:TopsStatCateEnum
 * Package:com.tops.common.enums
 * Description:
 *
 * {@code @date:2024/6/12} 下午3:44
 * {@code @author:WeiLiming}
 */
@Getter
public enum TopsStatCateEnum {

    CATE_DAY("day", "日期"),
    CATE_HOUR("hour", "小时"),
    /*FIX_DELAY(I18nUtil.getString("schedule_type_fix_delay"))*/;

    private final String code;
    private final String name;

    TopsStatCateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TopsStatCateEnum match(String name, TopsStatCateEnum defaultItem) {
        for (TopsStatCateEnum item : TopsStatCateEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return defaultItem;
    }
}
