package com.tops.common.enums;

/**
 * ClassName:TopsStatTypeEnum
 * Package:com.tops.common.enums
 * Description:
 *
 * @date:2024/6/12 下午4:05
 * @author:WeiLiming
 */

import lombok.Getter;

@Getter
public enum TopsStatTypeEnum {

    TYPE_ORDER_COUNT("orderCount", "订单单量"),
    TYPE_ORDER_FAIL_COUNT("orderFailCount", "订单失败数据"),
    /*FIX_DELAY(I18nUtil.getString("schedule_type_fix_delay"))*/;

    private final String code;
    private final String name;

    TopsStatTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static TopsStatCateEnum match(String name, TopsStatCateEnum defaultItem) {
        for (TopsStatCateEnum item : TopsStatCateEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return defaultItem;
    }
}
