package com.tops.common.aspect.limiter;

import com.alibaba.fastjson.JSON;
import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.domain.dto.InvokeEvent;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InternalRateLimitException;
import com.tops.common.exception.InvalidRequestException;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public abstract class BaseRateLimiter implements RateLimiter {

    /**
     * 带有限流的方法调用
     *
     * @param rateLimit
     * @param event
     */
    @Override
    public Object process(RateLimit rateLimit, InvokeEvent event) throws Throwable {
        checkRateLimit(rateLimit);
        Object result = null;
        if (Objects.equals(rateLimit.abortPolicy(), RateLimit.AbortPolicy.ABORT)) {
            result = tryProcess(rateLimit, event);
        } else if (Objects.equals(rateLimit.abortPolicy(), RateLimit.AbortPolicy.WAIT)) {
            long start = System.currentTimeMillis();
            long end = start + rateLimit.waitSeconds() * 1000;
            while (System.currentTimeMillis() < end) {
                try {
                    result = tryProcess(rateLimit, event);
                    break;
                } catch (InternalRateLimitException e) {
                    log.error("BaseRateLimiter.process 触发限流:{}", JSON.toJSONString(event.getPathName()));
                    Thread.sleep(rateLimit.waitGapMillis());
                }
            }
        } else {
            log.error("BaseRateLimiter.process 当前抛弃策略非法:{}", JSON.toJSONString(rateLimit.abortPolicy()));
            throw new InvalidRequestException(String.format("当前抛弃策略非法:%s", JSON.toJSONString(rateLimit.abortPolicy())));
        }
        return result;
    }

    protected abstract Object tryProcess(RateLimit rateLimit, InvokeEvent event) throws Throwable;


    private void checkRateLimit(RateLimit rateLimit) throws InvalidRequestException {
        if (rateLimit == null) {
            log.error("BaseRateLimiter.checkRateLimit 限流配置非法，rateLimit=null");
            throw new InvalidRequestException("限流配置非法，rateLimit=null");
        }
        if (Objects.equals(rateLimit.abortPolicy(), RateLimit.AbortPolicy.WAIT) && rateLimit.waitSeconds() <= 0) {
            log.error("BaseRateLimiter.checkRateLimit 限流配置非法，waitSeconds < 0");
            throw new InvalidRequestException("限流配置非法，waitSeconds < 0");
        }
    }
}
