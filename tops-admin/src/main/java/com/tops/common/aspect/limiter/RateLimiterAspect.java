package com.tops.common.aspect.limiter;


import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.domain.dto.InvokeEvent;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Aspect
@Component("rateLimiterAspectWQ")
public class RateLimiterAspect {
    @Autowired
    private RateLimiter rateLimiter;

    @Pointcut("@annotation(com.tops.common.aspect.annotation.RateLimit)")
    public void rateLimit() {
    }

    @Around(value = "rateLimit() && @annotation(rateLimit)")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        InvokeEvent invokeEvent = new InvokeEvent(joinPoint);
        return rateLimiter.process(rateLimit, invokeEvent);
    }
}
