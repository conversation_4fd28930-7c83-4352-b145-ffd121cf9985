package com.tops.common.aspect.annotation;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.lang.annotation.*;


/**
 * 方法限流配置
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {

    /**
     * 限流类型
     */
    public RateLimitType type() default RateLimitType.LOCAL;

    /**
     * 速率
     */
    public int maxRate();

    /**
     * 速率单位
     */
    public RateUnit rateUnit() default RateUnit.SECOND;

    /**
     * 抛弃策略
     */
    public AbortPolicy abortPolicy() default AbortPolicy.ABORT;

    /**
     * 抛弃策略为等待时，等待时长
     */
    public long waitSeconds() default 10;

    /**
     * 抛弃策略为等待时，等待时长
     */
    public long waitGapMillis() default 1000;

    @JSONType(serializeEnumAsJavaBean = true)
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public static enum RateLimitType {
        LOCAL("local", "本地单机限流"),
        CLUSTER("cluster", "远程集群限流"),
        ;
        private String code;
        private String desc;

        RateLimitType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
    @JSONType(serializeEnumAsJavaBean = true)
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public static enum RateUnit {
        SECOND("second", "次/秒"),
        MINUTE("minute", "次/分钟"),
        TWO_MINUTE("twoMinute", "次/2分钟"),
        ;
        private String code;
        private String desc;

        RateUnit(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
    @JSONType(serializeEnumAsJavaBean = true)
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public static enum AbortPolicy {
        ABORT("abort", "丢弃当前任务"),
        WAIT("wait", "等待"),
        ;
        private String code;
        private String desc;

        AbortPolicy(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
