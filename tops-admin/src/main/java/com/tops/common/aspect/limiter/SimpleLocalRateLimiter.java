package com.tops.common.aspect.limiter;

import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.domain.dto.InvokeEvent;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InternalRateLimitException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * 简易本地单机限流服务
 */
@Component
@Slf4j
public class SimpleLocalRateLimiter extends BaseRateLimiter {

    private ConcurrentHashMap<String, LinkedBlockingDeque<Integer>> limiterPool = new ConcurrentHashMap<>();

    @Autowired
    private ThreadUtil threadUtil;


    @Override
    protected Object tryProcess(RateLimit rateLimit, InvokeEvent event) throws Throwable {
        limiterPool.computeIfAbsent(event.getPathName(), (pathName) -> {
            threadUtil.submitThread(new RefreshTokenPoolTask(limiterPool, event.getPathName(), rateLimit.maxRate(), getMills(rateLimit.rateUnit())));
            return initTokenPool(rateLimit.maxRate());
        });
        LinkedBlockingDeque<Integer> tokenPool = limiterPool.get(event.getPathName());
        try {
            Integer poll = tokenPool.poll(10, TimeUnit.MICROSECONDS);
            if (poll == null) {
                throw new InternalRateLimitException(String.format("方法：%s触发限流", event.getPathName()));
            }
            return event.process();
        } catch (InterruptedException e) {
            log.warn("SimpleLocalRateLimiter.tryProcess 方法：{}触发限流", event.getPathName());
            throw new InternalRateLimitException(String.format("方法：%s触发限流", event.getPathName()));
        } catch (InternalRateLimitException e) {
            throw e;
        } catch (Throwable e) {
            throw e;
        }
    }


    private LinkedBlockingDeque<Integer> initTokenPool(int size) {
        LinkedBlockingDeque<Integer> tokenPool = new LinkedBlockingDeque<>(size);
        for (int i = 0; i < size; i++) {
            tokenPool.push(i + 10);
        }
        return tokenPool;
    }

    private Long getMills(RateLimit.RateUnit unit) {
        if (Objects.equals(unit, RateLimit.RateUnit.SECOND)) {
            return 1000L;
        } else if (Objects.equals(unit, RateLimit.RateUnit.MINUTE)) {
            return 60 * 1000L;
        } else if (Objects.equals(unit, RateLimit.RateUnit.TWO_MINUTE)) {
            return 120 * 1000L;
        }
        return 1000L;
    }

    private static class RefreshTokenPoolTask implements Runnable {
        private ConcurrentHashMap<String, LinkedBlockingDeque<Integer>> limiterPool;
        private String tokenPoolName;
        private int tokenSize;

        private long periodMillis;

        public RefreshTokenPoolTask(ConcurrentHashMap<String, LinkedBlockingDeque<Integer>> limiterPool, String tokenPoolName, int tokenSize, long periodMillis) {
            this.limiterPool = limiterPool;
            this.tokenPoolName = tokenPoolName;
            this.tokenSize = tokenSize;
            this.periodMillis = periodMillis;
        }

        /**
         * When an object implementing interface <code>Runnable</code> is used
         * to create a thread, starting the thread causes the object's
         * <code>run</code> method to be called in that separately executing
         * thread.
         * <p>
         * The general contract of the method <code>run</code> is that it may
         * take any action whatsoever.
         *
         * @see Thread#run()
         */
        @Override
        public void run() {
            while (limiterPool.containsKey(tokenPoolName)) {
                try {
                    Thread.sleep(periodMillis);
                    LinkedBlockingDeque<Integer> tokenPool = limiterPool.get(tokenPoolName);
                    int size = tokenPool.size();
                    if (tokenPool != null) {
                        for (int i = 0; i < tokenSize - size; i++) {
                            tokenPool.push(i + 10);
                        }
                    }

                } catch (InterruptedException e) {
                    limiterPool.remove(tokenPoolName);
                }
            }
        }
    }
}
