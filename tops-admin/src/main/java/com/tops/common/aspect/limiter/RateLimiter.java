package com.tops.common.aspect.limiter;

import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.domain.dto.InvokeEvent;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * 限流服务公共接口
 */
public interface RateLimiter {

    /**
     * 带有限流的方法调用
     */
    Object process(RateLimit rateLimit, InvokeEvent event) throws Throwable;

}
