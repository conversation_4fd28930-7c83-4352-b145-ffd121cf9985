package com.tops.ducc.bean.dto;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DUCCContext {
    /**
     * 当前环境
     */
    private String env;
    /**
     * 应用
     */
    private String application;
    /**
     * token
     */
    private String token;
    /**
     * ducc链接
     */
    private String duccUrl;
    /**
     * 空间
     */
    private String namespace;
    /**
     * 配置
     */
    private String config;
    /**
     * 环境
     */
    private String profile;
}
