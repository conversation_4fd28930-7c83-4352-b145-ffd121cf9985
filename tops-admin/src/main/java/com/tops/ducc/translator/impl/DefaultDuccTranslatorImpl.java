package com.tops.ducc.translator.impl;

import com.tops.ducc.bean.dto.DUCCContext;
import com.tops.ducc.bean.dto.DUCCItemMetaInfo;
import com.tops.ducc.config.DuccMetaConfig;
import com.tops.ducc.translator.DuccTranslator;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

@Slf4j
@Getter
@Setter
@Component
public class DefaultDuccTranslatorImpl extends BaseDuccTranslatorImpl {
    @Autowired
    private DuccMetaConfig duccMetaConfig;

    @Value("${ducc.http.env}")
    private String curEnv;

    @Value("${ducc.http.url}")
    private String url;


    @Override
    protected String getDefaultEnv() {
        return curEnv;
    }

    @Override
    protected String getToken(DUCCItemMetaInfo metaInfo) {
        return duccMetaConfig.getToken().get(metaInfo.getApplication());
    }

    @Override
    protected String getDUCCUrl(DUCCItemMetaInfo metaInfo) {
        return url;
    }


    /**
     * 如果duccContext为null，或者其中某个字段为空则返回false
     */
    @Override
    protected boolean checkDUCCContext(DUCCContext duccContext) {
        if (duccContext == null) {
            return false;
        }
        if (StringUtils.isBlank(duccContext.getEnv())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext env为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getApplication())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext application为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getToken())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext token为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getDuccUrl())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext duccURL为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getNamespace())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext namespace为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getConfig())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext config为空，duccContext校验失败");
            return false;
        }
        if (StringUtils.isBlank(duccContext.getProfile())) {
            log.warn("LocalCacheDUCCContextManager.checkDUCCContext profile为空，duccContext校验失败");
            return false;
        }
        return true;
    }
}
