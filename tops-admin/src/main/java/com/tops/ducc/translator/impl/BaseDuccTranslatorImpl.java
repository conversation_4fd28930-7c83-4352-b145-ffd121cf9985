package com.tops.ducc.translator.impl;

import com.tops.ducc.bean.dto.DUCCContext;
import com.tops.ducc.bean.dto.DUCCItemMetaInfo;
import com.tops.ducc.translator.DuccTranslator;
import org.apache.commons.lang3.StringUtils;


public abstract class BaseDuccTranslatorImpl implements DuccTranslator {
    @Override
    public final DUCCContext getDUCCContext(DUCCItemMetaInfo metaInfo) {
        if (StringUtils.isBlank(metaInfo.getApplication())) {
            return null;
        }
        DUCCContext duccContext = new DUCCContext();
        duccContext.setEnv(getDefaultEnv());
        duccContext.setApplication(metaInfo.getApplication());
        duccContext.setToken(getToken(metaInfo));
        duccContext.setDuccUrl(getDUCCUrl(metaInfo));
        duccContext.setNamespace(metaInfo.getNamespace());
        duccContext.setConfig(metaInfo.getConfig());
        duccContext.setProfile(metaInfo.getProfile());
        if (!checkDUCCContext(duccContext)) {
            return null;
        }
        return duccContext;
    }

    @Override
    public final String getEnv() {
        return getDefaultEnv();
    }

    protected abstract String getDefaultEnv();

    protected abstract String getToken(DUCCItemMetaInfo metaInfo);

    protected abstract String getDUCCUrl(DUCCItemMetaInfo metaInfo);

    protected abstract boolean checkDUCCContext(DUCCContext duccContext);
}
