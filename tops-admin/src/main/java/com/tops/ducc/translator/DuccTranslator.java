package com.tops.ducc.translator;

import com.tops.ducc.bean.dto.DUCCContext;
import com.tops.ducc.bean.dto.DUCCItemMetaInfo;

import java.util.Set;

public interface DuccTranslator {
    /**
     * 获取指定应用上下文（如果获取失败则返回null，例如缺少一个配置项）
     * @param metaInfo DUCC配置项元数据信息
     * @return DUCC上下文
     */
    DUCCContext getDUCCContext(DUCCItemMetaInfo metaInfo);
    /**
     * 获取env,用于告警及打点
     */
    String getEnv();
}
