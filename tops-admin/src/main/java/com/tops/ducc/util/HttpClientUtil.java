package com.tops.ducc.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tops.ducc.bean.dto.HttpDeleteWithBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.*;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.*;

@Slf4j
public class HttpClientUtil {
    public static final String METHOD_NAME_GET = "GET";
    public static final String METHOD_NAME_POST = "POST";
    public static final String METHOD_NAME_PUT = "PUT";

    /**
     * 设置请求参数
     *
     * @param
     * @return
     */
    public static List<NameValuePair> setHttpParams(Map<String, Object> paramMap) {
        List<NameValuePair> formParams = new ArrayList<NameValuePair>();
        Set<Map.Entry<String, Object>> set = paramMap.entrySet();
        for (Map.Entry<String, Object> entry : set) {
            formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
        }
        return formParams;
    }

    /**
     * 绕过验证
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     * @throws KeyStoreException
     */
    public static SSLConnectionSocketFactory createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
            // 信任所有
            @Override
            public boolean isTrusted(X509Certificate[] chain, String authType) {
                return true;
            }
        }).build();
        return new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
    }

    /**
     * 设置代理
     *
     * @param
     * @return
     */
    public static RequestConfig setProxy(String host, Integer port) {
        RequestConfig config;
        if (host != null && port != null) {
            HttpHost httpHost = new HttpHost(host, port);
            config = RequestConfig.custom().setProxy(httpHost).build();
        } else {
            config = RequestConfig.custom().build();
        }
        return config;
    }

    /**
     * Http GET请求方法 不使用代理、无参数
     *
     * @param url
     * @return
     */
    public static String httpGet(String url) {
        return httpGet(url, null, null, null, null);
    }

    public static String httpGet(String url, Map<String, String> headerMap) {
        return httpGet(url, null, headerMap, null, null);
    }

    /**
     * Http GET请求方法 不使用代理
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static String httpGet(String url, Map<String, Object> paramMap, Map<String, String> headerMap) {
        return httpGet(url, paramMap, headerMap, null, null);
    }

    /**
     * Http GET请求方法 使用代理 需要自己传入代理Ip地址和端口
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static String httpGet(String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) {
        return baseHttp(METHOD_NAME_GET, url, paramMap, headerMap, host, port);
    }

    /**
     * Http POST请求方法 不使用代理、无参数
     *
     * @param url
     * @return
     */
    public static String httpPost(String url) {
        return httpPost(url, null, null, null, null);
    }

    /**
     * Http POST请求方法 不使用代理、带Header
     *
     * @param url
     * @return
     */
    public static String httpPost(String url, Map<String, String> headerMap) {
        return httpPost(url, null, headerMap, null, null);
    }

    public static String httpPut(String url, Map<String, Object> paramMap, Map<String, String> headerMap) {
        return httpPut(url, paramMap, headerMap, null, null);
    }

    /**
     * Http POST请求方法 不使用代理
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static String httpPost(String url, Map<String, Object> paramMap, Map<String, String> headerMap) {
        return httpPost(url, paramMap, headerMap, null, null);
    }

    /**
     * Http POST请求方法 使用代理 需要自己传入代理Ip地址和端口
     *
     * @param url
     * @param paramMap
     * @return
     */
    public static String httpPost(String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) {
        return baseHttp(METHOD_NAME_POST, url, paramMap, headerMap, host, port);
    }

    public static String httpPut(String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) {
        return baseHttp(METHOD_NAME_PUT, url, paramMap, headerMap, host, port);
    }

    /**
     * 基础Http请求
     *
     * @param type
     * @param url
     * @param paramMap
     * @param host
     * @return
     */
    public static String baseHttp(String type, String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) {
        CloseableHttpClient httpClient = null;
        try {
            if (!METHOD_NAME_GET.equalsIgnoreCase(type) && !METHOD_NAME_POST.equalsIgnoreCase(type) && !METHOD_NAME_PUT.equalsIgnoreCase(type)) {
                throw new RuntimeException("不支持的请求类型");
            }
            String content = "";
            httpClient = HttpClients.custom().setSSLSocketFactory(createIgnoreVerifySSL()).build();
            HttpResponse httpResponse;
            if (METHOD_NAME_GET.equalsIgnoreCase(type)) {
                httpResponse = httpClient.execute(createHttpGet(url, paramMap, headerMap, host, port));
            } else if (METHOD_NAME_POST.equalsIgnoreCase(type)) {
                httpResponse = httpClient.execute(createHttpPost(url, paramMap, headerMap, host, port));
            } else {
                httpResponse = httpClient.execute(createHttpPut(url, paramMap, headerMap, host, port));
            }
            HttpEntity entity = httpResponse.getEntity();
            if (entity != null) {
                content = EntityUtils.toString(entity, "UTF-8");
            }
            return content;
        } catch (Exception e) {
            log.error("httpRequest请求方法发生异常，msg:{},cause:{},stack:{}", e.getMessage(), e.getCause(), e.getStackTrace());
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 创建HttpGet
     *
     * @param url
     * @param paramMap
     * @param host
     * @param port
     * @return
     */
    private static HttpGet createHttpGet(String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) {
        HttpGet httpGet = new HttpGet();
        if (paramMap != null) {
            List<NameValuePair> formParams = setHttpParams(paramMap);
            String param = URLEncodedUtils.format(formParams, "UTF-8");
            httpGet.setURI(URI.create(url + "?" + param));
        } else {
            httpGet.setURI(URI.create(url));
        }
        if (headerMap != null) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpGet.setHeader(entry.getKey(), entry.getValue());
            }
        }
        httpGet.setConfig(setProxy(host, port));
        // 设置User-Agent
        httpGet.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36");
        return httpGet;
    }

    /**
     * 创建HttpPost
     *
     * @param url
     * @param paramMap
     * @param host
     * @param port
     * @return
     */
    private static HttpPost createHttpPost(String url, Map<String, Object> paramMap, Map<String, String> headerMap, String host, Integer port) throws MalformedURLException {
        //继续执行业务
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(setProxy(host, port));
        if (paramMap != null) {
            HttpEntity entity = new StringEntity(JSONObject.toJSONString(paramMap), "UTF-8");
            httpPost.setEntity(entity);
        }
        if (headerMap != null) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 设置User-Agent
        httpPost.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36");

        return httpPost;
    }

    /**
     * 发起httpdelete请求
     *
     * @param url
     * @return
     */
    public static String httpDelete(String url, Map<String, Object> paramMap, HashMap<String, String> headerMap) {
        String result = "";
        HttpDeleteWithBody httpDelete = new HttpDeleteWithBody(url);
        for (Map.Entry entry : headerMap.entrySet()) {
            httpDelete.setHeader((String) entry.getKey(), (String) entry.getValue());
        }
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry entry : paramMap.entrySet()) {
            jsonObject.put((String) entry.getKey(), entry.getValue());
        }

        httpDelete.setEntity(new StringEntity(JSON.toJSONString(jsonObject), "UTF-8"));
        //创建内置httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        //发送请求，获得响应内容
        try {
            response = httpClient.execute(httpDelete);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                result = EntityUtils.toString(response.getEntity(), "utf-8");
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                System.out.println("释放资源、关闭http delete连接");
                if (httpClient != null) {
                    httpDelete.releaseConnection();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 创建HttpPut
     *
     * @param url
     * @param paramMap
     * @param headerMap
     * @param host
     * @param port
     * @return
     */
    private static HttpPut createHttpPut(String url, Object paramMap, Map<String, String> headerMap, String host, Integer port) throws MalformedURLException {

        //继续执行业务
        HttpPut httpPut = new HttpPut(url);
        httpPut.setConfig(setProxy(host, port));
        if (paramMap != null) {
            HttpEntity entity = new StringEntity(JSONObject.toJSONString(paramMap), "UTF-8");
            httpPut.setEntity(entity);
        }
        if (headerMap != null) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpPut.setHeader(entry.getKey(), entry.getValue());
            }
        }
        // 设置User-Agent
        httpPut.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36");

        return httpPut;

    }

}
