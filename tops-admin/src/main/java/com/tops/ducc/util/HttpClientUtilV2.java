package com.tops.ducc.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class HttpClientUtilV2 {

    /**
     * http put请求
     *
     * @param url
     * @param param
     * @param headerMap
     * @return
     */
    public static String put(String url, String param, Map<String, String> headerMap) throws IOException {

        if (StringUtils.isBlank(url) || StringUtils.isBlank(param)) {
            throw new RuntimeException("invalid url or param");
        }

        // 获得Http客户端
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 创建put请求
        HttpPut httpPut = new HttpPut(url);

        // 参数处理
        StringEntity paramEntity = new StringEntity(param, "UTF-8");
        httpPut.setEntity(paramEntity);

        // 请求头处理
        if (!Objects.isNull(headerMap) && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                httpPut.setHeader(entry.getKey(), entry.getValue());
            }
        }

        // 接收响应结果
        CloseableHttpResponse response = null;
        String result = null;
        try {
            response = httpClient.execute(httpPut);
            HttpEntity responseEntity = response.getEntity();
            result = EntityUtils.toString(responseEntity, "UTF-8");
        } catch (Exception e) {
            log.error("http put execute failed, url:{}, param:{}, header:{}, error msg:", url, param, JSONObject.toJSONString(headerMap), e);
            throw e;
        } finally {
            try {
                httpClient.close();
                response.close();
            } catch (Exception e) {
                log.error("http client/response close failed when execute put request, error:", e);
            }
        }

        return result;

    }

}
