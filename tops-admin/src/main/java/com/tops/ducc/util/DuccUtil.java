package com.tops.ducc.util;

import cn.hutool.setting.profile.Profile;
import cn.jdl.batrix.core.utils.ProfilerUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.ducc.bean.dto.DUCCContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * cf:https://cf.jd.com/display/JSF/8+open-api
 * ducc底层工具类，不推荐直接使用，其中部分更新接口存在删除操作，使用前需仔细查看文档确认是否会导致出乎意料的现象。
 */
@Component
@Slf4j
public class DuccUtil {

    public static final Integer SUCCESS_CODE = 200;

    public static final String GRAY_PREFIX = "IP_";


    public static final String UMP_KEY = "jdl-tops";

    public static final int RETRYTIMES = 1;

    public static final int FIFTYMILLIS = 5 * 10;


    /**
     * 随机数计算
     */
    private static Random random = new Random();


    /**
     * ducc批量数据更新  存在删除操作，使用前注意！！！！！
     *
     * @param duccContext ducc上下文
     * @param properties  全量配置：
     *                    <p>new ∩ old = inter 交集部分进行更新 </p>
     *                    <p>new - inter = newLeft new的差集部分进行新增 </p>
     *                    <p>old - inter = oldLeft old的差集部分进行删除 </p>
     */
    public static void batchNotifyDucc(DUCCContext duccContext, String properties, boolean createIfNotExist) throws IOException, InterruptedException {
        // 更新
        updateItemBatch(duccContext, properties, createIfNotExist);
        // 发布
        release(duccContext);
    }

    /**
     * ducc数据更新+发布
     *
     * @param key
     * @param value
     */
    public static void notifyDucc(DUCCContext duccContext, String key, String value, boolean createIfNotExist) throws IOException, InterruptedException {
        // 更新
        updateItem(duccContext, key, value, createIfNotExist);
        // 发布
        release(duccContext);
    }

    private static void retryNotifyDuccIfUnsuccessful(DUCCContext duccContext, String url, Map<String, Object> paramMap, String properties, HashMap<String, String> headerMap, boolean batch) throws IOException, InterruptedException {
        CallerInfo callerInfos = Profiler.registerInfo(UMP_KEY + ".retryNotifyDuccIfUnsuccessful");
        log.info("DuccUtil.retryNotifyDuccIfUnsuccessful duccContext:{},url:{},paramMap:{},properties:{},headerMap:{},batch:{}", JSON.toJSONString(duccContext), url, JSON.toJSONString(paramMap), properties, JSON.toJSONString(headerMap), batch);
        int retryTimesNow = 0;
        try {
            while (retryTimesNow < RETRYTIMES) {
                String thisRoundRes = "";
                if (!batch) {
                    thisRoundRes = HttpClientUtil.httpPut(url, paramMap, headerMap);
                } else {
                    thisRoundRes = HttpClientUtilV2.put(url, properties, headerMap);
                }
                JSONObject jsonObject = JSONObject.parseObject(thisRoundRes);
                Integer thisRoundCode = jsonObject.getInteger("code");
                if (SUCCESS_CODE.equals(thisRoundCode)) {
                    return;
                } else {
                    log.warn("DuccUtil.notifyDucc 本次调用的Response ：{}   " + thisRoundRes + "错误code: {}" + thisRoundCode);
                }
                retryTimesNow++;
                Thread.sleep(FIFTYMILLIS + random.nextInt(FIFTYMILLIS));
            }
            throw new IllegalArgumentException(String.format("推送ducc且重试%s次后失败", RETRYTIMES));
        } catch (Exception e) {
            Profiler.functionError(callerInfos);
            String alarmString = String.format("[%s] 环境,推送ducc且重试%s次后失败,推送失败的URL:[%s], 错误信息error msg:【%s】",
                    duccContext.getEnv(),RETRYTIMES, url, e.getMessage());
            log.error("DuccUtil.notifyDucc " + alarmString + "error msg:", e);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfos);
        }
    }

    /**
     * 请求配置项的数量
     *
     * @return
     */
    public static Integer queryItemsSize(DUCCContext duccContext, boolean createIfNotExist) {
        JSONObject jsonObject = queryItems(duccContext, createIfNotExist);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            log.info("DuccUtil.queryItemsSize response{}", jsonArray.size());
            return jsonArray.size();
        } else {
            throw new IllegalArgumentException("配置项请求有误，请稍后重试");
        }
    }

    /**
     * 请求配置项
     */
    public static JSONObject queryItems(DUCCContext duccContext, boolean createIfNotExist) {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("application", duccContext.getApplication());
        headerMap.put("token", duccContext.getToken());
        if (createIfNotExist) {
            createConfigIfNotExist(duccContext, headerMap);
            createProfileIfNotExist(duccContext, headerMap);
        }
        String queryItemsURL = getItemBatchURL(duccContext);
        String result = HttpClientUtil.httpGet(queryItemsURL, headerMap);
        return JSONObject.parseObject(result);
    }

    /**
     * 请求指定配置项的数量
     *
     * @return
     */
    public static Integer queryItemsSizeByItemKey(DUCCContext duccContext, String itemKey, boolean createIfNotExist) {
        JSONObject jsonObject = queryItems(duccContext, createIfNotExist);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            int number = 0;
            if (jsonArray.size() > 0) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObjectKey = jsonArray.getJSONObject(i);
                    if (matchStringByIndexOf(jsonObjectKey.getString("key"), itemKey) > 0) {
                        number++;
                    }
                }
            }
            return number;
        } else {
            throw new IllegalArgumentException("配置项请求有误，请稍后重试");
        }

    }


    /**
     * 判断是否存在指定的config
     *
     * @param duccContext ducc上下文
     */
    public static Boolean isExitConfig(DUCCContext duccContext) {
        log.info("DuccUtil.isExitConfig duccContext: {}", JSON.toJSONString(duccContext));
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("application", duccContext.getApplication());
        headerMap.put("token", duccContext.getToken());
        log.info("DuccUtil.isExitConfig header: {}", JSONObject.toJSONString(headerMap));
        String configBatchURL = getConfigBatchURL(duccContext);
        String result = HttpClientUtil.httpGet(configBatchURL, headerMap);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            return isExist(jsonArray, duccContext.getConfig());
        } else {
            throw new RuntimeException("请求duccConfig出错");
        }
    }


    /**
     * 是否存在记录
     *
     * @param jsonArray
     * @param value
     * @return
     */
    public static boolean isExist(JSONArray jsonArray, String value) {
        boolean result = false;
        for (int i = 0; i < jsonArray.size(); i++) {
            String existConfig = jsonArray.getJSONObject(i).getString("code");
            if (StringUtils.isNotEmpty(existConfig)) {
                if (existConfig.equals(value)) {
                    result = true;
                    break;
                }
            } else {
                String existItem = jsonArray.getJSONObject(i).getString("key");
                if (StringUtils.isNotEmpty(existItem)) {
                    if (existItem.equals(value)) {
                        result = true;
                        break;
                    }
                }
            }
        }
        return result;
    }

    /**
     * 通过String的indexOf(String str, int fromIndex)方法
     *
     * @param parent
     * @param child
     * @return
     */
    private static Integer matchStringByIndexOf(String parent, String child) {
        int count = 0;
        int index = 0;
        while ((index = parent.indexOf(child, index)) != -1) {
            index = index + child.length();
            count++;
        }
        return count;
    }

    /**
     * DUCC更新配置项
     * -- https://cf.jd.com/pages/viewpage.action?pageId=451018265
     *
     * @param duccContext ducc上下文
     */
    public static void updateItem(DUCCContext duccContext, String key, String value, boolean createIfNotExist) throws IOException, InterruptedException {
        log.info("DuccUtil.notifyDucc key: {}, value: {}", key, value);
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("application", duccContext.getApplication());
        headerMap.put("token", duccContext.getToken());
        log.info("DuccUtil.notifyDucc header: {}", JSONObject.toJSONString(headerMap));
        if (createIfNotExist) {
            createConfigIfNotExist(duccContext, headerMap);
            createProfileIfNotExist(duccContext, headerMap);
            createItemIfNotExist(duccContext, headerMap, key);
        }
        // 修改配置项value
        String updateURL = getItemURL(duccContext, key);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("value", value);
        log.info("DuccUtil.notifyDucc updateUrl: {}", updateURL);
        retryNotifyDuccIfUnsuccessful(duccContext, updateURL, paramMap, null, headerMap, false);
    }

    /**
     * DUCC批量更新配置项
     * -- https://cf.jd.com/pages/viewpage.action?pageId=451018265
     *
     * @param duccContext ducc上下文
     */
    public static void updateItemBatch(DUCCContext duccContext, String properties, boolean createIfNotExist) throws IOException, InterruptedException {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("application", duccContext.getApplication());
        headerMap.put("token", duccContext.getToken());
        headerMap.put("Accept", "application/json, text/plain, */*");
        headerMap.put("Content-Type", "application/properties");
        if (createIfNotExist) {
            //批量更新配置前检查config是否存在，不存在则创建
            createConfigIfNotExist(duccContext, headerMap);
            //批量更新配置前检查profile是否存在，不存在则创建
            createProfileIfNotExist(duccContext, headerMap);
        }
        //批量更新item
        String updateURL = getItemBatchURL(duccContext);
        retryNotifyDuccIfUnsuccessful(duccContext, updateURL, null, properties, headerMap, true);
    }

    /**
     * DUCC发布
     * -- https://cf.jd.com/pages/viewpage.action?pageId=451018076
     *
     * @param duccContext ducc上下文
     */
    public static void release(DUCCContext duccContext) throws IOException, InterruptedException {
        // 请求url
        String releaseURL = getReleaseURL(duccContext);
        // 请求头
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("application", duccContext.getApplication());
        headerMap.put("token", duccContext.getToken());
        // 请求参数
        Map<String, Object> releaseParamMap = new HashMap<>();
        retryNotifyDuccIfUnsuccessful(duccContext, releaseURL, releaseParamMap, null, headerMap, false);
    }

    /**
     * 配置URL——DUCC接口使用REST 风格设计
     * delete:删除配置
     */
    public static String getConfigURL(DUCCContext duccContext, String key) {
        StringBuffer configURL = new StringBuffer();
        configURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(key);
        return configURL.toString();
    }

    /**
     * 配置URL——DUCC接口使用REST 风格设计
     * post:新增配置
     */
    public static String getConfigURLWithOutKey(DUCCContext duccContext) {
        StringBuffer configURL = new StringBuffer();
        configURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config");
        return configURL.toString();
    }

    /**
     * 配置批量URL——DUCC接口使用REST 风格设计
     * get:查询命名空间下的配置
     */
    public static String getConfigBatchURL(DUCCContext duccContext) {
        StringBuffer configBatchURL = new StringBuffer();
        configBatchURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/configs");
        return configBatchURL.toString();
    }

    /**
     * 环境URL——DUCC接口使用REST 风格设计
     * delete: 删除环境
     */
    public static String getProfileURL(DUCCContext duccContext, String key) {
        StringBuffer profileURL = new StringBuffer();
        profileURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile/")
                .append(key);
        return profileURL.toString();
    }

    /**
     * 环境URL——DUCC接口使用REST 风格设计
     * post:新增环境
     */
    public static String getProfileURLWithOutKey(DUCCContext duccContext) {
        StringBuffer profileURL = new StringBuffer();
        profileURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile");
        return profileURL.toString();
    }

    /**
     * 环境批量URL——DUCC接口使用REST 风格设计
     * get:查询配置下的所有环境
     */
    public static String getProfileBatchURL(DUCCContext duccContext) {
        StringBuffer profileBatchURL = new StringBuffer();
        profileBatchURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profiles");
        return profileBatchURL.toString();
    }

    /**
     * 配置项URL——DUCC接口使用REST 风格设计
     * get:查询配置项详情
     * delete:删除配置项
     */
    public static String getItemURL(DUCCContext duccContext, String key) {
        StringBuffer itemURL = new StringBuffer();
        itemURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile/")
                .append(duccContext.getProfile())
                .append("/item/")
                .append(key);
        return itemURL.toString();
    }

    /**
     * 配置项URL——DUCC接口使用REST 风格设计
     * post:新增配置项
     * put:修改配置项
     */
    public static String getItemURLWithOutKey(DUCCContext duccContext) {
        StringBuffer itemURL = new StringBuffer();
        itemURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile/")
                .append(duccContext.getProfile())
                .append("/item");
        return itemURL.toString();
    }

    /**
     * 配置项批量URL——DUCC接口使用REST 风格设计
     * get:查询工作区配置项
     * post:批量新增配置项
     * put:批量修改配置项(这里是批量覆盖)！！！！批量覆盖！！！！批量覆盖！！！！
     */
    public static String getItemBatchURL(DUCCContext duccContext) {
        StringBuffer itemURL = new StringBuffer();
        itemURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile/")
                .append(duccContext.getProfile())
                .append("/items");
        return itemURL.toString();
    }

    /**
     * 发布URL——DUCC接口使用REST 风格设计
     * put:发布配置
     */
    public static String getReleaseURL(DUCCContext duccContext) {
        StringBuffer releaseURL = new StringBuffer();
        releaseURL.append(duccContext.getDuccUrl())
                .append("/namespace/")
                .append(duccContext.getNamespace())
                .append("/config/")
                .append(duccContext.getConfig())
                .append("/profile/")
                .append(duccContext.getProfile())
                .append("/release");
        return releaseURL.toString();
    }


    /**
     * 动态创建配置
     */
    public static void createConfigIfNotExist(DUCCContext duccContext, HashMap<String, String> headerMap) {
        //查询命名空间下的配置
        log.info("DuccUtil.createConfigIfNotExist header:{},duccContext:{}，", JSONObject.toJSONString(headerMap), JSON.toJSONString(duccContext));
        String configBatchURL = getConfigBatchURL(duccContext);
        log.info("DuccUtil.createConfigIfNotExist configBatchURL:{}", configBatchURL);
        String result = HttpClientUtil.httpGet(configBatchURL, headerMap);
        log.info("DuccUtil.createConfigIfNotExist result:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            if (!isExist(jsonArray, duccContext.getConfig())) {
                //新增配置
                String configURL = getConfigURLWithOutKey(duccContext);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("code", duccContext.getConfig());
                paramMap.put("name", "name");
                paramMap.put("description", "description");
                String addResult = HttpClientUtil.httpPost(configURL, paramMap, headerMap);
                log.info("DuccUtil.createConfigIfNotExist, post request, url:{}, param:{}, header:{}, response:{}",
                        configURL, JSONObject.toJSONString(paramMap), JSONObject.toJSONString(headerMap), JSONObject.toJSONString(addResult));
            }
        }
    }

    /***
     * 动态创建环境
     * @param headerMap
     * @param duccContext ducc上下文
     */
    public static void createProfileIfNotExist(DUCCContext duccContext, HashMap<String, String> headerMap) {
        //查询配置下的环境
        log.info("DuccUtil.createProfileIfNotExist header{},duccContext: {}", JSONObject.toJSONString(headerMap), JSON.toJSONString(duccContext));
        String profileBatchURL = getProfileBatchURL(duccContext);
        String result = HttpClientUtil.httpGet(profileBatchURL, headerMap);
        log.info("DuccUtil.createProfileIfNotExist result:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            if (!isExist(jsonArray, duccContext.getProfile())) {
                //新增环境
                String profileURL = getProfileURLWithOutKey(duccContext);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("code", duccContext.getProfile());
                paramMap.put("name", "name");
                paramMap.put("description", "description");
                String addResult = HttpClientUtil.httpPost(profileURL, paramMap, headerMap);

                if (log.isInfoEnabled()) {
                    log.info("DuccUtil.createProfile, post request, url:{}, param:{}, header:{}, response:{}",
                            profileURL, JSONObject.toJSONString(paramMap), JSONObject.toJSONString(headerMap), addResult);
                }
            }
        }
    }

    /**
     * 动态创建配置项
     *
     * @param headerMap   请求头
     * @param duccContext ducc上下文
     * @param itemStr     配置项key
     */
    public static void createItemIfNotExist(DUCCContext duccContext, HashMap<String, String> headerMap, String itemStr) {
        log.info("DuccUtil.createItemIfNotExist header{},duccContext: {}，itemStr{}", JSONObject.toJSONString(headerMap), JSON.toJSONString(duccContext), itemStr);
        String itemBatchURL = getItemBatchURL(duccContext);
        String result = HttpClientUtil.httpGet(itemBatchURL, headerMap);
        log.info("DuccUtil.createItemIfNotExist result:{}", result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if (SUCCESS_CODE.equals(Integer.valueOf(jsonObject.getString("code")))) {
            JSONArray jsonArray = (JSONArray) jsonObject.get("data");
            if (!isExist(jsonArray, itemStr)) {
                //新增配置项
                String itemURLWithOutKey = getItemURLWithOutKey(duccContext);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("key", itemStr);
                paramMap.put("value", "{}");
                paramMap.put("description", "description");
                String addResult = HttpClientUtil.httpPost(itemURLWithOutKey, paramMap, headerMap);
                if (log.isInfoEnabled()) {
                    log.info("DuccUtil.createItem, post request, url:{}, param:{}, header:{}",
                            itemURLWithOutKey, JSONObject.toJSONString(paramMap), JSONObject.toJSONString(headerMap), addResult);
                }
                log.info("DuccUtil.createItem result " + addResult);
            }
        }
    }
}
