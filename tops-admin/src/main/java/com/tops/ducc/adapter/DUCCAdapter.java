package com.tops.ducc.adapter;

import com.alibaba.fastjson.JSON;
import com.tops.ducc.bean.dto.DUCCContext;
import com.tops.ducc.bean.dto.DUCCItemMetaInfo;
import com.tops.ducc.translator.DuccTranslator;
import com.tops.ducc.util.DuccUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * cf: https://cf.jd.com/display/JSF/8+open-api
 * ducc上层工具类
 */
@Slf4j
@Component
public class DUCCAdapter {
    public static final String RESPONSE_CODE_SUCCESS="success";
    public static final String RESPONSE_CODE_PARAMS_INVALID="paramsInvalid";
    public static final String RESPONSE_CODE_UPDATE_FAIL="updateFail";
    public static final String RESPONSE_CODE_RELEASE_FAIL="releaseFail";

    @Autowired
    private DuccTranslator duccTranslator;

    /**
     * 更新DUCC Key的值
     * @param metaInfo ducc key坐标
     *                 <P></P>
     * @param value 目标值
     */
    public String updateDucc(DUCCItemMetaInfo metaInfo, String value) {
        //metaInfo==null的不处理
        if (metaInfo == null) {
            log.warn("DUCCAdapter.refreshLocalCache  metaInfo==null不触发刷新本地缓存操作");
            return RESPONSE_CODE_PARAMS_INVALID;
        }
        //key为空的不处理
        if (StringUtils.isBlank(metaInfo.getKey())) {
            log.warn("DUCCAdapter.refreshLocalCache  配置项key为空不触发刷新本地缓存操作");
            return RESPONSE_CODE_PARAMS_INVALID;
        }
        //value为空的不处理
        if (value == null) {
            log.warn("DUCCAdapter.refreshLocalCache  配置项变更value==null不触发刷新本地缓存操作");
            return RESPONSE_CODE_PARAMS_INVALID;
        }
        DUCCContext duccContext = duccTranslator.getDUCCContext(metaInfo);
        log.info("DUCCAdapter.refreshLocalCache  刷新本地缓存duccContext:{}", JSON.toJSONString(duccContext));
        if (duccContext == null) {
            log.warn("DUCCAdapter.refreshLocalCache  duccContext==null不触发刷新本地缓存操作", JSON.toJSONString(duccContext));
            return RESPONSE_CODE_PARAMS_INVALID;
        }
        try{
            DuccUtil.updateItem(duccContext, metaInfo.getKey(), value, false);
        }catch (Exception e){
            log.error("DUCCAdapter.refreshLocalCache  更新DUCC失败");
            return RESPONSE_CODE_UPDATE_FAIL;
        }
        try{
            DuccUtil.release(duccContext);
        }catch (Exception e){
            log.error("DUCCAdapter.refreshLocalCache  发布DUCC失败");
            return RESPONSE_CODE_RELEASE_FAIL;
        }

        return RESPONSE_CODE_SUCCESS;
    }

    public String getEnv() {
        return duccTranslator.getEnv();
    }
}
