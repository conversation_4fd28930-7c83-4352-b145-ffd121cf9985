package com.tops.ducc.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.laf.config.Configuration;
import com.jd.laf.config.spring.annotation.LafValue;
import com.tops.common.utils.StringUtils;
import com.tops.pfinder.bean.dto.Threshold;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Data
@Slf4j
@Component
public class DuccMetaConfig {
    /**
     * key:appName
     * value:duccToken
     */
    private Map<String, String> token;

    @LafValue(name = "ducc_meta")
    public void onConfigureChange(Configuration configuration) {
        log.info("DuccMetaConfig.onConfigureChange 开始刷新token，configuration:{}", JSON.toJSONString(configuration));
        if (configuration.getProperty("tokens") == null) {
            log.info("DuccMetaConfig.onConfigureChange 停止刷新token，tokens为null");
            return;
        }
        String json = configuration.getProperty("tokens").getString();
        if (StringUtils.isBlank(json)) {
            log.info("DuccMetaConfig.onConfigureChange 停止刷新token，tokens为空");
            return;
        }
        try {
            log.info("DuccMetaConfig.onConfigureChange 开始刷新token，配置为:{}", json);
            Map<String, String> token = JSON.parseObject(json, new TypeReference<Map<String, String>>() {
            });
            this.token = token;
        } catch (Exception e) {
            log.info("DuccMetaConfig.onConfigureChange token配置格式错误，请检查！");
        }
    }
}
