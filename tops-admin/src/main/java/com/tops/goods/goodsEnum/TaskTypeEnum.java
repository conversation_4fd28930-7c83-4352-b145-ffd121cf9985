package com.tops.goods.goodsEnum;

import java.util.Arrays;

/**
 * @ClassName: TaskTypeEnum
 * @Description: 任务类型枚举
 * @Auther: MaWenHua8
 * @Date: 2024/11/11
 * @version: V1.0
 */
public enum TaskTypeEnum {
    DATA_DUPLICATE(1, "data_duplicate", "数据重复监控任务"),
    DATA_SCALE(2, "data_scale", "数据量级监控任务");


    TaskTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    TaskTypeEnum() {
    }


    public static boolean isValidTaskType(Integer code) {
        if (code == null) {
            return false;
        }
        return Arrays.stream(TaskTypeEnum.values())
            .anyMatch(e -> e.getCode().equals(code));
    }

    private Integer code;
    private String type;
    private String desc;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
