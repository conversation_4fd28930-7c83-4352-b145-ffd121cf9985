package com.tops.goods.goodsEnum;

import java.util.Arrays;

/**
 * @ClassName: TaskTypeEnum
 * @Description: 配置类型枚举
 * @Auther: MaWenHua8
 * @Date: 2024/11/11
 * @version: V1.0
 */
public enum ConfigTypeEnum {
    BIG_DATA(1, "big_data", "大数据数据源"),
    MYSQL(2, "mysql", "MySQL数据源");


    ConfigTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    ConfigTypeEnum() {
    }


    public static boolean isValidTaskType(Integer code) {
        if (code == null) {
            return false;
        }
        return Arrays.stream(ConfigTypeEnum.values())
            .anyMatch(e -> e.getCode().equals(code));
    }

    private Integer code;
    private String type;
    private String desc;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
