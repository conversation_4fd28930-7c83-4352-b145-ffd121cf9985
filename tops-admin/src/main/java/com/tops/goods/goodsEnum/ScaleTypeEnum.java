package com.tops.goods.goodsEnum;

import java.util.Arrays;

/**
 * @ClassName: TaskTypeEnum
 * @Description: 量级类型枚举
 * @Auther: MaWenHua8
 * @Date: 2024/11/11
 * @version: V1.0
 */
public enum ScaleTypeEnum {
    UPPER_LIMIT(1, "upper_limit", "固定值"),
    DIFF_RATIO(2, "diff_ratio", "差异比率"),
    UPPER_LIMIT_AND_DIFF_RATIO(3, "upper_limit_and_diff_ratio", "固定值加差异比率");


    ScaleTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    ScaleTypeEnum() {
    }


    public static boolean isValidScaleType(Integer code) {
        if (code == null) {
            return false;
        }
        return Arrays.stream(ScaleTypeEnum.values())
            .anyMatch(e -> e.getCode().equals(code));
    }

    private Integer code;
    private String type;
    private String desc;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
