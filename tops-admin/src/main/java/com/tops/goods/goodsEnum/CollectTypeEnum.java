package com.tops.goods.goodsEnum;

import java.util.Arrays;

/**
 * @ClassName: TaskTypeEnum
 * @Description: 数据采集类型枚举
 * @Auther: MaWenHua8
 * @Date: 2024/11/11
 * @version: V1.0
 */
public enum CollectTypeEnum {
    HOUR(0, "hour", "小时级别"),
    DAY(1, "day", "天级别");


    CollectTypeEnum(Integer code, String type, String desc) {
        this.code = code;
        this.type = type;
        this.desc = desc;
    }

    CollectTypeEnum() {
    }


    public static boolean isValidCollectType(Integer code) {
        if (code == null) {
            return false;
        }
        return Arrays.stream(CollectTypeEnum.values())
            .anyMatch(e -> e.getCode().equals(code));
    }

    private Integer code;
    private String type;
    private String desc;


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
