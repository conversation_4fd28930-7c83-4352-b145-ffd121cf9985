package com.tops.goods.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: DataCollectDomain
 * @Description: 采集结果查询返回
 * @Auther: MaWenHua8
 * @Date: 2024/11/21
 * @version: V1.0
 */
@Data
public class DataCollectDomain implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 值
     */
    private Integer resultValue;

    /**
     * 采集维度 0:小时，1：天
     */
    private Integer collectDimension;

    /**
     * 采集时间
     */
    private Date collectTime;
}
