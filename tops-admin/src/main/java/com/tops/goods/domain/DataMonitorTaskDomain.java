package com.tops.goods.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: DataMonitorTaskDomain
 * @Description: 数据表任务实体
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataMonitorTaskDomain implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 数据源ID
     */
    private Integer dataSourceId;

    /**
     * 任务编码
     */
    private String taskNo;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型 com.tops.goods.goodsEnum.TaskTypeEnum
     */
    private Integer taskType;

    /**
     * SQL 的key
     */
    private String sqlKey;

    /**
     * 执行频率 cron表达式
     */
    private String execFrequency;

    /**
     * 自定义告警KEY
     */
    private String businessAlarmKey;

    /**
     * 量级类型 com.tops.goods.goodsEnum.ScaleTypeEnum
     */
    private Integer scaleType;

    /**
     * 固定值上限
     */
    private Integer upperLimit;

    /**
     * 差异比率
     */
    private BigDecimal diffRatio;

    /**
     * 任务授权
     */
    private String sharedOperateUser;

    /**
     * 最近一次执行时间
     */
    private Date lastExecutionTime;
    /**
     * 1:启用 2:禁用
     */
    private Integer enableFlag;
    private String createUser;
    private String updateUser;
    private Date createTime;
    private Date updateTime;
    /**
     * 1:有效  0:删除
     */
    private Integer yn;
}
