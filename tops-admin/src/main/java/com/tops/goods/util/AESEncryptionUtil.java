package com.tops.goods.util;

import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Base64;

/**
 * @ClassName: AESEncryptionUtil
 * @Description: AES加密解密方法
 * @Auther: MaWenHua8
 * @Date: 2024/11/11
 * @version: V1.0
 */
@Component
public class AESEncryptionUtil {

    /**
     * 根据登录信息生成加密密钥
     *
     * @param userIdentifier
     * @return
     * @throws NoSuchAlgorithmException
     */
    private static SecretKey generateKey(String userIdentifier) throws NoSuchAlgorithmException {
        // Use SHA-256 to hash the user identifier to get a consistent 256-bit key
        MessageDigest sha = MessageDigest.getInstance("SHA-256");
        byte[] key = sha.digest(userIdentifier.getBytes(StandardCharsets.UTF_8));
        key = Arrays.copyOf(key, 16);
        return new SecretKeySpec(key, "AES");
    }


    /**
     * 加密方法
     *
     * @param data
     * @return
     * @throws Exception
     */
    public static String encrypt(String userIdentifier, String data) throws Exception {
        // Calculate maximum input length
        int maxInputLength = (500 * 3 / 4) - 16;
        if (data.getBytes(StandardCharsets.UTF_8).length > maxInputLength) {
            throw new IllegalArgumentException("输入数据过长，无法保证加密后长度不超过200个字符");
        }
        SecretKey key = generateKey(userIdentifier);
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    /**
     * 解密方法
     *
     * @param encryptedData
     * @return
     * @throws Exception
     */
    public static String decrypt(String userIdentifier, String encryptedData) throws Exception {
        SecretKey key = generateKey(userIdentifier);
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] decryptedData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedData, StandardCharsets.UTF_8);
    }
}
