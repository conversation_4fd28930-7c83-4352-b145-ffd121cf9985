package com.tops.goods.controller;

import com.tops.goods.domain.DataCollectDomain;
import com.tops.goods.dto.DataCollectRequestVo;
import com.tops.goods.dto.DataCollectResponseVo;
import com.tops.goods.dto.DataCollectVo;
import com.tops.goods.service.DataCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataCollectController
 * @Description: 数据报表查询
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Controller
@RequestMapping("/dataCollect")
@Slf4j
public class DataCollectController {

    @Resource
    private DataCollectService dataCollectService;

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    @ResponseBody
    @RequestMapping(value = "/queryCollectData", method = RequestMethod.POST, produces = "application/json")
    public DataCollectResponseVo queryCollectData(@RequestBody DataCollectRequestVo requestVo) {
        try {
            if (requestVo == null || requestVo.getTaskId() == null || requestVo.getQueryType() == null) {
                return DataCollectResponseVo.paramError("入参不能为空");
            }
            List<DataCollectDomain> collectDomainList = new ArrayList<DataCollectDomain>();
            switch (requestVo.getQueryType()) {
                case 1://小时级别查近5h
                    log.info("小时级查询近5H");
                    collectDomainList = dataCollectService.getLastFiveHours(requestVo.getTaskId());
                    break;
                case 2://小时级别查最近10h
                    log.info("小时级查询近10H");
                    collectDomainList = dataCollectService.getLastTenHours(requestVo.getTaskId());
                    break;
                case 3://小时级别查最近24h
                    log.info("小时级查询近24H");
                    collectDomainList = dataCollectService.getLast24Hours(requestVo.getTaskId());
                    break;
                case 4://小时级别查最近24
                    log.info("小时级查询今天");
                    collectDomainList = dataCollectService.getTodayHours(requestVo.getTaskId());
                    break;
                case 5://小时级别查最近3天
                    log.info("小时级查询近3天");
                    collectDomainList = dataCollectService.getLastThreeDayHours(requestVo.getTaskId());
                    break;
                case 6://天级别查最近5天
                    log.info("天级查询近5天");
                    collectDomainList = dataCollectService.getLastFiveDays(requestVo.getTaskId());
                    break;
                case 7://天级别查最近7天
                    log.info("天级查询近7天");
                    collectDomainList = dataCollectService.getLastSevenDays(requestVo.getTaskId());
                    break;
                case 8://天级别查最近15天
                    log.info("天级查询近15天");
                    collectDomainList = dataCollectService.getLast15Days(requestVo.getTaskId());
                    break;
                default:
            }
            List<DataCollectVo> collectVoList = convert(collectDomainList);

            log.info("查询结果size:{}", collectVoList.size());
            return DataCollectResponseVo.success(collectVoList);
        } catch (Exception e) {
            log.error("查询采集数据异常", e);
            return DataCollectResponseVo.systemError(e.getMessage());
        }
    }


    private List<DataCollectVo> convert(List<DataCollectDomain> collectDomainList) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        List<DataCollectVo> voList = new ArrayList<DataCollectVo>();
        if (CollectionUtils.isEmpty(collectDomainList)) {
            return voList;
        }

        collectDomainList.forEach(d -> {
            DataCollectVo v = new DataCollectVo();
            v.setId(d.getId());
            v.setTaskId(d.getTaskId());
            v.setResultValue(d.getResultValue());
            v.setCollectDimension(d.getCollectDimension());
            v.setCollectTime(d.getCollectTime() != null ? formatter.format(d.getCollectTime()) : null);
            voList.add(v);
        });
        return voList;
    }
}
