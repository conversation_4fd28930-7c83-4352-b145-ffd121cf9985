package com.tops.goods.controller;

import com.alibaba.fastjson.JSON;
import com.tops.common.constant.CacheNames;
import com.tops.common.core.domain.R;
import com.tops.common.utils.redis.CacheUtils;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.dto.BaseResponseVo;
import com.tops.goods.dto.DataMonitorTaskRequestVo;
import com.tops.goods.dto.DataMonitorTaskResponseVo;
import com.tops.goods.dto.DataMonitorTaskVo;
import com.tops.goods.goodsEnum.ScaleTypeEnum;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.service.DataMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @ClassName: DataMonitorTaskController
 * @Description: 数据监控
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Controller
@RequestMapping("/dataMonitorTask")
@Slf4j
public class DataMonitorTaskController {

    @Resource
    private DataMonitorService dataMonitorService;

    // 正则表达式用于验证单个电子邮件地址，要求域名为 @jd.com
    private static final String EMAIL_REGEX = "^[\\w-\\.]+@jd\\.com$";

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";


    @ResponseBody
    @RequestMapping(value = "/saveTask", method = RequestMethod.POST, produces = "application/json")
    public BaseResponseVo saveTask(@RequestBody DataMonitorTaskRequestVo dataMonitorTaskRequestVo) {
        try {
            log.info("新增任务入参:{}", JSON.toJSONString(dataMonitorTaskRequestVo));
            if (ObjectUtils.isEmpty(dataMonitorTaskRequestVo)) {
                return BaseResponseVo.paramError("入参为空");
            }
            if (dataMonitorTaskRequestVo.getTaskType() == null
                || !TaskTypeEnum.isValidTaskType(dataMonitorTaskRequestVo.getTaskType())) {
                return BaseResponseVo.paramError("任务类型为空或者非法");
            }

            if (!validCommonParam(dataMonitorTaskRequestVo)) {
                log.info("通用参数校验未通过");
                return BaseResponseVo.paramError("通用参数校验未通过");
            }

            if (!validSpecParam(dataMonitorTaskRequestVo)) {
                log.info("特殊参数校验未通过");
                return BaseResponseVo.paramError("特殊参数校验未通过");
            }
            dataMonitorService.insertMonitorTask(dataMonitorTaskRequestVo);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("新增任务异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/updateTask", method = RequestMethod.POST, produces = "application/json")
    public BaseResponseVo updateTask(@RequestBody DataMonitorTaskRequestVo dataMonitorTaskRequestVo) {
        try {
            log.info("更新任务入参:{}", JSON.toJSONString(dataMonitorTaskRequestVo));
            if (ObjectUtils.isEmpty(dataMonitorTaskRequestVo)) {
                return BaseResponseVo.paramError("入参为空");
            }
            if (dataMonitorTaskRequestVo.getTaskType() == null
                || !TaskTypeEnum.isValidTaskType(dataMonitorTaskRequestVo.getTaskType())) {
                return BaseResponseVo.paramError("任务类型为空或者非法");
            }

            if (!validCommonParam(dataMonitorTaskRequestVo)) {
                log.info("通用参数校验未通过");
                return BaseResponseVo.paramError("通用参数校验未通过");
            }

            if (!validSpecParam(dataMonitorTaskRequestVo)) {
                log.info("特殊参数校验未通过");
                return BaseResponseVo.paramError("特殊参数校验未通过");
            }

            dataMonitorService.updateMonitorTask(dataMonitorTaskRequestVo);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("更新任务异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/updateEnableFlag", method = RequestMethod.POST)
    public BaseResponseVo updateEnableFlag(@RequestParam Integer id) {
        try {
            log.info("启用禁用任务入参:{}", id);
            if (id == null) {
                return BaseResponseVo.paramError("入参异常");
            }
            dataMonitorService.updateEnableFlag(id);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("启用禁用任务异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/delTaskById", method = RequestMethod.POST)
    public BaseResponseVo delTaskById(@RequestParam Integer id) {
        try {
            log.info("删除任务入参:{}", id);
            if (id == null) {
                return BaseResponseVo.paramError("入参异常");
            }
            dataMonitorService.delTaskById(id);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("启用禁用任务异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/queryTaskByPage", method = RequestMethod.POST)
    public DataMonitorTaskResponseVo queryTaskByPage(
        @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
        @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        try {
            List<DataMonitorTaskDomain> domainList = dataMonitorService.queryTaskByPage(pageNum, pageSize);
            List<DataMonitorTaskVo> voList = convert(domainList);
            log.info("查询结果:{}", JSON.toJSONString(domainList));
            int pageCount = CollectionUtils.isEmpty(domainList) ? 0 : (domainList.size() + pageSize - 1) / pageSize;
            return DataMonitorTaskResponseVo.success(voList, domainList.size(), pageCount);
        } catch (Exception e) {
            log.error("分页查询任务异常", e);
            return DataMonitorTaskResponseVo.systemError(e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/queryById", method = RequestMethod.POST)
    public R<DataMonitorTaskVo> queryById(@RequestParam Integer id) {
        try {
            DataMonitorTaskDomain domain = dataMonitorService.queryById(id);
            List<DataMonitorTaskVo> voList = convert(Arrays.asList(domain));
            log.info("查询结果:{}", JSON.toJSONString(voList));
            return R.ok(voList != null ? voList.get(0) : null);
        } catch (Exception e) {
            log.error("查询任务异常", e);
            return R.fail(e.getMessage());
        }
    }


    private List<DataMonitorTaskVo> convert(List<DataMonitorTaskDomain> domainList) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        List<DataMonitorTaskVo> voList = new ArrayList<DataMonitorTaskVo>();
        if (CollectionUtils.isEmpty(domainList)) {
            return voList;
        }

        domainList.forEach(d -> {
            DataMonitorTaskVo v = new DataMonitorTaskVo();
            v.setId(d.getId());
            v.setDataSourceId(d.getDataSourceId());
            v.setTaskNo(d.getTaskNo());
            v.setTaskName(d.getTaskName());
            v.setTaskType(d.getTaskType());
            v.setSqlKey(d.getSqlKey());
            String sqlContent = null;
            try {
                if (StringUtils.isNotBlank(d.getSqlKey())) {
                    sqlContent = CacheUtils.get(CacheNames.SYS_SQL, d.getSqlKey());
                    log.info("从Redis读取SQL结果:{}", JSON.toJSONString(sqlContent));
                }
            } catch (Exception e) {
                log.info("从Redis获取SQL异常", e);
            }
            v.setSqlContent(sqlContent);
            v.setExecFrequency(d.getExecFrequency());
            v.setBusinessAlarmKey(d.getBusinessAlarmKey());
            v.setScaleType(d.getScaleType());
            v.setUpperLimit(d.getUpperLimit());
            v.setDiffRatio(d.getDiffRatio());
            v.setLastExecutionTime(d.getLastExecutionTime() != null ? formatter.format(d.getLastExecutionTime()) : null);
            v.setEnableFlag(d.getEnableFlag());
            v.setCreateTime(d.getCreateTime() != null ? formatter.format(d.getCreateTime()) : null);
            v.setCreateUser(d.getCreateUser());
            v.setSharedOperateUser(d.getSharedOperateUser());
            v.setUpdateUser(d.getUpdateUser());
            v.setUpdateTime(d.getUpdateTime() != null ? formatter.format(d.getUpdateTime()) : null);
            v.setYn(d.getYn());
            voList.add(v);
        });
        return voList;
    }


    /**
     * CRON合法性校验
     *
     * @param cronExpression
     * @return
     */
    private boolean isValidCronExpression(String cronExpression) {
        try {
            // 尝试创建一个 CronExpression 对象
            new CronExpression(cronExpression);
            return true; // 如果没有抛出异常，则表达式是合法的
        } catch (Exception e) {
            log.info("CRON表达式异常");
            return false; // 如果抛出异常，则表达式不合法
        }
    }


    /**
     * 协同操作人校验
     *
     * @param sharedOperatorUser
     * @return
     */
    private boolean isValidSharedOperatorUser(String sharedOperatorUser) {
        if (StringUtils.isBlank(sharedOperatorUser)) {
            return true;
        }
        List<String> list = Arrays.asList(sharedOperatorUser.split(","));
        if (list.size() > 10) {
            log.info("协同操作人不得超过10个");
            return false;
        }
        return true;
    }


    /**
     * 邮箱合法性校验
     *
     * @param emailList
     * @return
     */
    private boolean areValidEmails(String emailList) {
        // 使用逗号分隔电子邮件地址，并去除前后的空格
        String[] emails = emailList.split(";");
        Pattern pattern = Pattern.compile(EMAIL_REGEX);

        for (String email : emails) {
            if (!pattern.matcher(email).matches()) {
                log.info("邮箱不合法");
                return false; // 如果任何一个电子邮件地址不合法，则返回false
            }
        }
        return true; // 所有电子邮件地址都合法
    }


    /**
     * 校验通用参数
     *
     * @param dataMonitorTaskRequestVo
     * @return
     */
    private boolean validCommonParam(DataMonitorTaskRequestVo dataMonitorTaskRequestVo) {
        if (StringUtils.isBlank(dataMonitorTaskRequestVo.getTaskName())
            || StringUtils.isBlank(dataMonitorTaskRequestVo.getSqlContent())
            || (!dataMonitorTaskRequestVo.getSqlContent().startsWith("select") && !dataMonitorTaskRequestVo.getSqlContent().startsWith("SELECT"))
            || StringUtils.isBlank(dataMonitorTaskRequestVo.getExecFrequency())
            || !isValidCronExpression(dataMonitorTaskRequestVo.getExecFrequency())
            || !isValidSharedOperatorUser(dataMonitorTaskRequestVo.getSharedOperateUser())) {
            return false;
        }
        return true;
    }


    /**
     * 特殊参数校验
     *
     * @param requestVo
     * @return
     */
    private boolean validSpecParam(DataMonitorTaskRequestVo requestVo) {
        Integer type = requestVo.getTaskType();
        if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(type)) {
            if (requestVo.getDataSourceId() == null) {
                log.info("数据重复任务参数校验未通过");
                return false;
            }
        }

        if (TaskTypeEnum.DATA_SCALE.getCode().equals(type)) {
            if (StringUtils.isBlank(requestVo.getBusinessAlarmKey())
                || requestVo.getScaleType() == null
                || !ScaleTypeEnum.isValidScaleType(requestVo.getScaleType())) {
                log.info("数据量级自定告警或者数据量级为空");
                return false;
            }

            boolean result = true;
            Integer scaleType = requestVo.getScaleType();
            if (ScaleTypeEnum.UPPER_LIMIT.getCode().equals(scaleType)) {
                result = requestVo.getUpperLimit() != null ? true : false;
            } else if (ScaleTypeEnum.DIFF_RATIO.getCode().equals(scaleType)) {
                result = isDiffRatioValid(requestVo.getDiffRatio());
            } else if (ScaleTypeEnum.UPPER_LIMIT_AND_DIFF_RATIO.getCode().equals(scaleType)) {
                result = isDiffRatioValid(requestVo.getDiffRatio()) && requestVo.getUpperLimit() != null;
            } else {
                result = false;
            }
            return result;
        }
        return true;
    }


    /**
     * 校验MySQL是否可连接
     *
     * @param ip
     * @param database
     * @param user
     * @param password
     * @return
     */
    private boolean checkConnection(String ip, String database, String user, String password) {
        String url = "jdbc:mysql://" + ip + ":3358/" + database;
        Connection connection = null;
        try {
            // 尝试加载 MySQL JDBC 驱动程序
            Class.forName("com.mysql.cj.jdbc.Driver");
            // 尝试建立连接
            connection = DriverManager.getConnection(url, user, password);
            // 如果连接不为空且未关闭，则连接成功
            return connection != null && !connection.isClosed();
        } catch (ClassNotFoundException e) {
            log.info("MySQL-JDBC,驱动未找到", e);
        } catch (SQLException e) {
            log.info("连接失败", e);
        } finally {
            // 确保关闭连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.info("关闭连接时出错", e);
                }
            }
        }
        return false;
    }


    /**
     * 校验精度
     *
     * @param diffRatio
     * @return
     */
    private boolean isDiffRatioValid(BigDecimal diffRatio) {
        if (diffRatio == null) {
            return false;
        }

        // 检查小数点后的位数（精度）
        int scale = diffRatio.scale();
        boolean hasAtMostTwoDecimalPlaces = scale <= 2;

        // 定义 BigDecimal 代表 99.99 和 0
        BigDecimal upperThreshold = new BigDecimal("99.99");
        BigDecimal lowerThreshold = BigDecimal.ZERO;

        // 检查值是否小于 99.99 且大于 0
        boolean isLessThanUpperThreshold = diffRatio.compareTo(upperThreshold) < 0;
        boolean isGreaterThanLowerThreshold = diffRatio.compareTo(lowerThreshold) > 0;

        // 只有当精度不超过两位且值在 (0, 99.99) 范围内时才返回 true
        return hasAtMostTwoDecimalPlaces && isLessThanUpperThreshold && isGreaterThanLowerThreshold;
    }
}
