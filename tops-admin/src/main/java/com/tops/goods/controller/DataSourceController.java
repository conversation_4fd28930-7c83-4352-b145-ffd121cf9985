package com.tops.goods.controller;

import com.alibaba.fastjson.JSON;
import com.tops.common.core.domain.R;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.BaseResponseVo;
import com.tops.goods.dto.DataSourceRequestVo;
import com.tops.goods.dto.DataSourceResponseVo;
import com.tops.goods.dto.DataSourceVo;
import com.tops.goods.goodsEnum.ConfigTypeEnum;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.service.DataSourceService;
import com.tops.goods.util.AESEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * @ClassName: DataSourceController
 * @Description: 数据源
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Controller
@RequestMapping("/dataSource")
@Slf4j
public class DataSourceController {

    @Resource
    private DataSourceService dataSourceService;

    // 正则表达式用于验证单个电子邮件地址，要求域名为 @jd.com
    private static final String EMAIL_REGEX = "^[\\w-\\.]+@jd\\.com$";

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";


    @ResponseBody
    @RequestMapping(value = "/saveSourceConfig", method = RequestMethod.POST, produces = "application/json")
    public BaseResponseVo saveSourceConfig(@RequestBody DataSourceRequestVo requestVo) {
        try {
            log.info("新增配置入参:{}", JSON.toJSONString(requestVo));
            if (ObjectUtils.isEmpty(requestVo)) {
                return BaseResponseVo.paramError("入参为空");
            }
            if (requestVo.getConfigType() == null
                || !ConfigTypeEnum.isValidTaskType(requestVo.getConfigType())) {
                return BaseResponseVo.paramError("类型为空或者非法");
            }

            if (!validCommonParam(requestVo)) {
                log.info("通用参数校验未通过");
                return BaseResponseVo.paramError("通用参数校验未通过");
            }

            if (!validSpecParam(requestVo)) {
                log.info("特殊参数校验未通过");
                return BaseResponseVo.paramError("特殊参数校验未通过");
            }
            dataSourceService.insertDataSource(requestVo);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("新增数据源配置异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/updateSourceConfig", method = RequestMethod.POST, produces = "application/json")
    public BaseResponseVo updateSourceConfig(@RequestBody DataSourceRequestVo requestVo) {
        try {
            log.info("更新配置入参:{}", JSON.toJSONString(requestVo));
            if (ObjectUtils.isEmpty(requestVo)) {
                return BaseResponseVo.paramError("入参为空");
            }
            if (requestVo.getConfigType() == null
                || !ConfigTypeEnum.isValidTaskType(requestVo.getConfigType())) {
                return BaseResponseVo.paramError("类型为空或者非法");
            }

            if (!validCommonParam(requestVo)) {
                log.info("通用参数校验未通过");
                return BaseResponseVo.paramError("通用参数校验未通过");
            }

            if (!validSpecParam(requestVo)) {
                log.info("特殊参数校验未通过");
                return BaseResponseVo.paramError("特殊参数校验未通过");
            }

            dataSourceService.updateDataSource(requestVo);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("更新数据源配置异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/updateEnableFlag", method = RequestMethod.POST)
    public BaseResponseVo updateEnableFlag(@RequestParam Integer id) {
        try {
            log.info("启用禁用数据源入参:{}", id);
            if (id == null) {
                return BaseResponseVo.paramError("入参异常");
            }
            dataSourceService.updateEnableFlag(id);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("启用禁用数据源异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/delDataSourceById", method = RequestMethod.POST)
    public BaseResponseVo delDataSourceById(@RequestParam Integer id) {
        try {
            log.info("删除数据源入参:{}", id);
            if (id == null) {
                return BaseResponseVo.paramError("入参异常");
            }
            dataSourceService.delDataSourceById(id);
            return BaseResponseVo.success();
        } catch (Exception e) {
            log.info("删除数据源异常", e);
            return BaseResponseVo.systemError(e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/queryDataSourceByPage", method = RequestMethod.POST)
    public DataSourceResponseVo queryDataSourceByPage(
        @RequestParam(value = "pageNum", required = false, defaultValue = "1") int pageNum,
        @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        try {
            List<DataSourceDomain> domainList = dataSourceService.queryDataSourceByPage(pageNum, pageSize);
            List<DataSourceVo> voList = convert(domainList);
            log.info("查询结果:{}", JSON.toJSONString(domainList));
            int pageCount = CollectionUtils.isEmpty(domainList) ? 0 : (domainList.size() + pageSize - 1) / pageSize;
            return DataSourceResponseVo.success(voList, domainList.size(), pageCount);
        } catch (Exception e) {
            log.error("分页查询数据源配置异常", e);
            return DataSourceResponseVo.systemError(e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/queryDataSourceByType", method = RequestMethod.POST)
    public DataSourceResponseVo queryDataSourceByType(@RequestParam Integer taskType) {
        try {
            if (taskType == null || !TaskTypeEnum.isValidTaskType(taskType)) {
                return DataSourceResponseVo.paramError("任务类型不合法");
            }
            Integer dataSourceType = null;
            if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(taskType)) {
                dataSourceType = ConfigTypeEnum.BIG_DATA.getCode();
            } else if (TaskTypeEnum.DATA_SCALE.getCode().equals(taskType)) {
                dataSourceType = ConfigTypeEnum.MYSQL.getCode();
            } else {
            }
            List<DataSourceDomain> domainList = dataSourceService.queryDataSourceByType(dataSourceType);
            List<DataSourceVo> voList = convert(domainList);
            log.info("查询结果:{}", JSON.toJSONString(domainList));
            int pageCount = 0;
            return DataSourceResponseVo.success(voList, domainList.size(), pageCount);
        } catch (Exception e) {
            log.error("分页查询数据源配置异常", e);
            return DataSourceResponseVo.systemError(e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/queryById", method = RequestMethod.POST)
    public R<DataSourceVo> queryById(@RequestParam Integer id) {
        try {
            DataSourceDomain domain = dataSourceService.queryById(id);
            List<DataSourceVo> voList = convert(Arrays.asList(domain));
            log.info("查询结果:{}", JSON.toJSONString(voList));
            return R.ok(voList != null ? voList.get(0) : null);
        } catch (Exception e) {
            log.error("查询任务异常", e);
            return R.fail(e.getMessage());
        }
    }


    private List<DataSourceVo> convert(List<DataSourceDomain> domainList) {
        SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        List<DataSourceVo> voList = new ArrayList<DataSourceVo>();
        if (CollectionUtils.isEmpty(domainList)) {
            return voList;
        }

        domainList.forEach(d -> {
            DataSourceVo v = new DataSourceVo();
            //通用
            v.setId(d.getId());
            v.setConfigType(d.getConfigType());
            v.setConfigCode(d.getConfigCode());
            v.setConfigName(d.getConfigName());
            v.setCoOperateUser(d.getCoOperateUser());
            v.setDbName(d.getDbName());

            //大数据
            v.setRunMarketCode(d.getRunMarketCode());
            v.setQueueCode(d.getQueueCode());
            v.setAccountCode(d.getAccountCode());
            v.setRunLogicClusterCode(d.getRunLogicClusterCode());
            v.setSharedPersons(d.getSharedPersons());

            //mysql
            v.setMysqlIp(d.getMysqlIp());
            v.setMysqlUserName(d.getMysqlUserName());
            try {
                if (d.getConfigType() != null && ConfigTypeEnum.MYSQL.getCode() == d.getConfigType()) {
                    v.setMysqlPassword(AESEncryptionUtil.decrypt(d.getMysqlUserName(), d.getMysqlPassword()));
                }
            } catch (Exception e) {
                log.info("taskNo:{}密文解密异常", d.getConfigCode(), e);
                throw new RuntimeException(e);
            }

            v.setEnableFlag(d.getEnableFlag());
            v.setCreateTime(d.getCreateTime() != null ? formatter.format(d.getCreateTime()) : null);
            v.setCreateUser(d.getCreateUser());
            v.setUpdateUser(d.getUpdateUser());
            v.setUpdateTime(d.getUpdateTime() != null ? formatter.format(d.getUpdateTime()) : null);
            v.setYn(d.getYn());
            voList.add(v);
        });
        return voList;
    }


    /**
     * 协同操作人校验
     *
     * @param sharedOperatorUser
     * @return
     */
    private boolean isValidSharedOperatorUser(String sharedOperatorUser) {
        if (StringUtils.isBlank(sharedOperatorUser)) {
            return true;
        }
        List<String> list = Arrays.asList(sharedOperatorUser.split(","));
        if (list.size() > 10) {
            log.info("协同操作人不得超过10个");
            return false;
        }
        return true;
    }


    /**
     * 邮箱合法性校验
     *
     * @param emailList
     * @return
     */
    private boolean areValidEmails(String emailList) {
        // 使用逗号分隔电子邮件地址，并去除前后的空格
        String[] emails = emailList.split(";");
        Pattern pattern = Pattern.compile(EMAIL_REGEX);

        for (String email : emails) {
            if (!pattern.matcher(email).matches()) {
                log.info("邮箱不合法");
                return false; // 如果任何一个电子邮件地址不合法，则返回false
            }
        }
        return true; // 所有电子邮件地址都合法
    }


    /**
     * 校验通用参数
     *
     * @param requestVo
     * @return
     */
    private boolean validCommonParam(DataSourceRequestVo requestVo) {
        if (StringUtils.isBlank(requestVo.getConfigName())
            || StringUtils.isBlank(requestVo.getDbName())
            || (StringUtils.isNotBlank(requestVo.getCoOperateUser()) && !isValidSharedOperatorUser(requestVo.getCoOperateUser()))) {
            return false;
        }
        return true;
    }


    /**
     * 特殊参数校验
     *
     * @param requestVo
     * @return
     */
    private boolean validSpecParam(DataSourceRequestVo requestVo) {
        Integer type = requestVo.getConfigType();
        if (ConfigTypeEnum.BIG_DATA.getCode().equals(type)) {
            if (StringUtils.isBlank(requestVo.getRunMarketCode())
                || StringUtils.isBlank(requestVo.getQueueCode())
                || StringUtils.isBlank(requestVo.getAccountCode())
                || StringUtils.isBlank(requestVo.getRunLogicClusterCode())
                || StringUtils.isBlank(requestVo.getSharedPersons())
                || !areValidEmails(requestVo.getSharedPersons())) {
                log.info("大数据数据源参数校验未通过");
                return false;
            }
        }

        if (ConfigTypeEnum.MYSQL.getCode().equals(type)) {
            if (StringUtils.isBlank(requestVo.getMysqlIp())
                || StringUtils.isBlank(requestVo.getMysqlUserName())
                || StringUtils.isBlank(requestVo.getMysqlPassword())
                || !checkConnection(requestVo.getMysqlIp(), requestVo.getDbName(), requestVo.getMysqlUserName(), requestVo.getMysqlPassword())) {
                log.info("MySQL数据源参数校验未通过");
                return false;
            }
        }
        return true;
    }


    /**
     * 校验MySQL是否可连接
     *
     * @param ip
     * @param database
     * @param user
     * @param password
     * @return
     */
    private boolean checkConnection(String ip, String database, String user, String password) {
        String url = "jdbc:mysql://" + ip + ":3358/" + database;
        Connection connection = null;
        try {
            // 尝试加载 MySQL JDBC 驱动程序
            Class.forName("com.mysql.cj.jdbc.Driver");
            // 尝试建立连接
            connection = DriverManager.getConnection(url, user, password);
            // 如果连接不为空且未关闭，则连接成功
            return connection != null && !connection.isClosed();
        } catch (ClassNotFoundException e) {
            log.info("MySQL-JDBC,驱动未找到", e);
        } catch (SQLException e) {
            log.info("连接失败", e);
        } finally {
            // 确保关闭连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.info("关闭连接时出错", e);
                }
            }
        }
        return false;
    }
}
