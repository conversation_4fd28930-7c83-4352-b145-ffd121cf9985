package com.tops.goods.mapper;

import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @InterfaceName: DataMonitorMapper
 * @Description: 数据源Mapper层
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */

public interface DataSourceMapper {
    Integer insertDataSource(DataSourceDomain domain);

    Integer updateDataSource(DataSourceDomain domain);

    List<DataSourceDomain> queryDataSourceByPage(@Param("startIndex") int startIndex, @Param("length") int length, @Param("createUser") String createUser);

    /**
     * 根据任务查询任务
     *
     * @return
     */
    DataSourceDomain queryById(Integer id);


    Integer updateEnableFlag(@Param("id") Integer id, @Param("enableFlag") Integer enableFlag, @Param("updateUser") String updateUser);


    Integer delDataSourceById(@Param("id") Integer id, @Param("updateUser") String updateUser);


    List<DataSourceDomain> queryDataSourceByType(@Param("type") Integer type, @Param("createUser") String createUser);
}
