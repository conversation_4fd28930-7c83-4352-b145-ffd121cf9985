package com.tops.goods.mapper;

import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataMonitorTaskDomain;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @InterfaceName: DataMonitorMapper
 * @Description: 数据监控Mapper层
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */

public interface DataMonitorMapper {
    Integer insertTask(DataMonitorTaskDomain domain);

    Integer updateMonitorTask(DataMonitorTaskDomain domain);

    List<DataMonitorTaskDomain> queryTaskByCondition(QueryCondition condition);

    Long updateLastExecutionTime(Integer taskId);


    List<DataMonitorTaskDomain> queryTaskByPage(@Param("startIndex") int startIndex, @Param("length") int length, @Param("createUser") String createUser);

    /**
     * 根据任务查询任务
     *
     * @return
     */
    DataMonitorTaskDomain queryById(Integer id);


    Integer updateEnableFlag(@Param("id") Integer id, @Param("enableFlag") Integer enableFlag, @Param("updateUser") String updateUser);


    Integer delTaskById(@Param("id") Integer id, @Param("updateUser") String updateUser);
}
