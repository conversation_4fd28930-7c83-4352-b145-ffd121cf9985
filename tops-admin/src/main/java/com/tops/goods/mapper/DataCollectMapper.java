package com.tops.goods.mapper;

import com.tops.goods.domain.DataCollectDomain;

import java.util.List;

/**
 * @InterfaceName: DataMonitorMapper
 * @Description: 数据监控Mapper层
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */

public interface DataCollectMapper {
    /**
     * 插入采集结果
     *
     * @param domain
     * @return
     */
    Integer insertCollectData(DataCollectDomain domain);


    /**
     * 结转采集结果
     *
     * @return
     */
    Integer delCollectData();


    /**
     * 小时维度今天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getTodayHours(Integer taskId);

    /**
     * 最近三小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastFiveHours(Integer taskId);

    /**
     * 最近10小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastTenHours(Integer taskId);

    /**
     * 最近24小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLast24Hours(Integer taskId);

    /**
     * 小时维度最近三天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastThreeDayHours(Integer taskId);

    /**
     * 天维度最近5天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastFiveDays(Integer taskId);

    /**
     * 天维度最近7天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastSevenDays(Integer taskId);

    /**
     * 天维度最近15天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLast15Days(Integer taskId);
}
