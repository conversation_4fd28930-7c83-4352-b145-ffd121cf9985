package com.tops.goods.xxlJob;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.message.Message;
import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataCollectDomain;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.goodsEnum.CollectTypeEnum;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.service.DataMonitorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: DataHourlyCollectJob
 * @Description: 小时级别数据采集定时任务XXJOB
 * @Auther: MaWenHua8
 * @Date: 2024/11/5
 * @version: V1.0
 */
@Component
@Slf4j
public class DataHourlyCollectJob {

    @Resource
    private DataMonitorService dataMonitorService;
    //@Value("${jmq4.topic.dataCollect}")
    private String dataCollectTopic;

    @Resource(name = "jmq4producer")
    private Producer producer;

    @XxlJob(value = "dataHourlyCollectJob")
    public ReturnT<String> resume(String param) {
        try {
            log.info("小时级别数据采集XXJob执行开始");
            QueryCondition condition = new QueryCondition();
            condition.setYn(1);
            condition.setEnableFlag(1);
            condition.setTaskType(TaskTypeEnum.DATA_SCALE.getCode());
            List<DataMonitorTaskDomain> taskDomainList = dataMonitorService.queryTaskByCondition(condition);
            log.info("启用且未逻辑删除的taskListSize:{}", taskDomainList.size());
            if (CollectionUtils.isNotEmpty(taskDomainList)) {
                taskDomainList.forEach(task -> {
                    try {
                        DataCollectDomain domain = new DataCollectDomain();
                        domain.setTaskId(task.getId());
                        domain.setCollectDimension(CollectTypeEnum.HOUR.getCode());
                        Message message = new Message(dataCollectTopic, JSON.toJSONString(domain), task.getTaskNo());
                        producer.send(message);
                    } catch (Exception e) {
                        log.info("taskNo:{}发送MQ异常", task.getTaskNo());
                    }
                });
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.info("小时级别数据采集任务执行异常", e);
            throw e;
        }
    }
}
