package com.tops.goods.xxlJob;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.message.Message;
import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.service.DataMonitorService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.executor.CronExpression;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: DataMonitorJob
 * @Description: 数据监控定时任务XXJOB
 * @Auther: MaWenHua8
 * @Date: 2024/11/5
 * @version: V1.0
 */
@Component
@Slf4j
public class DataMonitorJob {

    @Resource
    private DataMonitorService dataMonitorService;
    //@Value("${jmq4.topic.dataMonitor}")
    private String dataMonitorTopic;

    @Resource(name = "jmq4producer")
    private Producer producer;

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    @XxlJob(value = "dataMonitorJob")
    public ReturnT<String> resume(String param) {
        try {
            log.info("数据监控XXJob执行开始");
            QueryCondition condition = new QueryCondition();
            condition.setYn(1);
            condition.setEnableFlag(1);
            List<DataMonitorTaskDomain> taskDomainList = dataMonitorService.queryTaskByCondition(condition);
            log.info("启用且未逻辑删除的taskList:{}", JSON.toJSONString(taskDomainList));
            if (CollectionUtils.isNotEmpty(taskDomainList)) {
                taskDomainList.forEach(task -> {
                    try {
                        if (task.getLastExecutionTime() != null && !isExecTime(task)) {
                            return;
                        }
                        Message message = new Message(dataMonitorTopic, JSON.toJSONString(task), task.getTaskNo());
                        producer.send(message);
                    } catch (Exception e) {
                        log.info("taskNo:{}发送MQ异常", task.getTaskNo());
                    }
                });
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.info("数据监控任务执行异常", e);
            throw e;
        }
    }


    /**
     * 判断是否在执行时间
     *
     * @param domain
     * @return
     */
    private boolean isExecTime(DataMonitorTaskDomain domain) {
        try {
            //根据cron表达式获取下次执行时间
            String cronExpression = domain.getExecFrequency();
            CronExpression cron = new CronExpression(cronExpression);
            SimpleDateFormat formatter = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);

            Date currentTime = new Date();
            Date lastExecutionTime = domain.getLastExecutionTime();
            Date nextValidTime = cron.getNextValidTimeAfter(lastExecutionTime);

            log.info("taskNo:{}-[last]:【{}】,[now]:【{}】,[next]:【{}】,", domain.getTaskNo(), formatter.format(lastExecutionTime),
                formatter.format(currentTime), formatter.format(nextValidTime), formatter.format(nextValidTime));

            Instant currentInstant = currentTime.toInstant();
            Instant lastInstant = lastExecutionTime.toInstant();
            Instant nextInstant = nextValidTime.toInstant();


            long should = ChronoUnit.SECONDS.between(lastInstant, nextInstant);
            long period = ChronoUnit.SECONDS.between(lastInstant, currentInstant);

            log.info("taskNo:{}-should:{}秒,period:{}秒", domain.getTaskNo(), should, period);
            if (should > period) {
                log.info("taskNo:{}-cron:{}非当前执行时间", domain.getTaskNo(), domain.getExecFrequency());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.info("taskNo:{}计算执行时间异常", domain.getTaskNo(), e);
            return false;
        }
    }
}
