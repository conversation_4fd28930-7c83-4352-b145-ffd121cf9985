package com.tops.goods.xxlJob;

import com.tops.goods.service.DataCollectService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @ClassName: ClearCollectDataJob
 * @Description: 数据采集结转定时任务XXJOB
 * @Auther: MaWenHua8
 * @Date: 2024/11/5
 * @version: V1.0
 */
@Component
@Slf4j
public class ClearCollectDataJob {

    @Resource
    private DataCollectService dataCollectService;

    @XxlJob(value = "clearCollectDataJob")
    public ReturnT<String> resume(String param) {
        try {
            log.info("采集数据结转XXJob执行开始");
            dataCollectService.delCollectData();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.info("采集数据结转任务执行异常", e);
            throw e;
        }
    }
}
