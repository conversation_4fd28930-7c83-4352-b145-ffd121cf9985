package com.tops.goods.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.cp.oms.pk.client.service.SingleSequenceService;
import com.tops.common.core.domain.model.LoginUser;
import com.tops.common.helper.LoginHelper;
import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.DataSourceRequestVo;
import com.tops.goods.goodsEnum.ConfigTypeEnum;
import com.tops.goods.mapper.DataSourceMapper;
import com.tops.goods.service.DataMonitorService;
import com.tops.goods.service.DataSourceService;
import com.tops.goods.util.AESEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataMonitorServiceImpl
 * @Description: 任务服务实现
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */
@Service
@Slf4j
public class DataSourceServiceImpl implements DataSourceService {
    @Resource
    private SingleSequenceService singleSequenceService;

    @Resource
    private DataSourceMapper dataSourceMapper;

    @Resource
    private DataMonitorService dataMonitorService;


    @Override
    @Transactional
    public Integer insertDataSource(DataSourceRequestVo requestVo) throws Exception {
        if (ObjectUtils.isEmpty(requestVo)) {
            return 0;
        }
        long sequenceId = singleSequenceService.getSequence("t_ops_data_source_config");

        DataSourceDomain sourceDomain = new DataSourceDomain();

        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }

        //通用
        sourceDomain.setDbName(requestVo.getDbName());
        sourceDomain.setCoOperateUser(requestVo.getCoOperateUser());
        sourceDomain.setConfigType(requestVo.getConfigType());
        sourceDomain.setConfigCode("SRD" + sequenceId);
        sourceDomain.setConfigName(requestVo.getConfigName());

        //大数据
        if (ConfigTypeEnum.BIG_DATA.getCode().equals(requestVo.getConfigType())) {
            sourceDomain.setRunMarketCode(requestVo.getRunMarketCode());
            sourceDomain.setQueueCode(requestVo.getQueueCode());
            sourceDomain.setAccountCode(requestVo.getAccountCode());
            sourceDomain.setRunLogicClusterCode(requestVo.getRunLogicClusterCode());
            sourceDomain.setSharedPersons(requestVo.getSharedPersons());
        }

        //mysql
        if (ConfigTypeEnum.MYSQL.getCode().equals(requestVo.getConfigType())) {
            sourceDomain.setMysqlIp(requestVo.getMysqlIp());
            sourceDomain.setMysqlUserName(requestVo.getMysqlUserName());
            sourceDomain.setMysqlPassword(AESEncryptionUtil.encrypt(requestVo.getMysqlUserName(), requestVo.getMysqlPassword()));
        }

        sourceDomain.setCreateUser(loginUser.getUsername());
        log.info("插入入参：{}", JSON.toJSONString(sourceDomain));
        return dataSourceMapper.insertDataSource(sourceDomain);
    }

    @Override
    public Integer updateDataSource(DataSourceRequestVo requestVo) throws Exception {
        if (ObjectUtils.isEmpty(requestVo)) {
            return 0;
        }

        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        DataSourceDomain sourceDomain = new DataSourceDomain();
        sourceDomain.setId(requestVo.getId());

        DataSourceDomain dbDomain = queryById(requestVo.getId());
        if (ObjectUtils.isEmpty(dbDomain)) {
            throw new RuntimeException("该任务不存在");
        }

        //通用
        if (requestVo.getConfigType() != null && !dbDomain.getConfigType().equals(requestVo.getConfigType())) {
            sourceDomain.setConfigType(requestVo.getConfigType());
        }
        if (!StringUtils.equals(requestVo.getDbName(), dbDomain.getDbName())) {
            sourceDomain.setDbName(requestVo.getDbName());
        }
        if (!StringUtils.equals(requestVo.getCoOperateUser(), dbDomain.getCoOperateUser())) {
            sourceDomain.setCoOperateUser(requestVo.getCoOperateUser());
        }
        if (!dbDomain.getConfigName().equals(requestVo.getConfigName())) {
            sourceDomain.setConfigName(requestVo.getConfigName());
        }

        //大数据
        if (ConfigTypeEnum.BIG_DATA.getCode().equals(requestVo.getConfigType())) {
            if (!StringUtils.equals(requestVo.getRunMarketCode(), dbDomain.getRunMarketCode())) {
                sourceDomain.setRunMarketCode(requestVo.getRunMarketCode());
            }
            if (!StringUtils.equals(requestVo.getQueueCode(), dbDomain.getQueueCode())) {
                sourceDomain.setQueueCode(requestVo.getQueueCode());
            }
            if (!StringUtils.equals(requestVo.getAccountCode(), dbDomain.getAccountCode())) {
                sourceDomain.setAccountCode(requestVo.getAccountCode());
            }
            if (!StringUtils.equals(requestVo.getRunLogicClusterCode(), dbDomain.getRunLogicClusterCode())) {
                sourceDomain.setRunLogicClusterCode(requestVo.getRunLogicClusterCode());
            }
            if (!StringUtils.equals(requestVo.getSharedPersons(), dbDomain.getSharedPersons())) {
                sourceDomain.setSharedPersons(requestVo.getSharedPersons());
            }
        }


        //数据量级
        if (ConfigTypeEnum.MYSQL.getCode().equals(requestVo.getConfigType())) {
            if (!StringUtils.equals(requestVo.getMysqlIp(), dbDomain.getMysqlIp())) {
                sourceDomain.setMysqlIp(requestVo.getMysqlIp());
            }
            if (!StringUtils.equals(requestVo.getMysqlUserName(), dbDomain.getMysqlUserName())) {
                sourceDomain.setMysqlUserName(requestVo.getMysqlUserName());
            }
            if (!StringUtils.equals(requestVo.getMysqlPassword(), AESEncryptionUtil.decrypt(dbDomain.getMysqlUserName(), dbDomain.getMysqlPassword()))) {
                sourceDomain.setMysqlPassword(AESEncryptionUtil.encrypt(dbDomain.getMysqlUserName(), requestVo.getMysqlPassword()));
            }
        }
        sourceDomain.setUpdateUser(loginUser.getUsername());
        log.info("更新入参：{}", JSON.toJSONString(sourceDomain));
        return dataSourceMapper.updateDataSource(sourceDomain);
    }


    public List<DataSourceDomain> queryDataSourceByPage(int page, int pageSize) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        int startIndex = page != 0 ? (page - 1) * pageSize : page;
        int length = pageSize;
        return dataSourceMapper.queryDataSourceByPage(startIndex, length, loginUser.getUsername());
    }

    @Override
    public DataSourceDomain queryById(Integer id) {
        if (id == null) {
            return null;
        }
        return dataSourceMapper.queryById(id);
    }

    @Override
    public Integer updateEnableFlag(Integer id) {
        if (id == null) {
            return 0;
        }
        DataSourceDomain dbDomain = queryById(id);

        //1:启用 2:禁用
        Integer dbFlag = dbDomain.getEnableFlag();
        Integer dataSourceEnableFlag = dbFlag == 1 ? 2 : 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        if (dataSourceEnableFlag == 2) {
            QueryCondition condition = new QueryCondition();
            condition.setEnableFlag(1);
            condition.setDataSourceId(id);
            List<DataMonitorTaskDomain> taskDomainList = dataMonitorService.queryTaskByCondition(condition);
            if (CollectionUtils.isNotEmpty(taskDomainList)) {
                throw new RuntimeException("该数据源下存在启用状态任务，不可禁用");
            }
        }
        return dataSourceMapper.updateEnableFlag(id, dataSourceEnableFlag, loginUser.getUsername());
    }

    @Override
    public Integer delDataSourceById(Integer id) {
        if (id == null) {
            return 0;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        QueryCondition condition = new QueryCondition();
        condition.setYn(1);
        condition.setDataSourceId(id);
        List<DataMonitorTaskDomain> taskDomainList = dataMonitorService.queryTaskByCondition(condition);
        if (CollectionUtils.isNotEmpty(taskDomainList)) {
            throw new RuntimeException("该数据源下存在未删除任务，不可删除");
        }
        return dataSourceMapper.delDataSourceById(id, loginUser.getUsername());
    }

    @Override
    public List<DataSourceDomain> queryDataSourceByType(Integer type) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        if (type == null) {
            return new ArrayList<>();
        }
        return dataSourceMapper.queryDataSourceByType(type, loginUser.getUsername());
    }
}
