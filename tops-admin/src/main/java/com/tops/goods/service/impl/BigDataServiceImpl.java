package com.tops.goods.service.impl;

import com.jd.bdp.domain.dto.JsfAuthDTO;
import com.jd.bdp.gateway.sdk.GWException;
import com.jd.fastjson.JSON;
import com.jd.jbdp.edc.api.extract.model.dto.JSFResultDTO;
import com.jd.jbdp.edc.api.extract.service.JobInfoInterface;
import com.jd.jbdp.edc.model.vo.extract.Job;
import com.tops.goods.dto.BigDataRequestVo;
import com.tops.goods.service.BigDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: BigDataServiceImpl
 * @Description: 接口实现类
 * @Auther: MaWenHua8
 * @Date: 2024/11/4
 * @version: V1.0
 */
@Service
@Slf4j
public class BigDataServiceImpl implements BigDataService {
    @Resource
    private JobInfoInterface jobInfoInterface;

    @Override
    public JSFResultDTO saveTask(String tid, BigDataRequestVo requestVo) {
        try {
            Job job = new Job();
            job.setErp(requestVo.getOperator());
            job.setJobName(requestVo.getJobName());
            job.setDescription(requestVo.getDescription());
            job.setTargetFileName(requestVo.getTargetFileName());
            job.setTargetFileType(requestVo.getTargetFileType());
            job.setTargetType(requestVo.getTargetType());
            job.setSharedPersons(requestVo.getSharedPersons());
            job.setContent(requestVo.getContent());
            job.setDbName(requestVo.getDbName());
            job.setRunMarketCode(requestVo.getRunMarketCode());
            job.setQueueCode(requestVo.getQueueCode());
            job.setAccountCode(requestVo.getAccountCode());
            job.setRunLogicClusterCode(requestVo.getRunLogicClusterCode());
            job.setNoticeType(requestVo.getNoticeType());
            job.setProjectId(requestVo.getProjectId());
            job.setBizCode(requestVo.getBizCode());
            job.setEngineType("presto");
            log.info("taskNo:{}大数据入参:{}", tid, JSON.toJSONString(job));
            JSFResultDTO resultDTO = jobInfoInterface.save(new JsfAuthDTO(), job);
            log.info("taskNo:{}大数据出参:{}", tid, JSON.toJSONString(resultDTO));
            return resultDTO;
        } catch (GWException gwException) {
            log.info("大数据鉴权失败", gwException);
            throw gwException;
        } catch (Exception e) {
            log.info("大数据任务插入失败", e);
            throw e;
        }
    }
}
