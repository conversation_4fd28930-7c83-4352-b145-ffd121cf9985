package com.tops.goods.service;

import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.DataMonitorTaskRequestVo;

import java.util.List;

/**
 * @ClassName: DataMonitorService
 * @Description: 任务服务
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */
public interface DataMonitorService {

    /**
     * 插入TIDB任务
     *
     * @param dataMonitorTaskRequestVo
     * @return
     */
    Integer insertMonitorTask(DataMonitorTaskRequestVo dataMonitorTaskRequestVo) throws Exception;


    /**
     * 更新TIDB任务
     *
     * @param dataMonitorTaskRequestVo
     * @return
     */
    Integer updateMonitorTask(DataMonitorTaskRequestVo dataMonitorTaskRequestVo) throws Exception;


    /**
     * 查询任务
     *
     * @return
     */
    List<DataMonitorTaskDomain> queryTaskByCondition(QueryCondition condition);


    /**
     * 更新执行时间
     *
     * @param taskId
     */
    void updateLastExecutionTime(Integer taskId);


    /**
     * 查询任务
     *
     * @return
     */
    List<DataMonitorTaskDomain> queryTaskByPage(int page, int pageSize);


    /**
     * 根据任务查询任务
     *
     * @return
     */
    DataMonitorTaskDomain queryById(Integer id);

    /**
     * 启用停用TIDB任务
     *
     * @param id
     * @return
     */
    Integer updateEnableFlag(Integer id);


    Integer delTaskById(Integer id);


    Integer queryTotalNumber(DataSourceDomain dataSourceDomain, DataMonitorTaskDomain domain) throws Exception;
}
