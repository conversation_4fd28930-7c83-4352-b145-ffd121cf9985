package com.tops.goods.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.cp.oms.pk.client.service.SingleSequenceService;
import com.tops.common.constant.CacheNames;
import com.tops.common.core.domain.model.LoginUser;
import com.tops.common.helper.LoginHelper;
import com.tops.common.utils.redis.CacheUtils;
import com.tops.goods.condition.QueryCondition;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.DataMonitorTaskRequestVo;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.mapper.DataMonitorMapper;
import com.tops.goods.service.DataMonitorService;
import com.tops.goods.util.AESEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

/**
 * @ClassName: DataMonitorServiceImpl
 * @Description: 任务服务实现
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */
@Service
@Slf4j
public class DataMonitorServiceImpl implements DataMonitorService {
    @Resource
    private SingleSequenceService singleSequenceService;

    @Resource
    private DataMonitorMapper dataMonitorMapper;

    @Override
    @Transactional
    public Integer insertMonitorTask(DataMonitorTaskRequestVo dataMonitorTaskRequestVo) throws Exception {
        if (ObjectUtils.isEmpty(dataMonitorTaskRequestVo)) {
            return 0;
        }

        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }

        long sequenceId = singleSequenceService.getSequence("t_ops_monitor_task");
        String sqlKey = "SQL" + sequenceId;
        CacheUtils.put(CacheNames.SYS_SQL, sqlKey, dataMonitorTaskRequestVo.getSqlContent());
        DataMonitorTaskDomain taskDomain = new DataMonitorTaskDomain();

        //通用
        taskDomain.setDataSourceId(dataMonitorTaskRequestVo.getDataSourceId());
        taskDomain.setTaskName(dataMonitorTaskRequestVo.getTaskName());
        taskDomain.setTaskNo("TAK" + sequenceId);
        taskDomain.setTaskType(dataMonitorTaskRequestVo.getTaskType());
        taskDomain.setSqlKey(sqlKey);
        taskDomain.setExecFrequency(dataMonitorTaskRequestVo.getExecFrequency());
        taskDomain.setSharedOperateUser(dataMonitorTaskRequestVo.getSharedOperateUser());

        //数据重复
        if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(dataMonitorTaskRequestVo.getTaskType())) {
        }

        //数据量级
        if (TaskTypeEnum.DATA_SCALE.getCode().equals(dataMonitorTaskRequestVo.getTaskType())) {
            taskDomain.setBusinessAlarmKey(dataMonitorTaskRequestVo.getBusinessAlarmKey());
            taskDomain.setScaleType(dataMonitorTaskRequestVo.getScaleType());
            taskDomain.setUpperLimit(dataMonitorTaskRequestVo.getUpperLimit());
            taskDomain.setDiffRatio(dataMonitorTaskRequestVo.getDiffRatio());
        }

        taskDomain.setCreateUser(loginUser.getUsername());
        log.info("插入入参：{}", JSON.toJSONString(taskDomain));
        return dataMonitorMapper.insertTask(taskDomain);
    }

    @Override
    @Transactional
    public Integer updateMonitorTask(DataMonitorTaskRequestVo dataMonitorTaskRequestVo) throws Exception {
        if (ObjectUtils.isEmpty(dataMonitorTaskRequestVo)) {
            return 0;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }

        String sqlKey = dataMonitorTaskRequestVo.getSqlKey();
        CacheUtils.put(CacheNames.SYS_SQL, sqlKey, dataMonitorTaskRequestVo.getSqlContent());
        DataMonitorTaskDomain taskDomain = new DataMonitorTaskDomain();
        taskDomain.setId(dataMonitorTaskRequestVo.getId());
        DataMonitorTaskDomain dbDomain = queryById(dataMonitorTaskRequestVo.getId());
        if (ObjectUtils.isEmpty(dbDomain)) {
            throw new RuntimeException("该任务不存在");
        }

        //通用
        if (!dbDomain.getDataSourceId().equals(dataMonitorTaskRequestVo.getDataSourceId())) {
            taskDomain.setDataSourceId(dataMonitorTaskRequestVo.getDataSourceId());
        }
        if (!StringUtils.equals(dataMonitorTaskRequestVo.getTaskName(), dbDomain.getTaskName())) {
            taskDomain.setTaskName(dataMonitorTaskRequestVo.getTaskName());
        }
        if (!StringUtils.equals(dataMonitorTaskRequestVo.getExecFrequency(), dbDomain.getExecFrequency())) {
            taskDomain.setExecFrequency(dataMonitorTaskRequestVo.getExecFrequency());
        }
        if (!dbDomain.getTaskType().equals(dataMonitorTaskRequestVo.getTaskType())) {
            taskDomain.setTaskType(dataMonitorTaskRequestVo.getTaskType());
        }
        if (!StringUtils.equals(dataMonitorTaskRequestVo.getSharedOperateUser(), dbDomain.getSharedOperateUser())) {
            taskDomain.setSharedOperateUser(dataMonitorTaskRequestVo.getSharedOperateUser());
        }

        //数据重复
        if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(dataMonitorTaskRequestVo.getTaskType())) {
        }


        //数据量级
        if (TaskTypeEnum.DATA_SCALE.getCode().equals(dataMonitorTaskRequestVo.getTaskType())) {
            if (!StringUtils.equals(dataMonitorTaskRequestVo.getBusinessAlarmKey(), dbDomain.getBusinessAlarmKey())) {
                taskDomain.setBusinessAlarmKey(dataMonitorTaskRequestVo.getBusinessAlarmKey());
            }
            if (dbDomain.getScaleType() == null || !dbDomain.getScaleType().equals(dataMonitorTaskRequestVo.getScaleType())) {
                taskDomain.setScaleType(dataMonitorTaskRequestVo.getScaleType());
            }
            if (dbDomain.getUpperLimit() == null || !dbDomain.getUpperLimit().equals(dataMonitorTaskRequestVo.getUpperLimit())) {
                taskDomain.setUpperLimit(dataMonitorTaskRequestVo.getUpperLimit());
            }
            if (dbDomain.getDiffRatio() == null || !dbDomain.getDiffRatio().equals(dataMonitorTaskRequestVo.getDiffRatio())) {
                taskDomain.setDiffRatio(dataMonitorTaskRequestVo.getDiffRatio());
            }
        }

        taskDomain.setUpdateUser(loginUser.getUsername());
        log.info("更新入参：{}", JSON.toJSONString(taskDomain));
        return dataMonitorMapper.updateMonitorTask(taskDomain);
    }

    @Override
    public List<DataMonitorTaskDomain> queryTaskByCondition(QueryCondition condition) {
        return dataMonitorMapper.queryTaskByCondition(condition);
    }

    @Override
    public void updateLastExecutionTime(Integer taskId) {
        if (taskId == null) {
            return;
        }
        dataMonitorMapper.updateLastExecutionTime(taskId);
    }

    @Override
    public List<DataMonitorTaskDomain> queryTaskByPage(int page, int pageSize) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        int startIndex = page != 0 ? (page - 1) * pageSize : page;
        int length = pageSize;
        return dataMonitorMapper.queryTaskByPage(startIndex, length, loginUser.getUsername());
    }

    @Override
    public DataMonitorTaskDomain queryById(Integer id) {
        if (id == null) {
            return null;
        }
        return dataMonitorMapper.queryById(id);
    }

    @Override
    public Integer updateEnableFlag(Integer id) {
        if (id == null) {
            return 0;
        }
        DataMonitorTaskDomain dbDomain = queryById(id);

        //1:启用 2:禁用
        Integer dbFlag = dbDomain.getEnableFlag();
        Integer enableFlag = dbFlag == 1 ? 2 : 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        return dataMonitorMapper.updateEnableFlag(id, enableFlag, loginUser.getUsername());
    }

    @Override
    public Integer delTaskById(Integer id) {
        if (id == null) {
            return 0;
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (ObjectUtils.isEmpty(loginUser)) {
            throw new RuntimeException("登录人信息获取为空");
        }
        return dataMonitorMapper.delTaskById(id, loginUser.getUsername());
    }

    @Override
    public Integer queryTotalNumber(DataSourceDomain dataSourceDomain, DataMonitorTaskDomain domain) throws Exception {
        if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(domain.getTaskType())) {
            return null;
        }
        Integer resultNum = null;
        String ip = dataSourceDomain.getMysqlIp();
        String database = dataSourceDomain.getDbName();
        String userName = dataSourceDomain.getMysqlUserName();
        String password = AESEncryptionUtil.decrypt(dataSourceDomain.getMysqlUserName(), dataSourceDomain.getMysqlPassword());
        String url = "jdbc:mysql://" + ip + ":3358/" + database;
        Connection connection = null;
        try {
            // 尝试加载 MySQL JDBC 驱动程序
            Class.forName("com.mysql.cj.jdbc.Driver");
            // 尝试建立连接
            connection = DriverManager.getConnection(url, userName, password);
            // 如果连接为空或关闭返回
            if (connection == null || connection.isClosed()) {
                return null;
            }

            //执行SQL
            String sql = CacheUtils.get(CacheNames.SYS_SQL, domain.getSqlKey());
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery(sql);
            if (resultSet.next()) {
                resultNum = resultSet.getInt(1);
            }
        } catch (ClassNotFoundException e) {
            log.info("MySQL-JDBC,驱动未找到", e);
        } catch (SQLException e) {
            log.info("连接失败", e);
        } finally {
            // 确保关闭连接
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    log.info("关闭连接时出错", e);
                }
            }
        }
        return resultNum;
    }
}
