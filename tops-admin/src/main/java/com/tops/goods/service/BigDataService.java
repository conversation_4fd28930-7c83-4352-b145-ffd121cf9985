package com.tops.goods.service;

import com.jd.jbdp.edc.api.extract.model.dto.JSFResultDTO;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.dto.BigDataRequestVo;
import com.tops.goods.dto.DataMonitorTaskRequestVo;

import java.util.List;

/**
 * @InterfaceName: BigDataService
 * @Description: 大数据接口
 * @Auther: MaWenHua8
 * @Date: 2024/11/4
 * @version: V1.0
 */
public interface BigDataService {
    /**
     * 接口文档：https://joyspace.jd.com/pages/abU1BqNvnjFKQJClYqtm
     *
     * @param tid
     * @param requestVo
     * @return
     */
    JSFResultDTO saveTask(String tid, BigDataRequestVo requestVo);
}
