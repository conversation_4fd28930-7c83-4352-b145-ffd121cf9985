package com.tops.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.tops.goods.domain.DataCollectDomain;
import com.tops.goods.mapper.DataCollectMapper;
import com.tops.goods.service.DataCollectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataCollectServiceImpl
 * @Description:
 * @Auther: MaWenHua8
 * @Date: 2024/11/21
 * @version: V1.0
 */
@Slf4j
@Service
public class DataCollectServiceImpl implements DataCollectService {

    @Resource
    private DataCollectMapper dataCollectMapper;


    @Override
    public Integer insertCollectData(DataCollectDomain domain) {
        if (ObjectUtils.isEmpty(domain)) {
            return 0;
        }
        log.info("插入采集数据入参:{}", JSON.toJSONString(domain));
        return dataCollectMapper.insertCollectData(domain);
    }

    @Override
    public Integer delCollectData() {
        return dataCollectMapper.delCollectData();
    }

    @Override
    public List<DataCollectDomain> getTodayHours(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getTodayHours(taskId);
    }

    @Override
    public List<DataCollectDomain> getLastFiveHours(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLastFiveHours(taskId);
    }

    @Override
    public List<DataCollectDomain> getLastTenHours(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLastTenHours(taskId);
    }

    @Override
    public List<DataCollectDomain> getLast24Hours(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLast24Hours(taskId);
    }

    @Override
    public List<DataCollectDomain> getLastThreeDayHours(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLastThreeDayHours(taskId);
    }

    @Override
    public List<DataCollectDomain> getLastFiveDays(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLastFiveDays(taskId);
    }

    @Override
    public List<DataCollectDomain> getLastSevenDays(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLastSevenDays(taskId);
    }

    @Override
    public List<DataCollectDomain> getLast15Days(Integer taskId) {
        if (taskId == null) {
            return new ArrayList<DataCollectDomain>();
        }
        return dataCollectMapper.getLast15Days(taskId);
    }
}
