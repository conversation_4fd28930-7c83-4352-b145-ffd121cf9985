package com.tops.goods.service;

import com.tops.goods.domain.DataCollectDomain;
import com.tops.goods.dto.DataSourceRequestVo;

import java.util.List;

/**
 * @InterfaceName: DataSourceService
 * @Description: 采集结果保存服务
 * @Auther: MaWenHua8
 * @Date: 2024/11/19
 * @version: V1.0
 */
public interface DataCollectService {

    /**
     * 插入采集结果
     *
     * @param domain
     * @return
     */
    Integer insertCollectData(DataCollectDomain domain);

    /**
     * 结转采集结果 只保留15天
     *
     * @return
     */
    Integer delCollectData();


    /**
     * 小时维度今天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getTodayHours(Integer taskId);

    /**
     * 最近⑤小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastFiveHours(Integer taskId);

    /**
     * 最近10小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastTenHours(Integer taskId);

    /**
     * 最近24小时
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLast24Hours(Integer taskId);

    /**
     * 小时维度最近三天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastThreeDayHours(Integer taskId);

    /**
     * 天维度最近5天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastFiveDays(Integer taskId);

    /**
     * 天维度最近7天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLastSevenDays(Integer taskId);

    /**
     * 天维度最近15天
     *
     * @param taskId
     * @return
     */
    List<DataCollectDomain> getLast15Days(Integer taskId);
}
