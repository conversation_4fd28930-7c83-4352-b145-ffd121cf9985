package com.tops.goods.service;

import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.DataSourceRequestVo;

import java.util.List;

/**
 * @InterfaceName: DataSourceService
 * @Description:
 * @Auther: MaWenHua8
 * @Date: 2024/11/19
 * @version: V1.0
 */
public interface DataSourceService {

    /**
     * 插入配置
     *
     * @param requestVo
     * @return
     */
    Integer insertDataSource(DataSourceRequestVo requestVo) throws Exception;


    /**
     * 更新配置
     *
     * @param requestVo
     * @return
     */
    Integer updateDataSource(DataSourceRequestVo requestVo) throws Exception;


    /**
     * 查询配置
     *
     * @return
     */
    List<DataSourceDomain> queryDataSourceByPage(int page, int pageSize);


    /**
     * 根据ID查询
     *
     * @return
     */
    DataSourceDomain queryById(Integer id);

    /**
     * 启用停用TIDB任务
     *
     * @param id
     * @return
     */
    Integer updateEnableFlag(Integer id);


    Integer delDataSourceById(Integer id);


    /**
     * 查询配置
     *
     * @param type
     * @return
     */
    List<DataSourceDomain> queryDataSourceByType(Integer type);
}
