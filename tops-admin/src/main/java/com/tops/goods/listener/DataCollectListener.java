package com.tops.goods.listener;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.goods.domain.DataCollectDomain;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.goodsEnum.CollectTypeEnum;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.service.DataCollectService;
import com.tops.goods.service.DataMonitorService;
import com.tops.goods.service.DataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: DataCollectListener
 * @Description: 数据采集任务消费处理
 * @Auther: MaWenHua8
 * @Date: 2024/11/13
 * @version: V1.0
 */

@Slf4j
@Service("dataCollectListener")
public class DataCollectListener implements MessageListener {

    @Resource
    private DataMonitorService dataMonitorService;

    @Resource
    private DataSourceService dataSourceService;

    @Resource
    private DataCollectService dataCollectService;

    private static final String UMP_KEY = DataCollectListener.class.getSimpleName();

    @Override
    public void onMessage(List<Message> list) throws Exception {
        try {
            for (Message message : list) {
                DataCollectDomain collectDomain = JSON.parseObject(message.getText(), DataCollectDomain.class);
                if (collectDomain == null || collectDomain.getCollectDimension() == null) {
                    log.info("消息或者任务类型为空");
                    continue;
                }
                //执行
                if (!CollectTypeEnum.HOUR.getCode().equals(collectDomain.getCollectDimension())
                    || !CollectTypeEnum.DAY.getCode().equals(collectDomain.getCollectDimension())) {
                    log.info("无效的采集类型");
                }
                execCollect(collectDomain);
            }
        } catch (Exception e) {
            log.error("消费mq失败", e);
            throw e;
        }
    }


    /**
     * 执行数据重复任务
     *
     * @param collectDomain
     */
    private void execCollect(DataCollectDomain collectDomain) {
        //执行任务
        CallerInfo info = Profiler.registerInfo(UMP_KEY + ".execCollect");
        try {
            Integer taskId = collectDomain.getTaskId();
            DataMonitorTaskDomain taskDomain = dataMonitorService.queryById(taskId);
            if (ObjectUtils.isEmpty(taskDomain) || !TaskTypeEnum.DATA_SCALE.getCode().equals(taskDomain.getTaskType())) {
                log.info("该任务不存在或者任务类型不是数据量级");
                return;
            }

            DataSourceDomain dataSourceDomain = dataSourceService.queryById(taskDomain.getDataSourceId());
            if (ObjectUtils.isEmpty(dataSourceDomain)
                || StringUtils.isBlank(dataSourceDomain.getMysqlIp())
                || StringUtils.isBlank(dataSourceDomain.getMysqlUserName())
                || StringUtils.isBlank(dataSourceDomain.getMysqlPassword())) {
                log.info("taskNo:{}该任务数据源不存在", taskDomain.getTaskNo());
                return;
            }

            //数据查询
            Integer number = dataMonitorService.queryTotalNumber(dataSourceDomain, taskDomain);
            if (number == null) {
                log.info("taskNo:{}查询结果为空", taskDomain.getTaskNo());
            }


            DataCollectDomain dbCollectDomain = new DataCollectDomain();
            dbCollectDomain.setTaskId(collectDomain.getTaskId());
            dbCollectDomain.setResultValue(number);
            dbCollectDomain.setCollectDimension(collectDomain.getCollectDimension());
            dataCollectService.insertCollectData(dbCollectDomain);
        } catch (Exception e) {
            Profiler.functionError(info);
            log.info("采集任务执行异常", e);
        } finally {
            Profiler.registerInfoEnd(info);
        }
    }
}
