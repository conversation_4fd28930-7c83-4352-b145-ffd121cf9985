package com.tops.goods.listener;

import com.jd.fastjson.JSON;
import com.jd.jbdp.edc.model.enums.extract.FileSuffixEnum;
import com.jd.jbdp.edc.model.enums.extract.JobNoticeTypeEnum;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.constant.CacheNames;
import com.tops.common.utils.redis.CacheUtils;
import com.tops.goods.domain.DataMonitorTaskDomain;
import com.tops.goods.domain.DataSourceDomain;
import com.tops.goods.dto.BigDataRequestVo;
import com.tops.goods.goodsEnum.ScaleTypeEnum;
import com.tops.goods.goodsEnum.TaskTypeEnum;
import com.tops.goods.service.BigDataService;
import com.tops.goods.service.DataMonitorService;
import com.tops.goods.service.DataSourceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: DataMonitorTaskListener
 * @Description: 数据监控任务消费处理
 * @Auther: MaWenHua8
 * @Date: 2024/11/13
 * @version: V1.0
 */

@Slf4j
@Service("dataMonitorTaskListener")
public class DataMonitorTaskListener implements MessageListener {
    @Resource
    private BigDataService bigDataService;

    @Resource
    private DataMonitorService dataMonitorService;

    @Resource
    private DataSourceService dataSourceService;

    private static final String BIG_DATA_GOODS_APP_NAME = "jdl-trade-goods";

    private static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String YYYYMMDD = "yyyyMMdd";

    private static final String UMP_KEY = DataMonitorTaskListener.class.getSimpleName();

    @Override
    public void onMessage(List<Message> list) throws Exception {
        try {
            for (Message message : list) {
                DataMonitorTaskDomain dto = JSON.parseObject(message.getText(), DataMonitorTaskDomain.class);
                if (dto == null || dto.getTaskType() == null) {
                    log.info("消息或者任务类型为空");
                    continue;
                }

                //执行
                if (TaskTypeEnum.DATA_DUPLICATE.getCode().equals(dto.getTaskType())) {
                    execDuplicateTask(dto);
                } else if (TaskTypeEnum.DATA_SCALE.getCode().equals(dto.getTaskType())) {
                    execDataScaleTask(dto);
                } else {
                    log.info("无效的任务类型");
                }
            }
        } catch (Exception e) {
            log.error("消费mq失败", e);
            throw e;
        }
    }


    /**
     * 执行数据重复任务
     *
     * @param domain
     */
    private void execDuplicateTask(DataMonitorTaskDomain domain) {
        log.info("taskNo:{}数据重复任务执行开始=====>>>", domain.getTaskNo());
        //执行任务
        CallerInfo info = Profiler.registerInfo(UMP_KEY + ".execDuplicateTask");
        try {

            addTaskToBigData(domain);
            //更新执行时间
            updateLastExecTime(domain);
            log.info("taskNo:{}数据重复任务执行结束<=====", domain.getTaskNo());
        } catch (Exception e) {
            Profiler.functionError(info);
            log.info("taskNo:{}任务执行异常", domain.getTaskNo(), e);
        } finally {
            Profiler.registerInfoEnd(info);
        }
    }


    /**
     * 执行数据量级任务
     *
     * @param domain
     */
    private void execDataScaleTask(DataMonitorTaskDomain domain) {
        log.info("taskNo:{}数据量级任务执行开始=====>>>", domain.getTaskNo());
        //执行任务
        CallerInfo info = Profiler.registerInfo(UMP_KEY + ".execDataScaleTask");
        try {
            DataSourceDomain dataSourceDomain = dataSourceService.queryById(domain.getDataSourceId());
            if (dataSourceDomain == null
                || StringUtils.isBlank(dataSourceDomain.getMysqlIp())
                || StringUtils.isBlank(dataSourceDomain.getMysqlUserName())
                || StringUtils.isBlank(dataSourceDomain.getMysqlPassword())) {
                log.info("taskNo:{}MySQL数据源为空", domain.getTaskNo());
                return;
            }
            String redisKey = domain.getTaskNo() + "_" + "lastNumber";

            Integer number = dataMonitorService.queryTotalNumber(dataSourceDomain, domain);
            Integer lastNumber = CacheUtils.get(CacheNames.DATA_SCALE_LAST_NUMBER, redisKey);


            //固定值
            if (ScaleTypeEnum.UPPER_LIMIT.getCode().equals(domain.getScaleType()) && domain.getUpperLimit() != null) {
                log.info("taskNo:{}-当前值:[{}],固定值:[{}]", domain.getTaskNo(), number, domain.getUpperLimit());
                if (number == null) {
                }

                //当前值不为空
                if (number > domain.getUpperLimit()) {
                    String umpResult = "实例:" + dataSourceDomain.getMysqlIp() + "当前数据量:" + number + ",超过" + "固定值：" + domain.getUpperLimit()
                        + "，任务编码:" + domain.getTaskNo() + "。请关注!";
                    Profiler.businessAlarm(domain.getBusinessAlarmKey(), umpResult);
                }
            }

            //固定值+比率（查出来的值是否在固定值比率之内）
            if (ScaleTypeEnum.UPPER_LIMIT_AND_DIFF_RATIO.getCode().equals(domain.getScaleType()) && domain.getDiffRatio() != null && domain.getUpperLimit() != null) {
                log.info("taskNo:{}-当前值:[{}]，固定值:[{}]，配置比率:[{}]", domain.getTaskNo(), number, domain.getUpperLimit(), domain.getDiffRatio());
                //本次和上次都为空，则只更新时间
                if (number == null) {
                }


                if (isOverDiffRatio(number, domain.getUpperLimit(), domain.getDiffRatio())) {
                    String umpResult = "实例:" + dataSourceDomain.getMysqlIp() + "当前数据量:" + number + ",与" + "固定值：" + domain.getUpperLimit()
                        + "差异比率超过" + domain.getDiffRatio() + "%，任务编码:" + domain.getTaskNo() + "。请关注!";
                    Profiler.businessAlarm(domain.getBusinessAlarmKey(), umpResult);
                }
            }

            //比率（查出来的值是比上次的值变化超过比率）
            if (ScaleTypeEnum.DIFF_RATIO.getCode().equals(domain.getScaleType()) && domain.getDiffRatio() != null) {
                log.info("taskNo:{}-当前值:[{}]，上次值:[{}]，配置比率:[{}]", domain.getTaskNo(), number, lastNumber, domain.getDiffRatio());
                //本次和上次都为空，则只更新时间
                if (number == null && lastNumber == null) {
                }

                //本次不为空，上次为空，则将本次值放到Redis
                if (number != null && lastNumber == null) {
                    CacheUtils.put(CacheNames.DATA_SCALE_LAST_NUMBER, redisKey, number);
                }

                //本次为空，上次不为空，则保留上次值
                if (number == null && lastNumber != null) {
                    CacheUtils.put(CacheNames.DATA_SCALE_LAST_NUMBER, redisKey, lastNumber);
                }

                //本次不为空，上次不为空
                if (lastNumber != null && lastNumber != null) {
                    CacheUtils.put(CacheNames.DATA_SCALE_LAST_NUMBER, redisKey, number);
                    if (isOverDiffRatio(number, lastNumber, domain.getDiffRatio())) {
                        log.info("taskNo:{}超过异常比率:[{}]", domain.getTaskNo(), domain.getDiffRatio());
                        String umpResult = "实例:" + dataSourceDomain.getMysqlIp() + "当前数据量:" + number + ",与" + "上次值：" + lastNumber
                            + "差异比率超过" + domain.getDiffRatio() + "%，任务编码:" + domain.getTaskNo() + "。请关注!";
                        Profiler.businessAlarm(domain.getBusinessAlarmKey(), umpResult);
                    }
                }

            }

            //更新执行时间
            updateLastExecTime(domain);
            log.info("taskNo:{}数据量级任务执行结束<=====", domain.getTaskNo());
        } catch (Exception e) {
            Profiler.functionError(info);
            log.info("taskNo:{}任务执行异常", domain.getTaskNo(), e);
        } finally {
            Profiler.registerInfoEnd(info);
        }
    }


    /**
     * 添加任务到大数据
     *
     * @param domain
     */
    private void addTaskToBigData(DataMonitorTaskDomain domain) {
        try {
            DataSourceDomain dataSourceDomain = dataSourceService.queryById(domain.getDataSourceId());
            if (dataSourceDomain == null
                || StringUtils.isBlank(dataSourceDomain.getRunMarketCode())
                || StringUtils.isBlank(dataSourceDomain.getQueueCode())
                || StringUtils.isBlank(dataSourceDomain.getAccountCode())
                || StringUtils.isBlank(dataSourceDomain.getRunLogicClusterCode())
                || StringUtils.isBlank(dataSourceDomain.getSharedPersons())) {
                log.info("taskNo:{}MySQL数据源为空", domain.getTaskNo());
                return;
            }
            SimpleDateFormat formatter = new SimpleDateFormat(YYYYMMDD);
            BigDataRequestVo requestVo = new BigDataRequestVo();
            requestVo.setOperator(domain.getCreateUser());
            requestVo.setJobName(domain.getTaskName());
            requestVo.setGroupName(null);
            requestVo.setDescription(domain.getTaskName());
            requestVo.setTargetFileName(domain.getCreateUser() + "_" + formatter.format(new Date()) + domain.getTaskName());
            requestVo.setTargetType(3);
            requestVo.setTargetFileType(FileSuffixEnum.Csv.toName());
            requestVo.setSharedPersons(dataSourceDomain.getSharedPersons());
            String sqlContent = CacheUtils.get(CacheNames.SYS_SQL, domain.getSqlKey());
            if (StringUtils.isBlank(sqlContent)) {
                log.info("taskNo:{}执行SQL为空，跳过");
                return;
            }
            requestVo.setContent(sqlContent);

            //大数据集市及队列信息
            requestVo.setDbName(dataSourceDomain.getDbName());
            requestVo.setRunMarketCode(dataSourceDomain.getRunMarketCode());
            requestVo.setQueueCode(dataSourceDomain.getQueueCode());
            requestVo.setAccountCode(dataSourceDomain.getAccountCode());
            requestVo.setRunLogicClusterCode(dataSourceDomain.getRunLogicClusterCode());
            requestVo.setNoticeType(String.valueOf(JobNoticeTypeEnum.MAIL.toCode()));
            requestVo.setProjectId(null);
            requestVo.setBizCode(BIG_DATA_GOODS_APP_NAME);

            bigDataService.saveTask(domain.getTaskNo(), requestVo);
        } catch (Exception e) {
            log.info("taskNo:{}新增大数据任务异常", domain.getTaskNo(), e);
            throw e;
        }
    }

    /**
     * 更新执行时间
     *
     * @param domain
     */
    private void updateLastExecTime(DataMonitorTaskDomain domain) {
        try {
            dataMonitorService.updateLastExecutionTime(domain.getId());
        } catch (Exception e) {
            log.error("taskNo:{}更新执行时间异常", domain.getTaskNo(), e);
            throw e;
        }
    }


    /**
     * 计算差异比率
     *
     * @param number
     * @param lastNumber
     * @param diffRatio
     * @return
     */
    private boolean isOverDiffRatio(Integer number, Integer lastNumber, BigDecimal diffRatio) {
        if (number == null || lastNumber == null) {
            throw new IllegalArgumentException("Numbers cannot be null");
        }
        if (number.intValue() == lastNumber.intValue()) {
            return false;
        }

        // 计算最大值作为分母
        int maxNumber = Math.max(number, lastNumber);

        if (maxNumber == 0) {
            return true; // 如果大的那个数为0，直接返回true
        }

        // 计算差异
        int difference = Math.abs(lastNumber - number);

        // 计算实际差异比率
        BigDecimal actualDiffRatio = BigDecimal.valueOf(difference)
            .divide(BigDecimal.valueOf(maxNumber), 4, BigDecimal.ROUND_HALF_UP);

        // 将diffRatio转换为百分数形式进行比较
        BigDecimal diffRatioAsPercentage = diffRatio.divide(BigDecimal.valueOf(100));

        log.info("number:[{}]-lastNum:[{}]-差异比率:[{}]", number, lastNumber, actualDiffRatio);
        // 比较实际差异比率和指定的差异比率
        return actualDiffRatio.compareTo(diffRatioAsPercentage) > 0;
    }
}
