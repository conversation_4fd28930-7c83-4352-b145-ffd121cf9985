package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;


/**
 * @ClassName: BigDataRequestVo
 * @Description: 大数据临时任务创建请求类
 * @Auther: MaWenHua8
 * @Date: 2024/11/4
 * @version: V1.0
 */
@Data
public class BigDataRequestVo implements Serializable {
    private String operator;
    private String jobName;
    private String groupName;
    private String description;
    private String targetFileName;
    private String targetFileType;
    private Integer targetType;
    private String sharedPersons;
    private String content;
    private String dbName;
    private String runMarketCode;
    private String queueCode;
    private String accountCode;
    private String runLogicClusterCode;
    private String noticeType;
    private Long projectId;
    private String bizCode;
}
