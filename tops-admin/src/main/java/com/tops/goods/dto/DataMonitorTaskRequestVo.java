package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName: DataMonitorTaskRequestVo
 * @Description: 创建任务请求类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataMonitorTaskRequestVo implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 数据源ID
     */
    private Integer dataSourceId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * SQL 的KEY
     */
    private String sqlKey;

    /**
     * SQL
     */
    private String sqlContent;

    /**
     * 执行频率 cron表达式
     */
    private String execFrequency;

    /**
     * 自定义告警KEY
     */
    private String businessAlarmKey;


    /**
     * 量级类型
     */
    private Integer scaleType;

    /**
     * 固定值上限
     */
    private Integer upperLimit;

    /**
     * 差异比率
     */
    private BigDecimal diffRatio;

    /**
     * 任务授权
     */
    private String sharedOperateUser;
}
