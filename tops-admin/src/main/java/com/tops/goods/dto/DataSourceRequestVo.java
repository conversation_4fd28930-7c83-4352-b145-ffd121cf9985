package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: DataSourceRequestVo
 * @Description: 创建数据源配置请求类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataSourceRequestVo implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 配置类型 1:大数据，2：MySQL com.tops.goods.goodsEnum.ConfigTypeEnum
     */
    private Integer configType;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 集市code
     */
    private String runMarketCode;

    /**
     * queueCode
     */
    private String queueCode;

    /**
     * 生产账号code
     */
    private String accountCode;

    /**
     * 队列所属的逻辑计算集群
     */
    private String runLogicClusterCode;

    /**
     * 收件共享人列表, 邮箱,多个分号分割
     */
    private String sharedPersons;

    /**
     * MySQL实例IP
     */
    private String mysqlIp;

    /**
     * MySQL实例用户名 AES加解密
     */
    private String mysqlUserName;

    /**
     * MySQL实例密码 AES加解密
     */
    private String mysqlPassword;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 协同操作人
     */
    private String coOperateUser;
}
