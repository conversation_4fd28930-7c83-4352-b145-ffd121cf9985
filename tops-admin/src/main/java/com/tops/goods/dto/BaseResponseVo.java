package com.tops.goods.dto;

import com.tops.goods.domain.DataMonitorTaskDomain;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: BaseResponseVo
 * @Description:
 * @Auther: MaWenHua8
 * @Date: 2024/11/7
 * @version: V1.0
 */
@Data
public class BaseResponseVo implements Serializable {
    //成功
    public static final int SUCCESS_CODE = 200;
    //异常
    public static final int ERROR_CODE = -1;
    //参数校验未通过CODE
    public static final int PARAM_ERROR = 400;

    /**
     * 返回码，用户可自己定义返回码
     */
    private int code;

    /**
     * 错误消息
     */
    private String msg;


    public BaseResponseVo(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BaseResponseVo() {
    }

    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static BaseResponseVo paramError(String msg) {
        return new BaseResponseVo(PARAM_ERROR, msg);
    }

    /**
     * 系统异常
     *
     * @param msg
     * @return
     */
    public static BaseResponseVo systemError(String msg) {
        return new BaseResponseVo(ERROR_CODE, msg);
    }

    /**
     * 成功
     *
     * @return
     */
    public static BaseResponseVo success() {
        return new BaseResponseVo(SUCCESS_CODE, "成功");
    }
}
