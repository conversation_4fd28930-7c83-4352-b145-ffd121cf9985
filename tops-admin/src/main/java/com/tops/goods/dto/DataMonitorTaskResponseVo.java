package com.tops.goods.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

import java.io.Serializable;

/**
 * @ClassName: DataMonitorTaskResponseVo
 * @Description: 响应类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataMonitorTaskResponseVo extends BaseResponseVo implements Serializable {
    List<DataMonitorTaskVo> dataList;
    int total;
    int pageCount;

    public DataMonitorTaskResponseVo(int code, String msg, List<DataMonitorTaskVo> dataList, int total, int pageCount) {
        super.setCode(code);
        super.setMsg(msg);
        this.dataList = dataList;
        this.total = total;
        this.pageCount = pageCount;
    }


    /**
     * 成功
     *
     * @return
     */
    public static DataMonitorTaskResponseVo success(List<DataMonitorTaskVo> dataList, int total, int pageCount) {
        return new DataMonitorTaskResponseVo(SUCCESS_CODE, "成功", dataList, total, pageCount);
    }


    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static DataMonitorTaskResponseVo systemError(String msg) {
        return new DataMonitorTaskResponseVo(PARAM_ERROR, msg, new ArrayList<DataMonitorTaskVo>(), 0, 0);
    }
}
