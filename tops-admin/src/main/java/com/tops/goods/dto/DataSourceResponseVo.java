package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataSourceResponseVo
 * @Description: 响应类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataSourceResponseVo extends BaseResponseVo implements Serializable {
    List<DataSourceVo> dataList;
    int total;
    int pageCount;

    public DataSourceResponseVo(int code, String msg, List<DataSourceVo> dataList, int total, int pageCount) {
        super.setCode(code);
        super.setMsg(msg);
        this.dataList = dataList;
        this.total = total;
        this.pageCount = pageCount;
    }


    /**
     * 成功
     *
     * @return
     */
    public static DataSourceResponseVo success(List<DataSourceVo> dataList, int total, int pageCount) {
        return new DataSourceResponseVo(SUCCESS_CODE, "成功", dataList, total, pageCount);
    }


    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static DataSourceResponseVo systemError(String msg) {
        return new DataSourceResponseVo(PARAM_ERROR, msg, new ArrayList<DataSourceVo>(), 0, 0);
    }


    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static DataSourceResponseVo paramError(String msg) {
        return new DataSourceResponseVo(PARAM_ERROR, msg, new ArrayList<DataSourceVo>(), 0, 0);
    }
}
