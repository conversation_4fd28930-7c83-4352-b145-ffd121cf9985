package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: DataCollectRequestVo
 * @Description: 采集数据请求类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataCollectRequestVo implements Serializable {
    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 数据源ID
     * 1:getTodayHours
     * 2:getLastFiveHours
     * 3:getLastTenHours
     * 4:getLast24Hours
     * 5:getLastThreeDayHours
     * 6:getLastFiveDays
     * 7:getLastSevenDays
     * 8:getLast15Days
     */
    private Integer queryType;
}
