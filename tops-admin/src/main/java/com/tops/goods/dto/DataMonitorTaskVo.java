package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName: DataMonitorTaskVo
 * @Description: 对外展示任务实体
 * @Auther: MaWenHua8
 * @Date: 2024/11/8
 * @version: V1.0
 */
@Data
public class DataMonitorTaskVo implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 数据源ID
     */
    private Integer dataSourceId;

    /**
     * 任务编码
     */
    private String taskNo;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * SQL 的KEY
     */
    private String sqlKey;

    /**
     * SQL
     */
    private String sqlContent;

    /**
     * 执行频率 cron表达式
     */
    private String execFrequency;

    /**
     * 自定义告警KEY
     */
    private String businessAlarmKey;

    /**
     * 量级类型 com.tops.goods.goodsEnum.ScaleTypeEnum
     */
    private Integer scaleType;

    /**
     * 固定值上限
     */
    private Integer upperLimit;

    /**
     * 差异比率
     */
    private BigDecimal diffRatio;

    /**
     * 任务授权
     */
    private String sharedOperateUser;

    /**
     * 最近一次执行时间
     */
    private String lastExecutionTime;

    /**
     * 1:启用 2:禁用
     */
    private Integer enableFlag;

    private String createUser;
    private String updateUser;
    private String createTime;
    private String updateTime;
    /**
     * 1:有效  0:删除
     */
    private Integer yn;
}
