package com.tops.goods.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: DataCollectResponseVo
 * @Description: 响应类
 * @Auther: MaWenHua8
 * @Date: 2024/11/6
 * @version: V1.0
 */
@Data
public class DataCollectResponseVo extends BaseResponseVo implements Serializable {
    List<DataCollectVo> dataList;

    public DataCollectResponseVo(int code, String msg, List<DataCollectVo> dataList) {
        super.setCode(code);
        super.setMsg(msg);
        this.dataList = dataList;
    }


    /**
     * 成功
     *
     * @return
     */
    public static DataCollectResponseVo success(List<DataCollectVo> dataList) {
        return new DataCollectResponseVo(SUCCESS_CODE, "成功", dataList);
    }


    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static DataCollectResponseVo systemError(String msg) {
        return new DataCollectResponseVo(PARAM_ERROR, msg, new ArrayList<>());
    }


    /**
     * 参数有误
     *
     * @param msg
     * @return
     */
    public static DataCollectResponseVo paramError(String msg) {
        return new DataCollectResponseVo(PARAM_ERROR, msg, new ArrayList<>());
    }
}
