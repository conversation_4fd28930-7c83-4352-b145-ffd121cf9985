package com.tops.goods.condition;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: QueryCondition
 * @Description:
 * @Auther: MaWenHua8
 * @Date: 2024/11/21
 * @version: V1.0
 */
@Data
public class QueryCondition implements Serializable {
    /**
     * 数据源
     */
    private Integer dataSourceId;
    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 是否启用  1：启用   2:禁用
     */
    private Integer enableFlag;

    /**
     * 是否删除 1：未删除  0:删除
     */
    private Integer yn;
}
