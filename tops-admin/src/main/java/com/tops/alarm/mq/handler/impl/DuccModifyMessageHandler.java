package com.tops.alarm.mq.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tops.alarm.bean.dto.DuccModifyApprovalContent;
import com.tops.alarm.bean.dto.ShutdownApprovalContent;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.audit.service.ApprovalService;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.ducc.adapter.DUCCAdapter;
import com.tops.ducc.bean.dto.DUCCItemMetaInfo;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.jdos.domain.ApplicationInfo;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;
import com.tops.jdos.service.JDOSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("duccModifyMessageHandler")
public class DuccModifyMessageHandler implements XbpMessageHandler {
    private final static String SHUTDOWN_FAIL_CODE = "fail";
    private final static String SHUTDOWN_SUCCESS_CODE = "success";
    @Value("${xbp.custom.processIds.duccModify}")
    private Integer handledProcessId;
    @Autowired
    private DUCCAdapter duccAdapter;
    @Autowired
    private TicketServiceAdapter ticketServiceAdapter;

    @Autowired
    @Qualifier("xbpApprovalService")
    private ApprovalService xbpApprovalService;

    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    @Autowired
    private TopsEnvUtils topsEnvUtils;

    @Override
    public boolean match(Integer processId) {
        if (processId == null) {
            return false;
        }
        return Objects.equals(handledProcessId, processId);
    }

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (instanceId == null) {
            log.warn("ShutdownMessageHandler.handle 流程实例Id为null，忽略该回调");
            return;
        }
        //流程结束回调
        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            //TODO wangqin83 更新本地审批记录状态：状态变更为开始处理
            xbpApprovalService.updateApprovalStatus();
            //TODO wangqin83 按照实际业务逻辑处理请求，并收集处理结果
            //查询当前要处理的订单
            //TODO wangqin83 针对查询审批实例 增加异常处理
            ApprovalDTO approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());
            List<DuccModifyApprovalContent> approvalContent = approvalDTO.parseInstanceContent(new TypeReference<List<DuccModifyApprovalContent>>() {
            });
            Map<DuccModifyApprovalContent, String>  resultMap=new LinkedHashMap<>();
            for (DuccModifyApprovalContent duccModifyApprovalContent : approvalContent) {
                DUCCItemMetaInfo metaInfo = new DUCCItemMetaInfo();
                metaInfo.setApplication(duccModifyApprovalContent.getApplication());
                metaInfo.setNamespace(duccModifyApprovalContent.getNamespace());
                metaInfo.setConfig(duccModifyApprovalContent.getConfig());
                metaInfo.setProfile(duccModifyApprovalContent.getProfile());
                metaInfo.setKey(duccModifyApprovalContent.getKey());
                String responseCode = duccAdapter.updateDucc(metaInfo, duccModifyApprovalContent.getNewValue());
                if (Objects.equals(responseCode,DUCCAdapter.RESPONSE_CODE_SUCCESS)){
                    resultMap.put(duccModifyApprovalContent,"变更成功");
                }else if (Objects.equals(responseCode,DUCCAdapter.RESPONSE_CODE_PARAMS_INVALID)){
                    resultMap.put(duccModifyApprovalContent,"变更失败：参数非法");
                }else if (Objects.equals(responseCode,DUCCAdapter.RESPONSE_CODE_UPDATE_FAIL)){
                    resultMap.put(duccModifyApprovalContent,"变更失败：更新DUCC失败");
                }else if (Objects.equals(responseCode,DUCCAdapter.RESPONSE_CODE_RELEASE_FAIL)){
                    resultMap.put(duccModifyApprovalContent,"变更失败：发布DUCC失败。DUCC已更新但未发布，请注意恢复DUCC更新");
                }
            }

            ApprovalContext context = new ApprovalContext()
                .withProcessInstanceId(instanceId.toString())
                .withPin("wangqin83")
                .withComment(getComment(resultMap));
            ticketServiceAdapter.comment(context);
            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalDTO),
                    getDDMsgTitle(),
                    getDDMsgContent(),
                    getDetailURL(instanceId.toString()));
            }
        }
        //TODO wangqin83 更新本地审批记录状态：状态变更为处理完成
        xbpApprovalService.updateApprovalStatus();
    }

    public String getComment(Map<DuccModifyApprovalContent,String> result) {
        TopsHtmlUtils.HtmlStringBuilder htmlStringBuilder = new TopsHtmlUtils.HtmlStringBuilder()
            .startTable();
        htmlStringBuilder.startRow()
            .startCol()
            .append("应用名称")
            .endCol()
            .startCol()
            .append("命名空间")
            .endCol()
            .startCol()
            .append("配置")
            .endCol()
            .startCol()
            .append("环境")
            .endCol()
            .startCol()
            .append("配置项")
            .endCol()
            .startCol()
            .append("预期更新后的值")
            .endCol()
            .startCol()
            .append("更新结果")
            .endCol()
            .endRow();
        if (!CollectionUtils.isEmpty(result)) {
            for (DuccModifyApprovalContent content : result.keySet()) {
                htmlStringBuilder.startCol()
                    .append(content.getApplication())
                    .endCol()
                    .startCol()
                    .append(content.getNamespace())
                    .endCol()
                    .startCol()
                    .append(content.getConfig())
                    .endCol()
                    .startCol()
                    .append(content.getProfile())
                    .endCol()
                    .startCol()
                    .append(content.getKey())
                    .endCol()
                    .startCol()
                    .append(content.getNewValue())
                    .endCol()
                    .startCol()
                    .append(result.get(content))
                    .endCol()
                    .endRow();
            }
        }
        htmlStringBuilder.endTable();
        return htmlStringBuilder.toString();
    }

    private String getDDMsgTitle() {
        StringBuilder content = new StringBuilder();
        content.append(topsEnvUtils.getEnvDesc());
        content.append("：DUCC降级预案执行结果通知");
        return content.toString();
    }

    private String getDDMsgContent() {
        StringBuilder content = new StringBuilder();
        content.append("DUCC降级预案执行结果通知：执行结束，变更结果详见XBP审批工单");
        return content.toString();
    }

    private String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }

    private String getDongDongReceiver(ApprovalDTO approvalDTO) {
        StringBuilder receiver = new StringBuilder(approvalDTO.getCreateUser()).append(",weiliming,lixiaoliang16,wangqin83,zhuhongru1");
        return receiver.toString();
    }


}
