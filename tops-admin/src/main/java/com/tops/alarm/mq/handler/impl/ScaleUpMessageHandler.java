package com.tops.alarm.mq.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tops.alarm.bean.dto.ScaleUpApprovalContent;
import com.tops.alarm.bean.dto.ShutdownApprovalContent;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.audit.service.ApprovalService;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;
import com.tops.jdos.service.JDOSService;
import com.tops.order.domain.dto.CancelOrderApprovalContent;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.dto.CancelOrderResult;
import com.tops.order.enums.CancelOrderResultEnum;
import com.tops.order.enums.CancelOrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Component("scaleUpMessageHandler")
public class ScaleUpMessageHandler implements XbpMessageHandler {
    private final static String SCALE_UP_FAIL_CODE = "fail";
    private final static String SCALE_UP_SUCCESS_CODE = "success";

    @Value("${xbp.custom.processIds.scaleUp}")
    private Integer handledProcessId;
    @Autowired
    private JDOSService jdosService;
    @Autowired
    private TicketServiceAdapter ticketServiceAdapter;

    @Autowired
    @Qualifier("xbpApprovalService")
    private ApprovalService xbpApprovalService;

    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    @Autowired
    private TopsEnvUtils topsEnvUtils;

    @Override
    public boolean match(Integer processId) {
        if (processId == null) {
            return false;
        }
        return Objects.equals(handledProcessId, processId);
    }

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (instanceId == null) {
            log.warn("ScaleUpMessageHandler.handle 流程实例Id为null，忽略该回调");
            return;
        }
        //流程结束回调
        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            //TODO wangqin83 更新本地审批记录状态：状态变更为开始处理
            xbpApprovalService.updateApprovalStatus();
            //TODO wangqin83 按照实际业务逻辑处理请求，并收集处理结果
            //查询当前要处理的订单
            //TODO wangqin83 针对查询审批实例 增加异常处理
            ApprovalDTO approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());
            ScaleUpApprovalContent approvalContent = approvalDTO.parseInstanceContent(new TypeReference<ScaleUpApprovalContent>() {
            });
            String result = SCALE_UP_SUCCESS_CODE;
            try {
                GroupInfo group = new GroupInfo();
                group.setName(approvalContent.getGroup());
                jdosService.scaleUpGroup(group, approvalContent.getScaleUpCount(), approvalContent.getAppName());
            } catch (InvalidRequestException e) {
                result = SCALE_UP_FAIL_CODE;
                log.warn("ScaleUpMessageHandler.handle 扩容分组:{}失败，参数异常：{}，e:", approvalContent.getGroup(), e.getMsg(), e);
            } catch (InternalFailureException e) {
                result = SCALE_UP_FAIL_CODE;
                log.error("ScaleUpMessageHandler.handle 扩容分组:{}失败，内部异常：{}，e:", approvalContent.getGroup(), e.getMsg(), e);
            } catch (DependencyFailureException e) {
                result = SCALE_UP_FAIL_CODE;
                log.error("ScaleUpMessageHandler.handle 扩容分组:{}失败，外部依赖异常：{}，e:", approvalContent.getGroup(), e.getMsg(), e);
            } catch (Exception e) {
                result = SCALE_UP_FAIL_CODE;
                log.error("ScaleUpMessageHandler.handle 扩容分组:{}失败，未知异常，e:", approvalContent.getGroup(), e);
            }
            ApprovalContext context = new ApprovalContext()
                .withProcessInstanceId(instanceId.toString())
                .withPin("wangqin83")
                .withComment(getComment(approvalContent.getAppName(), approvalContent.getGroup(), result));
            ticketServiceAdapter.comment(context);
            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalDTO),
                    getDDMsgTitle(),
                    getDDMsgContent(approvalContent.getReason(), approvalContent.getAppName(), approvalContent.getGroup(), result, approvalContent.getIpList().size()),
                    getDetailURL(instanceId.toString()));
            }
        }
        //TODO wangqin83 更新本地审批记录状态：状态变更为处理完成
        xbpApprovalService.updateApprovalStatus();
    }

    public String getComment(String appName, String groupName, String result) {
        TopsHtmlUtils.HtmlStringBuilder htmlStringBuilder = new TopsHtmlUtils.HtmlStringBuilder()
            .startTable();
        htmlStringBuilder.startRow()
            .startCol()
            .append("应用名称")
            .endCol()
            .startCol()
            .append("分组名称")
            .endCol()
            .startCol()
            .append("处理结果")
            .endCol()
            .endRow();
        htmlStringBuilder.startCol()
            .append(appName)
            .endCol()
            .startCol()
            .append(groupName)
            .endCol()
            .startCol()
            .append(result)
            .endCol()
            .endRow();
        htmlStringBuilder.endTable();
        return htmlStringBuilder.toString();
    }

    private String getDDMsgTitle() {
        StringBuilder content = new StringBuilder();
        content.append(topsEnvUtils.getEnvDesc());
        content.append("：关停容器结果通知");
        return content.toString();
    }

    private String getDDMsgContent(String reason, String appName, String groupName, String result, Integer scaleUpCount) {
        String resultDesc = "";
        if (Objects.equals(result, SCALE_UP_FAIL_CODE)) {
            resultDesc = "扩容失败";
        } else if (Objects.equals(result, SCALE_UP_SUCCESS_CODE)) {
            resultDesc = "扩容成功";
        }
        StringBuilder content = new StringBuilder();
        content.append("扩容分组结果通知：处理完成，本次发起").append(groupName).append("分组，扩容").append(scaleUpCount).append("个容器：\n")
            .append("- ").append("处理结果：").append(resultDesc).append("：\n");
        content.append("扩容分组所属应用：").append(appName).append("。\n");
        content.append("扩容原因：").append(reason).append("。");
        return content.toString();
    }

    private String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }

    private String getDongDongReceiver(ApprovalDTO approvalDTO) {
        StringBuilder receiver = new StringBuilder(approvalDTO.getCreateUser()).append(",weiliming,lixiaoliang16,wangqin83,zhuhongru1");
        return receiver.toString();
    }
}
