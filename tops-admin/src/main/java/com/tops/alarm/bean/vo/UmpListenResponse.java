package com.tops.alarm.bean.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UmpListenResponse {
    private String code;
    private String msg;

    public static UmpListenResponse success(){
        UmpListenResponse umpListenResponse = new UmpListenResponse();
        umpListenResponse.setCode("0");
        umpListenResponse.setMsg("调用成功");
        return umpListenResponse;
    }

    public static UmpListenResponse fail(){
        UmpListenResponse umpListenResponse = new UmpListenResponse();
        umpListenResponse.setCode("1");
        umpListenResponse.setMsg("调用失败");
        return umpListenResponse;
    }

    public static UmpListenResponse fail(String msg){
        UmpListenResponse umpListenResponse = new UmpListenResponse();
        umpListenResponse.setCode("1");
        umpListenResponse.setMsg(msg);
        return umpListenResponse;
    }
}
