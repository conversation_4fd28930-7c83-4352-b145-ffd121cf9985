package com.tops.alarm.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class DuccModifyApprovalContent {
    /**
     * 应用
     */
    private String application;
    /**
     * 命名空间
     */
    private String namespace;
    /**
     * 配置
     */
    private String config;
    /**
     * 环境
     */
    private String profile;
    /**
     * 配置项key
     */
    private String key;
    /**
     * 更新后的值
     */
    private String newValue;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DuccModifyApprovalContent content = (DuccModifyApprovalContent) o;
        return Objects.equals(application, content.application) && Objects.equals(namespace, content.namespace) && Objects.equals(config, content.config) && Objects.equals(profile, content.profile) && Objects.equals(key, content.key) && Objects.equals(newValue, content.newValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(application, namespace, config, profile, key, newValue);
    }
}
