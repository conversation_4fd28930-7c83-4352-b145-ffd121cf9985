package com.tops.alarm.bean.vo;


import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 支撑应急预案的逻辑表达能力
 */
@Data
public class AutoBotsOpsDTO {
    /**
     * 报警时间
     */
    private Date alarmTime;

    /**
     * 需要判断的ump指标
     */
    private List<AutoBotsOpsItemDTO> itemList;

    /**
     * must/should, 默认为must
     */
    private String matchType;

    /**
     * app name
     */
    private String appName;
}
