package com.tops.alarm.bean.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警处理上下文
 */
@Getter
@Setter
public class AlarmHandleContext {
    /**
     * 请求元数据
     */
    private RequestMeta requestMeta;
    /**
     * 平台域
     */
    private PlatformInfo platform;
    /**
     * 应用域
     */
    private ApplicationInfo applicationInfo;
    /**
     * 指标域
     */
    private MetricInfo metricInfo;
    /**
     * 告警域
     */
    private AlarmInfo alarmInfo;
    /**
     * 联系人域
     */
    private ContactInfo contactInfo;


    /**
     * 补充平台编码
     */
    public AlarmHandleContext withTraceId(String traceId) {
        if (this.requestMeta == null) {
            this.requestMeta = new RequestMeta();
        }
        this.requestMeta.setTraceId(traceId);
        return this;
    }

    /**
     * 补充平台编码
     */
    public AlarmHandleContext withPlatformNo(String platformNo) {
        if (this.platform == null) {
            this.platform = new PlatformInfo();
        }
        this.platform.setPlatformNo(platformNo);
        return this;
    }

    /**
     * 补充应用英文名
     */
    public AlarmHandleContext withAppName(String appName) {
        if (this.applicationInfo == null) {
            this.applicationInfo = new ApplicationInfo();
        }
        this.applicationInfo.setAppName(appName);
        return this;
    }

    /**
     * 补充应用英文名
     */
    public AlarmHandleContext withGroupNames(List<String> groupNames) {
        if (this.applicationInfo == null) {
            this.applicationInfo = new ApplicationInfo();
        }
        if (this.applicationInfo.getGroups() == null) {
            this.applicationInfo.setGroups(new ArrayList<>());
        }
        List<GroupInfo> nowGroups = this.applicationInfo.getGroups();
        nowGroups.addAll(groupNames.stream().map(name -> {
            GroupInfo groupInfo = new GroupInfo();
            groupInfo.setName(name);
            return groupInfo;
        }).collect(Collectors.toList()));
        return this;
    }

    /**
     * 补充应用中文名
     */
    public AlarmHandleContext withAppAlias(String appAlias) {
        if (this.applicationInfo == null) {
            this.applicationInfo = new ApplicationInfo();
        }
        this.applicationInfo.setAppAlias(appAlias);
        return this;
    }

    /**
     * 补充指标打点Key
     */
    public AlarmHandleContext withEndPoint(String endPoint) {
        if (this.metricInfo == null) {
            this.metricInfo = new MetricInfo();
        }
        this.metricInfo.setEndpoint(endPoint);
        return this;
    }

    /**
     * 补充指标打点Key
     */
    public AlarmHandleContext withAlarmMetric(String alarmMetric) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setAlarmMetric(alarmMetric);
        return this;
    }

    /**
     * 补充告警指标当前值
     */
    public AlarmHandleContext withMetricCurrent(Double current) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setCurrent(current);
        return this;
    }

    /**
     * 补充告警指标告警阈值
     */
    public AlarmHandleContext withMetricThreshold(Double threshold) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setThresholdValue(threshold);
        return this;
    }

    /**
     * 补充指标打点Key
     */
    public AlarmHandleContext withLevel(String level) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setLevel(level);
        return this;
    }
    /**
     * 补充指标打点Key
     */
    public AlarmHandleContext withStartTime(Date startTime) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setStartTime(startTime);
        return this;
    }
    /**
     * 补充指标打点Key
     */
    public AlarmHandleContext withEndTime(Date endTime) {
        if (this.alarmInfo == null) {
            this.alarmInfo = new AlarmInfo();
        }
        this.alarmInfo.setEndTime(endTime);
        return this;
    }

    /**
     * 补充联系人ERP信息
     */
    public AlarmHandleContext withErps(List<String> erps) {
        if (this.contactInfo == null) {
            this.contactInfo = new ContactInfo();
        }
        this.contactInfo.setErps(erps);
        return this;
    }

    public String queryTraceId() {
        if (this.requestMeta == null) {
            return null;
        }
        return this.requestMeta.getTraceId();
    }

    public String queryPlatformNo() {
        if (this.platform == null) {
            return null;
        }
        return this.platform.getPlatformNo();
    }

    public String queryAppName() {
        if (this.applicationInfo == null) {
            return null;
        }
        return this.applicationInfo.getAppName();
    }

    public String queryEndpoint() {
        if (this.metricInfo == null) {
            return null;
        }
        return this.metricInfo.getEndpoint();
    }

    public String queryAlarmMetric() {
        if (this.alarmInfo == null) {
            return null;
        }
        return this.alarmInfo.getAlarmMetric();
    }

    public String queryAlarmLevel() {
        if (this.alarmInfo == null) {
            return null;
        }
        return this.alarmInfo.getLevel();
    }

    public Double queryMetricCurrent() {
        if (this.alarmInfo == null) {
            return null;
        }
        return this.alarmInfo.getCurrent();
    }

    public List<String> queryContactErps() {
        if (this.contactInfo == null) {
            return null;
        }
        return this.contactInfo.getErps();
    }

    public List<String> queryGroups() {
        if (this.applicationInfo == null) {
            return new ArrayList<>();
        }
        if (this.applicationInfo.getGroups() == null) {
            return new ArrayList<>();
        }
        return this.applicationInfo.getGroups().stream().map(GroupInfo::getName).collect(Collectors.toList());
    }
}
