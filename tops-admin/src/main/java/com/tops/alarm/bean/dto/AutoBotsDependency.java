package com.tops.alarm.bean.dto;

import com.tops.alarm.service.ExceptionHandleService;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.service.AnalysisFlowService;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.jdos.service.EasyOpsService;
import com.tops.jdos.service.JDOSService;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AutoBotsDependency {
    private AutoBotsServiceAdapter autoBotsServiceAdapter;
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    private JDOSService jdosService;
    private EasyOpsService easyOpsService;
    private ExceptionHandleService exceptionHandleService;
    private AnalysisFlowService analysisFlowService;
}
