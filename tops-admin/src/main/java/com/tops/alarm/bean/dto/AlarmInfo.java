package com.tops.alarm.bean.dto;

import com.tops.alarm.enums.AlarmLevelEnum;
import com.tops.alarm.enums.MetricTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class AlarmInfo implements Serializable {
    private static final long serialVersionUID = 5712906112731906956L;
    /**
     * 告警类型
     * @see MetricTypeEnum
     */
    private String alarmMetric;
    /**
     * 告警等级
     * @see AlarmLevelEnum
     */
    private String level;
    /**
     * 指标类型当前值
     */
    private Double current;
    /**
     * 告警阈值
     */
    private Double thresholdValue;

    private Date startTime;
    private Date endTime;
}
