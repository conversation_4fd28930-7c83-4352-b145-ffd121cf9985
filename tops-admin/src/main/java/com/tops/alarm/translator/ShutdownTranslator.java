package com.tops.alarm.translator;

import com.alibaba.fastjson.JSON;
import com.tops.alarm.bean.dto.ShutdownApprovalContent;
import com.tops.alarm.bean.dto.ShutdownRequest;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.dto.CallbackOrderApprovalContent;
import com.tops.order.domain.vo.CallbackOrderRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_ORDERS_CALLBACK_STATUS;
@Slf4j
@Component
public class ShutdownTranslator implements XbpTranslator {
    @Override
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException {
        if (!(request instanceof ShutdownRequest)) {
            throw new InternalFailureException("回传请求类型匹配错误");
        }

        ShutdownRequest shutdownRequest = (ShutdownRequest) request;
        SubmitApprovalRequest approvalRequest = new SubmitApprovalRequest();
        approvalRequest.setProcessCode(XbpProcessEnum.SHUTDOWN_CONTAINER.getCode());
        approvalRequest.setPlatform(ApprovalPlatformEnum.XBP.getCode());

        //获取回调时要使用的报文
        ShutdownApprovalContent approvalContent = new ShutdownApprovalContent();
        approvalContent.setAppName(shutdownRequest.getAppName());
        approvalContent.setGroup(shutdownRequest.getGroup());
        approvalContent.setIpList(shutdownRequest.getIpList());
        approvalContent.setReason(shutdownRequest.getReason());
        approvalRequest.setInstanceContent(JSON.toJSONString(approvalContent));
        approvalRequest.setInstanceContentKey("");
        //获取审批实例前端展示信息：表单部分
        Map<String, String> instanceForm = getInstanceForm(shutdownRequest);
        approvalRequest.setInstanceForm(instanceForm);
        //获取操作人、操作时间
        approvalRequest.setOperator("zhuhongru1");
        approvalRequest.setOperateTime(new Date());
        return approvalRequest;
    }

    /**
     * 获取：回传流程实例-表单信息
     */
    private Map<String, String> getInstanceForm(ShutdownRequest request) {
        Map<String, String> instanceForm = new HashMap<>();
        instanceForm.put(XBP_JDOS_APP_NAME, request.getAppName());
        instanceForm.put(XBP_JDOS_GROUP_NAME, request.getGroup());
        instanceForm.put(XBP_JDOS_ABNORMAL_IP_LIST, JSON.toJSONString(request.getIpList()));
        instanceForm.put(XBP_JDOS_SHUTDOWN_REASON, request.getReason());
        return instanceForm;
    }

    @Override
    public boolean isMatch(String processName) {
        return XbpProcessEnum.SHUTDOWN_CONTAINER.getCode().equals(processName);
    }
}
