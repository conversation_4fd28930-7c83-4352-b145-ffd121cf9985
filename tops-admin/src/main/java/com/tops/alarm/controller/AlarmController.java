package com.tops.alarm.controller;

import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.alarm.bean.vo.MetricVO;
import com.tops.alarm.bean.dto.AlarmHandleContext;
import com.tops.alarm.bean.dto.AutoBotsDependency;
import com.tops.alarm.enums.MetricTypeEnum;
import com.tops.alarm.enums.PlatformTypeEnum;
import com.tops.alarm.service.ExceptionHandleService;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.service.AnalysisFlowService;
import com.tops.autobots.task.AutoBotsAskTask;
import com.tops.common.utils.ThreadUtil;
import com.tops.alarm.bean.vo.UmpListenRequest;
import com.tops.alarm.bean.vo.UmpListenResponse;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.jdos.service.EasyOpsService;
import com.tops.jdos.service.JDOSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping("/tops/alarm")
@Configuration
@EnableScheduling
public class AlarmController {


    private AtomicInteger counter = new AtomicInteger();
    private Random random = new Random(47);
    @Autowired
    private ThreadUtil threadUtil;
    @Autowired
    private AutoBotsServiceAdapter autoBotsServiceAdapter;
    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    @Autowired
    private JDOSService jdosService;
    @Autowired
    private EasyOpsService easyOpsService;
    @Autowired
    private ExceptionHandleService exceptionHandleService;
    @Autowired
    private  AnalysisFlowService analysisFlowService;

    @PostMapping("/listen")
    public UmpListenResponse listen(@RequestBody UmpListenRequest request) {
        String traceId = System.currentTimeMillis() + "";
        log.info("UmpController.listen traceId:{},request:{}", traceId, JSON.toJSONString(request));
        AlarmHandleContext alarmHandleContext = getMetricInfo(request);
        AutoBotsDependency autoBotsDependency = new AutoBotsDependency();
        autoBotsDependency.setAutoBotsServiceAdapter(autoBotsServiceAdapter);
        autoBotsDependency.setDongdongSendMsgHandle(dongdongSendMsgHandle);
        autoBotsDependency.setJdosService(jdosService);
        autoBotsDependency.setEasyOpsService(easyOpsService);
        autoBotsDependency.setExceptionHandleService(exceptionHandleService);
        autoBotsDependency.setAnalysisFlowService(analysisFlowService);
        AutoBotsAskTask autoBotsAskTask = new AutoBotsAskTask(autoBotsDependency, alarmHandleContext);
        threadUtil.submitThread(autoBotsAskTask);
        return UmpListenResponse.success();
    }

    @Async
    @Scheduled(cron = "0/1 * * * * ?")
    protected void productRelationLocalCacheFromRedis() {
        CallerInfo callerInfo = Profiler.registerInfo("UmpController.testWarn");
        try {
            int count = counter.incrementAndGet();
            Thread.sleep(100);
            if ((count % 5) == 0) {
                throw new RuntimeException("ERROR");
            }
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 将告警消息报文解析按领域解析到上下文中
     */
    private AlarmHandleContext getMetricInfo(UmpListenRequest request) {
        String endpoint = request.getLabels().get("endpoint").get(0);
        String provider = request.getLabels().get("provider").get(0);
        String startTimeStampStr = request.getLabels().get("analysisStartTime").get(0);
        String endTimeStampStr = request.getLabels().get("analysisEndTime").get(0);

        String alarmMetric = getAlarmMetric(request);
        String appAlias = getAppAlias(request);
        String appName = getAppName(request);
        List<String> groupNames = new ArrayList<>();
        if (Objects.equals(PlatformTypeEnum.MDC.getCode(), provider)) {
            List<String> groups = request.getLabels().get("group");
            if (groups != null) {
                groupNames.addAll(groups);
            }
        }
        String level = request.getLevel();
        Set<String> erps = new HashSet<>();
        //TODO wangqin83 暂时不发送至实际告警接收人
//        List<String> timLines = request.getContact().getTimLines();
//        if (!CollectionUtils.isEmpty(timLines)){
//            erps.addAll(timLines);
//        }
//        List<String> jdMes = request.getContact().getJdMes();
//        if (!CollectionUtils.isEmpty(jdMes)){
//            erps.addAll(jdMes);
//        }

        Double current = null;
        Double threshold = null;
        MetricVO metricVO = request.getData().get(MetricTypeEnum.getDataKeyByCode(alarmMetric));
        if (metricVO != null) {
            current = metricVO.getCurrent();
            threshold = metricVO.getThresholdValue();
        }

        AlarmHandleContext alarmHandleContext = new AlarmHandleContext();
        alarmHandleContext
            //补充请求元数据
            .withTraceId(System.currentTimeMillis() + "")
            //补充平台信息
            .withPlatformNo(provider)
            //补充应用信息
            .withAppAlias(appAlias)
            .withAppName(appName)
            .withGroupNames(groupNames)
            //补充指标信息
            .withEndPoint(endpoint)
            //补充告警信息
            .withAlarmMetric(alarmMetric)
            .withLevel(level)
            .withMetricCurrent(current)
            .withMetricThreshold(threshold)
            .withStartTime(new Date(Long.parseLong(startTimeStampStr) * 1000))
            .withEndTime(new Date(Long.parseLong(endTimeStampStr) * 1000))
            //补充联系人信息
            .withErps(new ArrayList<>(erps));
        return alarmHandleContext;
    }

    private String getAlarmMetric(UmpListenRequest request) {
        String provider = request.getLabels().get("provider").get(0);
        String alarmMetric = null;
        if (Objects.equals(PlatformTypeEnum.UMP.getCode(), provider)) {
            alarmMetric = request.getLabels().get("ext_alarmMetric").get(0);
        } else if (Objects.equals(PlatformTypeEnum.P_FINDER.getCode(), provider)) {
            //TODO wangqin83 实现PFinder平台的告警类型识别

        } else if (Objects.equals(PlatformTypeEnum.MDC.getCode(), provider)) {
            alarmMetric = request.getLabels().get("ext_metric").get(0);
        }
        return alarmMetric;
    }

    private String getAppName(UmpListenRequest request) {
        String provider = request.getLabels().get("provider").get(0);
        String appName = null;
        if (Objects.equals(PlatformTypeEnum.UMP.getCode(), provider)) {
            appName = request.getLabels().get("ext_appName").get(0);
        } else if (Objects.equals(PlatformTypeEnum.P_FINDER.getCode(), provider)) {
            //TODO wangqin83 实现PFinder平台的识别

        } else if (Objects.equals(PlatformTypeEnum.MDC.getCode(), provider)) {
            appName = request.getLabels().get("app").get(0);
        }
        return appName;
    }

    private String getAppAlias(UmpListenRequest request) {
        String provider = request.getLabels().get("provider").get(0);
        String appAlias = null;
        if (Objects.equals(PlatformTypeEnum.UMP.getCode(), provider)) {
            appAlias = request.getLabels().get("ext_appAlias").get(0);
        } else if (Objects.equals(PlatformTypeEnum.P_FINDER.getCode(), provider)) {
            //TODO wangqin83 实现PFinder平台的识别

        } else if (Objects.equals(PlatformTypeEnum.MDC.getCode(), provider)) {
            appAlias = "MDC平台不支持展示应用中文名";
        }
        return appAlias;
    }

}
