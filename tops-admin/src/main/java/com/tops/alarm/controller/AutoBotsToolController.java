package com.tops.alarm.controller;

import com.jd.fastjson.JSON;
import com.jd.fastjson.JSONObject;
import com.tops.alarm.bean.vo.*;
import com.tops.audit.service.ApprovalService;
import com.tops.autobots.domain.DuccModifyDTO;
import com.tops.autobots.domain.DuccModifyRequest;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookResponse;
import com.tops.autobots.service.AnalysisOpsService;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.JsonUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.order.adapter.TopsLogBookAdaptor;
import com.tops.jdos.utils.HttpUtil;
import com.tops.order.adapter.TopsOrderRequestRecordAdapter;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/tops/tools")
@Configuration
@EnableScheduling
public class AutoBotsToolController {
    @Resource
    private AnalysisOpsService analysisOpsService;

    @Autowired
    private TopsEnvUtils topsEnvUtils;

    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;

    @Resource
    private TopsLogBookAdaptor topsLogBookAdaptor;

    @Resource
    private TopsOrderRequestRecordAdapter topsOrderRequestRecordAdapter;

    @PostMapping("/ump")
    public R<AutoBotsOpsResponse> checkUmp(@RequestBody AutoBotsOpsRequest request) {
        log.info("request:{}", JSON.toJSONString(request));
        String jsonStr = JsonUtils.ofMarkDown(request.getQueryText());
        log.info("jsonStr:{}", jsonStr);
        AutoBotsOpsDTO autoBotsOpsDTOS = JsonUtils.parseObject(jsonStr, AutoBotsOpsDTO.class);
        log.info("autoBotsUmpDTOS:{}", JSON.toJSONString(autoBotsOpsDTOS));
        AutoBotsOpsResponse autoBotsOpsResponse = null;
        R<AutoBotsOpsResponse> result = null;
        try {
            autoBotsOpsResponse = analysisOpsService.getAutoBotsUmp(autoBotsOpsDTOS);
            result = R.ok(autoBotsOpsResponse);
        } catch (InvalidRequestException e) {
            log.error("checkUmp InvalidRequestException  error:{}", e.getMsg(), e);
            result = R.fail(e.getMsg());
        } catch (DependencyFailureException e) {
            log.error("checkUmp DependencyFailureException  error:{}", e.getMsg(), e);
            result = R.fail(e.getMsg());
        } catch (InternalFailureException e) {
            log.error("checkUmp InternalFailureException  error:{}", e.getMsg(), e);
            result = R.fail(e.getMsg());
        }

        return result;
    }

    /**
     * 查询mdc信息, 由于数据量较大，所以采用异步处理
     * @param params
     * @return
     */
    @PostMapping("/mdc")
    public void asyncCheckMdc(@RequestBody JSONObject params) {
        log.info("request:{}", JSON.toJSONString(params));
        String queryText = params.getString("queryText");
        String jsonStr = JsonUtils.ofMarkDown(queryText);
        log.info("jsonStr:{}", jsonStr);
        AutoBotsOpsDTO autoBotsOpsDTOS = JsonUtils.parseObject(jsonStr, AutoBotsOpsDTO.class);
        log.info("autoBotsUmpDTOS:{}", JSON.toJSONString(autoBotsOpsDTOS));
        AutoBotsOpsResponse autoBotsOpsResponse = null;
        R<AutoBotsOpsResponse> result = null;
        try {
            autoBotsOpsResponse = analysisOpsService.getAutoBotsMdc(autoBotsOpsDTOS);
            result = R.ok(autoBotsOpsResponse);
        } catch (InvalidRequestException e) {
            result = R.fail(e.getMsg());
            log.error("checkUmp InvalidRequestException  error:{}", e.getMsg(), e);
        } catch (DependencyFailureException e) {
            result = R.fail(e.getMsg());
            log.error("checkUmp DependencyFailureException  error:{}", e.getMsg(), e);
        } catch (InternalFailureException e) {
            result = R.fail(e.getMsg());
            log.error("checkUmp InternalFailureException  error:{}", e.getMsg(), e);
        }

        //异步发送结果
        String callbackKey = params.getString("__callbackKey");
        String callbackUrl = params.getString("__callbackUrl");
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(result));
        jsonObject.put("__callbackKey", callbackKey);
        HttpUtil.post(callbackUrl, jsonObject.toJSONString(), null);
    }

    /**
     * 使用ducc插件
     * @param request
     */
    @PostMapping("/ducc")
    public R<AutoBotsOpsResponse> applyDUCCEmergency(@RequestBody AutoBotsOpsRequest request) {
        R<AutoBotsOpsResponse> result = null;

        try {
            log.info("request:{}", JSON.toJSONString(request));
            String jsonStr = JsonUtils.ofMarkDown(request.getQueryText());
            log.info("jsonStr:{}", jsonStr);

            DuccModifyRequest duccModifyRequest = JsonUtils.parseObject(jsonStr, DuccModifyRequest.class);
            if (duccModifyRequest != null && !CollectionUtils.isEmpty(duccModifyRequest.getDuccModifyDTOS())) {
                List<DuccModifyDTO> duccModifyDTOS = duccModifyRequest.getDuccModifyDTOS();
                //预发环境、生产环境推送咚咚消息
                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(),
                    getDDMsgTitle(),
                    getDDMsgContentSUCCESS1(duccModifyDTOS));
                //todo 替换成其他不影响线上生产的应急预案
//                SubmitApprovalRequest xbpRequest = duccModifyTranslator.getXbpRequest(duccModifyRequest);
//                approvalService.submitApproval(xbpRequest);
            }

            result = R.ok();
        } catch (Exception e) {
            result = R.fail(e.getMessage());
            log.error("checkUmp DependencyFailureException  error:{}", e.getMessage(), e);
        }

        return result;
    }


    //查询纯配日志
    /**
     * 对外开放服务查询接口,获取接单报文
     */
    @PostMapping("/outbound")
    public R<TopsOrderRequestEsRecord> getOutboundReceiveRecord(@RequestBody TopsOrderRequestBo bo) {
        try {
            log.info("查询请求信息bo= {}", JSON.toJSONString(bo));
            if (StringUtils.isEmpty(bo.getOrderNo()) && StringUtils.isBlank(bo.getCustomerOrderNo()) && StringUtils.isBlank(bo.getCustomOrderNo())) {
                return R.fail("订单号或/客户订单号/自定义单号为空, 请输入相关单据");
            }

            return R.ok(topsOrderRequestRecordAdapter.getLastReceiveRecord(bo, true));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        } catch (Exception e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        }
    }

    /**
     * 对外开放服务查询接口,获取接单报文
     */
    @PostMapping("/express")
    public R<TopsOrderRequestEsRecord> getExpressReceiveRecord(@RequestBody TopsOrderRequestBo bo) {
        try {
            log.info("查询请求信息bo= {}", JSON.toJSONString(bo));
            if (StringUtils.isEmpty(bo.getOrderNo()) && StringUtils.isBlank(bo.getCustomerOrderNo()) && StringUtils.isBlank(bo.getCustomOrderNo())) {
                return R.fail("订单号或/客户订单号/自定义单号为空, 请输入相关单据");
            }

            return R.ok(topsOrderRequestRecordAdapter.getLastReceiveRecord(bo, false));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        } catch (Exception e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        }
    }




    //@todo 从工作流获取当前登录人员
    private String getDongDongReceiver() {
        StringBuilder receiver = new StringBuilder().append("zhuhongru1");
        return receiver.toString();
    }


    private String getDDMsgTitle() {
        StringBuilder content = new StringBuilder();
        content.append(topsEnvUtils.getEnvDesc());
        content.append("：DUCC降级预案执行结果通知");
        return content.toString();
    }

    private String getDDMsgContentSUCCESS1(List<DuccModifyDTO> duccModifyDTOS) {
        StringBuilder content = new StringBuilder();
        content.append("DUCC降级预案执行结果通知：封板期间不实际发起审批流。提交审批源数据如下：\n")
            .append(com.alibaba.fastjson.JSON.toJSONString(duccModifyDTOS));
        return content.toString();
    }

    @GetMapping("/log")
    public R<List<LogbookResponse.LogBookData>> log(@RequestParam LogbookBO logbookBO) {
        log.info("request:{}", JSON.toJSONString(logbookBO));
        return R.ok(topsLogBookAdaptor.getLogBook(logbookBO));
    }
}
