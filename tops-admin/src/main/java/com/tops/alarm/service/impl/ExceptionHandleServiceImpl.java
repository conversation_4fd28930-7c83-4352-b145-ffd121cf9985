package com.tops.alarm.service.impl;

import com.tops.alarm.bean.dto.ScaleUpRequest;
import com.tops.alarm.bean.dto.ShutdownRequest;
import com.tops.alarm.service.ExceptionHandleService;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.audit.service.ApprovalService;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ExceptionHandleServiceImpl implements ExceptionHandleService {

    private final static Integer MAX_SCALE_UP_COUNT = 10;

    @Autowired
    private ApprovalService approvalService;
    /**
     * 各工单转换器
     */
    @Resource
    List<XbpTranslator> xbpTranslators;

    /**
     * @param request 关停容器信息
     */
    @Override
    public void shutdownApproval(ShutdownRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (request == null) {
            log.warn("ExceptionHandleServiceImpl.shutdownApproval 入参非法 request不可为null");
            throw new InvalidRequestException("request不可为null");
        }
        if (CollectionUtils.isEmpty(request.getIpList())) {
            log.warn("ExceptionHandleServiceImpl.shutdownApproval 入参非法 request.ipList不可为空");
            throw new InvalidRequestException("请选择要关停的容器");
        }
        if (StringUtils.isBlank(request.getAppName())) {
            log.warn("ExceptionHandleServiceImpl.shutdownApproval 入参非法 request.appName不可为空");
            throw new InvalidRequestException("请选择应用");
        }
        if (StringUtils.isBlank(request.getGroup())) {
            log.warn("ExceptionHandleServiceImpl.shutdownApproval 入参非法 request.group不可为空");
            throw new InvalidRequestException("请选择分组");
        }
        if (StringUtils.isBlank(request.getReason())) {
            log.warn("ExceptionHandleServiceImpl.shutdownApproval 入参非法 request.reason不可为空");
            throw new InvalidRequestException("请填写关停原因");
        }

        //提交审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.SHUTDOWN_CONTAINER.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    /**
     * @param request 关停容器信息
     */
    @Override
    public void scaleUpApproval(ScaleUpRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (request == null) {
            log.warn("ExceptionHandleServiceImpl.scaleUpApproval 入参非法 request不可为null");
            throw new InvalidRequestException("request不可为null");
        }
        if (CollectionUtils.isEmpty(request.getIpList())) {
            log.warn("ExceptionHandleServiceImpl.scaleUpApproval 入参非法 request.ipList不可为空");
            throw new InvalidRequestException("请填写异常的容器");
        }
        if (StringUtils.isBlank(request.getAppName())) {
            log.warn("ExceptionHandleServiceImpl.scaleUpApproval 入参非法 request.appName不可为空");
            throw new InvalidRequestException("请选择应用");
        }
        if (StringUtils.isBlank(request.getGroup())) {
            log.warn("ExceptionHandleServiceImpl.scaleUpApproval 入参非法 request.group不可为空");
            throw new InvalidRequestException("请选择分组");
        }
        if (StringUtils.isBlank(request.getReason())) {
            log.warn("ExceptionHandleServiceImpl.scaleUpApproval 入参非法 request.reason不可为空");
            throw new InvalidRequestException("请填写扩容原因");
        }
        int abnormalCount = request.getIpList().size();
        int scaleUpCount = Math.min(abnormalCount, MAX_SCALE_UP_COUNT);
        request.setScaleUpCount(scaleUpCount);
        //提交审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.SCALE_UP_GROUP.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    private XbpTranslator getTranslator(String processCode) {
        return xbpTranslators.stream().filter(xbpTranslator -> xbpTranslator.isMatch(processCode)).findFirst().get();
    }
}
