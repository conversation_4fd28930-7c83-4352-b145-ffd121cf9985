package com.tops.alarm.service;

import com.tops.alarm.bean.dto.ScaleUpRequest;
import com.tops.alarm.bean.dto.ShutdownRequest;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

public interface ExceptionHandleService {
    void shutdownApproval(ShutdownRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    void scaleUpApproval(ScaleUpRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException;
}
