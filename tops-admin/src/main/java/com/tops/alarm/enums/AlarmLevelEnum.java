package com.tops.alarm.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum AlarmLevelEnum {
    CLEARED("CLEARED", "恢复"),
    WARNING("WARNING", "警告"),
    CRITICAL("<PERSON><PERSON><PERSON><PERSON>", "紧急"),
    ;

    private String code;
    private String desc;

    AlarmLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AlarmLevelEnum getEnumByCode(String code) {
        for (AlarmLevelEnum value : AlarmLevelEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
