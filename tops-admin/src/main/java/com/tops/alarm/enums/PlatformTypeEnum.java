package com.tops.alarm.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PlatformTypeEnum {

    UMP("ump","ump"),
    P_FINDER("titan-pfinder-cn","pFinder"),
    MDC("mdc","mdc"),
    ;

    private String code;
    private String desc;

    PlatformTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (PlatformTypeEnum value : PlatformTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static PlatformTypeEnum getEnumByCode(String code) {
        for (PlatformTypeEnum value : PlatformTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value;
            }
        }
        return null;
    }
}
