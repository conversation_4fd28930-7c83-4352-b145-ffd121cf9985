package com.tops.alarm.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum MetricTypeEnum {
    /**
     * 接口指标-UMP平台
     */
    AVAILABLE_RATE("availableRate", "availability", "可用率"),
    TP99("tp99", "tp99", "TP99"),
    INVOKE_COUNT("invokeCount", "invokes", "调用次数"),
    /**
     * 机器硬件资源指标-MDC平台
     */
    MIN_CPU_USAGE_PERCENT("min_cpu_usage_percent", "min_cpu_usage_percent", "CPU使用率"),
    MIN_MEM_USAGE_PERCENT("min_mem_usage_percent", "min_mem_usage_percent", "RSS内存使用率"),
    MIN_FS_USAGE_PERCENT("min_fs_usage_percent", "min_fs_usage_percent", "磁盘使用率"),
    MIN_TCP_RETRANS_COUNT("min_tcp_retrans_count", "min_tcp_retrans_count", "TCP重传数"),
    ;

    private String code;
    private String dataKey;
    private String desc;

    MetricTypeEnum(String code, String dataKey, String desc) {
        this.code = code;
        this.dataKey = dataKey;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (MetricTypeEnum value : MetricTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static String getDataKeyByCode(String code) {
        for (MetricTypeEnum value : MetricTypeEnum.values()) {
            if (Objects.equals(code, value.getCode())) {
                return value.getDataKey();
            }
        }
        return null;
    }
}
