package com.tops.employee.adapter;


import com.tops.common.utils.TopsHttpUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.TopsMD5Utils;
import com.tops.employee.dto.DeptInfoDTO;
import com.tops.employee.dto.DeptInfoResponse;
import com.tops.employee.dto.ErpDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 组织机构
 */
@Service
@Slf4j
public class DeptAdapter {

    public static final String SUCCESS_CODE = "200";
    // 组织机构获取url
    public static String url = "http://omdmv.jd.local/service/hrOrganizationService/rest/getOrganizationByCode";

    /**
     * 请求获取组织机构信息
     *
     * @param params
     * @return
     */
    public DeptInfoDTO request(Map<String, String> params) {
        //String orgFullName = "京东集团-京东物流-技术与数据智能部-中台技术部-交易平台组";
        String requestUri = url + "/" + params.get("orgCode");
        List<ErpDTO> erpDTOList = new ArrayList<>();
        Map<String, Object> uriParams = new HashMap<>();
        uriParams.put("appCode", "lpc-t-supperman_JONE");
        uriParams.put("businessId", "654321");
        SimpleDateFormat aDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.FFF");
        String requestTimestamp = aDate.format(new Date());
        uriParams.put("requestTimestamp", requestTimestamp);
        uriParams.put("responseFormat", "JSON");
        uriParams.put("organizationCode", params.get("orgCode"));
        String str = uriParams.get("appCode").toString() + uriParams.get("businessId") + uriParams.get("requestTimestamp") + "9c1c6fc684d46d19019b" + uriParams.get("organizationCode");

        String sign = TopsMD5Utils.toMD5(str);
        uriParams.put("sign", sign);
        String response = TopsHttpUtils.doGet(requestUri, uriParams);
        DeptInfoDTO deptInfoDTO = null;
        if (StringUtils.isNotBlank(response)) {
            try {
                response = URLDecoder.decode(response, "utf-8");
            } catch (UnsupportedEncodingException e) {
                log.error("URLDecoder解析异常", e);
            }
            DeptInfoResponse deptInfoResponse = TopsJsonUtils.parseObject(response, DeptInfoResponse.class);
            if (!SUCCESS_CODE.equals(deptInfoResponse.getResStatus()) || deptInfoResponse.getResponsebody() == null) {
                return deptInfoDTO;
            }
            deptInfoDTO = new DeptInfoDTO();
            deptInfoDTO.setOrgCode(deptInfoResponse.getResponsebody().getOrganizationCode());
            deptInfoDTO.setOrgName(deptInfoResponse.getResponsebody().getOrganizationName());
            deptInfoDTO.setOrgFullCode(deptInfoResponse.getResponsebody().getOrganizationFullPath());
            deptInfoDTO.setOrgFullName(deptInfoResponse.getResponsebody().getOrganizationFullname());
            return deptInfoDTO;
        }
        return deptInfoDTO;
    }

}
