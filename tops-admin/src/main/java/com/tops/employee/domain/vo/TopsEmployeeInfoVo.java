package com.tops.employee.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 人员信息视图对象 tops_employee_info
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
public class TopsEmployeeInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @ExcelProperty(value = "自增主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private String userId;

    /**
     * 头像URL
     */
    @ExcelProperty(value = "头像URL")
    private String avatar;

    /**
     * ERP账号
     */
    @ExcelProperty(value = "ERP账号")
    private String erp;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 部门编码
     */
    @ExcelProperty(value = "部门编码")
    private String orgCode;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String orgName;

    /**
     * 部门全码
     */
    @ExcelProperty(value = "部门全码")
    private String fullOrgCode;

    /**
     * 部门全称
     */
    @ExcelProperty(value = "部门全称")
    private String fullOrgName;

    /**
     * 电话号码
     */
    @ExcelProperty(value = "电话号码")
    private String phone;

    /**
     * 邮箱地址
     */
    @ExcelProperty(value = "邮箱地址")
    private String email;

    /**
     * 职位名称
     */
    @ExcelProperty(value = "职位名称")
    private String positionName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private Integer sex;

    /**
     * 生日
     */
    @ExcelProperty(value = "生日")
    private Date birthday;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄")
    private Long age;

    /**
     * 级别
     */
    @ExcelProperty(value = "级别")
    private String levelName;

    /**
     * 入职日期
     */
    @ExcelProperty(value = "入职日期")
    private Date entryDate;

    /**
     * 值班通知
     */
    @ExcelProperty(value = "值班通知", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private Integer dutyNotify;

    /**
     * 小组名称
     */
    @ExcelProperty(value = "小组名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_dept_sub_group")
    private String subGroup;

    /**
     * 扩展字段
     */
    @ExcelProperty(value = "扩展字段")
    private String extMap;

    /**
     * 帐号状态
     */
    @ExcelProperty(value = "帐号状态")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
