package com.tops.employee.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tops.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 人员信息对象 tops_employee_info
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_employee_info")
public class TopsEmployeeInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 头像URL
     */
    private String avatar;
    /**
     * ERP账号
     */
    private String erp;
    /**
     * 姓名
     */
    private String name;
    /**
     * 部门编码
     */
    private String orgCode;
    /**
     * 部门名称
     */
    private String orgName;
    /**
     * 部门全码
     */
    private String fullOrgCode;
    /**
     * 部门全称
     */
    private String fullOrgName;
    /**
     * 电话号码
     */
    private String phone;
    /**
     * 邮箱地址
     */
    private String email;
    /**
     * 职位名称
     */
    private String positionName;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 生日
     */
    private Date birthday;
    /**
     * 年龄
     */
    private Long age;
    /**
     * 级别
     */
    private String levelName;
    /**
     * 入职日期
     */
    private Date entryDate;
    /**
     * 值班通知
     */
    private Integer dutyNotify;
    /**
     * 小组名称
     */
    private String subGroup;
    /**
     * 扩展字段
     */
    private String extMap;
    /**
     * 帐号状态
     */
    private String status;
    /**
     * 备注
     */
    private String remark;

}
