package com.tops.employee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * ClassName:DeptInfoResponse
 * Package:com.jdl.lt.m.mgt.sso.domain
 * Description:
 *
 * @date:2022/4/18 14:00
 * @author:WeiLiming
 */
public class DeptInfoResponse {

    @JsonProperty("appCode")
    private String appCode;
    @JsonProperty("resStatus")
    private String resStatus;
    @JsonProperty("resMsg")
    private String resMsg;
    @JsonProperty("resCount")
    private Integer resCount;
    @JsonProperty("responsebody")
    private ResponsebodyDTO responsebody;

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getResStatus() {
        return resStatus;
    }

    public void setResStatus(String resStatus) {
        this.resStatus = resStatus;
    }

    public String getResMsg() {
        return resMsg;
    }

    public void setResMsg(String resMsg) {
        this.resMsg = resMsg;
    }

    public Integer getResCount() {
        return resCount;
    }

    public void setResCount(Integer resCount) {
        this.resCount = resCount;
    }

    public ResponsebodyDTO getResponsebody() {
        return responsebody;
    }

    public void setResponsebody(ResponsebodyDTO responsebody) {
        this.responsebody = responsebody;
    }

    public static class ResponsebodyDTO {
        @JsonProperty("organizationCode")
        private String organizationCode;
        @JsonProperty("organizationName")
        private String organizationName;
        @JsonProperty("organizationFullname")
        private String organizationFullname;
        @JsonProperty("organizationFullPath")
        private String organizationFullPath;
        @JsonProperty("organizationDescription")
        private Object organizationDescription;
        @JsonProperty("departmentsNumber")
        private Integer departmentsNumber;
        @JsonProperty("departmentHead")
        private Object departmentHead;
        @JsonProperty("departmentAverage")
        private Double departmentAverage;
        @JsonProperty("hasChildDepartment")
        private Boolean hasChildDepartment;
        @JsonProperty("oneLevelOrgCode")
        private Object oneLevelOrgCode;
        @JsonProperty("oneLevelOrgName")
        private Object oneLevelOrgName;
        @JsonProperty("twoLevelOrgCode")
        private Object twoLevelOrgCode;
        @JsonProperty("twoLevelOrgName")
        private Object twoLevelOrgName;
        @JsonProperty("threeLevelOrgCode")
        private Object threeLevelOrgCode;
        @JsonProperty("threeLevelOrgName")
        private Object threeLevelOrgName;
        @JsonProperty("organizationLevel")
        private String organizationLevel;
        @JsonProperty("departmentHeadCode")
        private Object departmentHeadCode;
        @JsonProperty("departmentHeadErp")
        private Object departmentHeadErp;
        @JsonProperty("departmentHeadPosiName")
        private Object departmentHeadPosiName;
        @JsonProperty("departmentHeadPhoto")
        private Object departmentHeadPhoto;
        @JsonProperty("departmentHeadSex")
        private Object departmentHeadSex;
        @JsonProperty("isMultiPosition")
        private Object isMultiPosition;
        @JsonProperty("organizationAttribution")
        private Object organizationAttribution;
        @JsonProperty("buGroup")
        private String buGroup;
        @JsonProperty("businessDepartment")
        private Object businessDepartment;

        public String getOrganizationCode() {
            return organizationCode;
        }

        public void setOrganizationCode(String organizationCode) {
            this.organizationCode = organizationCode;
        }

        public String getOrganizationName() {
            return organizationName;
        }

        public void setOrganizationName(String organizationName) {
            this.organizationName = organizationName;
        }

        public String getOrganizationFullname() {
            return organizationFullname;
        }

        public void setOrganizationFullname(String organizationFullname) {
            this.organizationFullname = organizationFullname;
        }

        public String getOrganizationFullPath() {
            return organizationFullPath;
        }

        public void setOrganizationFullPath(String organizationFullPath) {
            this.organizationFullPath = organizationFullPath;
        }

        public Object getOrganizationDescription() {
            return organizationDescription;
        }

        public void setOrganizationDescription(Object organizationDescription) {
            this.organizationDescription = organizationDescription;
        }

        public Integer getDepartmentsNumber() {
            return departmentsNumber;
        }

        public void setDepartmentsNumber(Integer departmentsNumber) {
            this.departmentsNumber = departmentsNumber;
        }

        public Object getDepartmentHead() {
            return departmentHead;
        }

        public void setDepartmentHead(Object departmentHead) {
            this.departmentHead = departmentHead;
        }

        public Double getDepartmentAverage() {
            return departmentAverage;
        }

        public void setDepartmentAverage(Double departmentAverage) {
            this.departmentAverage = departmentAverage;
        }

        public Boolean getHasChildDepartment() {
            return hasChildDepartment;
        }

        public void setHasChildDepartment(Boolean hasChildDepartment) {
            this.hasChildDepartment = hasChildDepartment;
        }

        public Object getOneLevelOrgCode() {
            return oneLevelOrgCode;
        }

        public void setOneLevelOrgCode(Object oneLevelOrgCode) {
            this.oneLevelOrgCode = oneLevelOrgCode;
        }

        public Object getOneLevelOrgName() {
            return oneLevelOrgName;
        }

        public void setOneLevelOrgName(Object oneLevelOrgName) {
            this.oneLevelOrgName = oneLevelOrgName;
        }

        public Object getTwoLevelOrgCode() {
            return twoLevelOrgCode;
        }

        public void setTwoLevelOrgCode(Object twoLevelOrgCode) {
            this.twoLevelOrgCode = twoLevelOrgCode;
        }

        public Object getTwoLevelOrgName() {
            return twoLevelOrgName;
        }

        public void setTwoLevelOrgName(Object twoLevelOrgName) {
            this.twoLevelOrgName = twoLevelOrgName;
        }

        public Object getThreeLevelOrgCode() {
            return threeLevelOrgCode;
        }

        public void setThreeLevelOrgCode(Object threeLevelOrgCode) {
            this.threeLevelOrgCode = threeLevelOrgCode;
        }

        public Object getThreeLevelOrgName() {
            return threeLevelOrgName;
        }

        public void setThreeLevelOrgName(Object threeLevelOrgName) {
            this.threeLevelOrgName = threeLevelOrgName;
        }

        public String getOrganizationLevel() {
            return organizationLevel;
        }

        public void setOrganizationLevel(String organizationLevel) {
            this.organizationLevel = organizationLevel;
        }

        public Object getDepartmentHeadCode() {
            return departmentHeadCode;
        }

        public void setDepartmentHeadCode(Object departmentHeadCode) {
            this.departmentHeadCode = departmentHeadCode;
        }

        public Object getDepartmentHeadErp() {
            return departmentHeadErp;
        }

        public void setDepartmentHeadErp(Object departmentHeadErp) {
            this.departmentHeadErp = departmentHeadErp;
        }

        public Object getDepartmentHeadPosiName() {
            return departmentHeadPosiName;
        }

        public void setDepartmentHeadPosiName(Object departmentHeadPosiName) {
            this.departmentHeadPosiName = departmentHeadPosiName;
        }

        public Object getDepartmentHeadPhoto() {
            return departmentHeadPhoto;
        }

        public void setDepartmentHeadPhoto(Object departmentHeadPhoto) {
            this.departmentHeadPhoto = departmentHeadPhoto;
        }

        public Object getDepartmentHeadSex() {
            return departmentHeadSex;
        }

        public void setDepartmentHeadSex(Object departmentHeadSex) {
            this.departmentHeadSex = departmentHeadSex;
        }

        public Object getIsMultiPosition() {
            return isMultiPosition;
        }

        public void setIsMultiPosition(Object isMultiPosition) {
            this.isMultiPosition = isMultiPosition;
        }

        public Object getOrganizationAttribution() {
            return organizationAttribution;
        }

        public void setOrganizationAttribution(Object organizationAttribution) {
            this.organizationAttribution = organizationAttribution;
        }

        public String getBuGroup() {
            return buGroup;
        }

        public void setBuGroup(String buGroup) {
            this.buGroup = buGroup;
        }

        public Object getBusinessDepartment() {
            return businessDepartment;
        }

        public void setBusinessDepartment(Object businessDepartment) {
            this.businessDepartment = businessDepartment;
        }
    }
}
