package com.tops.employee.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName:ErpDTO
 * Package:com.jdl.lt.m.mgt.adapters.dto
 * Description:
 *
 * @date:2022/3/22 10:13
 * @author:WeiLiming
 */
@Data
@ApiModel(value = "ERP对象", description = "ERP基础信息")
public class ErpDTO {
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "ERP")
    private String erp;
    @ApiModelProperty(value = "姓名")
    private String name;
    @ApiModelProperty(value = "机构编码")
    private String orgCode;
    @ApiModelProperty(value = "机构名称")
    private String orgName;
    @ApiModelProperty(value = "机构全编码")
    private String fullOrgCode;
    @ApiModelProperty(value = "机构全名称")
    private String fullOrgName;
    @ApiModelProperty(value = "电话")
    private String phone;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "职位")
    private String positionName;
    @ApiModelProperty(value = "性别")

    private Integer sex;
    @ApiModelProperty(value = "生日")
    private String birthday;
    @ApiModelProperty(value = "年龄")
    private Integer age;
    @ApiModelProperty(value = "职级")
    private String levelName;
    @ApiModelProperty(value = "入职时间")
    private String entryDate;

}
