package com.tops.employee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ClassName:HrOrgSubUserResponse
 * Package:org.jeecg.ops.mgt.login.dto
 * Description:
 *
 * @date:2022/10/24 20:32
 * @author:WeiL<PERSON>ing
 */
@NoArgsConstructor
@Data
public class HrOrgSubUserResponse {

    @JsonProperty("appCode")
    private String appCode;
    @JsonProperty("resStatus")
    private String resStatus;
    @JsonProperty("resMsg")
    private String resMsg;
    @JsonProperty("resCount")
    private Integer resCount;
    @JsonProperty("responsebody")
    private ResponsebodyDTO responsebody;

    @NoArgsConstructor
    @Data
    public static class ResponsebodyDTO {
        @JsonProperty("subordinateOrgs")
        private List<SubordinateOrgsDTO> subordinateOrgs;
        @JsonProperty("baseOrgInfo")
        private BaseOrgInfoDTO baseOrgInfo;
        @JsonProperty("directlyUsers")
        private List<DirectlyUsersDTO> directlyUsers;

        @NoArgsConstructor
        @Data
        public static class BaseOrgInfoDTO {
            @JsonProperty("organizationCode")
            private String organizationCode;
            @JsonProperty("organizationName")
            private String organizationName;
            @JsonProperty("organizationFullname")
            private String organizationFullname;
            @JsonProperty("organizationFullPath")
            private String organizationFullPath;
            @JsonProperty("organizationDescription")
            private Object organizationDescription;
            @JsonProperty("departmentsNumber")
            private Integer departmentsNumber;
            @JsonProperty("departmentHead")
            private String departmentHead;
            @JsonProperty("departmentAverage")
            private Double departmentAverage;
            @JsonProperty("hasChildDepartment")
            private Boolean hasChildDepartment;
            @JsonProperty("oneLevelOrgCode")
            private Object oneLevelOrgCode;
            @JsonProperty("oneLevelOrgName")
            private Object oneLevelOrgName;
            @JsonProperty("twoLevelOrgCode")
            private Object twoLevelOrgCode;
            @JsonProperty("twoLevelOrgName")
            private Object twoLevelOrgName;
            @JsonProperty("threeLevelOrgCode")
            private Object threeLevelOrgCode;
            @JsonProperty("threeLevelOrgName")
            private Object threeLevelOrgName;
            @JsonProperty("organizationLevel")
            private String organizationLevel;
            @JsonProperty("departmentHeadCode")
            private Object departmentHeadCode;
            @JsonProperty("departmentHeadErp")
            private Object departmentHeadErp;
            @JsonProperty("departmentHeadPosiName")
            private Object departmentHeadPosiName;
            @JsonProperty("departmentHeadPhoto")
            private Object departmentHeadPhoto;
            @JsonProperty("departmentHeadSex")
            private Object departmentHeadSex;
            @JsonProperty("isMultiPosition")
            private String isMultiPosition;
        }

        @NoArgsConstructor
        @Data
        public static class SubordinateOrgsDTO {
            @JsonProperty("organizationCode")
            private String organizationCode;
            @JsonProperty("organizationName")
            private String organizationName;
            @JsonProperty("organizationFullname")
            private String organizationFullname;
            @JsonProperty("organizationFullPath")
            private String organizationFullPath;
            @JsonProperty("organizationDescription")
            private Object organizationDescription;
            @JsonProperty("departmentsNumber")
            private Integer departmentsNumber;
            @JsonProperty("departmentHead")
            private String departmentHead;
            @JsonProperty("departmentAverage")
            private Double departmentAverage;
            @JsonProperty("hasChildDepartment")
            private Boolean hasChildDepartment;
            @JsonProperty("oneLevelOrgCode")
            private Object oneLevelOrgCode;
            @JsonProperty("oneLevelOrgName")
            private Object oneLevelOrgName;
            @JsonProperty("twoLevelOrgCode")
            private Object twoLevelOrgCode;
            @JsonProperty("twoLevelOrgName")
            private Object twoLevelOrgName;
            @JsonProperty("threeLevelOrgCode")
            private Object threeLevelOrgCode;
            @JsonProperty("threeLevelOrgName")
            private Object threeLevelOrgName;
            @JsonProperty("organizationLevel")
            private String organizationLevel;
            @JsonProperty("departmentHeadCode")
            private Object departmentHeadCode;
            @JsonProperty("departmentHeadErp")
            private Object departmentHeadErp;
            @JsonProperty("departmentHeadPosiName")
            private Object departmentHeadPosiName;
            @JsonProperty("departmentHeadPhoto")
            private Object departmentHeadPhoto;
            @JsonProperty("departmentHeadSex")
            private Object departmentHeadSex;
            @JsonProperty("isMultiPosition")
            private String isMultiPosition;
        }

        @NoArgsConstructor
        @Data
        public static class DirectlyUsersDTO {
            @JsonProperty("userCode")
            private String userCode;
            @JsonProperty("userName")
            private String userName;
            @JsonProperty("realName")
            private String realName;
            @JsonProperty("email")
            private Object email;
            @JsonProperty("mobile")
            private Object mobile;
            @JsonProperty("telephone")
            private Object telephone;
            @JsonProperty("sex")
            private String sex;
            @JsonProperty("birthday")
            private Object birthday;
            @JsonProperty("age")
            private Integer age;
            @JsonProperty("headImg")
            private Object headImg;
            @JsonProperty("entryDate")
            private Object entryDate;
            @JsonProperty("positiveDate")
            private Object positiveDate;
            @JsonProperty("organizationCode")
            private Object organizationCode;
            @JsonProperty("organizationName")
            private String organizationName;
            @JsonProperty("organizationFullPath")
            private Object organizationFullPath;
            @JsonProperty("organizationFullName")
            private String organizationFullName;
            @JsonProperty("positionCode")
            private Object positionCode;
            @JsonProperty("positionName")
            private Object positionName;
            @JsonProperty("levelCode")
            private Object levelCode;
            @JsonProperty("levelName")
            private String levelName;
            @JsonProperty("superiorId")
            private Object superiorId;
            @JsonProperty("superiorName")
            private Object superiorName;
            @JsonProperty("isManager")
            private Object isManager;
            @JsonProperty("workType")
            private Object workType;
            @JsonProperty("workYears")
            private Double workYears;
            @JsonProperty("cityCode")
            private Object cityCode;
            @JsonProperty("placeCode")
            private Object placeCode;
            @JsonProperty("idPhoto")
            private Object idPhoto;
            @JsonProperty("workState")
            private Object workState;
            @JsonProperty("nation")
            private Object nation;
            @JsonProperty("placeOrigin")
            private Object placeOrigin;
            @JsonProperty("maritalStatus")
            private Object maritalStatus;
            @JsonProperty("politicalStatus")
            private Object politicalStatus;
            @JsonProperty("beforeWorkYears")
            private Double beforeWorkYears;
            @JsonProperty("address")
            private Object address;
            @JsonProperty("emergencyContact")
            private Object emergencyContact;
            @JsonProperty("emergencyContactMobile")
            private Object emergencyContactMobile;
            @JsonProperty("authority")
            private Object authority;
            @JsonProperty("isMealMenu")
            private Object isMealMenu;
            @JsonProperty("isAttendanceMenu")
            private Object isAttendanceMenu;
            @JsonProperty("result")
            private Object result;
            @JsonProperty("levelGrade")
            private Object levelGrade;
            @JsonProperty("commonName")
            private Object commonName;
            @JsonProperty("userType")
            private Object userType;
            @JsonProperty("companyCode")
            private Object companyCode;
            @JsonProperty("companyName")
            private Object companyName;
        }
    }
}
