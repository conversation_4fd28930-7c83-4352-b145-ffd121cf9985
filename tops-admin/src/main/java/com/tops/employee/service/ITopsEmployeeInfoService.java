package com.tops.employee.service;

import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.employee.domain.bo.TopsEmployeeInfoBo;
import com.tops.employee.domain.vo.TopsEmployeeInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 人员信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface ITopsEmployeeInfoService {

    /**
     * 查询人员信息
     */
    TopsEmployeeInfoVo queryById(Long id);

    /**
     * 查询人员信息列表
     */
    TableDataInfo<TopsEmployeeInfoVo> queryPageList(TopsEmployeeInfoBo bo, PageQuery pageQuery);

    /**
     * 查询人员信息列表
     */
    List<TopsEmployeeInfoVo> queryList(TopsEmployeeInfoBo bo);

    /**
     * 新增人员信息
     */
    Boolean insertByBo(TopsEmployeeInfoBo bo);

    /**
     * 修改人员信息
     */
    Boolean updateByBo(TopsEmployeeInfoBo bo);

    /**
     * 校验并批量删除人员信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
