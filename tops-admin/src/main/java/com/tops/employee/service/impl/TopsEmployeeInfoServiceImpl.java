package com.tops.employee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.employee.adapter.HrErpAdapter;
import com.tops.employee.domain.TopsEmployeeInfo;
import com.tops.employee.domain.bo.TopsEmployeeInfoBo;
import com.tops.employee.domain.vo.TopsEmployeeInfoVo;
import com.tops.employee.dto.ErpDTO;
import com.tops.employee.mapper.TopsEmployeeInfoMapper;
import com.tops.employee.service.ITopsEmployeeInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RequiredArgsConstructor
@Service
public class TopsEmployeeInfoServiceImpl implements ITopsEmployeeInfoService {

    private final TopsEmployeeInfoMapper baseMapper;

    @Autowired
    private HrErpAdapter hrErpAdapter;

    /**
     * 查询人员信息
     */
    @Override
    public TopsEmployeeInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询人员信息列表
     */
    @Override
    public TableDataInfo<TopsEmployeeInfoVo> queryPageList(TopsEmployeeInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopsEmployeeInfo> lqw = buildQueryWrapper(bo);
        Page<TopsEmployeeInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询人员信息列表
     */
    @Override
    public List<TopsEmployeeInfoVo> queryList(TopsEmployeeInfoBo bo) {
        LambdaQueryWrapper<TopsEmployeeInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TopsEmployeeInfo> buildQueryWrapper(TopsEmployeeInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopsEmployeeInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getUserId()), TopsEmployeeInfo::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getAvatar()), TopsEmployeeInfo::getAvatar, bo.getAvatar());
        lqw.like(StringUtils.isNotBlank(bo.getErp()), TopsEmployeeInfo::getErp, bo.getErp());
        lqw.like(StringUtils.isNotBlank(bo.getName()), TopsEmployeeInfo::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgCode()), TopsEmployeeInfo::getOrgCode, bo.getOrgCode());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgName()), TopsEmployeeInfo::getOrgName, bo.getOrgName());
        lqw.eq(StringUtils.isNotBlank(bo.getFullOrgCode()), TopsEmployeeInfo::getFullOrgCode, bo.getFullOrgCode());
        lqw.like(StringUtils.isNotBlank(bo.getFullOrgName()), TopsEmployeeInfo::getFullOrgName, bo.getFullOrgName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), TopsEmployeeInfo::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), TopsEmployeeInfo::getEmail, bo.getEmail());
        lqw.like(StringUtils.isNotBlank(bo.getPositionName()), TopsEmployeeInfo::getPositionName, bo.getPositionName());
        lqw.eq(bo.getSex() != null, TopsEmployeeInfo::getSex, bo.getSex());
        lqw.eq(bo.getBirthday() != null, TopsEmployeeInfo::getBirthday, bo.getBirthday());
        lqw.eq(bo.getAge() != null, TopsEmployeeInfo::getAge, bo.getAge());
        lqw.like(StringUtils.isNotBlank(bo.getLevelName()), TopsEmployeeInfo::getLevelName, bo.getLevelName());
        lqw.eq(bo.getEntryDate() != null, TopsEmployeeInfo::getEntryDate, bo.getEntryDate());
        lqw.eq(bo.getDutyNotify() != null, TopsEmployeeInfo::getDutyNotify, bo.getDutyNotify());
        lqw.eq(StringUtils.isNotBlank(bo.getSubGroup()), TopsEmployeeInfo::getSubGroup, bo.getSubGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getExtMap()), TopsEmployeeInfo::getExtMap, bo.getExtMap());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TopsEmployeeInfo::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增人员信息
     */
    @Override
    public Boolean insertByBo(TopsEmployeeInfoBo bo) {
        String[] erpArr = bo.getErp().split(",");
        for (String erp : erpArr) {
            // 1. 通过EPR获取EPR信息
            ErpDTO erpDTO = hrErpAdapter.request(erp);
            // 2. 信息构建
            TopsEmployeeInfo add = new TopsEmployeeInfo();
            add.setUserId(erpDTO.getUserId());
            add.setAvatar(erpDTO.getAvatar());
            add.setErp(erp);
            add.setName(erpDTO.getName());
            add.setOrgCode(erpDTO.getOrgCode());
            add.setOrgName(erpDTO.getOrgName());
            add.setFullOrgCode(erpDTO.getFullOrgCode());
            add.setFullOrgName(erpDTO.getFullOrgName());
            add.setPhone(erpDTO.getPhone());
            add.setEmail(erpDTO.getEmail());
            add.setPositionName(erpDTO.getPositionName());
            //原始 男1 女2， 系统男0 女1
            add.setSex(erpDTO.getSex() - 1);
            add.setBirthday(TopsDateUtils.toDate(erpDTO.getBirthday()));
            add.setAge(Long.valueOf(erpDTO.getAge()));
            add.setLevelName(erpDTO.getLevelName());
            add.setEntryDate(TopsDateUtils.toDate(erpDTO.getEntryDate()));
            if (bo.getDutyNotify() != null) {
                add.setDutyNotify(bo.getDutyNotify());
            }
            if (bo.getSubGroup() != null) {
                add.setSubGroup(bo.getSubGroup());
            }
            //baseMapper.insertOrUpdate(add, new LambdaUpdateWrapper<TopsEmployeeInfo>().set(TopsEmployeeInfo::getErp, erp));
            // 查询是否存在记录
            TopsEmployeeInfo existingRecord = baseMapper.selectOne(
                new LambdaQueryWrapper<TopsEmployeeInfo>().eq(TopsEmployeeInfo::getErp, erp)
            );
            if (existingRecord != null) {
                // 存在记录，执行更新
                add.setId(existingRecord.getId()); // 确保设置了正确的ID
                baseMapper.updateById(add);
            } else {
                // 不存在记录，执行插入
                baseMapper.insert(add);
            }

        }
        //TopsEmployeeInfo add = BeanUtil.toBean(bo, TopsEmployeeInfo.class);
        return Boolean.TRUE;
    }

    /**
     * 修改人员信息
     */
    @Override
    public Boolean updateByBo(TopsEmployeeInfoBo bo) {
        if (bo.getId() == null) {
            return false;
        }
        LambdaUpdateWrapper<TopsEmployeeInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TopsEmployeeInfo::getId, bo.getId());

        if (bo.getDutyNotify() != null) {
            updateWrapper.set(TopsEmployeeInfo::getDutyNotify, bo.getDutyNotify());
        }

        // 如果需要更新其他字段，可以在这里添加其他的set操作

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private boolean validEntityBeforeSave(TopsEmployeeInfo entity) {
        // 如果只是更新值班通知状态，我们可以跳过这个验证
        if (entity.getDutyNotify() != null && entity.getErp() == null) {
            return true;
        }

        TopsEmployeeInfoBo bo = new TopsEmployeeInfoBo();
        bo.setErp(entity.getErp());
        List<TopsEmployeeInfoVo> vos = queryList(bo);
        if (vos.size() > 0) {
            return false;
        }
        return true;
    }

    /**
     * 批量删除人员信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
