package com.tops.employee.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.employee.domain.bo.TopsEmployeeInfoBo;
import com.tops.employee.domain.vo.TopsEmployeeInfoVo;
import com.tops.employee.service.ITopsEmployeeInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 人员信息
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/employee/employeeInfo")
public class TopsEmployeeInfoController extends BaseController {

    private final ITopsEmployeeInfoService iTopsEmployeeInfoService;

    /**
     * 查询人员信息列表
     */
    @SaCheckPermission("employee:employeeInfo:list")
    @GetMapping("/list")
    public TableDataInfo<TopsEmployeeInfoVo> list(TopsEmployeeInfoBo bo, PageQuery pageQuery) {
        return iTopsEmployeeInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人员信息列表
     */
    @SaCheckPermission("employee:employeeInfo:export")
    @Log(title = "人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsEmployeeInfoBo bo, HttpServletResponse response) {
        List<TopsEmployeeInfoVo> list = iTopsEmployeeInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员信息", TopsEmployeeInfoVo.class, response);
    }

    /**
     * 获取人员信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("employee:employeeInfo:query")
    @GetMapping("/{id}")
    public R<TopsEmployeeInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long id) {
        return R.ok(iTopsEmployeeInfoService.queryById(id));
    }

    /**
     * 新增人员信息
     */
    @SaCheckPermission("employee:employeeInfo:add")
    @Log(title = "人员信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsEmployeeInfoBo bo) {
        return toAjax(iTopsEmployeeInfoService.insertByBo(bo));
    }

    /**
     * 修改人员信息
     */
    @SaCheckPermission("employee:employeeInfo:edit")
    @Log(title = "人员信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsEmployeeInfoBo bo) {
        return toAjax(iTopsEmployeeInfoService.updateByBo(bo));
    }

    /**
     * 删除人员信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("employee:employeeInfo:remove")
    @Log(title = "人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsEmployeeInfoService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
