package com.tops.pfinder.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jd.jsf.gd.util.NetUtils;
import com.jd.jsf.open.api.ApplicationService;
import com.jd.jsf.open.api.ConsumerService;
import com.jd.jsf.open.api.domain.Client;
import com.jd.jsf.open.api.domain.app.JsfDependInfo;
import com.jd.jsf.open.api.vo.PageRequest;
import com.jd.jsf.open.api.vo.Result;
import com.jd.jsf.open.api.vo.request.QueryAppIfaceRequest;
import com.jd.jsf.open.api.vo.request.QueryConsumerRequest;
import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.jdos.utils.HttpUtil;
import com.tops.pfinder.bean.dto.*;
import com.tops.pfinder.enums.PFinderApiEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Component
public class JsfAdapter {
    private final static Integer SUCCESS_CODE = 1;
    @Autowired
    private ConsumerService consumerService;
    @Autowired
    private ApplicationService applicationService;

    private AtomicInteger counter = new AtomicInteger();

    @RateLimit(type = RateLimit.RateLimitType.LOCAL
        , maxRate = 5
        , rateUnit = RateLimit.RateUnit.SECOND
        , abortPolicy = RateLimit.AbortPolicy.WAIT
        , waitSeconds = 10)
    public List<JsfConsumerConfigDTO> queryJsfConsumerConfig(String appName, String apiName, String queryAlias) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (StringUtils.isBlank(apiName)) {
            log.warn("JsfAdapter.queryJsfConsumerConfig 查询JSF配置失败，apiName必填");
            throw new InvalidRequestException("查询JSF配置失败，apiName必填");
        }
        QueryConsumerRequest request = new QueryConsumerRequest();
        //固定必填参数
        request.setAppKey("jdos_jdl-pms-service");
        request.setOperator("wangqin83");
        request.setTimeStamp(System.currentTimeMillis());
        request.setSign(request.sign("SafVROYIftiNREZcoBmklaqwOppzSM"));
        request.setClientIp(NetUtils.getLocalHost());
        //
        if (apiName.contains("#")) {
            apiName = apiName.split("#")[0];
        }

        if (appName != null && !appName.startsWith("jdos_")) {
            appName = "jdos_" + appName;
        }
        request.setInterfaceName(apiName);
        request.setAppName(appName);
        if (StringUtils.isNotBlank(queryAlias)) {
            request.setAlias(queryAlias);
        }
        Result<List<Client>> response = null;
        try {
            response = consumerService.searchConsumer(request);
        } catch (Exception e) {
            log.error("JsfAdapter.queryJsfConsumerConfig 解析consumer配置失败。appName:{},apiName:{},e:", appName, apiName, e);
            throw new DependencyFailureException("查询失败");
        }
        log.info("JsfAdapter.queryJsfConsumerConfig appName:{},apiName:{},response:{}", appName, apiName, JSON.toJSONString(response));
        if (response == null) {
            log.error("JsfAdapter.queryJsfConsumerConfig 查询失败，返回结果为null");
            throw new DependencyFailureException("查询失败，返回结果为null");
        }
        if (!Objects.equals(response.getCode(), SUCCESS_CODE)) {
            log.error("JsfAdapter.queryJsfConsumerConfig 查询JSF consumer配置失败，appName:{},apiName:{},response:{}", appName, apiName, JSON.toJSONString(response));
            throw new DependencyFailureException(String.format("查询失败,code:%s,msg:%s", response.getCode(), response.getMsg()));
        }
        Map<String, JsfConsumerConfigDTO> aliasConfigMapping = new LinkedHashMap<>();
        for (Client datum : response.getData()) {
            String alias = datum.getAlias();
            if (aliasConfigMapping.containsKey(alias)) {
                continue;
            }
            try {
                JsfConsumerConfigDTO consumerConfigDTO = revolveTimeout(alias, apiName, datum.getSafUrlDesc());
                aliasConfigMapping.put(alias, consumerConfigDTO);
            } catch (Exception e) {
                log.error("JsfAdapter.queryJsfConsumerConfig 解析consumer配置失败，格式异常。appName:{},apiName:{},response:{}", appName, apiName, JSON.toJSONString(response));
            }
        }
        return new ArrayList<>(aliasConfigMapping.values());
    }

    public Map<String, JsfConsumerInfo> queryJsfConsumerInfo(Integer appId, String interfaceName, Integer offset, Integer pageSize) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (StringUtils.isBlank(interfaceName)) {
            log.warn("JsfAdapter.queryJsfConsumerConfig 查询JSF配置失败，apiName必填");
            throw new InvalidRequestException("查询JSF配置失败，apiName必填");
        }
        QueryAppIfaceRequest request = new QueryAppIfaceRequest();
        //固定必填参数
        request.setAppKey("jdos_jdl-pms-service");
        request.setOperator("wangqin83");
        request.setTimeStamp(System.currentTimeMillis());
        request.setSign(request.sign("SafVROYIftiNREZcoBmklaqwOppzSM"));
        request.setClientIp(NetUtils.getLocalHost());
        request.setOffset(offset);
        request.setPageSize(pageSize);
        //
        if (interfaceName.contains("#")) {
            interfaceName = interfaceName.split("#")[0];
        }
        request.setInterfaceName(interfaceName);
        request.setAppId(appId);
        Result<List<JsfDependInfo>> response = null;
        try {
            response = applicationService.getDependOnMeByIfaceAndAppId(request);
        } catch (Exception e) {
            log.error("JsfAdapter.queryJsfConsumerInfo 解析consumer失败。,apiName:{},e:", interfaceName, e);
            throw new DependencyFailureException("查询失败");
        }
        log.info("JsfAdapter.queryJsfConsumerInfo ,apiName:{},response:{}", interfaceName, JSON.toJSONString(response));
        if (response == null) {
            log.error("JsfAdapter.queryJsfConsumerInfo 查询失败，返回结果为null");
            throw new DependencyFailureException("查询失败，返回结果为null");
        }
        if (!Objects.equals(response.getCode(), SUCCESS_CODE)) {
            log.error("JsfAdapter.queryJsfConsumerInfo 查询JSF consumer失败，,apiName:{},response:{}", interfaceName, JSON.toJSONString(response));
            throw new DependencyFailureException(String.format("查询失败,code:%s,msg:%s", response.getCode(), response.getMsg()));
        }
        Map<String, JsfConsumerInfo> result = new LinkedHashMap<>();
        for (JsfDependInfo datum : response.getData()) {
            String consumerAppName = datum.getAppName();
            if (StringUtils.isBlank(consumerAppName)) {
                JsfConsumerInfo jsfConsumerInfo = new JsfConsumerInfo();
                jsfConsumerInfo.setApiName(datum.getIfaceName());
                jsfConsumerInfo.setConsumerAppName("*");
                result.put("*", jsfConsumerInfo);
                continue;
            }
            if (result.containsKey(consumerAppName)) {
                continue;
            }
            JsfConsumerInfo jsfConsumerInfo = new JsfConsumerInfo();
            jsfConsumerInfo.setApiName(datum.getIfaceName());
            jsfConsumerInfo.setConsumerAppName(datum.getAppName());
            jsfConsumerInfo.setConsumerAppOwner(datum.getLeader());
            jsfConsumerInfo.setConsumerAppOwnerNickName(datum.getLeaderName());
            jsfConsumerInfo.setConsumerAppOwnerDeptName(datum.getDeptName());
            result.put(consumerAppName, jsfConsumerInfo);
        }
        return result;
    }

    public static JsfConsumerConfigDTO revolveTimeout(String alias, String apiName, String desc) {
        int index = desc.indexOf("attrs:{");
        String substring = desc.substring(index + "attrs:{".length(), desc.length() - 2);
        String[] split = substring.split(",");
        JsfConsumerConfigDTO consumerConfigDTO = new JsfConsumerConfigDTO();
        consumerConfigDTO.setAlias(alias);
        consumerConfigDTO.setApiName(apiName);
        for (String s : split) {
            if (s.contains("timeout")) {
                String[] split1 = s.trim().split("=");
                String timeout = split1[1];
                consumerConfigDTO.setTimeout(timeout);

            }
            if (s.contains("retries")) {
                String[] split1 = s.trim().split("=");
                String retries = split1[1];
                consumerConfigDTO.setRetries(retries);
            }
        }
        if (StringUtils.isBlank(consumerConfigDTO.getTimeout())) {
            consumerConfigDTO.setTimeout("5000");
        }
        if (StringUtils.isBlank(consumerConfigDTO.getRetries())) {
            consumerConfigDTO.setRetries("0");
        }
        return consumerConfigDTO;
    }


    public Map<String,Double> queryConsumerAppCallTimes(String interfaceName, String methodName,Date startTime,Date endTime,String ck) throws InternalFailureException,InvalidRequestException,DependencyFailureException {
        JSONObject parameters = new JSONObject();
        parameters.put("startTime",startTime.getTime());
        parameters.put("endTime", endTime.getTime());
        parameters.put("topN", 100);
        parameters.put("interfaceName", interfaceName);
        parameters.put("method", methodName);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Cookie", ck);
        JSFResponse<QueryConsumerAppCallTimesResponse> response = new JSFResponse<>();
        try {
            String responseJson = HttpUtil.post("taishan.jd.com"
                , "api/jsf/v1/traffic_protection/traffic_observe/metric/queryConsumerAppCallTimes"
                , JSON.toJSONString(parameters), headers);
            response = JSON.parseObject(responseJson, new TypeReference<JSFResponse<QueryConsumerAppCallTimesResponse>>() {
            });
            log.info("PFinderAdapter.queryMetricTraceIds request:{},response:{}", JSON.toJSONString(parameters), JSON.toJSONString(response));
            List<Double> data = response.getContent().getData();
            List<String> metric = response.getContent().getMetric();
            Map<String,Double> result=new LinkedHashMap<>();
            for (int i = 0; i < data.size(); i++) {
                result.put(metric.get(i),data.get(i));
            }
            return result;
        } catch (Exception e) {
            log.error("PFinderAdapter.queryMetricTraceIds 查询指标对应traceIds失败：未知异常,request:{},exception:", JSON.toJSONString(parameters), e);
            throw new DependencyFailureException("查询指标对应traceIds失败：未知异常");
        }
    }
}
