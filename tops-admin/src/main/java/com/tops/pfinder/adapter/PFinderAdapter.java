package com.tops.pfinder.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tops.common.aspect.annotation.RateLimit;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.ThreadUtil;
import com.tops.jdos.service.JDOSService;
import com.tops.jdos.utils.HttpUtil;
import com.tops.pfinder.bean.dto.*;
import com.tops.pfinder.bean.po.SpanPO;
import com.tops.pfinder.bean.po.TracePO;
import com.tops.pfinder.bean.vo.Metric;
import com.tops.pfinder.bean.vo.Node;
import com.tops.pfinder.bean.vo.Span;
import com.tops.pfinder.bean.vo.Trace;
import com.tops.pfinder.domain.PFinderContext;
import com.tops.pfinder.enums.ComponentEnum;
import com.tops.pfinder.enums.MetricTypeEnum;
import com.tops.pfinder.enums.PFinderApiEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Getter
@Setter
@Slf4j
@Component
public class PFinderAdapter implements ApplicationContextAware {
    @Value("${pfinder.api.host}")
    private String host;

    @Value("${pfinder.api.token}")
    private String token;

    @Value("${trace.component.name.length.max}")
    private Integer COMPONENT_NAME_LENGTH_MAX;
    @Autowired
    private JDOSService jdosService;

    private ApplicationContext applicationContext;


    public void queryTrace(PFinderContext context) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("token", token);
        List<String> traceIds = context.getTraceIds();
        Map<String, TraceInfo> traceInfoMap = context.getTraces().stream().collect(Collectors.toMap(TraceInfo::getTraceId, traceInfo -> traceInfo, (t1, t2) -> t1));
        PFinderAdapter proxyAdapter = applicationContext.getBean(PFinderAdapter.class);
        for (String traceId : traceIds) {
            try {
                Trace response = proxyAdapter.queryTraceHttp(parameters, headers, traceId);
                TraceInfo traceInfo = resolveTrace(response, traceId);
                log.info("PFinderAdapter.queryTrace traceInfo:{}", JSON.toJSONString(traceInfo));
                traceInfoMap.put(traceId, traceInfo);
            } catch (Exception e) {
                log.error("PFinderAdapter.queryTrace 查询traceId对应trace失败：未知异常,traceId:{},exception:", traceId, e);
                throw new DependencyFailureException("查询traceId对应trace失败：未知异常");
            }
        }
        context.withTraces(new ArrayList<>(traceInfoMap.values()));
    }

    @RateLimit(type = RateLimit.RateLimitType.LOCAL
        , maxRate = 1
        , rateUnit = RateLimit.RateUnit.SECOND
        , abortPolicy = RateLimit.AbortPolicy.WAIT
        , waitSeconds = 10
        , waitGapMillis = 10)
    public Trace queryTraceHttp(Map<String, String> parameters, Map<String, String> headers, String traceId) throws URISyntaxException, IOException {
        HttpUtil httpUtil = new HttpUtil();
        String responseJson = httpUtil.getRequest(host
            , String.format(PFinderApiEnum.QUERY_TRACE.getApi(), traceId)
            , parameters, headers);
        Trace response = new Trace();
        response = JSON.parseObject(responseJson, new TypeReference<Trace>() {
        });
        log.info("PFinderAdapter.queryTrace traceId:{},response:{}", traceId, JSON.toJSONString(response));

        return response;
    }

    @RateLimit(type = RateLimit.RateLimitType.LOCAL
        , maxRate = 1
        , rateUnit = RateLimit.RateUnit.SECOND
        , abortPolicy = RateLimit.AbortPolicy.WAIT
        , waitSeconds = 10
        , waitGapMillis = 10)
    public void queryMetricTraceIds(PFinderContext context) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("appCoord", context.getAppCoord());
        parameters.put("methodId", context.getApiMetricId().toString());
        //TODO wangqin83 替换为实际参数
        parameters.put("pageIndex", context.getPageNum().toString());
        parameters.put("pageSize", context.getPageSize().toString());
        parameters.put("timeBegin", context.getStartTime().getTime() + "");
        parameters.put("timeEnd", context.getEndTime().getTime() + "");

        Map<String, String> headers = new HashMap<>();
        headers.put("token", token);
        headers.put("Content-Type", "application/json");
        PFinderResponse<List<TraceIdInfo>> response = new PFinderResponse<>();
        try {
            String responseJson = HttpUtil.post(host
                , PFinderApiEnum.QUERY_TRACE_IDS.getApi()
                , JSON.toJSONString(parameters), headers);
            response = JSON.parseObject(responseJson, new TypeReference<PFinderResponse<List<TraceIdInfo>>>() {
            });
            log.info("PFinderAdapter.queryMetricTraceIds request:{},response:{}", JSON.toJSONString(parameters), JSON.toJSONString(response));
            List<String> traceIds = resolveMetricTraceIds(response);
            context.withTraceIds(traceIds);
        } catch (InternalFailureException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("PFinderAdapter.queryMetricTraceIds 查询指标对应traceIds失败：未知异常,request:{},exception:", JSON.toJSONString(parameters), e);
            throw new DependencyFailureException("查询指标对应traceIds失败：未知异常");
        }
    }

    @RateLimit(type = RateLimit.RateLimitType.LOCAL
        , maxRate = 10
        , rateUnit = RateLimit.RateUnit.SECOND
        , abortPolicy = RateLimit.AbortPolicy.WAIT
        , waitSeconds = 10
        , waitGapMillis = 10)
    public void queryMetric(PFinderContext context) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, String> parameters = new HashMap<>();
        parameters.put("name", context.getApiMetricName());
        Map<String, String> headers = new HashMap<>();
        headers.put("token", token);
        HttpUtil httpUtil = new HttpUtil();
        Metric response = new Metric();
        try {
            String responseJson = httpUtil.getRequest(host
                , PFinderApiEnum.QUERY_METRIC.getApi()
                , parameters, headers);
            log.info("PFinderAdapter.queryMetric name:{},responseJson:{}", context.getApiMetricName(), responseJson);
            response = JSON.parseObject(responseJson, new TypeReference<Metric>() {
            });
            log.info("PFinderAdapter.queryMetric name:{},response:{}", context.getApiMetricName(), JSON.toJSONString(response));
            Long metricId = resolveMetricId(response);
            context.withApiMetricId(metricId);
        } catch (URISyntaxException e) {
            log.error("PFinderAdapter.queryMetric 查询接口对应指标ID失败：URI语法异常,apiMetricName:{},exception:", context.getApiMetricName(), e);
            throw new InternalFailureException("查询接口对应指标ID失败：URI语法异常");
        } catch (IOException e) {
            log.error("PFinderAdapter.queryMetric 查询接口对应指标ID失败：IO异常,apiMetricName:{},exception:", context.getApiMetricName(), e);
            throw new InternalFailureException("查询接口对应指标ID失败：IO异常");
        } catch (InternalFailureException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            log.error("PFinderAdapter.queryMetric 查询接口对应指标ID失败：未知异常,apiMetricName:{},exception:", context.getApiMetricName(), e);
            throw new DependencyFailureException("查询接口对应指标ID失败：未知异常");
        }
    }


    public Map<String, JsfConsumerMetricDTO> queryJsfConsumerMetric(String apiName, List<String> consumerApps, Boolean tooManyConsumerApp) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<String, JsfConsumerMetricDTO> result = new HashMap<>();
        if (tooManyConsumerApp) {
            for (String appName : consumerApps) {
                Map<String, JsfConsumerMetricDTO> metrics = queryJsfConsumerMetricByApp(apiName, appName, consumerApps);
                if (!org.springframework.util.CollectionUtils.isEmpty(metrics)) {
                    result.putAll(metrics);
                }
            }
        } else {
            Map<String, JsfConsumerMetricDTO> metrics = queryJsfConsumerMetricByApp(apiName, null, consumerApps);
            if (!org.springframework.util.CollectionUtils.isEmpty(metrics)) {
                result = metrics;
            }
        }
        return result;
    }


    public Map<String, JsfConsumerMetricDTO> queryJsfConsumerMetricByApp(String apiName, String targetAppName, List<String> allConsumerApps) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        PFinderAdapter proxyAdapter = applicationContext.getBean(PFinderAdapter.class);
        List<JSONObject> tp99Response = new ArrayList<>();
        try {
            tp99Response = proxyAdapter.queryJsfConsumerMetricHttp(apiName, targetAppName, MetricTypeEnum.TP99);
        } catch (Exception e) {
            for (int i = 0; i < 5; i++) {
                try {
                    log.info("PFinderAdapter.queryJsfConsumerMetric 查询数据异常，开始重试");
                    tp99Response = proxyAdapter.queryJsfConsumerMetricHttp(apiName, targetAppName, MetricTypeEnum.TP99);
                    break;
                } catch (Exception exception) {
                    log.error("PFinderAdapter.queryJsfConsumerMetric 查询指标数据失败：exception:", e);
                }
            }
            log.error("PFinderAdapter.queryJsfConsumerMetric 查询指标数据重试后失败");
            throw new DependencyFailureException("查询指标对应traceIds失败：未知异常");
        }
        List<JSONObject> totalCountResponse = new ArrayList<>();
        try {
            totalCountResponse = proxyAdapter.queryJsfConsumerMetricHttp(apiName, targetAppName, MetricTypeEnum.TOTAL_COUNT);
        } catch (Exception e) {
            for (int i = 0; i < 5; i++) {
                try {
                    log.info("PFinderAdapter.queryJsfConsumerMetric 查询数据异常，开始重试");
                    totalCountResponse = proxyAdapter.queryJsfConsumerMetricHttp(apiName, targetAppName, MetricTypeEnum.TOTAL_COUNT);
                    break;
                } catch (Exception exception) {
                    log.error("PFinderAdapter.queryJsfConsumerMetric 查询指标数据失败：exception:", e);
                }
            }
            log.error("PFinderAdapter.queryJsfConsumerMetric 查询指标数据重试后失败");
            throw new DependencyFailureException("查询指标对应traceIds失败：未知异常");
        }

        return resolveMetric(apiName, allConsumerApps, tp99Response, totalCountResponse, MetricTypeEnum.TP99);
    }

    @RateLimit(type = RateLimit.RateLimitType.LOCAL
        , maxRate = 10
        , rateUnit = RateLimit.RateUnit.SECOND
        , abortPolicy = RateLimit.AbortPolicy.WAIT
        , waitSeconds = 10
        , waitGapMillis = 500)
    public List<JSONObject> queryJsfConsumerMetricHttp(String apiName, String appName, MetricTypeEnum metricTypeEnum) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        //配置接口及应用
        JSONObject labelEquals = new JSONObject();
        labelEquals.put("MID", String.format("JsfC@%s", apiName));
        labelEquals.put("MT", "HISTOGRAM");
        labelEquals.put("_AGT", "pro");
        if (StringUtils.isNotBlank(appName)) {
            labelEquals.put("APP", appName);
        }

        JSONObject dataSelect = new JSONObject();
        dataSelect.put("labelEquals", labelEquals);
        dataSelect.put("labelNotEquals", new HashMap<>());
        dataSelect.put("labelNotIn", new HashMap<>());
        dataSelect.put("labelMatches", new HashMap<>());
        dataSelect.put("labelNotMatches", new HashMap<>());
        dataSelect.put("enableIdConvert", true);

        //配置聚合方式
        JSONObject aggregation = new JSONObject();
        aggregation.put("type", metricTypeEnum.getAggregationType());
        List<String> groupBy = new ArrayList<>();
        groupBy.add("APP");
        aggregation.put("groupBy", groupBy);


        //组装参数
        JSONObject parameters = new JSONObject();
        parameters.put("dataSelect", dataSelect);
        parameters.put("aggregation", aggregation);

        Date begin = DateUtils.queryDayByDiffSource(new Date(), -1);
        Date end = DateUtils.queryDayLastMinByDiffSource(begin, 0);

        parameters.put("beginTimestamp", begin.getTime());
        parameters.put("endTimestamp", end.getTime());
        parameters.put("minStep", 60000L);

        Map<String, String> headers = new HashMap<>();
        headers.put("token", token);
        headers.put("Content-Type", "application/json");

        List<JSONObject> response = new ArrayList<>();
        try {
            String responseJson = HttpUtil.post(host
                , PFinderApiEnum.QUERY_METRIC_DATA.getApi()
                , JSON.toJSONString(parameters), headers);
            response = JSON.parseObject(responseJson, new TypeReference<List<JSONObject>>() {
            });
            log.info("PFinderAdapter.queryJsfConsumerMetric request:{},response size:{}", JSON.toJSONString(parameters), JSON.toJSONString(response.size()));
        } catch (Exception e) {
            log.error("PFinderAdapter.queryJsfConsumerMetric 查询指标数据重失败：未知异常,request:{},exception:", JSON.toJSONString(parameters), e);
            throw new DependencyFailureException("查询指标对应traceIds失败：未知异常");
        }
        return response;
    }

    private Long resolveMetricId(Metric response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null) {
            log.error("PFinderAdapter.resolveMetricId 查询接口对应指标ID失败：调用接口返回失败");
            throw new DependencyFailureException("查询接口对应指标ID失败：调用接口返回失败");
        }
        try {
            return response.getId();
        } catch (Exception e) {
            log.error("PFinderAdapter.resolveMetricId 查询接口对应指标ID失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询接口对应指标ID失败：解析返回值失败");
        }
    }

    private List<String> resolveMetricTraceIds(PFinderResponse<List<TraceIdInfo>> response) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null || !Objects.equals(response.getCode(), 200) || response.getData() == null) {
            log.error("PFinderAdapter.resolveMetricTraceIds 查询指标对应traceIds失败：调用接口返回失败");
            throw new DependencyFailureException("查询指标对应traceIds失败：调用接口返回失败");
        }
        try {
            return response.getData().stream().map(TraceIdInfo::getTraceId).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("PFinderAdapter.resolveMetricTraceIds 查询指标对应traceIds失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询指标对应traceIds失败：解析返回值失败");
        }
    }

    private TraceInfo resolveTrace(Trace response, String traceId) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (response == null) {
            log.error("PFinderAdapter.resolveTrace 查询traceId对应trace失败：调用接口返回失败");
            throw new DependencyFailureException("查询traceId对应trace失败：调用接口返回失败");
        }
        try {
            SpanInfo root = resolveRootSpan(response.getGraph(), traceId);
            TraceInfo traceInfo = new TraceInfo();
            traceInfo.setRoot(root);
            traceInfo.setTraceId(traceId);
            traceInfo.setTraceNo(root.getTraceNo());
            return traceInfo;
        } catch (Exception e) {
            log.error("PFinderAdapter.resolveTrace 查询traceId对应trace失败：解析返回值失败，exception:", e);
            throw new InternalFailureException("查询traceId对应trace失败：解析返回值失败");
        }
    }

    private Map<String, JsfConsumerMetricDTO> resolveMetric(String apiName, List<String> allConsumerApps, List<JSONObject> tpResponses, List<JSONObject> countResponses, MetricTypeEnum metricTypeEnum) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        if (tpResponses == null || countResponses == null) {
            log.error("PFinderAdapter.resolveMetric 查询接口对应指标数据失败：调用接口返回失败");
            throw new DependencyFailureException("查询接口对应指标数据失败：调用接口返回失败");
        }
        if (CollectionUtils.isEmpty(tpResponses) || CollectionUtils.isEmpty(countResponses)) {
            return null;
        }
        Map<String, JSONObject> tpResponsesMap = tpResponses.stream().filter(response -> {
            Map<String, String> labels = (Map<String, String>) response.get("labels");
            return allConsumerApps.contains(labels.get("APP").replace("(JDOS)", ""));
        }).collect(Collectors.toMap((response) -> {
            Map<String, String> labels = (Map<String, String>) response.get("labels");
            return labels.get("APP");
        }, response -> response, (r1, r2) -> r1));

        Map<String, JSONObject> countResponsesMap = countResponses.stream().filter(response -> {
            Map<String, String> labels = (Map<String, String>) response.get("labels");
            return allConsumerApps.contains(labels.get("APP").replace("(JDOS)", ""));
        }).collect(Collectors.toMap((response) -> {
            Map<String, String> labels = (Map<String, String>) response.get("labels");
            return labels.get("APP");
        }, response -> response, (r1, r2) -> r1));

        Map<String, JsfConsumerMetricDTO> result = new HashMap<>();
        for (String appName : tpResponsesMap.keySet()) {
            List<MetricDealMidDTO> midDTO = toMidDTO(tpResponsesMap.get(appName), countResponsesMap.get(appName));
            midDTO.sort(Comparator.comparing(MetricDealMidDTO::getTp).reversed());
            long count = midDTO.stream().mapToLong(MetricDealMidDTO::getCount).sum();
            //解析TP99
            if (Objects.equals(metricTypeEnum, MetricTypeEnum.TP99)) {
                count = (long) (count * 0.01);
                int curSum = 0;
                for (MetricDealMidDTO metricDealMidDTO : midDTO) {
                    curSum += metricDealMidDTO.getCount();
                    if (curSum >= count) {
                        JsfConsumerMetricDTO jsfConsumerMetricDTO = new JsfConsumerMetricDTO();
                        jsfConsumerMetricDTO.setConsumerAppName(appName.replace("(JDOS)", ""));
                        jsfConsumerMetricDTO.setComponentType(ComponentEnum.JSF_CONSUMER.getComponentType());
                        jsfConsumerMetricDTO.setComponentName(apiName);
                        jsfConsumerMetricDTO.setTp99(metricDealMidDTO.getTp().toString());
                        result.put(appName, jsfConsumerMetricDTO);
                        break;
                    }
                }
            } else {
                log.error("PFinderAdapter.resolveMetric 不支持统计该类型数据");
                throw new InvalidRequestException("不支持统计该类型数据");
            }
        }
        return result;
    }

    private SpanInfo resolveRootSpan(Node rootNode, String traceId) {
        SpanInfo rootSpan = new SpanInfo();
        if (rootNode == null) {
            return rootSpan;
        }

        rootSpan.withAppInfo(rootNode.getAppName(), rootNode.getAppPlatform());

        ComponentInfo component = new ComponentInfo();
        component.setComponentName("root");
        component.setComponentDesc("root");
        component.setComponentType("root");
        rootSpan.setComponent(component);

        rootSpan.setSpanNo(rootNode.getAppName());
        rootSpan.setTraceNo(rootNode.getAppName());
        List<SpanInfo> children = resolveSpan(rootNode, rootSpan.getSpanNo(), traceId,rootSpan.getTraceNo());
        rootSpan.withChildren(children);
        //merge child span
        mergeSameChildren(rootSpan);
        //TODO wangqin83 增加parent span与child span 比例统计 ：按时间段（17-19、20:00-20:30、00:00-00:30）
        return rootSpan;
    }

    private List<SpanInfo> resolveSpan(Node rootNode, String parentSpanNo, String traceId,String traceNo) {
        if (rootNode == null) {
            return null;
        }
        List<SpanInfo> spanInfos = new ArrayList<>();
        List<Span> spans = rootNode.getSpans();
        for (Span span : spans) {
            if (Objects.equals(span.getComponent(), ComponentEnum.JSF_PROVIDER.getCode())) {
                for (Node son : span.getSons()) {
                    spanInfos.addAll(resolveSpan(son, parentSpanNo, traceId,traceNo));
                }
            } else if (Objects.equals(span.getComponent(), ComponentEnum.JSF_CONSUMER.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                //TODO wangqin83 调大componentName长度
                //保留接口名
                int index = span.getOperation().indexOf("@");
                String componentName = span.getOperation().substring(index + 1);
                if (componentName.contains("(generic)")) {
                    componentName = componentName.replace("(generic)","");
                }
                spanInfo.withComponentName(componentName.substring(0, Math.min(COMPONENT_NAME_LENGTH_MAX, componentName.length())));
//                spanInfo.withComponentName(span.getOperation().substring(0,Math.min(100,span.getOperation().length())));
                spanInfo.getComponent().setComponentType(ComponentEnum.JSF_CONSUMER.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                Set<String> uniqueChildName = new HashSet<>();
                for (Node son : span.getSons()) {
                    spanInfo.withAppInfo(son.getAppName(), son.getAppPlatform());
                    List<SpanInfo> sonSpan = resolveSpan(son, spanInfo.getSpanNo(), traceId,traceNo);
                    for (SpanInfo info : sonSpan) {
                        if (isMiddle(info.getComponent().getComponentName())
                            && uniqueChildName.contains(info.getComponent().getComponentName())) {
                            continue;
                        }
                        uniqueChildName.add(info.getComponent().getComponentName());
                        children.add(info);
                    }
                }
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.JMQ_PRODUCER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_JMQ_PRODUCER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.KAFKA_PRODUCER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_KAFKA_PRODUCER.getCode())) {
                for (Node son : span.getSons()) {
                    spanInfos.addAll(resolveSpan(son, parentSpanNo, traceId,traceNo));
                }
            } else if (Objects.equals(span.getComponent(), ComponentEnum.JMQ_CONSUMER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_JMQ_CONSUMER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.KAFKA_CONSUMER.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_KAFKA_CONSUMER.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                //TODO wangqin83 调大componentName长度
                int index = span.getOperation().indexOf("@");
                String componentName = span.getOperation().substring(index + 1);
                if (componentName.contains("(generic)")) {
                    componentName = componentName.replace("(generic)","");
                }
                spanInfo.withComponentName(componentName.substring(0, Math.min(COMPONENT_NAME_LENGTH_MAX, componentName.length())));
//                spanInfo.withComponentName(span.getOperation().substring(0,Math.min(100,span.getOperation().length())));

                spanInfo.getComponent().setComponentType(ComponentEnum.JMQ_CONSUMER.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                Set<String> uniqueChildName = new HashSet<>();
                //TODO wangqin83 获取MQ提供方
                if (CollectionUtils.isEmpty(span.getSons())) {
                    spanInfo.withAppInfo("未探知到MQ提供方", "jdos");
                }
                for (Node son : span.getSons()) {
                    spanInfo.withAppInfo(son.getAppName(), son.getAppPlatform());
                    List<SpanInfo> sonSpan = resolveSpan(son, spanInfo.getSpanNo(), traceId,traceNo);
                    for (SpanInfo info : sonSpan) {
                        if (isMiddle(info.getComponent().getComponentName())
                            && uniqueChildName.contains(info.getComponent().getComponentName())) {
                            continue;
                        }
                        uniqueChildName.add(info.getComponent().getComponentName());
                        children.add(info);
                    }
                }
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.JIMDB_CLIENT.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_JIMDB_CLIENT.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                spanInfo.withAppInfo(rootNode.getAppName(), rootNode.getAppPlatform());
                //TODO wangqin83 优化详情
                spanInfo.withComponentName("Redis");
                spanInfo.getComponent().setComponentType(ComponentEnum.JIMDB_CLIENT.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.MYSQL_CONNECTOR.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                spanInfo.withAppInfo(rootNode.getAppName(), rootNode.getAppPlatform());
                spanInfo.withComponentName("MySQL");
                spanInfo.getComponent().setComponentType(ComponentEnum.MYSQL_CONNECTOR.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.ES.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_ES.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                spanInfo.withAppInfo(rootNode.getAppName(), rootNode.getAppPlatform());
                spanInfo.withComponentName("Elastic Search");
                spanInfo.getComponent().setComponentType(ComponentEnum.ES.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.HBASE_CLIENT.getCode())) {
                SpanInfo spanInfo = new SpanInfo();
                spanInfo.withAppInfo(rootNode.getAppName(), rootNode.getAppPlatform());
                spanInfo.withComponentName("HBase");
                spanInfo.getComponent().setComponentType(ComponentEnum.HBASE_CLIENT.getComponentType());
                List<SpanInfo> children = new ArrayList<>();
                spanInfo.withChildren(children);
                spanInfo.setSpanNo(span.getId().toString());
                spanInfo.setParentNo(parentSpanNo);
                spanInfo.setTraceNo(traceNo);
                spanInfos.add(spanInfo);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.APACHE_HTTP_CLIENT.getCode())) {
                log.info("PFinderAdapter.resolveSpan 忽略该类型组件：{},prefix:{},traceId:{}", span.getComponent(), span.getComponentPrefix(), traceId);
            } else if (Objects.equals(span.getComponent(), ComponentEnum.CUSTOMIZE_TRACE.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_SPRING_TASK_EXECUTOR.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.ASYNC_CUSTOMIZE_TRACE.getCode())
                || Objects.equals(span.getComponent(), ComponentEnum.FASTJSON.getCode())) {
//                log.debug("PFinderAdapter.resolveSpan 忽略该类型组件：{},prefix:{}", span.getComponent(), span.getComponentPrefix());
            } else {
                log.info("PFinderAdapter.resolveSpan 暂不支持该类型组件：{},prefix:{}", span.getComponent(), span.getComponentPrefix());
                continue;
            }
        }
        return spanInfos;
    }

    public static void main(String[] args) {
        PFinderAdapter pFinderAdapter = new PFinderAdapter();
        pFinderAdapter.setHost("pfinder-api-open.jd.com");
        pFinderAdapter.setToken("********************************");

        PFinderContext context = new PFinderContext();
        context.withComponentName("cn.jdl.pms.api.ProductRecommendationService#checkProduct");
        context.withAppName("jdl-pms-service");
        context.withAppCoord("jdl-pms-service(jdos)");
//        context.withComponentName("cn.jdl.oms.supplychain.service.CreateOutboundOrderService#createOrder");
//        context.withAppName("jdl-oms-outbound");
//        context.withAppCoord("jdl-oms-outbound(jdos)");


        try {
            pFinderAdapter.queryMetric(context);
            pFinderAdapter.queryMetricTraceIds(context);
            pFinderAdapter.queryTrace(context);


            List<TraceInfo> traces = context.getTraces();
            for (TraceInfo trace : traces) {
                TracePO po = TraceInfo.toPO(trace);
                SpanInfo root = trace.getRoot();
                po.setTraceNo(root.getApplication().getName());
                List<SpanPO> spanPOS = SpanInfo.toPO(getSpans(root), po.getTraceNo());
                log.info("PFinder.main   tracePO:{}", JSON.toJSONString(po));
                log.info("PFinder.main   spanPOS:{}", JSON.toJSONString(spanPOS));
            }

        } catch (InvalidRequestException e) {
            throw new RuntimeException(e);
        } catch (DependencyFailureException e) {
            throw new RuntimeException(e);
        } catch (InternalFailureException e) {
            throw new RuntimeException(e);
        }
    }

    private static List<SpanInfo> getSpans(SpanInfo root) {
        if (root == null) {
            return new ArrayList<>();
        }
        List<SpanInfo> result = new ArrayList<>();
        result.add(root);
        if (root.getChildren() != null) {
            for (SpanInfo child : root.getChildren()) {
                result.addAll(getSpans(child));
            }
        }
        return result;
    }

    public static boolean isMiddle(String componentName) {
        return Objects.equals(componentName, "mysql")
            || Objects.equals(componentName, "jimdb")
            || Objects.equals(componentName, "redis")
            || Objects.equals(componentName, "es")
            || Objects.equals(componentName, "hbase")
            || Objects.equals(componentName, "Redis")
            || Objects.equals(componentName, "MySQL")
            || Objects.equals(componentName, "Elastic Search")
            || Objects.equals(componentName, "HBase")
            ;
    }

    public static boolean isMiddleByType(String componentType) {
        return Objects.equals(componentType, ComponentEnum.MYSQL_CONNECTOR.getComponentType())
            || Objects.equals(componentType, ComponentEnum.JIMDB_CLIENT.getComponentType())
            || Objects.equals(componentType, ComponentEnum.ES.getComponentType())
            || Objects.equals(componentType, ComponentEnum.HBASE_CLIENT.getComponentType())
            ;
    }

    public static boolean isMQ(String componentType) {
        return Objects.equals(componentType, ComponentEnum.JMQ_CONSUMER.getComponentType());
    }

    private void mergeSameChildren(SpanInfo rootSpan) {
        if (rootSpan == null) {
            return;
        }
        if (CollectionUtils.isEmpty(rootSpan.getChildren())) {
            return;
        }
        List<SpanInfo> children = rootSpan.getChildren();
        Map<String, SpanInfo> childrenMap = new LinkedHashMap<>();
        for (SpanInfo child : children) {
            if (childrenMap.containsKey(child.getComponent().getComponentName())) {
                SpanInfo existChild = childrenMap.get(child.getComponent().getComponentName());
                existChild.getChildren().addAll(child.getChildren());
                existChild.setInvokeCount(existChild.getInvokeCount() + 1);
                continue;
            }
            childrenMap.put(child.getComponent().getComponentName(), child);
        }
        rootSpan.setChildren(new ArrayList<>(childrenMap.values()));
        for (SpanInfo child : rootSpan.getChildren()) {
            mergeSameChildren(child);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private List<MetricDealMidDTO> toMidDTO(JSONObject tp99, JSONObject count) throws InvalidRequestException, DependencyFailureException, InternalFailureException {
        Map<Long, MetricDealMidDTO> metricDealMidDTOS = new HashMap<>();
        if (Objects.equals(tp99.get("code"), 10002)) {
            return null;
        }
        try {
            List<List<Object>> values = (List<List<Object>>) tp99.get("values");
            for (List<Object> value : values) {
                BigDecimal valueDouble = (BigDecimal) value.get(1);
                Long time = (Long) value.get(0);
                MetricDealMidDTO metricDealMidDTO = new MetricDealMidDTO();
                metricDealMidDTO.setTime(time);
                metricDealMidDTO.setTp(valueDouble.longValue());
                metricDealMidDTO.setCount(valueDouble.longValue());
                metricDealMidDTOS.put(metricDealMidDTO.getTime(), metricDealMidDTO);
            }
        } catch (Exception e) {
            log.error("PFinderAdapter.resolveMetric 查询接口对应指标数据失败：解析返回值失败，exception:", e);
            throw new DependencyFailureException("查询接口对应指标数据失败：调用接口返回格式异常");
        }

        if (Objects.equals(count.get("code"), 10002)) {
            return null;
        }
        try {
            List<List<Object>> values = (List<List<Object>>) tp99.get("values");
            for (List<Object> value : values) {
                BigDecimal valueDouble = (BigDecimal) value.get(1);
                Long time = (Long) value.get(0);
                MetricDealMidDTO metricDealMidDTO = metricDealMidDTOS.get(time);
                if (metricDealMidDTO != null) {
                    metricDealMidDTO.setCount(valueDouble.longValue());
                    metricDealMidDTOS.put(time, metricDealMidDTO);
                }
            }
        } catch (Exception e) {
            log.error("PFinderAdapter.resolveMetric 查询接口对应指标数据失败：解析返回值失败，exception:", e);
            throw new DependencyFailureException("查询接口对应指标数据失败：调用接口返回格式异常");
        }
        return new ArrayList<>(metricDealMidDTOS.values());
    }

    @Getter
    @Setter
    private static class MetricDealMidDTO {
        private Long time;
        private Long tp;
        private Long count;
    }
}
