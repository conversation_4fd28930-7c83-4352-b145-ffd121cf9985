package com.tops.pfinder.bean.dto;

import com.tops.jdos.bean.dto.Application;
import com.tops.pfinder.bean.po.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class TraceInfo implements Serializable {
    private static final long serialVersionUID = 7457611188842032874L;
    private SpanInfo root;
    private String traceName;
    private String traceNo;
    private String traceId;


    public static TracePO toPO(TraceInfo traceInfo) {
        TracePO tracePO = new TracePO();
        tracePO.setTraceNo(tracePO.getTraceNo());
        tracePO.setTraceName(tracePO.getTraceName());
        tracePO.setTraceDesc(tracePO.getTraceDesc());
        return tracePO;
    }

    public static TraceInfo fromPO(TracePO trace,
                                   List<SpanPO> spans,
                                   Map<String, Application> applications,
                                   Map<String, List<JsfConsumerConfigPO>> consumerConfigs,
                                   Map<String, JsfConsumerMetricPO> consumerMetrics,
                                   Map<String, List<TraceInvokeTimesPO>> invokeTimes) {
        List<SpanInfo> spanInfos = SpanInfo.fromPO(spans, applications, consumerConfigs, consumerMetrics,invokeTimes);
        SpanInfo root = SpanInfo.makeTree(spanInfos);
        TraceInfo traceInfo = new TraceInfo();
        traceInfo.setRoot(root);
        traceInfo.setTraceName(trace.getTraceName());
        traceInfo.setTraceNo(trace.getTraceNo());
        return traceInfo;
    }
}
