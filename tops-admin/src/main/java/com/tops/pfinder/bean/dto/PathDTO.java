package com.tops.pfinder.bean.dto;

import com.tops.pfinder.bean.vo.JsfConsumerConfigVO;
import com.tops.pfinder.bean.vo.JsfConsumerMetricVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PathDTO {
    private String componentName;
    private String appName;
    private String appOwner;
    private List<JsfConsumerConfigVO> consumerConfig;
    private JsfConsumerMetricVO consumerMetric;
}
