package com.tops.pfinder.bean.dto;

import com.tops.pfinder.bean.po.JsfConsumerConfigPO;
import com.tops.pfinder.bean.po.JsfConsumerMetricPO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class JsfConsumerMetricDTO {
    private String consumerAppName;
    private String componentType;
    private String componentName;
    private String tp99;

    public static JsfConsumerMetricDTO fromPO(JsfConsumerMetricPO po) {
        if (po == null) {
            return null;
        }
        JsfConsumerMetricDTO jsfConsumerConigDTO = new JsfConsumerMetricDTO();
        jsfConsumerConigDTO.setConsumerAppName(po.getConsumerAppName());
        jsfConsumerConigDTO.setComponentType(po.getComponentType());
        jsfConsumerConigDTO.setComponentName(po.getComponentName());
        jsfConsumerConigDTO.setTp99(po.getTp99());
        return jsfConsumerConigDTO;
    }

    public static List<JsfConsumerMetricDTO> fromPO(List<JsfConsumerMetricPO> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return new ArrayList<>();
        }
        return pos.stream().map(JsfConsumerMetricDTO::fromPO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<JsfConsumerMetricPO> toPO(List<JsfConsumerMetricDTO> dtos) {
        return dtos.stream().map(JsfConsumerMetricDTO::toPO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static JsfConsumerMetricPO toPO(JsfConsumerMetricDTO dto) {
        if (dto == null) {
            return null;
        }
        JsfConsumerMetricPO po = new JsfConsumerMetricPO();
        po.setConsumerAppName(dto.getConsumerAppName());
        po.setComponentType(dto.getComponentType());
        po.setComponentName(dto.getComponentName());
        po.setTp99(dto.getTp99());
        po.setYn(1);
        return po;
    }
}
