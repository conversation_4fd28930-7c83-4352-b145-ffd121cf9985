package com.tops.pfinder.bean.po;

import com.tops.pfinder.bean.dto.ApplicationInfo;
import com.tops.pfinder.bean.dto.ComponentInfo;
import com.tops.pfinder.bean.dto.SpanInfo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class SpanPO {
    private Long id;
    private String traceNo;
    private String spanNo;
    private String parentSpanNo;
    private String appName;
    private String appCoord;
    private String appOwner;
    private String componentType;
    private String componentName;
    private String componentDesc;
    private Integer sort;
    private Integer yn;


}
