package com.tops.pfinder.bean.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class JiaoYiSpanExportDTO {
    @ExcelProperty(index = 0, value = "应用名称")
    private String appName;
    @ExcelProperty(index = 1, value = "应用负责人")
    private String appOwner;
    @ExcelProperty(index = 2, value = "组件名称")
    private String componentName;
    @ExcelProperty(index = 3, value = "组件类型")
    private String componentType;
    @ExcelProperty(index = 4, value = "链路（如果该组件出现在多个链路，则只展示其中一条）")
    private String path;
}
