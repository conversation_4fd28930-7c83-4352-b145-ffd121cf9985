package com.tops.pfinder.bean.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PanelDataPO {
    /**
     * <pre>
     * id
     * </pre>
     */
    private Long id;

    /**
     * <pre>
     * 面板编码
     * </pre>
     */
    private String panelNo;

    /**
     * <pre>
     * 数据类型
     * </pre>
     */
    private String type;

    /**
     * <pre>
     * 数据值
     * </pre>
     */
    private String value;

    /**
     * <pre>
     * 记录日期
     * </pre>
     */
    private Date recordTime;

    /**
     * 异常详情
     */
    private String details;

    /**
     * <pre>
     * 逻辑删除标志：1：未删除 0：已删除
     * </pre>
     */
    private Integer yn;
}
