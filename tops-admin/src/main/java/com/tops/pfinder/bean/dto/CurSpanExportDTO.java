package com.tops.pfinder.bean.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CurSpanExportDTO {
    @ExcelProperty(index = 0, value = "组件名称")
    private String componentName;
    @ExcelProperty(index = 1, value = "组件类型")
    private String componentType;
    @ExcelProperty(index = 2, value = "应用名称")
    private String appName;
    @ExcelProperty(index = 3, value = "应用负责人")
    private String appOwner;
    @ExcelProperty(index = 4, value = "链路")
    private String path;
}
