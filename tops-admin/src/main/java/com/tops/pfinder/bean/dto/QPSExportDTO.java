package com.tops.pfinder.bean.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class QPSExportDTO {
    @ExcelProperty(index = 0, value = "调用方应用")
    private String consumerAppName;
    @ExcelProperty(index = 1, value = "调用方接口所处应用负责人")
    private String consumerAppOwner;
    @ExcelProperty(index = 2, value = "调用方接口")
    private String consumerComponent;
    @ExcelProperty(index = 3, value = "应用名称")
    private String appName;
    @ExcelProperty(index = 4, value = "应用负责人")
    private String appOwner;
    @ExcelProperty(index = 5, value = "组件名称")
    private String componentName;
    @ExcelProperty(index = 6, value = "时间段")
    private String duration;
    @ExcelProperty(index = 7, value = "预估QPS")
    private Integer qps;
    @ExcelProperty(index = 8, value = "预估QPS计算过程")
    private String desc;


}
