package com.tops.pfinder.bean.dto;

import com.tops.pfinder.bean.po.JsfConsumerConfigPO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class JsfConsumerConfigDTO {
    private String apiName;
    private String alias;
    private String timeout;
    private String retries;

    public static JsfConsumerConfigDTO fromPO(JsfConsumerConfigPO po, String apiName) {
        if (po == null) {
            return null;
        }
        JsfConsumerConfigDTO jsfConsumerConigDTO = new JsfConsumerConfigDTO();
        jsfConsumerConigDTO.setApiName(apiName);
        jsfConsumerConigDTO.setAlias(po.getAlias());
        jsfConsumerConigDTO.setTimeout(po.getTimeout());
        jsfConsumerConigDTO.setRetries(po.getRetries());
        return jsfConsumerConigDTO;
    }

    public static List<JsfConsumerConfigDTO> fromPO(List<JsfConsumerConfigPO> pos, String apiName) {
        if (CollectionUtils.isEmpty(pos)) {
            return new ArrayList<>();
        }
        return pos.stream().map(po -> JsfConsumerConfigDTO.fromPO(po, apiName)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static List<JsfConsumerConfigPO> toPO(List<JsfConsumerConfigDTO> dtos, String spanNo,String parentSpanNo) {
        return dtos.stream().map(dto -> JsfConsumerConfigDTO.toPO(dto, spanNo,parentSpanNo)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static JsfConsumerConfigPO toPO(JsfConsumerConfigDTO dto, String spanNo,String parentSpanNo) {
        if (dto == null) {
            return null;
        }
        JsfConsumerConfigPO po = new JsfConsumerConfigPO();
        po.setSpanNo(spanNo);
        po.setParentSpanNo(parentSpanNo);
        po.setAlias(dto.getAlias());
        po.setTimeout(dto.getTimeout());
        po.setRetries(dto.getRetries());
        po.setYn(1);
        return po;
    }
}
