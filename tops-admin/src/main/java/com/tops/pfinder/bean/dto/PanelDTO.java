package com.tops.pfinder.bean.dto;

import com.tops.pfinder.bean.po.PanelPO;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class PanelDTO {
    private String panelNo;
    /**
     * <pre>
     * 面板中文名称
     * </pre>
     */
    private String panelNickName;
    private String panelDesc;
    /**
     * <pre>
     * 领域编码
     * </pre>
     */
    private String domainName;
    private Integer sort;
    /**
     * <pre>
     * 数据源平台
     * </pre>
     */
    private String platform;

    /**
     * <pre>
     * 监控数据源key
     * </pre>
     */
    private String monitorDataKey;
    private Map<String, List<PanelDataDTO>> datas;

    public static PanelDTO fromPO(PanelPO panel) {
        PanelDTO panelDTO = new PanelDTO();
        panelDTO.setPanelNo(panel.getPanelNo());
        panelDTO.setPanelNickName(panel.getPanelNickName());
        panelDTO.setPanelDesc(panel.getPanelDesc());
        panelDTO.setDomainName(panel.getDomainName());
        panelDTO.setSort(panel.getSort());
        panelDTO.setPlatform(panel.getPlatform());
        panelDTO.setMonitorDataKey(panel.getMonitorDataKey());
        Map<String, List<PanelDataDTO>> datas = new HashMap<>();
        panelDTO.setDatas(datas);
        return panelDTO;
    }
}
