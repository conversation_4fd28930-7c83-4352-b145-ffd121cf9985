package com.tops.pfinder.bean.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TraceExportDTO {
    @ExcelProperty(index = 0, value = "组件名称")
    private String componentName;
    @ExcelProperty(index = 1, value = "组件类型")
    private String componentType;
    @ExcelProperty(index = 2, value = "应用名称")
    private String appName;
    @ExcelProperty(index = 3, value = "应用负责人")
    private String appOwner;
    @ExcelProperty(index = 4, value = "调用方接口")
    private String consumerComponent;
    @ExcelProperty(index = 5, value = "调用方应用")
    private String consumerAppName;
    @ExcelProperty(index = 6, value = "调用方接口所处应用负责人")
    private String consumerAppOwner;
}
