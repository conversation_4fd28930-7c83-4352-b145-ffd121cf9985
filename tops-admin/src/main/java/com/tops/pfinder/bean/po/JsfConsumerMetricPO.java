package com.tops.pfinder.bean.po;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class JsfConsumerMetricPO {
    private Long id;
    private String consumerAppName;
    private String componentType;
    private String componentName;
    private String tp99;
    private Integer yn;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JsfConsumerMetricPO that = (JsfConsumerMetricPO) o;
        return Objects.equals(consumerAppName, that.consumerAppName) && Objects.equals(componentType, that.componentType) && Objects.equals(componentName, that.componentName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(consumerAppName, componentType, componentName);
    }
}
