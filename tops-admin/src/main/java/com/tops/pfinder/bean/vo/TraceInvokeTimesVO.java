package com.tops.pfinder.bean.vo;

import com.tops.common.utils.DateUtils;
import com.tops.pfinder.bean.dto.TraceInvokeTimesDTO;
import com.tops.pfinder.bean.po.TraceInvokeTimesPO;
import com.tops.pfinder.enums.PeriodEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class TraceInvokeTimesVO {


    /**
     * <pre>
     * 节点no
     * </pre>
     */
    private String spanNo;

    /**
     * <pre>
     * 父节点no
     * </pre>
     */
    private String parentSpanNo;

    /**
     * <pre>
     * 父节点调用子节点的调用比例
     * </pre>
     */
    private Float times;

    /**
     * <pre>
     * 高峰时间段
     * </pre>
     */
    private String period;

    /**
     * <pre>
     * 记录日期
     * </pre>
     */
    private String recordDate;


    public static List<TraceInvokeTimesVO> fromDTO(List<TraceInvokeTimesDTO> dto) {
        if (CollectionUtils.isEmpty(dto)){
            return new ArrayList<>();
        }

        return dto.stream().map(TraceInvokeTimesVO::fromDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }
    public static TraceInvokeTimesVO fromDTO(TraceInvokeTimesDTO dto) {
        if (dto==null){
            return null;
        }
        TraceInvokeTimesVO vo = new TraceInvokeTimesVO();
        vo.setSpanNo(dto.getSpanNo());
        vo.setParentSpanNo(dto.getParentSpanNo());
        vo.setTimes(dto.getTimes());
        vo.setPeriod(dto.getPeriod().getDesc());
        vo.setRecordDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD,dto.getRecordDate()));
        return vo;
    }
}
