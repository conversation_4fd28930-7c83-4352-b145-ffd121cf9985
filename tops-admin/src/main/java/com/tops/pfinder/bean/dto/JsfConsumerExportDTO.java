package com.tops.pfinder.bean.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class JsfConsumerExportDTO {
    @ExcelProperty(index = 0, value = "接口名称")
    private String apiName;

    @ExcelProperty(index = 1, value = "Consumer应用名")
    private String consumerAppName;
    @ExcelProperty(index = 2, value = "Consumer应用负责人")
    private String consumerAppOwner;
    @ExcelProperty(index = 3, value = "Consumer应用负责人中文名")
    private String consumerAppOwnerNickName;
    @ExcelProperty(index = 4, value = "Consumer应用负责人所属部门")
    private String consumerAppOwnerDeptName;
    @ExcelProperty(index = 5, value = "调用量（24小时）")
    private double invokeTimes;

}
