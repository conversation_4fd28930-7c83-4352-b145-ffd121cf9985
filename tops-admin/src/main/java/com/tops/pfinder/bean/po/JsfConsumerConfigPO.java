package com.tops.pfinder.bean.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
public class JsfConsumerConfigPO {
    private Long id;
    private String parentSpanNo;
    private String spanNo;
    private String alias;
    private String timeout;
    private String retries;
    private Integer yn;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JsfConsumerConfigPO that = (JsfConsumerConfigPO) o;
        return Objects.equals(parentSpanNo, that.parentSpanNo) && Objects.equals(spanNo, that.spanNo) && Objects.equals(alias, that.alias);
    }

    @Override
    public int hashCode() {
        return Objects.hash(parentSpanNo, spanNo, alias);
    }
}
