package com.tops.pfinder.bean.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.tops.common.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class PanelExportDTO {
    @ExcelProperty(index = 0, value = "记录时间")
    private String recordTime;
    @ExcelProperty(index = 1, value = "面板编码")
    private String panelNo;
    @ExcelProperty(index = 2, value = "面板名称")
    private String panelNickName;
    @ExcelProperty(index = 3, value = "能力域")
    private String domainName;
    @ExcelProperty(index = 4, value = "数据来源平台")
    private String platform;
    @ExcelProperty(index = 5, value = "监控Key")
    private String monitorDataKey;
    @ExcelProperty(index = 6, value = "可用率[0%-99.00%)分钟数")
    private String exceptionLevel9900;
    @ExcelProperty(index = 7, value = "可用率[0%-99.90%)分钟数")
    private String exceptionLevel9990;
    @ExcelProperty(index = 8, value = "可用率[0%-99.95%)分钟数")
    private String exceptionLevel9995;
    @ExcelProperty(index = 9, value = "可用率[0%-99.99%)分钟数")
    private String exceptionLevel9999;
    @ExcelProperty(index = 10, value = "可用率[0%-100.00%)分钟数")
    private String exceptionLevel10000;
    @ExcelProperty(index = 11, value = "请求失败次数")
    private String failCount;
    @ExcelProperty(index = 12, value = "可用率[0%-99.00%)分钟数详情")
    private String exceptionLevel9900Details;
    @ExcelProperty(index = 13, value = "可用率[0%-99.90%)分钟数详情")
    private String exceptionLevel9990Details;
    @ExcelProperty(index = 14, value = "可用率[0%-99.95%)分钟数详情")
    private String exceptionLevel9995Details;
    @ExcelProperty(index = 15, value = "可用率[0%-99.99%)分钟数详情")
    private String exceptionLevel9999Details;
    @ExcelProperty(index = 16, value = "可用率[0%-100.00%)分钟数详情")
    private String exceptionLevel10000Details;
    @ExcelProperty(index = 17, value = "请求失败详情")
    private String failCountDetails;


    public static PanelExportDTO fromDTO(PanelDTO panelDTO,
                                         PanelDataDTO exceptionLevel9900,
                                         PanelDataDTO exceptionLevel9990,
                                         PanelDataDTO exceptionLevel9995,
                                         PanelDataDTO exceptionLevel9999,
                                         PanelDataDTO exceptionLevel10000,
                                         PanelDataDTO failCountData,
                                         String date) {
        PanelExportDTO panelExportDTO = new PanelExportDTO();
        panelExportDTO.setPanelNo(panelDTO.getPanelNo());
        panelExportDTO.setPanelNickName(panelDTO.getPanelNickName());
        panelExportDTO.setDomainName(panelDTO.getDomainName());
        panelExportDTO.setPlatform(panelDTO.getPlatform());
        panelExportDTO.setMonitorDataKey(panelDTO.getMonitorDataKey());
        if (exceptionLevel9900 != null) {
            panelExportDTO.setExceptionLevel9900(exceptionLevel9900.getValue());
            panelExportDTO.setExceptionLevel9900Details(JSON.toJSONString(exceptionLevel9900.getDetails()));
        }
        if (exceptionLevel9990 != null) {
            panelExportDTO.setExceptionLevel9990(exceptionLevel9990.getValue());
            panelExportDTO.setExceptionLevel9990Details(JSON.toJSONString(exceptionLevel9990.getDetails()));
        }
        if (exceptionLevel9995 != null) {
            panelExportDTO.setExceptionLevel9995(exceptionLevel9995.getValue());
            panelExportDTO.setExceptionLevel9995Details(JSON.toJSONString(exceptionLevel9995.getDetails()));
        }
        if (exceptionLevel9999 != null) {
            panelExportDTO.setExceptionLevel9999(exceptionLevel9999.getValue());
            panelExportDTO.setExceptionLevel9999Details(JSON.toJSONString(exceptionLevel9999.getDetails()));
        }
        if (exceptionLevel10000 != null) {
            panelExportDTO.setExceptionLevel10000(exceptionLevel10000.getValue());
            panelExportDTO.setExceptionLevel10000Details(JSON.toJSONString(exceptionLevel10000.getDetails()));
        }
        if (failCountData != null) {
            panelExportDTO.setFailCount(failCountData.getValue());
            panelExportDTO.setFailCountDetails(JSON.toJSONString(failCountData.getDetails()));
        }
        if (date != null) {
            panelExportDTO.setRecordTime(date);
        }
        return panelExportDTO;
    }
}
