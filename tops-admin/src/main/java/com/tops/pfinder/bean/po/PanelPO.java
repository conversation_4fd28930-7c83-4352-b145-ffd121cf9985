package com.tops.pfinder.bean.po;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PanelPO {
    /**
     * <pre>
     * id
     * </pre>
     */
    private Long	id;

    /**
     * <pre>
     * 面板编码
     * </pre>
     */
    private String	panelNo;

    /**
     * <pre>
     * 面板中文名称
     * </pre>
     */
    private String	panelNickName;

    /**
     * <pre>
     * 面板描述
     * </pre>
     */
    private String	panelDesc;

    /**
     * <pre>
     * 领域编码
     * </pre>
     */
    private String	domainName;

    /**
     * <pre>
     * 数据源平台
     * </pre>
     */
    private String	platform;

    /**
     * <pre>
     * 监控数据源key
     * </pre>
     */
    private String	monitorDataKey;

    /**
     * <pre>
     * 排序
     * </pre>
     */
    private Integer	sort;

    /**
     * <pre>
     * 逻辑删除标志：1：未删除 0：已删除
     * </pre>
     */
    private Integer	yn;

    public PanelPO() {
    }

    public PanelPO(String panelNo, String panelNickName, String panelDesc, String domainName, String platform, String monitorDataKey, Integer sort, Integer yn) {
        this.panelNo = panelNo;
        this.panelNickName = panelNickName;
        this.panelDesc = panelDesc;
        this.domainName = domainName;
        this.platform = platform;
        this.monitorDataKey = monitorDataKey;
        this.sort = sort;
        this.yn = yn;
    }
}
