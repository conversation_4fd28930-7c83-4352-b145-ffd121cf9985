package com.tops.pfinder.bean.dto;

import com.tops.pfinder.bean.po.TraceInvokeTimesPO;
import com.tops.pfinder.enums.PeriodEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@Setter
public class TraceInvokeTimesDTO {
    /**
     * <pre>
     * id
     * </pre>
     */
    private Long id;

    /**
     * <pre>
     * 节点no
     * </pre>
     */
    private String spanNo;

    /**
     * <pre>
     * 父节点no
     * </pre>
     */
    private String parentSpanNo;
    private String componentName;
    private String parentComponentName;
    private String traceNo;

    /**
     * <pre>
     * 父节点调用子节点的调用比例
     * </pre>
     */
    private Float times;

    /**
     * <pre>
     * 高峰时间段
     * </pre>
     */
    private PeriodEnum period;

    /**
     * <pre>
     * 记录日期
     * </pre>
     */
    private Date recordDate;


    public static TraceInvokeTimesPO toPO(TraceInvokeTimesDTO dto) {
        if (dto == null) {
            return null;
        }
        TraceInvokeTimesPO po = new TraceInvokeTimesPO();
        po.setSpanNo(dto.getSpanNo());
        po.setParentSpanNo(dto.getParentSpanNo());
        po.setTimes(dto.getTimes());
        po.setPeriod(dto.getPeriod().getCode());
        po.setRecordDate(dto.getRecordDate());
        return po;
    }

    public static List<TraceInvokeTimesPO> toPO(List<TraceInvokeTimesDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        return dtos.stream().map(TraceInvokeTimesDTO::toPO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TraceInvokeTimesDTO fromPO(TraceInvokeTimesPO po) {
        if (po == null) {
            return null;
        }
        TraceInvokeTimesDTO dto = new TraceInvokeTimesDTO();
        dto.setId(po.getId());
        dto.setSpanNo(po.getSpanNo());
        dto.setParentSpanNo(po.getParentSpanNo());
        dto.setTimes(po.getTimes());
        dto.setPeriod(PeriodEnum.getEnumByCode(po.getPeriod()));
        dto.setRecordDate(po.getRecordDate());
        return dto;
    }

    public static List<TraceInvokeTimesDTO> fromPO(List<TraceInvokeTimesPO> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.stream().map(TraceInvokeTimesDTO::fromPO).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
