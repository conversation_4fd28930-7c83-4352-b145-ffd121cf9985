package com.tops.pfinder.bean.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

@Getter
@Setter
public class TraceInvokeTimesPO {
    /**
     * <pre>
     * id
     * </pre>
     */
    private Long	id;

    /**
     * <pre>
     * 节点no
     * </pre>
     */
    private String	spanNo;

    /**
     * <pre>
     * 父节点no
     * </pre>
     */
    private String	parentSpanNo;

    /**
     * <pre>
     * 父节点调用子节点的调用比例
     * </pre>
     */
    private Float	times;

    /**
     * <pre>
     * 高峰时间段
     * </pre>
     */
    private String	period;

    /**
     * <pre>
     * 记录日期
     * </pre>
     */
    private Date recordDate;

    /**
     * <pre>
     * 逻辑删除标志：1：未删除 0：已删除
     * </pre>
     */
    private Integer	yn;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TraceInvokeTimesPO po = (TraceInvokeTimesPO) o;
        return Objects.equals(spanNo, po.spanNo) && Objects.equals(parentSpanNo, po.parentSpanNo) && Objects.equals(period, po.period) && Objects.equals(recordDate, po.recordDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(spanNo, parentSpanNo, period, recordDate);
    }
}
