package com.tops.pfinder.bean.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.pfinder.bean.vo.ItemStyleVO;
import com.tops.pfinder.bean.vo.SpanVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class HasEsSpanExportDTO {
    @ExcelProperty(index = 0, value = "依赖ES的组件名称")
    private String componentName;
    @ExcelProperty(index = 1, value = "依赖ES的组件类型")
    private String componentType;
    @ExcelProperty(index = 2, value = "依赖ES的应用名称")
    private String appName;
    @ExcelProperty(index = 3, value = "依赖ES的应用负责人")
    private String appOwner;
    @ExcelProperty(index = 4, value = "ES所属应用名称")
    private String esAppName;
    @ExcelProperty(index = 5, value = "ES所属应用负责人")
    private String esAppOwner;
    @ExcelProperty(index = 6, value = "链路")
    private String path;
}
