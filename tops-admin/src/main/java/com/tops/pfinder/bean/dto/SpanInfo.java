package com.tops.pfinder.bean.dto;

import com.tops.common.utils.StringUtils;
import com.tops.jdos.bean.dto.Application;
import com.tops.pfinder.bean.po.JsfConsumerConfigPO;
import com.tops.pfinder.bean.po.JsfConsumerMetricPO;
import com.tops.pfinder.bean.po.SpanPO;
import com.tops.pfinder.bean.po.TraceInvokeTimesPO;
import com.tops.pfinder.enums.ComponentEnum;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class SpanInfo implements Serializable {
    private static final long serialVersionUID = 1274796406690392945L;
    private Long id;
    private String traceNo;
    private String spanNo;
    private Integer sort;
    private String parentNo;
    private ComponentInfo component;
    private ApplicationInfo application;
    private MetricInfo metric;
    private List<SpanInfo> children;
    private List<JsfConsumerConfigDTO> consumerConfigs;
    private JsfConsumerMetricDTO consumerMetric;
    private Integer invokeCount = 1;
    private List<TraceInvokeTimesDTO> invokeTimes;
    private List<String> path;
    private SpanInfo existSpan;

    public SpanInfo withAppInfo(String appName, String platform) {
        if (this.application == null) {
            this.application = new ApplicationInfo();
        }
        this.application.setName(appName);
        this.application.setAppCoord(String.format(ApplicationInfo.TEMPLATE_APP_COORD, appName, platform.toLowerCase()));
        return this;
    }

    public SpanInfo withChildren(List<SpanInfo> children) {
        this.children = children;
        return this;
    }

    public SpanInfo withComponentName(String componentName) {
        if (this.component == null) {
            this.component = new ComponentInfo();
        }
        this.component.setComponentName(componentName);
        return this;
    }

    public static SpanInfo fromPO(SpanPO span,
                                  Map<String, SpanPO> spanMap,
                                  Map<String, Application> applications,
                                  Map<String, List<JsfConsumerConfigPO>> consumerConfigs,
                                  Map<String, JsfConsumerMetricPO> consumerMetrics,
                                  Map<String, List<TraceInvokeTimesPO>> invokeTimes) {
        if (span == null) {
            return null;
        }
        SpanInfo spanInfo = new SpanInfo();
        spanInfo.setId(span.getId());
        spanInfo.setSpanNo(span.getSpanNo());
        ComponentInfo component = new ComponentInfo();
        component.setComponentName(span.getComponentName());
        component.setComponentDesc(span.getComponentDesc());
        component.setComponentType(span.getComponentType());
        spanInfo.setComponent(component);

        ApplicationInfo application = new ApplicationInfo();
        application.setName(span.getAppName());
        application.setAppCoord(span.getAppCoord());
        Application jdosApp = applications.get(span.getAppName());
        if (jdosApp != null && StringUtils.isNotBlank(jdosApp.getOwner())) {
            application.setAppOwner(jdosApp.getOwner());
        } else {
            application.setAppOwner("未知负责人");
        }
        spanInfo.setApplication(application);
        spanInfo.setChildren(new ArrayList<>());

        spanInfo.setSort(span.getSort());
        spanInfo.setParentNo(span.getParentSpanNo());
        spanInfo.setTraceNo(spanInfo.getTraceNo());
        if (Objects.equals(ComponentEnum.JSF_CONSUMER.getComponentType(), spanInfo.getComponent().getComponentType())) {
            spanInfo.setConsumerConfigs(JsfConsumerConfigDTO.fromPO(consumerConfigs.get(spanInfo.getParentNo() + "-" + spanInfo.getSpanNo()), spanInfo.getComponent().getComponentName()));

            String key = "";
            if (org.apache.commons.lang3.StringUtils.isNotBlank(spanInfo.getParentNo())) {
                key += spanInfo.getParentNo();
            }
            key += "-" + spanInfo.getSpanNo();
            List<TraceInvokeTimesPO> pos = invokeTimes.get(key);
            spanInfo.setInvokeTimes(TraceInvokeTimesDTO.fromPO(pos));
        }
        if (StringUtils.isNotBlank(spanInfo.getParentNo())
            && spanMap.get(spanInfo.getParentNo()) != null) {
            SpanPO parentSpan = spanMap.get(spanInfo.getParentNo());
            if (parentSpan != null) {
                spanInfo.setConsumerMetric(JsfConsumerMetricDTO.fromPO(consumerMetrics.get(parentSpan.getAppName() + "-" + spanInfo.getComponent().getComponentName())));
            }
        }

        return spanInfo;
    }

    public static List<SpanInfo> fromPO(List<SpanPO> spans,
                                        Map<String, Application> applications,
                                        Map<String, List<JsfConsumerConfigPO>> consumerConfigs,
                                        Map<String, JsfConsumerMetricPO> consumerMetrics,
                                        Map<String, List<TraceInvokeTimesPO>> invokeTimes) {
        if (CollectionUtils.isEmpty(spans)) {
            return new ArrayList<>();
        }
        Map<String, SpanPO> poMap = spans.stream().collect(Collectors.toMap(SpanPO::getSpanNo, po -> po, (p1, p2) -> p1));
        return spans.stream().map(po -> SpanInfo.fromPO(po, poMap, applications, consumerConfigs, consumerMetrics, invokeTimes)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static SpanInfo makeTree(List<SpanInfo> spans) {
        Map<String, SpanInfo> tree = spans.stream().collect(Collectors.toMap(SpanInfo::getSpanNo, span -> span));
        SpanInfo root = null;
        for (SpanInfo span : spans) {
            if (StringUtils.isBlank(span.getParentNo())) {
                root = span;
                continue;
            }
            SpanInfo parent = tree.get(span.getParentNo());
            if (parent == null) {
                continue;
            }
            parent.getChildren().add(span);
            //TODO wangqin83 开启排序
//            parent.getChildren().sort(Comparator.comparingInt(SpanInfo::getSort));
        }

        return root;
    }

    public static List<SpanPO> toPO(List<SpanInfo> spans, String traceNo) {
        if (CollectionUtils.isEmpty(spans)) {
            return new ArrayList<>();
        }
        return spans.stream().map(spanInfo -> SpanInfo.toPO(spanInfo, traceNo)).collect(Collectors.toList());
    }

    public static SpanPO toPO(SpanInfo span, String traceNo) {
        if (span == null || StringUtils.isBlank(traceNo)) {
            return null;
        }
        SpanPO spanPO = new SpanPO();
        spanPO.setTraceNo(traceNo);
        spanPO.setSpanNo(span.getSpanNo());
        spanPO.setParentSpanNo(span.getParentNo());
        if (span.getApplication() != null) {
            spanPO.setAppName(span.getApplication().getName());
            spanPO.setAppCoord(span.getApplication().getAppCoord());
            spanPO.setAppOwner(span.getApplication().getAppOwner());
        }
        if (span.getComponent() != null) {
            spanPO.setComponentType(span.getComponent().getComponentType());
            spanPO.setComponentName(span.getComponent().getComponentName());
            spanPO.setComponentDesc(span.getComponent().getComponentDesc());
        }
        spanPO.setSort(span.getSort());
        spanPO.setYn(1);
        return spanPO;
    }
}
