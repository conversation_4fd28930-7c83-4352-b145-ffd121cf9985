package com.tops.autobots.constants;

public interface AutoBotsConstants {

    /**
     * 占位符依次为：打点KEY、指标名称（可用率、TP99、调用次数）、当前数值
     */
    String UMP_QUESTION_DESC_TEMPLATE = "UMP KEY：%s%s变动为%.2f，如何处理？";
    /**
     * 占位符依次为：打点KEY、维度信息、指标名称（可用率、TP99、调用次数）、当前数值
     */
    String P_FINDER_QUESTION_DESC_TEMPLATE = "PFinder KEY：%s（%s）%s变动为%.2f，如何处理？";
    /**
     * 占位符依次为：应用英文编码、指标名称（CPU使用率、RSS内存使用率、TCP重传数）、当前数值
     */
    String MDC_QUESTION_DESC_TEMPLATE = "看下这组数据应该怎样运维：%s";
    /**
     * 占位符依次为：文档名称、异常占比JSON
     */
    String QUESTION_DESC_TEMPLATE_HANDLER_MDC = "根据%s文档，回答%s";
    /**
     * 占位符依次为：文档名称、异常占比JSON
     */
    String QUESTION_DESC_TEMPLATE_HANDLER_DUCC = "根据%s文档，回答如果我想启用降级开关，应该如何操作？";
    /**
     * 咚咚消息模板
     */
    String DONG_DONG_MSG_TEMPLATE = "问题：\n%s\n\n\n处理方案：\n%s";
}
