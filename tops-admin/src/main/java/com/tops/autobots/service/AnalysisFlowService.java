package com.tops.autobots.service;

import com.tops.autobots.domain.UmpAlarmDTO;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

/**
 * autobots分析流服务-本地编排
 */
public interface AnalysisFlowService {
    void startFlow(UmpAlarmDTO umpAlarm,String traceId)throws InternalFailureException, InvalidRequestException, DependencyFailureException;
}
