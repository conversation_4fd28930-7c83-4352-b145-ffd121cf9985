package com.tops.autobots.service.impl;

import com.jd.fastjson.JSON;
import com.jd.llm.client.domain.autobots.AutoBotsWfRequest;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookResponse;
import com.tops.autobots.handler.plan.async.AutoBotsAsyncWorkflowHandler;
import com.tops.autobots.service.AutoBotsAsyncMessageService;
import com.tops.autobots.translator.AutoBotsAsyncMessageTranslator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class AutoBotsAsyncMessageServiceImpl implements AutoBotsAsyncMessageService {

    @Resource
    private AutoBotsAsyncWorkflowHandler autoBotsAsyncWorkflowHandler;

    @Resource
    private AutoBotsAsyncMessageTranslator autoBotsAsyncMessageTranslator;

    /**
     * 根据logbook查询结果推送工作流
     * @param logbookBO
     * @param dataList
     */
    @Override
    public void sendAsyncLogBookMessage(LogbookBO logbookBO, List<LogbookResponse.LogBookData> dataList) {
        try {
            log.info("查询日志后推送京ME,logbookBO:{}, dataList:{}", JSON.toJSONString(logbookBO), JSON.toJSONString(dataList));
            AutoBotsWfRequest request = autoBotsAsyncMessageTranslator.autoBotsWfRequest(logbookBO, dataList);
            log.info("查询日志后推送京ME, request:{}", JSON.toJSONString(request));
            autoBotsAsyncWorkflowHandler.handleAsyncMessage(request);
        } catch (Exception e) {
            log.error("查询日志后推送京ME失败,", e);
        }
    }
}
