package com.tops.autobots.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.tops.alarm.bean.vo.AutoBotsOpsDTO;
import com.tops.alarm.bean.vo.AutoBotsOpsItemDTO;
import com.tops.alarm.bean.vo.AutoBotsOpsResponse;
import com.tops.autobots.service.AnalysisOpsService;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.ObjectUtils;
import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import com.tops.jdos.domain.ApplicationInfo;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;
import com.tops.jdos.service.EasyOpsService;
import com.tops.jdos.service.JDOSService;
import com.tops.ump.adapter.UmpAdapter;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;
import com.tops.ump.enums.MatchTypeEnum;
import com.tops.ump.enums.TimeDimensionEnum;
import com.tops.ump.enums.UmpAutobotsOperatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AnalysisOpsServiceImpl implements AnalysisOpsService {
    @Resource
    private UmpAdapter umpAdapter;

    @Resource
    private JDOSService jdosService;

    @Resource
    private EasyOpsService easyOpsService;

    /**
     * 获取umpKey 默认endTime往前6分钟
     *
     * @param umpKeys
     * @param endTime
     * @return
     */
    private QueryMonitorDataRequest initQueryMonitorDataRequest(List<String> umpKeys, Date endTime) {
        QueryMonitorDataRequest umpRequest = new QueryMonitorDataRequest();
        umpRequest.setMonitorKeys(umpKeys);
        umpRequest.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
        Date startDate = DateUtils.addMinutes(endTime, -6);
        umpRequest.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startDate));
        umpRequest.setDagaCycle(TimeDimensionEnum.MINUTE.getCode());
        umpRequest.setDataTypes("TP99,TP999,AvailRate");
        return umpRequest;
    }

    @Override
    public AutoBotsOpsResponse getAutoBotsUmp(AutoBotsOpsDTO autoBotsOpsDTO) throws InvalidRequestException,
        DependencyFailureException, InternalFailureException {
        AutoBotsOpsResponse response = new AutoBotsOpsResponse();
        List<String> umpKeys = autoBotsOpsDTO.getItemList().stream().map(AutoBotsOpsItemDTO::getKey).
            collect(Collectors.toList());
        QueryMonitorDataRequest dataRequest = initQueryMonitorDataRequest(umpKeys, autoBotsOpsDTO.getAlarmTime());
        Map<String, Pair<Boolean, String>> resultMessageMap = new HashMap<>();
        try {
            //umpKey汇总数据
            Map<String, List<MetricDataDTO>> umpKeyMap = umpAdapter.queryPanelData(dataRequest, 20);
            //判断大小
            for (AutoBotsOpsItemDTO autoBotsOpsItemDTO : autoBotsOpsDTO.getItemList()) {
                Pair<Boolean, String> checkResult = checkUmpResult(autoBotsOpsItemDTO, umpKeyMap);
                resultMessageMap.put(autoBotsOpsItemDTO.getKey(), checkResult);
            }

            //判断所有条件是否都需要成立
            if (MatchTypeEnum.MUST.getCode().equals(autoBotsOpsDTO.getMatchType())) {
                response.setCheckStatus(resultMessageMap.entrySet().stream()
                    .allMatch(entry -> entry.getValue().getLeft()));
            } else if (MatchTypeEnum.SHOULD.getCode().equals(autoBotsOpsDTO.getMatchType())) {
                response.setCheckStatus(resultMessageMap.entrySet().stream()
                    .anyMatch(entry -> entry.getValue().getLeft()));
            }

            response.setMessage(JSON.toJSONString(resultMessageMap));
        } catch (InvalidRequestException e) {
            throw e;
        } catch (DependencyFailureException e) {
            throw e;
        } catch (InternalFailureException e) {
            throw e;
        } catch (Exception e) {
            throw new InternalFailureException(e.getMessage());
        }

        return response;
    }

    @Override
    public AutoBotsOpsResponse getAutoBotsMdc(AutoBotsOpsDTO autoBotsOpsDTO) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        try {
            //请求行云部署分组信息
            ApplicationInfo applicationInfo = jdosService.queryApplication(autoBotsOpsDTO.getAppName());
            List<GroupInfo> groups = applicationInfo.getGroups();
            List<String> resourceIds = new ArrayList<>();
            for (GroupInfo group : groups) {
                resourceIds.addAll(group.getContainers().stream().map(ContainerInfo::getIp).collect(Collectors.toList()));
            }
            Date now = new Date();
            Date ago0Min = DateUtils.queryMinByDiffSource(now, 0);
            Date ago6Min = DateUtils.queryMinByDiffSource(now, -6);
            Date ago1Min = DateUtils.queryMinByDiffSource(now, -1);
            List<InfrastructureInfoDTO> infrastructureInfo = new ArrayList<>();
            try {
                for (int i = 0; i <= 18; i++) {
                    infrastructureInfo = easyOpsService.getInfrastructureInfo(autoBotsOpsDTO.getAppName(), resourceIds, ago6Min, ago0Min);
                    if (!validResult(infrastructureInfo, ago1Min, resourceIds, false)) {
                        Thread.sleep(10000);
                    } else {
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate");
                throw new DependencyFailureException("获取硬件信息失败");
            }

            if (validResult(infrastructureInfo, ago1Min, resourceIds, true)) {
                log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate 获取硬件信息失败。外部接口返回数据错误。流程中断");
                throw new DependencyFailureException("获取硬件信息失败。外部接口返回数据错误。流程中断");
            }

            Map<String, List<InfrastructureInfoDTO>> infrastructureInfoMap = infrastructureInfo.stream()
                .collect(Collectors.groupingBy(InfrastructureInfoDTO::getResourceId));

            Map<String, Pair<Boolean, String>> resultMessageMap = new HashMap<>();
            //判断是否有问题
            for(AutoBotsOpsItemDTO autoBotsOpsItemDTO : autoBotsOpsDTO.getItemList()) {
                Pair<Boolean, String> checkResult = checkMdcResult(autoBotsOpsItemDTO, infrastructureInfoMap);
                resultMessageMap.put(autoBotsOpsItemDTO.getMetric(), checkResult);
            }

            boolean checkStatus = resultMessageMap.entrySet().stream().anyMatch(entry -> entry.getValue().getLeft());
            AutoBotsOpsResponse autoBotsOpsResponse = new AutoBotsOpsResponse();
            autoBotsOpsResponse.setMessage(JSON.toJSONString(resultMessageMap));
            autoBotsOpsResponse.setCheckStatus(checkStatus);
            return autoBotsOpsResponse;
        } catch (InternalFailureException e) {
            log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate,InternalFailureException", e);
            throw e;
        } catch (InvalidRequestException e) {
            log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate,InvalidRequestException", e);
            throw e;
        } catch (DependencyFailureException e) {
            log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate,DependencyFailureException", e);
            throw e;
        } catch (Exception e) {
            throw new InternalFailureException(e.getMessage());
        }
    }

    private Pair<Boolean, String> checkUmpResult(AutoBotsOpsItemDTO autoBotsOpsItemDTO, Map<String, List<MetricDataDTO>> umpKeyMap) throws InternalFailureException, NoSuchFieldException, IllegalAccessException {
        List<MetricDataDTO> metricDataDTOS = umpKeyMap.get(autoBotsOpsItemDTO.getKey());
        if(metricDataDTOS == null || metricDataDTOS.isEmpty()) {
            throw new InternalFailureException("没有找到umpKey数据");
        }

        metricDataDTOS.sort(Comparator.comparing(MetricDataDTO::getDataTime));
        int duration = 0;
        List<Pair<String, Double>> result = new ArrayList<>();
        for(MetricDataDTO metricDataDTO : metricDataDTOS) {
            Field field = metricDataDTO.getClass().getDeclaredField(autoBotsOpsItemDTO.getMetric());
            field.setAccessible(true);
            Double val = (Double) field.get(metricDataDTO);
            if(checkThreshold(val, autoBotsOpsItemDTO)) {
                duration += 1;
                result.add(ImmutablePair.of(metricDataDTO.getDataTime(), val));
                if(duration == Integer.parseInt(autoBotsOpsItemDTO.getDuration())) {
                    return ImmutablePair.of(true, "该ump条件触发，相关信息：" + JSON.toJSONString(result));
                }
            } else {
                duration = 0;
                result.clear();
            }
        }

        return ImmutablePair.of(false, "该ump条件没有触发");
    }

    private Pair<Boolean, String> checkMdcResult(AutoBotsOpsItemDTO autoBotsOpsItemDTO, Map<String, List<InfrastructureInfoDTO>> infrastructureInfoDTOMap) throws InternalFailureException, NoSuchFieldException, IllegalAccessException {
        if(infrastructureInfoDTOMap == null || infrastructureInfoDTOMap.isEmpty()) {
            throw new InternalFailureException("没有找到机器信息数据");
        }

        for (Map.Entry<String, List<InfrastructureInfoDTO>> entry : infrastructureInfoDTOMap.entrySet()) {
            entry.getValue().sort(Comparator.comparing(InfrastructureInfoDTO::getTimestamp));
            int duration = 0;
            List<Pair<String, Double>> result = new ArrayList<>();
            for(InfrastructureInfoDTO infrastructureInfoDTO : entry.getValue()) {
                Field field = infrastructureInfoDTO.getClass().getDeclaredField(autoBotsOpsItemDTO.getMetric());
                field.setAccessible(true);
                Double val = (Double) field.get(infrastructureInfoDTO);
                if(checkThreshold(val, autoBotsOpsItemDTO)) {
                    duration += 1;
                    Date date = new Date(infrastructureInfoDTO.getTimestamp());
                    result.add(ImmutablePair.of(DateUtils.dateTime(date), val));
                    if(duration == Integer.parseInt(autoBotsOpsItemDTO.getDuration())) {
                        return ImmutablePair.of(true, "该ump条件触发，相关信息：" + JSON.toJSONString(result));
                    }
                } else {
                    duration = 0;
                    result.clear();
                }
            }
        }

        return ImmutablePair.of(false, "该ump条件没有触发");
    }


    private boolean checkThreshold(Double val, AutoBotsOpsItemDTO autoBotsOpsItemDTO) throws NoSuchFieldException, IllegalAccessException {
        if(autoBotsOpsItemDTO.getOperator().equals(UmpAutobotsOperatorEnum.GREATER.getCode())) {
            return val > Double.parseDouble(autoBotsOpsItemDTO.getThreshold());
        } else if (autoBotsOpsItemDTO.getOperator().equals(UmpAutobotsOperatorEnum.LESS.getCode())) {
            return val < Double.parseDouble(autoBotsOpsItemDTO.getThreshold());
        }

        return false;
    }

    private boolean validResult(List<InfrastructureInfoDTO> infrastructureInfoList, Date ago1Min, List<String> resourceIds, boolean needLog) {
        List<InfrastructureInfoDTO> infrastructureInfoLastSecond = infrastructureInfoList.stream()
            .filter(infrastructureInfoDTO -> ago1Min.getTime() == infrastructureInfoDTO.getTimestamp())
            .collect(Collectors.toList());
        if (infrastructureInfoLastSecond.size() < resourceIds.size()) {
            if (needLog) {
                log.info("最后一分钟数据较少infrastructureInfoLastSecond:{}, resourceIds:{}", infrastructureInfoLastSecond.size(), resourceIds.size());
            }

            return false;
        }

        Iterator it = infrastructureInfoList.iterator();
        //部分机器可能刚扩容没有数据，过滤这部分数据
        while (it.hasNext()) {
            InfrastructureInfoDTO infrastructureInfoDTO = (InfrastructureInfoDTO) it.next();
            if (ObjectUtils.hasNullProperties(infrastructureInfoDTO)) {
                it.remove();
            }
        }

        log.info("数据复合要求，可以提供给大模型");
        return true;
    }
}
