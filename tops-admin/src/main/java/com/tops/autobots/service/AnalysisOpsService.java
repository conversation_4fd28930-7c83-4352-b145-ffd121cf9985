package com.tops.autobots.service;

import com.tops.alarm.bean.vo.AutoBotsOpsDTO;
import com.tops.alarm.bean.vo.AutoBotsOpsResponse;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import org.springframework.stereotype.Service;

@Service
public interface AnalysisOpsService {
    /**
     * 根据输入的ump信息，解析
     * @return
     */
    AutoBotsOpsResponse getAutoBotsUmp(AutoBotsOpsDTO autoBotsOpsDTO) throws InvalidRequestException,
        DependencyFailureException, InternalFailureException;

    /**
     * 根据输入的ump信息，解析
     * @return
     */
    AutoBotsOpsResponse getAutoBotsMdc(AutoBotsOpsDTO autoBotsOpsDTO) throws InvalidRequestException,
        DependencyFailureException, InternalFailureException;

}
