package com.tops.autobots.service.impl;

import com.jd.fastjson.JSON;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.UmpAlarmDTO;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.autobots.service.AnalysisFlowService;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AnalysisFlowServiceImpl implements AnalysisFlowService {

    @Autowired
    List<AutoBotsHandler> autoBotsHandlers;

    @Override
    public void startFlow(UmpAlarmDTO umpAlarm, String traceId) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        log.info("AnalysisFlowServiceImpl.startFlow 开始启动分析流程 umpAlarm:{},traceId:{}", JSON.toJSONString(umpAlarm), traceId);
        AutoBotsPlanContext context = new AutoBotsPlanContext();
        context.setUmpAlarm(umpAlarm);
        context.setTraceId(traceId);
        try {
            for (AutoBotsHandler autoBotsHandler : autoBotsHandlers) {
                autoBotsHandler.process(context);
                log.info("AnalysisFlowServiceImpl.startFlow 应急预案分析完成, 分析过程{}", JSON.toJSONString(context.getProcessMsgMap()));
            }
        } catch (Exception e) {
            log.error("AnalysisFlowServiceImpl.startFlow 分析流处理过程中出现异常, processMsg:{}, e:", context.getProcessMsgMap(), e);
        }
    }
}
