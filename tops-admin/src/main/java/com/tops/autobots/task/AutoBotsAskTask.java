package com.tops.autobots.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tops.alarm.bean.dto.*;
import com.tops.autobots.domain.AnswerDTO;
import com.tops.autobots.domain.MdcOperateRequest;
import com.tops.autobots.domain.QuestionDTO;
import com.tops.alarm.enums.AlarmLevelEnum;
import com.tops.alarm.enums.MetricTypeEnum;
import com.tops.alarm.enums.PlatformTypeEnum;
import com.tops.autobots.domain.UmpAlarmDTO;
import com.tops.autobots.enums.MDCOperateTypeEnum;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.ObjectUtils;
import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import com.tops.jdos.domain.ApplicationInfo;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能体提问任务
 *
 * <AUTHOR>
 * @date 2024 8-30
 */
@Slf4j
public class AutoBotsAskTask implements Runnable {

    /**
     * 占位符依次为：打点KEY、指标名称（可用率、TP99、调用次数）、当前数值
     */
    private final static String UMP_QUESTION_DESC_TEMPLATE = "UMP KEY：%s%s变动为%.2f，如何处理？";
    /**
     * 占位符依次为：打点KEY、维度信息、指标名称（可用率、TP99、调用次数）、当前数值
     */
    private final static String P_FINDER_QUESTION_DESC_TEMPLATE = "PFinder KEY：%s（%s）%s变动为%.2f，如何处理？";
    /**
     * 占位符依次为：应用英文编码、指标名称（CPU使用率、RSS内存使用率、TCP重传数）、当前数值
     */
    private final static String MDC_QUESTION_DESC_TEMPLATE = "看下这组数据应该怎样运维：%s";
    private final static String DONG_DONG_MSG_TEMPLATE = "问题：\n%s\n\n\n处理方案：\n%s";
    private AutoBotsDependency autoBotsDependency;

    private AlarmHandleContext alarmHandleContext;

    public AutoBotsAskTask(AutoBotsDependency autoBotsDependency, AlarmHandleContext alarmHandleContext) {
        this.autoBotsDependency = autoBotsDependency;
        this.alarmHandleContext = alarmHandleContext;
    }

    /**
     * When an object implementing interface <code>Runnable</code> is used
     * to create a thread, starting the thread causes the object's
     * <code>run</code> method to be called in that separately executing
     * thread.
     * <p>
     * The general contract of the method <code>run</code> is that it may
     * take any action whatsoever.
     *
     * @see Thread#run()
     */
    @Override
    public void run() {
        if (Objects.equals(alarmHandleContext.queryAlarmLevel(), AlarmLevelEnum.WARNING.getCode())) {
            return;
        }
        log.info("AutoBotsAskTask.run 收到critical告警，触发预案决策流程，context:{}", JSON.toJSONString(alarmHandleContext));
        PlatformTypeEnum platformTypeEnum = PlatformTypeEnum.getEnumByCode(alarmHandleContext.queryPlatformNo());
        String desc = null;
        String platform = null;
        if (platformTypeEnum == null || Objects.equals(platformTypeEnum.getCode(), PlatformTypeEnum.UMP.getCode())) {
//            desc = String.format(UMP_QUESTION_DESC_TEMPLATE, alarmHandleContext.queryEndpoint(), MetricTypeEnum.getDescByCode(alarmHandleContext.queryAlarmMetric()), alarmHandleContext.queryMetricCurrent());
//            platform = PlatformTypeEnum.UMP.getCode();

            UmpAlarmDTO umpAlarm = new UmpAlarmDTO();
            umpAlarm.setUmpKey(alarmHandleContext.getMetricInfo().getEndpoint());
            umpAlarm.setAppName(alarmHandleContext.getApplicationInfo().getAppName());
            umpAlarm.setStartTime(alarmHandleContext.getAlarmInfo().getStartTime());
            umpAlarm.setEndTime(alarmHandleContext.getAlarmInfo().getEndTime());
            umpAlarm.setMetrics(alarmHandleContext.getAlarmInfo().getAlarmMetric());

            try {
                autoBotsDependency.getAnalysisFlowService().startFlow(umpAlarm, alarmHandleContext.getRequestMeta().getTraceId());
            } catch (InternalFailureException e) {
                log.error("AutoBotsAskTask.run 启动UMP分析流程失败，msg:{},exception:", e.getMsg(), e);
            } catch (InvalidRequestException e) {
                log.error("AutoBotsAskTask.run 启动UMP分析流程失败，msg:{},exception:", e.getMsg(), e);
            } catch (DependencyFailureException e) {
                log.error("AutoBotsAskTask.run 启动UMP分析流程失败，msg:{},exception:", e.getMsg(), e);
            } catch (Exception e) {
                log.error("AutoBotsAskTask.run 启动UMP分析流程失败，exception:", e);
            }
            return;
        } else if (Objects.equals(platformTypeEnum.getCode(), PlatformTypeEnum.P_FINDER.getCode())) {
            desc = String.format(P_FINDER_QUESTION_DESC_TEMPLATE, alarmHandleContext.queryEndpoint(), "维度信息", MetricTypeEnum.getDescByCode(alarmHandleContext.queryAlarmMetric()), alarmHandleContext.queryMetricCurrent());
            //TODO wangqin83 暂不支持PFinder平台
            platform = PlatformTypeEnum.P_FINDER.getCode();
            return;
        } else if (Objects.equals(platformTypeEnum.getCode(), PlatformTypeEnum.MDC.getCode())) {
            platform = PlatformTypeEnum.MDC.getCode();
            try {
                //请求行云部署分组信息
                ApplicationInfo applicationInfo = autoBotsDependency.getJdosService().queryGroups(alarmHandleContext.queryAppName(), alarmHandleContext.queryGroups());
                //请求易维获取分组硬件指标信息
                if (applicationInfo.getGroups().size() > 1) {
                    log.error("AutoBotsAskTask.run 生成MDC异常提问报文失败，终止流程：参数非法，暂不支持多分组异常处理");
                    return;
                }
                GroupInfo groupInfo = applicationInfo.getGroups().get(0);
                List<String> resourceIds = groupInfo.getContainers().stream().map(ContainerInfo::getIp).collect(Collectors.toList());
                Date now = new Date();
                Date ago0Min = DateUtils.queryMinByDiffSource(now, 0);
                Date ago6Min = DateUtils.queryMinByDiffSource(now, -6);
                Date ago1Min = DateUtils.queryMinByDiffSource(now, -1);
                List<InfrastructureInfoDTO> infrastructureInfo = new ArrayList<>();
                for (int i = 0; i <= 18; i++) {
                    infrastructureInfo = autoBotsDependency.getEasyOpsService().getInfrastructureInfo(alarmHandleContext.queryAppName(), resourceIds, ago6Min, ago0Min);
                    if (!validResult(infrastructureInfo, ago1Min, resourceIds, false)) {
                        Thread.sleep(10000);
                    } else {
                        break;
                    }
                }

                validResult(infrastructureInfo, ago1Min, resourceIds, true);
                MdcMetric mdcMetric = new MdcMetric();
                mdcMetric.setAppName(alarmHandleContext.queryAppName());
                mdcMetric.setGroup(groupInfo.getName());
                mdcMetric.setIpList(resourceIds);
                mdcMetric.setMetrics(infrastructureInfo);
                desc = String.format(MDC_QUESTION_DESC_TEMPLATE, JSON.toJSONString(mdcMetric));
            } catch (InvalidRequestException e) {
                log.error("AutoBotsAskTask.run 生成MDC异常提问报文失败，终止流程：参数非法，msg:{},exception:", e.getMsg(), e);
                return;
            } catch (InternalFailureException e) {
                log.error("AutoBotsAskTask.run 生成MDC异常提问报文失败，终止流程：内部异常，msg:{},exception:", e.getMsg(), e);
                return;
            } catch (DependencyFailureException e) {
                log.error("AutoBotsAskTask.run 生成MDC异常提问报文失败，终止流程：依赖异常，msg:{},exception:", e.getMsg(), e);
                return;
            } catch (Exception e) {
                log.error("AutoBotsAskTask.run 生成MDC异常提问报文失败，终止流程：依赖异常，msg:{},exception:", e.getMessage(), e);
                return;
            }
        }
        QuestionDTO questionDTO = QuestionDTO.initQuestion(alarmHandleContext.queryTraceId(), desc);
        AnswerDTO answerDTO = autoBotsDependency.getAutoBotsServiceAdapter().askQuestion(questionDTO);
        if (Objects.equals(platform, PlatformTypeEnum.MDC.getCode())) {
            try {
                String answer;
                String jsonPattern = "```json\\s*([\\s\\S]*?)```";
                Pattern pattern = Pattern.compile(jsonPattern);
                Matcher matcher = pattern.matcher(answerDTO.getAutoBotsAnswer());

                if (matcher.find()) {
                    // 提取JSON字符串
                    answer = matcher.group(1).trim();
                } else {
                    log.error("AutoBotsAskTask.run 返回信息错误answerDTO:{}", JSON.toJSONString(answerDTO));
                    return;
                }

                MdcOperateRequest mdcOperateRequest = JSON.parseObject(answer, MdcOperateRequest.class);
                handleMdcRequest(mdcOperateRequest);
                sendDongDongMsg(alarmHandleContext.queryContactErps(), answerDTO.getAutoBotsAnswer());
            } catch (Exception e) {
                log.error("AutoBotsAskTask.run answerDTO:{}, exception:", JSON.toJSONString(answerDTO), e);
            }
        } else {
            String msg = String.format(DONG_DONG_MSG_TEMPLATE, questionDTO.getQuestionDesc(),  answerDTO.getAutoBotsAnswer());
            sendDongDongMsg(alarmHandleContext.queryContactErps(), msg);
        }
    }

    private void sendDongDongMsg(List<String> jdMes, String msg) {
        StringBuilder receivers = new StringBuilder("wangqin83,zhuhongru1,");
        if (CollectionUtils.isNotEmpty(jdMes)) {
            for (String jdMe : jdMes) {
                receivers.append(jdMe).append(",");
            }
        }
        String receiver = receivers.substring(0, receivers.length() - 1);
        autoBotsDependency.getDongdongSendMsgHandle().sendMsg(receiver, "物流交易平台紧急告警预案匹配结果", msg);
    }

    public void handleMdcRequest(MdcOperateRequest request) {
        log.info("回调请求体为:{}", JSON.toJSONString(request));
        try {
            if (Objects.equals(request.getOperateType(), MDCOperateTypeEnum.OFF_LINE.getCode())) {
                ShutdownRequest shutdownRequest = new ShutdownRequest();
                shutdownRequest.setAppName(request.getAppName());
                shutdownRequest.setGroup(request.getGroup());
                List<String> ipList = JSON.parseObject(request.getProblemIpList(), new TypeReference<List<String>>() {
                });
                shutdownRequest.setIpList(ipList);
                shutdownRequest.setReason(request.getReason());
                autoBotsDependency.getExceptionHandleService().shutdownApproval(shutdownRequest);
            } else if (Objects.equals(request.getOperateType(), MDCOperateTypeEnum.Expansion.getCode())) {
                ScaleUpRequest scaleUpRequest = new ScaleUpRequest();
                scaleUpRequest.setAppName(request.getAppName());
                scaleUpRequest.setGroup(request.getGroup());
                scaleUpRequest.setIpList(new ArrayList<>(Arrays.asList(request.getProblemIpList().split(","))));
                scaleUpRequest.setReason(request.getReason());
                autoBotsDependency.getExceptionHandleService().scaleUpApproval(scaleUpRequest);
            }
        } catch (InternalFailureException e) {
            log.error("AutoBotsAskTask.handleMdcRequest 内部异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (DependencyFailureException e) {
            log.error("AutoBotsAskTask.handleMdcRequest 外部依赖异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (InvalidRequestException e) {
            log.warn("AutoBotsAskTask.handleMdcRequest 入参非法 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (Exception e) {
            log.error("AutoBotsAskTask.handleMdcRequest 系统异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMessage(), e);
            return;
        }
    }

    private boolean validResult(List<InfrastructureInfoDTO> infrastructureInfo, Date ago1Min, List<String> resourceIds, boolean needLog) {
        List<InfrastructureInfoDTO> infrastructureInfoLastSecond = infrastructureInfo.stream()
            .filter(infrastructureInfoDTO -> ago1Min.getTime() == infrastructureInfoDTO.getTimestamp())
            .collect(Collectors.toList());
        if (infrastructureInfoLastSecond.size() < resourceIds.size()) {
            if (needLog) {
                log.info("最后一分钟数据较少infrastructureInfoLastSecond:{}, resourceIds:{}", infrastructureInfoLastSecond.size(), resourceIds.size());
            }

            return false;
        }

        for (InfrastructureInfoDTO infrastructureInfoDTO : infrastructureInfoLastSecond) {
            if (ObjectUtils.hasNullProperties(infrastructureInfoDTO)) {
                if (needLog) {
                    log.info("最后一分钟数据缺少属性 infrastructureInfo:{}", JSONObject.toJSONString(infrastructureInfoDTO));
                }

                return false;
            }
        }

        log.info("数据复合要求，可以提供给大模型");
        return true;
    }
}
