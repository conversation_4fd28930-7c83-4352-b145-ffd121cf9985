package com.tops.autobots.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.llm.client.domain.autobots.AutoBotsRequest;
import com.jd.llm.client.domain.autobots.AutoBotsResult;
import com.jd.llm.client.domain.autobots.BotsResponse;
import com.jd.llm.client.service.AutoBotsService;
import com.tops.autobots.domain.AnswerDTO;
import com.tops.autobots.domain.QuestionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.UUID;

@Slf4j
@Component
public class AutoBotsServiceAdapter {
    @Value("${jsf.consumer.autobots.botId}")
    private String autoBotId;
    @Value("${jsf.consumer.autobots.botSecret}")
    private String autoBotToken;
    @Autowired
    private AutoBotsService autoBotsService;
    @Value("${autoBots.callbackUrl}")
    private String callbackUrl;

    public AnswerDTO askQuestion(QuestionDTO question) {
        //TODO wangqin83 增加打点
        String agentId = autoBotId;//autobots平台获取智能体ID
        String token = autoBotToken;//autobots平台获取智能体秘钥
        AutoBotsRequest request = getRequest(question);
        BotsResponse<AutoBotsResult> response = null;
        try {
            response = autoBotsService.searchAiRequest(agentId, token, request);
            log.info("AutoBotsServiceAdapter.askQuestion request:{}，response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
        } catch (Exception e) {
            //TODO wangqin83 增加异常处理
            log.error("UmpController.testSearchAiRequest e:", e);
        } finally {

        }
        if (botAnswerSuccess(response)) {
            String status = response.getData().getStatus();
            //异步执行，轮询获取结果
            if (botAnswerLoading(response)) {
                try {
                    response = autoBotsService.searchAiResult(agentId, token, request);
                    // status为running，表示持续输出，为finished表示结束输出（或者判断finished=true）
                    while (botAnswerRunning(response)) {
                        response = autoBotsService.searchAiResult(agentId, token, request);
                        if (botAnswerFinish(response)) {
                            log.info("UmpController.testSearchAiRequest ,智能问答获取结果：{}", JSONObject.toJSONString(response));
                        }
                    }
                } catch (Exception e) {
                    //TODO wangqin83 增加异常处理
                    log.error("UmpController.testSearchAiRequest e:", e);
                }

            } else if (botAnswerNo(response)) {//大模型无法回复
                AnswerDTO answerDTO = new AnswerDTO();
                answerDTO.setAutoBotsAnswer(String.format("无法解答该问题:%s。小秘正在学习中。", question.getQuestionDesc()));
                return answerDTO;
            }
        } else {
            //TODO wangqin83 增加异常处理
        }
        AnswerDTO answerDTO = new AnswerDTO();
        answerDTO.setAutoBotsAnswer(response.getData().getResponseAll());
        return answerDTO;
    }

    private AutoBotsRequest getRequest(QuestionDTO question) {
        AutoBotsRequest request = new AutoBotsRequest();
        request.setTraceId(UUID.randomUUID().toString());//会话ID，同一个会话相同
        request.setReqId(String.valueOf(System.currentTimeMillis()));//请求ID，每次请求不一样
        request.setErp(question.getAsker());
        request.setKeyword(question.getQuestionDesc());//用户查询关键字
        //todo 未来考虑扩展
        request.setCallbackUrl(callbackUrl + "mdc");
        return request;
    }

    private boolean botAnswerSuccess(BotsResponse<AutoBotsResult> response) {
        return response.getCode() == 200;
    }

    private boolean botAnswerRunning(BotsResponse<AutoBotsResult> response) {
        String status = response.getData().getStatus();
        return Objects.equals("running", status);
    }

    private boolean botAnswerFinish(BotsResponse<AutoBotsResult> response) {
        String status = response.getData().getStatus();
        return Objects.equals("finished", status);
    }

    private boolean botAnswerLoading(BotsResponse<AutoBotsResult> response) {
        String status = response.getData().getStatus();
        return Objects.equals("loading", status);
    }

    private boolean botAnswerNo(BotsResponse<AutoBotsResult> response) {
        String status = response.getData().getStatus();
        return Objects.equals("no", status);
    }
}
