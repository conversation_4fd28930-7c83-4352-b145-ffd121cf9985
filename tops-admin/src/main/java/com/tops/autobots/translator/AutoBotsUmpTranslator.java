package com.tops.autobots.translator;

import com.tops.autobots.domain.AlarmUmpRequestDTO;
import com.tops.autobots.domain.MetricsDataAutobotDTO;
import com.tops.autobots.domain.OrderQuantityRequestDTO;
import com.tops.common.utils.DateUtils;
import com.tops.ump.bean.dto.MetricDataDTO;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName AutoBotsUmpTraslator
 * @Description 用于将ump参数转化为不同的类型，提高文档匹配准确率
 * @date 2024年10月29日 5:50 PM
 */
@Component
public class AutoBotsUmpTranslator {
    /**
     * ump参数没有使用驼峰命名，这里做标准还
     * @param metricDataDTOS
     * @return
     * @throws ParseException
     */
    public List<MetricsDataAutobotDTO> convertMetricsDataAutoBotDTO(List<MetricDataDTO> metricDataDTOS) throws ParseException {
        List<MetricsDataAutobotDTO> metricsDataAutobotDTOList = new ArrayList<>();
        for(MetricDataDTO metricDataDTO : metricDataDTOS) {
            MetricsDataAutobotDTO metricsDataAutobotDTO = new MetricsDataAutobotDTO();
            Date date = DateUtils.parseDate(metricDataDTO.getDataTime(), "yyyyMMddHHmmss");
            metricsDataAutobotDTO.setDataTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date));
            metricsDataAutobotDTO.setSuccessCount(metricDataDTO.getSuccessCount());
            metricsDataAutobotDTO.setAvailRate(metricDataDTO.getAvailRate());
            metricsDataAutobotDTO.setTp99(metricDataDTO.getTP99());
            metricsDataAutobotDTOList.add(metricsDataAutobotDTO);
        }

        return metricsDataAutobotDTOList;
    }


    /**
     * 接单请求量检查
     * @param metricDataDTOS
     * @return
     * @throws ParseException
     */
    public List<OrderQuantityRequestDTO> convertOrderQuantityDTO(List<MetricDataDTO> metricDataDTOS) throws ParseException {
        List<OrderQuantityRequestDTO> orderQuantityRequestDTOS = new ArrayList<>();
        for(MetricDataDTO metricDataDTO : metricDataDTOS) {
            OrderQuantityRequestDTO orderQuantityRequestDTO = new OrderQuantityRequestDTO();
            orderQuantityRequestDTO.setOrderQuantity(metricDataDTO.getSuccessCount());
            Date date = DateUtils.parseDate(metricDataDTO.getDataTime(), "yyyyMMddHHmmss");
            orderQuantityRequestDTO.setDateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date));
            orderQuantityRequestDTOS.add(orderQuantityRequestDTO);
        }

        return orderQuantityRequestDTOS;
    }

    /**
     * 启动条件检查
     * @param metricDataDTOS
     * @return
     * @throws ParseException
     */
    public List<AlarmUmpRequestDTO> convertAlarmUmpRequestDTO(List<MetricDataDTO> metricDataDTOS) throws ParseException {
        List<AlarmUmpRequestDTO> alarmUmpRequestDTOS = new ArrayList<>();
        for(MetricDataDTO metricDataDTO : metricDataDTOS) {
            AlarmUmpRequestDTO alarmUmpRequestDTO = new AlarmUmpRequestDTO();
            Date date = DateUtils.parseDate(metricDataDTO.getDataTime(), "yyyyMMddHHmmss");
            alarmUmpRequestDTO.setDataTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, date));
            alarmUmpRequestDTO.setAlarmSuccessCount(metricDataDTO.getSuccessCount());
            alarmUmpRequestDTO.setAlarmAvailRate(metricDataDTO.getAvailRate());
            alarmUmpRequestDTO.setAlarmTP99(metricDataDTO.getTP99());
            alarmUmpRequestDTOS.add(alarmUmpRequestDTO);
        }

        return alarmUmpRequestDTOS;
    }
}
