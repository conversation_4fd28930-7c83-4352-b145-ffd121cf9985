package com.tops.autobots.translator;

import com.alibaba.fastjson.JSON;
import com.tops.alarm.bean.dto.DuccModifyApprovalContent;
import com.tops.alarm.bean.dto.ShutdownApprovalContent;
import com.tops.alarm.bean.dto.ShutdownRequest;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.autobots.domain.DuccModifyDTO;
import com.tops.autobots.domain.DuccModifyRequest;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;

@Slf4j
@Component
public class DuccModifyTranslator implements XbpTranslator {
    @Override
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException {
        if (!(request instanceof DuccModifyRequest)) {
            throw new InternalFailureException("回传请求类型匹配错误");
        }

        DuccModifyRequest duccModifyRequest = (DuccModifyRequest) request;
        SubmitApprovalRequest approvalRequest = new SubmitApprovalRequest();
        approvalRequest.setProcessCode(XbpProcessEnum.DUCC_MODIFY.getCode());
        approvalRequest.setPlatform(ApprovalPlatformEnum.XBP.getCode());

        //获取回调时要使用的报文
        List<DuccModifyApprovalContent> approvalContents = new ArrayList<>();
        for (DuccModifyDTO duccModifyDTO : duccModifyRequest.getDuccModifyDTOS()) {
            DuccModifyApprovalContent approvalContent = new DuccModifyApprovalContent();
            approvalContent.setApplication(duccModifyDTO.getApplication());
            approvalContent.setNamespace(duccModifyDTO.getNamespace());
            approvalContent.setConfig(duccModifyDTO.getConfig());
            approvalContent.setProfile(duccModifyDTO.getProfile());
            approvalContent.setKey(duccModifyDTO.getKey());
            approvalContent.setNewValue(duccModifyDTO.getNewValue());
            approvalContents.add(approvalContent);
        }
        approvalRequest.setInstanceContent(JSON.toJSONString(approvalContents));
        approvalRequest.setInstanceContentKey("");
        //获取审批实例前端展示信息：表单部分
        Map<String, String> instanceForm = getInstanceForm(duccModifyRequest);
        approvalRequest.setInstanceForm(instanceForm);

        Map<String, String> tableForm = getInstanceTableForm(duccModifyRequest);
        approvalRequest.setInstanceTableForm(tableForm);
        //获取操作人、操作时间
        approvalRequest.setOperator("zhuhongru1");
        approvalRequest.setOperateTime(new Date());
        return approvalRequest;
    }

    /**
     * 获取：回传流程实例-表单信息
     */
    private Map<String, String> getInstanceForm(DuccModifyRequest request) {
        Map<String, String> instanceForm = new HashMap<>();
        instanceForm.put(XBP_DUCC_MODIFY_REASON, request.getReason());
        return instanceForm;
    }

    /**
     * 获取：取消流程实例-表格信息
     */
    private Map<String, String> getInstanceTableForm(DuccModifyRequest request) {
        Map<String, String> instanceTableForm = new HashMap<>();
        instanceTableForm.put(XBP_DUCC_MODIFY_TABLE, JSON.toJSONString(request.getDuccModifyDTOS()));
        return instanceTableForm;
    }

    @Override
    public boolean isMatch(String processName) {
        return XbpProcessEnum.DUCC_MODIFY.getCode().equals(processName);
    }
}
