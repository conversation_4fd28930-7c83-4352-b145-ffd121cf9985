package com.tops.autobots.translator;

import com.alibaba.fastjson.JSON;
import com.jd.llm.client.domain.autobots.AutoBotsWfRequest;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Description: 工作流对象转换类
 * @Author: zhuhongru1
 * @CreateDate: 2025/5/1 18:16
 * @Copyright: Copyright (c) 2021 JDL.CN All Rights Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Component
public class AutoBotsAsyncMessageTranslator {
    private static final String ROBOT_ID = "00_0e6c2704eac64f05";

    private static final String TENANT_ID = "CN.JD.GROUP";

    private static final String MESSAGE_CONTENT_TYPE = "text";

    private static final String APP_KEY = "U2KYEGMGkqPKemUePzU8";

    public AutoBotsWfRequest autoBotsWfRequest(LogbookBO logbookBO, List<LogbookResponse.LogBookData> dataList) {
        AutoBotsWfRequest request = new AutoBotsWfRequest();
        request.setTraceId(UUID.randomUUID().toString());//通过相同的traceId拿结果
        request.setErp(logbookBO.getErp());
        //执行的工作流ID，如果不传默认用智能体绑定的工作流
        request.setWorkflowId(logbookBO.getWorkflowId());
        Map<String, Object> workflowParams = new HashMap<>();
        workflowParams.put("workflowId", logbookBO.getWorkflowId());
        workflowParams.put("robotId", ROBOT_ID);
        workflowParams.put("appId", APP_KEY);
        workflowParams.put("tenantId", TENANT_ID);
        workflowParams.put("contentType", MESSAGE_CONTENT_TYPE);
        workflowParams.put("content", JSON.toJSONString(dataList));
        workflowParams.put("atUser", logbookBO.getErp());
        workflowParams.put("group", logbookBO.getGroupId());
        workflowParams.put("erpName", logbookBO.getErpName());
        request.setExtParams(workflowParams);
        return request;
    }
}
