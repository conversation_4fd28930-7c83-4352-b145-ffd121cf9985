package com.tops.autobots.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class QuestionDTO implements Serializable {
    /**
     * 序列化唯一标识
     */
    private static final long serialVersionUID = -7468410676970208633L;
    /**
     * traceId
     */
    private String traceId;
    /**
     * 问题描述
     */
    private String questionDesc;
    /**
     * 提问人ERP
     */
    private String asker;

    public static QuestionDTO initQuestion(String traceId, String desc) {
        QuestionDTO questionDTO = new QuestionDTO();
        questionDTO.setTraceId(traceId);
        questionDTO.setQuestionDesc(desc);
        questionDTO.setAsker("wangqin83");
        return questionDTO;
    }

}
