package com.tops.autobots.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName AutobotsPlanContext
 * @Description 应急预案上下文
 * @date 2024年10月18日 10:26 AM
 */
@Data
public class AutoBotsPlanContext {
    /**
     * 告警信息
     */
    private UmpAlarmDTO umpAlarm;

    /**
     * ump key
     */
    private String umpKey;

    /**
     * 触发时间
     */
    private Date alarmTime;

    /**
     * 人工问题/机器回答记录,输出给用户卡片
     */
    private Map<String, List<String>> processMsgMap = new HashMap<>();

    /**
     * 是否需要继续执行应急预案
     */
    private boolean needContinue = true;

    /**
     * traceId
     */
    private String traceId;

    /**
     * appName
     */
    private String appName;

    /**
     * 应急预案名称
     */
    private Map<String, Boolean> planMap = new HashMap<>();
}
