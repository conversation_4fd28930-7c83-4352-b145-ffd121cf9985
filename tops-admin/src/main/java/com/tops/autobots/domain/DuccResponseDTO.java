package com.tops.autobots.domain;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;
import lombok.Getter;
import lombok.Setter;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_APPLICATION;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_CONFIG;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_KEY;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_NAMESPACE;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_NEW_VALUE;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_DUCC_MODIFY_TABLE_FIELD_PROFILE;

/**
 * <AUTHOR>
 * @ClassName DuccResponseDTO
 * @Description 未来ducc接入工作流，不再使用此对象
 * @date 2024年10月30日 7:29 PM
 */
@Getter
@Setter
@Deprecated
public class DuccResponseDTO {
    /**
     * 应用
     */
    private String application;
    /**
     * 命名空间
     */
    private String namespace;
    /**
     * 配置
     */
    private String config;
    /**
     * 环境
     */
    private String profile;
    /**
     * 配置项key
     */
    private String key;
    /**
     * 更新后的值
     */
    private String newValue;
}
