package com.tops.autobots.domain;

import com.alibaba.fastjson.annotation.J<PERSON>NField;
import lombok.Getter;
import lombok.Setter;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;

@Getter
@Setter
public class DuccModifyDTO {
    /**
     * 应用
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_APPLICATION)
    private String application;
    /**
     * 命名空间
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_NAMESPACE)
    private String namespace;
    /**
     * 配置
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_CONFIG)
    private String config;
    /**
     * 环境
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_PROFILE)
    private String profile;
    /**
     * 配置项key
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_KEY)
    private String key;
    /**
     * 更新后的值
     */
    @J<PERSON><PERSON><PERSON>(name = XBP_DUCC_MODIFY_TABLE_FIELD_NEW_VALUE)
    private String newValue;
}
