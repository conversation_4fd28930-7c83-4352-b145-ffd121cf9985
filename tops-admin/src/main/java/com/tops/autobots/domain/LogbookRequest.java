package com.tops.autobots.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 查询
 * @Author: zhuhongru1
 * @CreateDate: 2025/4/16 21:03
 * @Copyright: Copyright (c) 2021 JDL.CN All Rights Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Data
public class LogbookRequest {
    /**
     * 系统来源
     */
    private String source;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * app名称
     */
    private String appName;

    /**
     * 查询字段
     */
    private String queryString;

    private int currentStartTimestamp = 0;

    private int currentEndTimestamp = 0;

    /**
     * 分组信息
     */
    private List<String> groups;

    /**
     * ip地址
     */
    private List<String> hosts;

    /**
     * 开始时间
     */
    private long startTimestamp;

    /**
     * 结束时间
     */
    private long endTimestamp;
}
