package com.tops.autobots.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: logbook查询字段
 * @Author: zhuhongru1
 * @CreateDate: 2025/4/16 20:50
 * @Copyright: Copyright (c) 2021 JDL.CN All Rights Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Data
public class LogbookBO {
    /**
     * 查询字段
     */
    private String queryString;

    /**
     * 操作时间
     */
    private Long operateTime;

    /**
     * 分组
     */
    private List<String> groups;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 推送erp
     */
    private String erp;

    /**
     * erp姓名
     */
    private String erpName;

    /**
     * 推送京ME
     */
    private String groupId;

    /**
     * 推送工作流
     */
    private String workflowId;
}
