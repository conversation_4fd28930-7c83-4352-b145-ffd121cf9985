package com.tops.autobots.handler.plan.async;

import cn.hutool.core.thread.ThreadUtil;
import com.jd.fastjson.JSONObject;
import com.jd.llm.client.domain.autobots.AutoBotsResult;
import com.jd.llm.client.domain.autobots.AutoBotsWfRequest;
import com.jd.llm.client.domain.autobots.BotsResponse;
import com.jd.llm.client.service.AutoBotsService;
import com.tops.common.exception.InternalFailureException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
public class AutoBotsAsyncWorkflowHandler {
    @Resource
    private AutoBotsService autoBotsService;

    private static final String AGENT_ID = "17680";

    private static final String AGENT_TOKEN = "067385fc93264608bad67408ef8a4ce5";
    /**
     * 日志查询结果异步推送到京ME
     */
    public void handleAsyncMessage(AutoBotsWfRequest request) {
        Long FLOW_TIME_OUT = 5 * 60 * 1000L;
        log.info("回调智能体: request:{}", JSONObject.toJSONString(request));
        BotsResponse<AutoBotsResult> response = autoBotsService.runWorkflow(AGENT_ID, AGENT_TOKEN, request);
        if (response.getCode() == 200) {
            long startTime = System.currentTimeMillis();
            response = autoBotsService.getWorkflowResult(AGENT_ID, AGENT_TOKEN, request);
            log.info("工作流结果调用结果确认：{}", JSONObject.toJSONString(response));
            // 判断没有结束，及超时（最好设置个超时，防止异常情况）
            while (response.getCode() == 200 && !response.getData().isFinished() && System.currentTimeMillis() - startTime < FLOW_TIME_OUT) {
                // 休眠一段时间，否则调用量太多会触发限流
                ThreadUtil.sleep(10000);
                log.info("工作流结果调用结果重试确认：{}", JSONObject.toJSONString(response));
                response = autoBotsService.getWorkflowResult(AGENT_ID, AGENT_TOKEN, request);
            }
            log.info("工作流结果：" + JSONObject.toJSONString(response));
        } else {
            log.error("工作流执行失败：" + response.getMsg());
        }
    }
}
