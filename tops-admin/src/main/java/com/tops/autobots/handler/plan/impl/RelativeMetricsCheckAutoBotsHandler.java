package com.tops.autobots.handler.plan.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.domain.AnswerDTO;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.MetricsDataAutobotDTO;
import com.tops.autobots.domain.QuestionDTO;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.autobots.translator.AutoBotsUmpTranslator;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.ump.adapter.UmpAdapter;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;
import com.tops.ump.enums.TimeDimensionEnum;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName StartAutoBotsHandler
 * @Description 查询是否需要启动流程
 * @date 2024年10月18日 10:51 AM
 */
@Slf4j
@Service
@Order(20)
public class RelativeMetricsCheckAutoBotsHandler implements AutoBotsHandler {
    @Resource
    AutoBotsServiceAdapter autoBotsServiceAdapter;

    @Resource
    UmpAdapter umpAdapter;

    @Resource
    AutoBotsUmpTranslator autoBotsUmpTranslator;

    @Override
    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException, ParseException {
        for (String plan : context.getProcessMsgMap().keySet()) {
            if (!context.getPlanMap().get(plan)) {
                log.info("plan{}不满足触发条件, 跳过执行", plan);
                continue;
            }

            //1 获取辅助指标
            String relativeMetricsQuestion = initQuestionByPlan("请确认我还需要关注哪些辅助指标", plan, "运维格式要求 - 辅助指标检查预案");
            addHumanMsg(relativeMetricsQuestion, context, plan);
            AnswerDTO relativeMetricsAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), relativeMetricsQuestion));
            JSONObject relativeMetricsAnswer = getAnswer(relativeMetricsAnswerDTO.getAutoBotsAnswer());
            addAIMsg("辅助的key为" + JSON.toJSONString(relativeMetricsAnswer), context, plan);
            //为空不做处理,这里认为应急预案没写,
            if (validRelativeMetrics(relativeMetricsAnswer)) {
                JSONArray relativeKeysArray = relativeMetricsAnswer.getJSONArray("relativeKeys");
                List<String> relativeKeys = relativeKeysArray.toJavaList(String.class);
                //2 获取UMP KEY信息
                QueryMonitorDataRequest queryMonitorDataRequest = initQueryMonitorDataRequest(relativeKeys,
                    context.getAlarmTime());
                Map<String, List<MetricDataDTO>> umpInfoMap = umpAdapter.queryPanelData(queryMonitorDataRequest, 20);

                Map<String, List<MetricsDataAutobotDTO>> umpInfoAutobotsMap = new HashMap<>();
                for (String umpKey : umpInfoMap.keySet()) {
                    umpInfoAutobotsMap.put(umpKey, autoBotsUmpTranslator.convertMetricsDataAutoBotDTO(umpInfoMap.get(umpKey)));
                }

                //3 询问大模型是否有问题
                String relativeMetricsCheck = initQuestionByPlan(context.getUmpKey() + "请确认应急预案关联的辅助指标数据是否正确" + JSON.toJSONString(umpInfoAutobotsMap), plan, "运维格式要求 - 辅助指标检查预案");
                addHumanMsg(relativeMetricsCheck, context, plan);
                AnswerDTO relativeCheckAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), relativeMetricsCheck));
                /**
                 * {
                 *   "result":false,
                 *   "detail":"umpKey1的successCount在连续3分钟内的前两分钟未达到600的要求（分别为500和550），umpKey2的tp99在连续3分钟内符合条件（分别为105ms、110ms、115ms），但整体条件不满足，应急预案不触发。"}
                 */
                JSONObject relativeCheckAnswer = getAnswer(relativeCheckAnswerDTO.getAutoBotsAnswer());
                addAIMsg("辅助指标检查结果为:" + JSON.toJSONString(relativeCheckAnswer), context, plan);
                context.getPlanMap().put(plan, relativeCheckAnswer.getBoolean("result"));
            } else {
                addAIMsg("未配置辅助指标,跳过检查", context, plan);
            }
        }
    }

    /**
     * 解析辅助指标
     * {
     * "relativeKeys": [
     * "jdl-pms.ability.customerConfigService_cn_jdl_b2c",
     * "jdl-pms.ext.service.limitOrderApiAdapter.queryOrderLimit",
     * "jdl-pms.ability.customerConfigRemoteService_cn_jdl_c2c",
     * "jdl-pms.ext.service.cityAgingNewApi.queryMixtureProductAging"
     * ]
     * }
     *
     * @param json
     * @return
     */
    private boolean validRelativeMetrics(JSONObject json) {
        try {
            JSONArray jsonArray = json.getJSONArray("relativeKeys");
            return jsonArray != null;
        } catch (Exception ex) {
            log.info("无法解析辅助指标信息:", JSON.toJSONString(json));
        }

        return false;
    }
}
