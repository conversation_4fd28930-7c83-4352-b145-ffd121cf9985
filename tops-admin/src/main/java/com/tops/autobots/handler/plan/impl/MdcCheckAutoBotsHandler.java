package com.tops.autobots.handler.plan.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tops.alarm.bean.dto.MdcMetric;
import com.tops.alarm.enums.PlatformTypeEnum;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.constants.AutoBotsConstants;
import com.tops.autobots.domain.*;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.common.constant.Constants;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.ObjectUtils;
import com.tops.jdos.adapter.EasyOpsAdapter;
import com.tops.jdos.bean.dto.InfrastructureInfoDTO;
import com.tops.jdos.domain.ApplicationInfo;
import com.tops.jdos.domain.ContainerInfo;
import com.tops.jdos.domain.GroupInfo;
import com.tops.jdos.service.EasyOpsService;
import com.tops.jdos.service.JDOSService;
import com.tops.ump.adapter.UmpAdapter;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName StartAutoBotsHandler
 * @Description 查询是否需要启动流程
 * @date 2024年10月18日 10:51 AM
 */
@Slf4j
//@Service
//@Order(30)
public class MdcCheckAutoBotsHandler implements AutoBotsHandler {
    @Resource
    private AutoBotsServiceAdapter autoBotsServiceAdapter;

    @Resource
    private EasyOpsService easyOpsService;

    @Resource
    private JDOSService jdosService;

    @Override
    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        if (!context.isNeedContinue()) {
            log.info("MdcCheckAutoBotsHandler.process 前置节点已判断无需执行预案");
            return;
        }
        //1 获取预案
        Map<String, Boolean> contingencyPlans = new LinkedHashMap<>();
        context.getPlanMap().forEach((contingencyPlan, needContinue) -> {
            if (needContinue) {
                contingencyPlans.put(contingencyPlan, needContinue);
            }
        });
        if (CollectionUtils.isEmpty(contingencyPlans)) {
            log.info("MdcCheckAutoBotsHandler.process 当前无需要判断的预案");
            return;
        }
        Map<String, ExceptionRateDTO> appExceptionRate = queryAppExceptionRate(context.getAppName(), context.getTraceId());
        List<Map<String, ExceptionRateDTO>> appExceptionRates = new ArrayList<>();
        appExceptionRates.add(appExceptionRate);
        String exceptionRateJSON = JSON.toJSONString(appExceptionRates);
        for (String contingencyPlan : contingencyPlans.keySet()) {
            String relativeMetricsQuestion = getQuestionDesc(contingencyPlan, exceptionRateJSON);
            addHumanMsg(relativeMetricsQuestion, context, contingencyPlan);
            AnswerDTO relativeMetricsAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), relativeMetricsQuestion));
            JSONObject answer = getAnswer(relativeMetricsAnswerDTO.getAutoBotsAnswer());
            //为空不做处理,这里认为应急预案没写,
            if (validRelativeMetrics(answer)) {
                Boolean result = answer.getBoolean("result");
                contingencyPlans.put(contingencyPlan, result);
                addAIMsg("异常机器占比小于阈值:" + JSON.toJSONString(answer), context, contingencyPlan);
            } else {
                addAIMsg("未配置相关指标,跳过检查", context, contingencyPlan);
            }
        }
        Optional<Boolean> any = contingencyPlans.values().stream().filter(Boolean::booleanValue).findAny();
        context.setNeedContinue(any.isPresent());
    }

    /**
     * 解析相关指标
     * {
     * "relativeKeys": [
     * "jdl-pms.ability.customerConfigService_cn_jdl_b2c",
     * "jdl-pms.ext.service.limitOrderApiAdapter.queryOrderLimit",
     * "jdl-pms.ability.customerConfigRemoteService_cn_jdl_c2c",
     * "jdl-pms.ext.service.cityAgingNewApi.queryMixtureProductAging"
     * ]
     * }
     *
     * @param json
     * @return
     */
    private boolean validRelativeMetrics(JSONObject json) {
        try {
            JSONArray jsonArray = json.getJSONArray("relativeKeys");
            return jsonArray != null;
        } catch (Exception ex) {
            log.info("无法解析相关指标信息:", JSON.toJSONString(json));
        }

        return false;
    }

    private String getQuestionDesc(String contingencyPlan, String exceptionRateJSON) {
        return String.format(AutoBotsConstants.QUESTION_DESC_TEMPLATE_HANDLER_MDC, contingencyPlan, exceptionRateJSON);
    }

    private Map<String, ExceptionRateDTO> queryAppExceptionRate(String appName, String traceId) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        Map<String, ExceptionRateDTO> result = new HashMap<>();
        //请求行云部署分组信息
        ApplicationInfo applicationInfo = jdosService.queryApplication(appName);
        List<GroupInfo> groups = applicationInfo.getGroups();
        List<String> resourceIds = new ArrayList<>();
        for (GroupInfo group : groups) {
            resourceIds.addAll(group.getContainers().stream().map(ContainerInfo::getIp).collect(Collectors.toList()));
        }
        Date now = new Date();
        Date ago0Min = DateUtils.queryMinByDiffSource(now, 0);
        Date ago6Min = DateUtils.queryMinByDiffSource(now, -6);
        Date ago1Min = DateUtils.queryMinByDiffSource(now, -1);
        List<InfrastructureInfoDTO> infrastructureInfo = new ArrayList<>();
        try {
            for (int i = 0; i <= 18; i++) {
                infrastructureInfo = easyOpsService.getInfrastructureInfo(appName, resourceIds, ago6Min, ago0Min);
                if (!validResult(infrastructureInfo, ago1Min, resourceIds, false)) {
                    Thread.sleep(10000);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate");
            throw new DependencyFailureException("获取硬件信息失败");
        }
        if (validResult(infrastructureInfo, ago1Min, resourceIds, true)) {
            log.error("MdcCheckAutoBotsHandler.queryAppExceptionRate 获取硬件信息失败。外部接口返回数据错误。流程中断");
            throw new DependencyFailureException("获取硬件信息失败。外部接口返回数据错误。流程中断");
        }
        MdcMetric mdcMetric = new MdcMetric();
        mdcMetric.setAppName(appName);
        mdcMetric.setGroup("total");
        mdcMetric.setIpList(resourceIds);
        mdcMetric.setMetrics(infrastructureInfo);
        String desc = String.format(AutoBotsConstants.MDC_QUESTION_DESC_TEMPLATE, JSON.toJSONString(mdcMetric));
        QuestionDTO questionDTO = QuestionDTO.initQuestion(traceId, desc);
        AnswerDTO answerDTO = autoBotsServiceAdapter.askQuestion(questionDTO);
        try {
            String answer = answerDTO.getAutoBotsAnswer().replaceFirst("```json", "");
            answer = answer.substring(0, answer.length() - 4);
            MdcOperateRequest mdcOperateRequest = JSON.parseObject(answer, MdcOperateRequest.class);
            List<String> ipList = JSON.parseObject(mdcOperateRequest.getProblemIpList(), new TypeReference<List<String>>() {
            });

            ExceptionRateDTO exceptionRateDTO = new ExceptionRateDTO();
            exceptionRateDTO.setExceptionRate(((double) ipList.size()) / resourceIds.size() + "");
            result.put(appName, exceptionRateDTO);
        } catch (Exception e) {
            log.error("AutoBotsAskTask.run exception:", e);
        }
        return result;
    }

    private boolean validResult(List<InfrastructureInfoDTO> infrastructureInfo, Date ago1Min, List<String> resourceIds, boolean needLog) {
        List<InfrastructureInfoDTO> infrastructureInfoLastSecond = infrastructureInfo.stream()
            .filter(infrastructureInfoDTO -> ago1Min.getTime() == infrastructureInfoDTO.getTimestamp())
            .collect(Collectors.toList());
        if (infrastructureInfoLastSecond.size() < resourceIds.size()) {
            if (needLog) {
                log.info("最后一分钟数据较少infrastructureInfoLastSecond:{}, resourceIds:{}", infrastructureInfoLastSecond.size(), resourceIds.size());
            }

            return false;
        }

        for (InfrastructureInfoDTO infrastructureInfoDTO : infrastructureInfoLastSecond) {
            if (ObjectUtils.hasNullProperties(infrastructureInfoDTO)) {
                if (needLog) {
                    log.info("最后一分钟数据缺少属性 infrastructureInfo:{}", JSONObject.toJSONString(infrastructureInfoDTO));
                }

                return false;
            }
        }

        log.info("数据复合要求，可以提供给大模型");
        return true;
    }
}
