package com.tops.autobots.handler.plan;

import com.alibaba.fastjson.JSONObject;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.MetricsDataAutobotDTO;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;
import com.tops.ump.enums.TimeDimensionEnum;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @ClassName AbstractAutoBotsHandler
 * @Description 用途
 * @date 2024年10月18日 10:33 AM
 */
public interface AutoBotsHandler {
    static String QUESTION_FORMAT = "根据文档\"%s\"和\"%s\",%s";

    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException, ParseException;

    default JSONObject getAnswer(String autoBotsMsg) {
        // 正则表达式用于匹配以 markdown 或 json 开头的块
        String pattern ="```(?:markup|json)\\s*(\\{.*?\\})\\s*```";
        Pattern regex = Pattern.compile(pattern, Pattern.DOTALL);
        Matcher matcher = regex.matcher(autoBotsMsg);

        if (matcher.find()) {
            return JSONObject.parseObject(matcher.group(1).trim());
        }
        return null;
    }

    default void addHumanMsg(String humanMessage, AutoBotsPlanContext context, String plan) {
         if(!context.getProcessMsgMap().containsKey(plan)) {
             context.getProcessMsgMap().put(plan, new ArrayList<>());
         }

         context.getProcessMsgMap().get(plan).add("运维助手:" + humanMessage);
    }

    default void addAIMsg(String humanMessage, AutoBotsPlanContext context, String plan) {
        context.getProcessMsgMap().get(plan).add("AIMessage:" + humanMessage);
    }

    default String initQuestionByPlan(String question, String plan, String opsPlan) {
        return String.format(QUESTION_FORMAT, plan, opsPlan, question);
    }

    /**
     * 获取umpKey 默认endTime往前6分钟
     * @param umpKeys
     * @param endTime
     * @return
     */
    default QueryMonitorDataRequest initQueryMonitorDataRequest(List<String> umpKeys, Date endTime) {
        QueryMonitorDataRequest umpRequest = new QueryMonitorDataRequest();
        umpRequest.setMonitorKeys(umpKeys);
        umpRequest.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, endTime));
        Date startDate = DateUtils.addMinutes(endTime, -6);
        umpRequest.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, startDate));
        umpRequest.setDagaCycle(TimeDimensionEnum.MINUTE.getCode());
        return umpRequest;
    }
}
