package com.tops.autobots.handler.plan.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.domain.AnswerDTO;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.OrderQuantityRequestDTO;
import com.tops.autobots.domain.QuestionDTO;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.autobots.translator.AutoBotsUmpTranslator;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.ump.adapter.UmpAdapter;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName OrderQuantityCheckHandler
 * @Description 用途
 * @date 2024年10月23日 10:39 AM
 */
@Slf4j
@Service
@Order(30)
public class OrderQuantityCheckHandler implements AutoBotsHandler {
    @Resource
    AutoBotsServiceAdapter autoBotsServiceAdapter;

    @Resource
    UmpAdapter umpAdapter;

    @Resource
    AutoBotsUmpTranslator autoBotsUmpTranslator;

    @Override
    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException, ParseException {
        for (String plan : context.getProcessMsgMap().keySet()) {
            if (!context.getPlanMap().get(plan)) {
                log.info("plan{}不满足触发条件, 跳过执行", plan);
                continue;
            }

            String orderQuantityQuestion = initQuestionByPlan("输出所有的接单量指标", plan, "接单量指标检查");
            addHumanMsg(orderQuantityQuestion, context, plan);
            AnswerDTO orderQuantityAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), orderQuantityQuestion));
            JSONObject orderQuantityAnswer = getAnswer(orderQuantityAnswerDTO.getAutoBotsAnswer());
            addAIMsg("需要查询的业务身份为" + JSON.toJSONString(orderQuantityAnswer), context, plan);
            if (orderQuantityResponseValidCheck(orderQuantityAnswer)) {
                Map<String, String> umpBusinessUnitMap = new HashMap<>();
                //查询ump
                for (String key : orderQuantityAnswer.keySet()) {
                    umpBusinessUnitMap.put(orderQuantityAnswer.getString(key), key);
                }

                List<String> umpKeys = new ArrayList<>(umpBusinessUnitMap.keySet());
                QueryMonitorDataRequest queryMonitorDataRequest = initQueryMonitorDataRequest(umpKeys, context.getAlarmTime());
                Map<String, List<MetricDataDTO>> umpInfoMap = umpAdapter.queryPanelData(queryMonitorDataRequest, 20);
                Map<String, List<OrderQuantityRequestDTO>> umpInfoCheckMap = new HashMap<>();
                for (String umpKey : umpInfoMap.keySet()) {
                    List<OrderQuantityRequestDTO> orderQuantityRequestDTOS = autoBotsUmpTranslator.convertOrderQuantityDTO(umpInfoMap.get(umpKey));
                    umpInfoCheckMap.put(umpBusinessUnitMap.get(umpKey), orderQuantityRequestDTOS);
                }

                String orderQuantityCheckQuestion = initQuestionByPlan(context.getUmpKey() + "请确认相关接单量指标数据是否正确" + JSON.toJSONString(umpInfoCheckMap), plan, "运维格式要求-接单请求量检查预案");
                addHumanMsg(orderQuantityCheckQuestion, context, plan);
                AnswerDTO orderQuantityCheckQuestionAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), orderQuantityCheckQuestion));
                JSONObject orderQuantityCheckAnswer = getAnswer(orderQuantityCheckQuestionAnswerDTO.getAutoBotsAnswer());
                addAIMsg("订单量是否有问题触发结果:" + JSONObject.toJSONString(orderQuantityCheckAnswer), context, plan);
                //结果询问大模型
                context.getPlanMap().put(plan, orderQuantityCheckAnswer.getBoolean("result"));
            }
        }
    }

    /**
     * {
     * "cn_jdl_b2c":"eca.onlineByteDance-jdl-eca-standard-ability.commonCreateOrderV1_kd"
     * }
     *
     * @param orderQuantityAnswer
     * @return
     */
    private boolean orderQuantityResponseValidCheck(JSONObject orderQuantityAnswer) {
        return orderQuantityAnswer != null && !orderQuantityAnswer.isEmpty();
    }
}
