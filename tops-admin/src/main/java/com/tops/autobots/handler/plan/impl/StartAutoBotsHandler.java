package com.tops.autobots.handler.plan.impl;

import cn.jdl.oms.core.model.MaterialInfo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jd.fastjson.JSON;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.domain.AlarmUmpRequestDTO;
import com.tops.autobots.domain.AnswerDTO;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.MetricsDataAutobotDTO;
import com.tops.autobots.domain.QuestionDTO;
import com.tops.autobots.domain.UmpAlarmDTO;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.autobots.translator.AutoBotsUmpTranslator;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.ump.adapter.UmpAdapter;
import com.tops.ump.bean.dto.MetricDataDTO;
import com.tops.ump.bean.dto.QueryMonitorDataRequest;
import com.tops.ump.enums.TimeDimensionEnum;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName StartAutoBotsHandler
 * @Description 查询是否需要启动流程
 * @date 2024年10月18日 10:51 AM
 */
@Slf4j
@Service
@Order(1)
public class StartAutoBotsHandler implements AutoBotsHandler {
    @Resource
    AutoBotsServiceAdapter autoBotsServiceAdapter;

    @Resource
    UmpAdapter umpAdapter;

    @Resource
    AutoBotsUmpTranslator autoBotsUmpTranslator;

    @Override
    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException, ParseException {
        UmpAlarmDTO umpAlarm = context.getUmpAlarm();
        if (validFormatResponse(umpAlarm)) {
            context.setAppName(umpAlarm.getAppName());
            String relativePlanQuestion = initQuestionByPlan(umpAlarm.getUmpKey() + "进行UMP应急预案匹配,", "UMP应急预案匹配", null);
            AnswerDTO relativeAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), relativePlanQuestion));
            JSONObject relativeAnswerResponseJSON = getAnswer(relativeAnswerDTO.getAutoBotsAnswer());
            addHumanMsg(relativePlanQuestion, context, "default");
            if (!validRelativeAnswerResponse(relativeAnswerResponseJSON)) {
                addAIMsg("无法获取到对应的应急预案", context, "default");
                return;
            }

            //可以查到应急预案, 初始化上下文信息
            initPlanProcessMap(context, relativeAnswerResponseJSON);
            for (String plan : context.getProcessMsgMap().keySet()) {
                addAIMsg("返回的应急预案名称:" + plan, context, plan);
                //3.1 查询ump数据
                String umpKey = umpAlarm.getUmpKey();
                Date endDate = umpAlarm.getEndTime();
                context.setAlarmTime(endDate);
                QueryMonitorDataRequest umpRequest = initQueryMonitorDataRequest(Arrays.asList(umpKey), endDate);
                Map<String, List<MetricDataDTO>> umpInfoMap = umpAdapter.queryPanelData(umpRequest, 20);

                //3.2组装参数询问大模型
                List<MetricDataDTO> metricDataDTOList = umpInfoMap.get(umpKey);
                List<AlarmUmpRequestDTO> alarmUmpRequestDTOS = autoBotsUmpTranslator.convertAlarmUmpRequestDTO(metricDataDTOList);
                String startQuestion = initQuestionByPlan("请检查是否需要启动应急预案" + JSON.toJSONString(alarmUmpRequestDTOS), plan, "运维格式要求 - 判断是否需要启动应急预案");
                addHumanMsg(startQuestion, context, plan);

                //3.3 判断是否需要继续
                AnswerDTO startAnswerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), startQuestion));
                JSONObject startResponseJson = getAnswer(startAnswerDTO.getAutoBotsAnswer());
                addAIMsg("是否启动应急预案" + JSON.toJSONString(startResponseJson), context, plan);
                /**{"result":false,"description":"可用率低于0.99，但未持续6分钟，无需启用应急预案"}**/
                context.getPlanMap().put(plan, startResponseJson.getBoolean("result"));
            }
        } else {
            addAIMsg("UMP推送报警报文缺少关键信息" + JSON.toJSONString(umpAlarm), context, "default");
            context.setNeedContinue(false);
        }
    }

    /**
     * 解析相关指标
     * {
     * "umpKey":"online.cn.jdl.oms.supplychain.sc.pop.extension.stock.PopReleaseStockExtension.execute",
     * "appName":"jdl-oms-outbound-worker",
     * "startTime":"10:48:00",
     * "endTime":"10:49:00",
     * "metrics":"tp99"
     * }
     *
     * @param jsonObject
     * @return
     */
    private boolean validFormatResponse(UmpAlarmDTO umpAlarm) {
        if (umpAlarm.getUmpKey() == null
            || umpAlarm.getAppName() == null
            || umpAlarm.getStartTime() == null
            || umpAlarm.getEndTime() == null
            || umpAlarm.getMetrics() == null) {
            return false;
        }
        return true;
    }

    /**
     * {
     * "plan": ["航空禁运功能的降级预案", "大件纯配-商家扩展点的降级预案"]
     * }
     *
     * @param jsonObject
     */
    private boolean validRelativeAnswerResponse(JSONObject jsonObject) {
        return jsonObject.getJSONArray("plan") != null;
    }

    /**
     * 初始化上下文信息
     */
    private void initPlanProcessMap(AutoBotsPlanContext context, JSONObject relativeAnswerResponseJSON) {
        JSONArray planNameJSONArray = relativeAnswerResponseJSON.getJSONArray("plan");
        List<String> planNameList = planNameJSONArray.toJavaList(String.class);
        for (String planName : planNameList) {
            context.getProcessMsgMap().put(planName, new ArrayList<>(context.getProcessMsgMap().get("default")));
        }

        context.getProcessMsgMap().remove("default");
    }
}

