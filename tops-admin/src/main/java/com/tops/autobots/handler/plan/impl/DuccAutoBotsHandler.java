package com.tops.autobots.handler.plan.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.service.ApprovalService;
import com.tops.autobots.adapter.AutoBotsServiceAdapter;
import com.tops.autobots.constants.AutoBotsConstants;
import com.tops.autobots.domain.*;
import com.tops.autobots.handler.plan.AutoBotsHandler;
import com.tops.autobots.translator.DuccModifyTranslator;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.BeanCopyUtils;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.objectweb.asm.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName StartAutoBotsHandler
 * @Description 查询是否需要启动流程
 * @date 2024年10月18日 10:51 AM
 */
@Slf4j
@Service
@Order(40)
public class DuccAutoBotsHandler implements AutoBotsHandler {

    @Resource
    private AutoBotsServiceAdapter autoBotsServiceAdapter;

    @Autowired
    private DuccModifyTranslator duccModifyTranslator;
    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private TopsEnvUtils topsEnvUtils;

    @Override
    public void process(AutoBotsPlanContext context) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        for(String contingencyPlan : context.getProcessMsgMap().keySet()) {
            if (!context.getPlanMap().get(contingencyPlan)) {
                log.info("plan{}不满足触发条件, 跳过执行", contingencyPlan);
                continue;
            }

            String ducccSearchQuestion = initQuestionByPlan("回答如果我想启用降级开关，应该如何操作", contingencyPlan, "触发ducc降级开关");
            addHumanMsg(ducccSearchQuestion, context, contingencyPlan);
            AnswerDTO answerDTO = autoBotsServiceAdapter.askQuestion(QuestionDTO.initQuestion(context.getTraceId(), ducccSearchQuestion));
            JSONObject answer = getAnswer(answerDTO.getAutoBotsAnswer());
            addAIMsg("相关应急预案配置:" + JSON.toJSONString(answer), context, contingencyPlan);
            //为空不做处理,这里认为应急预案没写,
            List<DuccResponseDTO> duccResponseDTOS = resolveAnswer(answer);

            if (!CollectionUtils.isEmpty(duccResponseDTOS)) {
                //预发环境、生产环境推送咚咚消息
                if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                    List<DuccModifyDTO> duccModifyDTOS = duccResponseDTOS.stream().map(duccResponseDTO -> {
                        DuccModifyDTO duccModifyDTO = new DuccModifyDTO();
                        BeanCopyUtils.copy(duccResponseDTO, duccModifyDTO);
                        return duccModifyDTO;
                    }).collect(Collectors.toList());
                    dongdongSendMsgHandle.sendMsg(getDongDongReceiver(),
                        getDDMsgTitle(),
                        getDDMsgContentSUCCESS1(duccModifyDTOS));
                    return;
                }
                //TODO 提交审批
//                    DuccModifyRequest duccModifyRequest = new DuccModifyRequest();
//                    duccModifyRequest.setReason(JSON.toJSONString(context.getProcessMsgMap().get(contingencyPlan)));
//                    duccModifyRequest.setDuccModifyDTOS(duccModifyDTOS);
//                    SubmitApprovalRequest xbpRequest = duccModifyTranslator.getXbpRequest(duccModifyRequest);
//                    approvalService.submitApproval(xbpRequest);
            } else {
                addAIMsg("未配置相关指标,跳过检查", context, contingencyPlan);
            }
        }
    }

    /**
     * 解析相关指标
     * [
     *    "duccConfiguration": [
     *         {
     *             "application": "appName1",
     *             "namespace": "dataSpace1",
     *             "config": "configGroup1",
     *             "profile": "profile1",
     *             "key": "switch1",
     *             "newValue": "s1"
     *         },
     *         {
     *             "application": "appName1",
     *             "namespace": "dataSpace1",
     *             "config": "configGroup1",
     *             "profile": "profile1",
     *             "key": "switch2",
     *             "newValue": "s2"
     *         }
     *    ]
     * ]
     *
     * @return
     */
    private List<DuccResponseDTO> resolveAnswer(JSONObject answer) {
        try {
            return JSON.parseArray(answer.getString("duccConfiguration"), DuccResponseDTO.class);
        } catch (Exception ex) {
            log.info("无法解析相关指标信息:{}", JSON.toJSONString(answer));
        }

        return new ArrayList<>();
    }

    private String getDDMsgTitle() {
        StringBuilder content = new StringBuilder();
        content.append(topsEnvUtils.getEnvDesc());
        content.append("：DUCC降级预案执行结果通知");
        return content.toString();
    }

    private String getDDMsgContentERROR1() {
        StringBuilder content = new StringBuilder();
        content.append("DUCC降级预案执行结果通知：匹配到多预案情况暂不支持");
        return content.toString();
    }

    private String getDDMsgContentSUCCESS1(List<DuccModifyDTO> duccModifyDTOS) {
        StringBuilder content = new StringBuilder();
        content.append("DUCC降级预案执行结果通知：封板期间不实际发起审批流。提交审批源数据如下：\n")
            .append(JSON.toJSONString(duccModifyDTOS));
        return content.toString();
    }

    private String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }

    private String getDongDongReceiver() {
        StringBuilder receiver = new StringBuilder().append("weiliming,lixiaoliang16,wangqin83,zhuhongru1");
        return receiver.toString();
    }
}
