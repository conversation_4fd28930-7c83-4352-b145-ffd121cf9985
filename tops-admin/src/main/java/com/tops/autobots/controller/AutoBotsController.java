package com.tops.autobots.controller;

import com.alibaba.fastjson.JSON;
import com.tops.alarm.bean.dto.ScaleUpRequest;
import com.tops.alarm.bean.dto.ShutdownRequest;
import com.tops.alarm.service.ExceptionHandleService;
import com.tops.audit.service.impl.XbpApprovalService;
import com.tops.autobots.domain.AutoBotsPlanContext;
import com.tops.autobots.domain.MdcOperateRequest;
import com.tops.autobots.enums.MDCOperateTypeEnum;
import com.tops.autobots.handler.plan.impl.MdcCheckAutoBotsHandler;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Slf4j
@RestController
@RequestMapping("/tops/autoBots")
@Configuration
@EnableScheduling
public class AutoBotsController {

    @Autowired
    private ExceptionHandleService exceptionHandleService;
//    @Autowired
//    private MdcCheckAutoBotsHandler mdcCheckAutoBotsHandler;

    @PostMapping("/mdc")
    public void handleMdcRequest(@RequestBody MdcOperateRequest request) {
        log.info("回调请求体为:{}", JSON.toJSONString(request));
        try {
            if (Objects.equals(request.getOperateType(), MDCOperateTypeEnum.OFF_LINE.getCode())) {
                ShutdownRequest shutdownRequest = new ShutdownRequest();
                shutdownRequest.setAppName(request.getAppName());
                shutdownRequest.setGroup(request.getGroup());
                shutdownRequest.setIpList(new ArrayList<>(Arrays.asList(request.getProblemIpList().split(","))));
                shutdownRequest.setReason(request.getReason());
                exceptionHandleService.shutdownApproval(shutdownRequest);
            } else if (Objects.equals(request.getOperateType(), MDCOperateTypeEnum.Expansion.getCode())) {
                ScaleUpRequest scaleUpRequest = new ScaleUpRequest();
                scaleUpRequest.setAppName(request.getAppName());
                scaleUpRequest.setGroup(request.getGroup());
                scaleUpRequest.setIpList(new ArrayList<>(Arrays.asList(request.getProblemIpList().split(","))));
                scaleUpRequest.setReason(request.getReason());
                exceptionHandleService.scaleUpApproval(scaleUpRequest);
            }
        } catch (InternalFailureException e) {
            log.error("AutoBotsController.handleMdcRequest 内部异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (DependencyFailureException e) {
            log.error("AutoBotsController.handleMdcRequest 外部依赖异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (InvalidRequestException e) {
            log.warn("AutoBotsController.handleMdcRequest 入参非法 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return;
        } catch (Exception e) {
            log.error("AutoBotsController.handleMdcRequest 系统异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMessage(), e);
            return;
        }
    }

    @PostMapping("/test")
    public void test(@RequestBody MdcOperateRequest request) throws InternalFailureException, InvalidRequestException, DependencyFailureException {
        AutoBotsPlanContext context = new AutoBotsPlanContext();
        context.setNeedContinue(true);
        context.setTraceId("xxxxxxxxxxxxxxx");
        context.setAppName(request.getAppName());
        Map<String,Boolean> planMap=new HashMap<>();
        planMap.put("【产品中心】供给校验服务【航空禁运功能】降级预案",true);
        context.setPlanMap(planMap);
//        mdcCheckAutoBotsHandler.process(context);
    }
}
