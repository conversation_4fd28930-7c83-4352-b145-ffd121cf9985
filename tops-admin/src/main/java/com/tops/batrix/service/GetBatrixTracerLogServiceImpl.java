package com.tops.batrix.service;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.luben.zstd.Zstd;
import com.tops.batrix.dto.BatrixTracerLog;
import com.tops.batrix.dto.BatrixTracerModel;
import com.tops.batrix.dto.BatrixTracerQueryCondition;
import com.tops.batrix.mapper.BatrixTracerLogMapper;
import com.tops.batrix.mapper.BatrixTracerModelMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Service("getBatrixTracerLogServiceImpl")
public class GetBatrixTracerLogServiceImpl implements GetBatrixTracerLogService {

    public static final long DAY_TIME = 1000*60*60*24;

    @Value("${spring.clickhouse.dbName}")
    private String clickhouseDbName;

    @Resource
    BatrixTracerModelMapper batrixTracerModelMapper;

    @Resource
    BatrixTracerLogMapper batrixTracerLogMapper;

    @Override
    public BatrixTracerModel getRequestContent(BatrixTracerQueryCondition condition) {
        check(condition);
        List<BatrixTracerModel> batrixTracerModels = batrixTracerModelMapper.selectTracerModelList(clickhouseDbName, condition);
        if (CollectionUtils.isEmpty(batrixTracerModels)) {
            return null;
        }
        return batrixTracerModels.get(0);
    }

    @Override
    public List<BatrixTracerModel> getRequestContentList(BatrixTracerQueryCondition condition) {
        check(condition);
        List<BatrixTracerModel> batrixTracerModels = batrixTracerModelMapper.selectTracerModelList(clickhouseDbName, condition);
        if (CollectionUtils.isEmpty(batrixTracerModels)) {
            return null;
        }
        for(BatrixTracerModel batrixTracerModel : batrixTracerModels) {
            String responseEncrypiton = batrixTracerModel.getResponseContent();
            byte[] base64 = java.util.Base64.getDecoder().decode(responseEncrypiton.getBytes(StandardCharsets.UTF_8));
            byte[] zstd = decompressBytes(base64);
            batrixTracerModel.setResponseContent(new String(zstd));

        }
        return batrixTracerModels;
    }

    @Override
    @DS(value = "clickhouse")
    public BatrixTracerLog getDependencyRequestContent(BatrixTracerQueryCondition condition, String method) {
        check(condition);
        // 查询链路必须要有traceId
        if (StringUtils.isNotBlank(condition.getTraceId())) {
            throw new IllegalArgumentException("traceId必填");
        }
        if (method == null) {
            throw new IllegalArgumentException("方法名不能为空");
        }
        String[] split = method.split("#");
        if (split.length != 2) {
            throw new IllegalArgumentException("方法名异常,格式：api#method");
        }
        List<BatrixTracerModel> batrixTracerModels = batrixTracerModelMapper.selectTracerModelList(clickhouseDbName, condition);
        if (CollectionUtils.isEmpty(batrixTracerModels)) {
            return null;
        }
        BatrixTracerModel batrixTracerModel = batrixTracerModels.get(0);
        String linkId = batrixTracerModel.getLinkId();
        List<BatrixTracerLog> batrixTracerLogs = batrixTracerLogMapper.selectTracerLogList(clickhouseDbName, condition, linkId);
        for (BatrixTracerLog batrixTracerLog : batrixTracerLogs) {
            if (Objects.equals(batrixTracerLog.getNodeCode(), split[0])
                    && Objects.equals(batrixTracerLog.getNodeName(), split[1]) ) {
                return batrixTracerLog;
            }
        }
        return null;
    }


    @Override
    @DS(value = "clickhouse")
    public BatrixTracerLog getDependencyRequestContentByNodeCode(BatrixTracerQueryCondition condition, String nodeCode, String nodeName) {
        check(condition);
        // 查询链路必须要有traceId
        if (StringUtils.isNotBlank(condition.getTraceId())) {
            throw new IllegalArgumentException("traceId必填");
        }
        if (nodeCode == null) {
            throw new IllegalArgumentException("方法名不能为空");
        }
        List<BatrixTracerModel> batrixTracerModels = batrixTracerModelMapper.selectTracerModelList(clickhouseDbName, condition);
        if (CollectionUtils.isEmpty(batrixTracerModels)) {
            return null;
        }
        BatrixTracerModel batrixTracerModel = batrixTracerModels.get(0);
        String linkId = batrixTracerModel.getLinkId();
        List<BatrixTracerLog> batrixTracerLogs = batrixTracerLogMapper.selectTracerLogList(clickhouseDbName, condition, linkId);
        for (BatrixTracerLog batrixTracerLog : batrixTracerLogs) {
            if (Objects.equals(batrixTracerLog.getNodeCode(), nodeCode)
                     && Objects.equals(batrixTracerLog.getNodeName(), nodeName) ) {
                 return batrixTracerLog;
             }
        }
        return null;
    }

    @Override
    @DS(value = "clickhouse")
    public List<BatrixTracerLog> getDependencyRequestContentList(BatrixTracerQueryCondition condition) {
        check(condition);
        // 查询链路必须要有traceId
        if (StringUtils.isBlank(condition.getTraceId())) {
            throw new IllegalArgumentException("traceId必填");
        }
        List<BatrixTracerModel> batrixTracerModels = batrixTracerModelMapper.selectTracerModelList(clickhouseDbName, condition);
        if (CollectionUtils.isEmpty(batrixTracerModels)) {
            return null;
        }
        BatrixTracerModel batrixTracerModel = batrixTracerModels.get(0);
        String linkId = batrixTracerModel.getLinkId();
        List<BatrixTracerLog> batrixTracerLogs = batrixTracerLogMapper.selectTracerLogList(clickhouseDbName, condition, linkId);
        return batrixTracerLogs;
    }

    /**
     * 校验参数是否合法
     * @param condition
     */
    private void check(BatrixTracerQueryCondition condition) {
        if (null == condition) {
            throw new IllegalArgumentException("condition必填");
        }

        if (null == condition.getAppCode()) {
            throw new IllegalArgumentException("appCode必填");
        }

        if (null == condition.getStartTime() || null == condition.getEndTime()) {
            throw new IllegalArgumentException("时间必填必填");
        }

        if (StringUtils.isNotBlank(condition.getTraceId()) && StringUtils.isNotBlank(condition.getBizId())) {
            throw new IllegalArgumentException("traceId或bizId二选一必填");
        }

        if (StringUtils.isNotBlank(condition.getBizId()))  {
            if (condition.getStartTime() - condition.getEndTime() > DAY_TIME * 15) {
                throw new IllegalArgumentException("查询起始结束时间差不能多于半个月，请调整查询时间");
            }
        }else if (StringUtils.isBlank(condition.getBizId()) && StringUtils.isNotBlank(condition.getTraceId())){
            if (condition.getStartTime() - condition.getEndTime()  > DAY_TIME * 3) {
                throw new IllegalArgumentException("未填写业务id只填写traceId的情况下查询起始结束时间差不能多于3天，请调整查询时间");
            }
        }

        condition.setStartTimeDate(new Date(condition.getStartTime()));
        condition.setEndTimeDate(new Date(condition.getEndTime()));
    }


    /**
     * 解压
     */
    public static byte[] decompressBytes(byte[] bytes) {
        int size = (int) Zstd.decompressedSize(bytes);
        byte[] ob = new byte[size];
        Zstd.decompress(ob, bytes);

        return ob;
    }

}
