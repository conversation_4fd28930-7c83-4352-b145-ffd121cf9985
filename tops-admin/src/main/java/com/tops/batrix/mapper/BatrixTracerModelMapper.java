package com.tops.batrix.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.batrix.dto.BatrixTracerModel;
import com.tops.batrix.dto.BatrixTracerQueryCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Mapper
@DS("clickhouse")
public interface BatrixTracerModelMapper {

    /**
     * 查询链路列表
     *
     * @param dbName    数据库名
     * @param condition 查询参数
     * @return
     */
    List<BatrixTracerModel> selectTracerModelList(@Param("dbName") String dbName, @Param("dto") BatrixTracerQueryCondition condition);

    /**
     * 根据appName,uri,linkId查询链路
     *
     * @param appName          应用名称
     * @param uri              接口
     * @param linkId           链路id
     * @param clickhouseDbName 数据库名
     * @return
     */
    BatrixTracerModel selectByAppUriLinkId(@Param("appName") String appName, @Param("uri") String uri, @Param("linkId") String linkId, @Param("clickhouseDbName") String clickhouseDbName);

}
