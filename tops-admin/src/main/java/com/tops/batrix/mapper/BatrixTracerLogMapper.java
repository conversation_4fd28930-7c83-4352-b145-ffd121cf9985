package com.tops.batrix.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.batrix.dto.BatrixTracerLog;
import com.tops.batrix.dto.BatrixTracerQueryCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description
 * @copyright    &copy;2025 JDL.CN All Right Reserved
 * @Creator:     liujiangwai1
 * @Date:        2025/8/1
 * @version      1.0
 * @since        1.8
 */
@Mapper
@DS("clickhouse")
public interface BatrixTracerLogMapper {

    /**
     * 查询log列表
     * @param tracerLogCountDto
     * @return
     */
    List<BatrixTracerLog> selectTracerLogList(@Param("dbName") String dbName, @Param("dto") BatrixTracerQueryCondition tracerLogCountDto, @Param("linkId") String linkId);
}
