package com.tops.exception.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName ExceptionCenterMessageDTO
 * https://joyspace.jd.com/pages/8XakNIiSEIELnNGbBTR6
 * @Description 用途
 * @date 2024年09月03日 2:34 PM
 */
@Data
public class ExceptionCenterMessageDTO {
    /**
     * 恢复状态
     */
    private Integer resolveStatus;

    /**
     * 事业部编码 deptNo
     */
    private String deptNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * id
     */
    private Long id;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 错误码
     */
    private String errCode;

    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 修改时间(作为记录的ts)
     */
    private String updateTimeStr;
}
