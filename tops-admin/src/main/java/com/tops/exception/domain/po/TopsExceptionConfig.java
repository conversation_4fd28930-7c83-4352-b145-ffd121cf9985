package com.tops.exception.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.tops.common.core.domain.BaseEntity;

/**
 * 定时任务配置对象 tops_exception_config
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_exception_config")
public class TopsExceptionConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 事业部
     */
    private String fulfillmentAccountNo;
    /**
     * 仓编码
     */
    private String warehouseNo;
    /**
     * 异常码
     */
    private String errorCode;
    /**
     * 首次恢复时间(分钟)
     */
    private Integer firstResumeDelay;
    /**
     * 恢复间隔
     */
    private Integer resumeInterval;
    /**
     * 场景
     */
    private String resumeScene;
    /**
     * 恢复别名
     */
    private String resumeAlias;
    /**
     * 场景描述
     */
    private String resumeDesc;
    /**
     * 最大恢复次数
     */
    private Integer resumeCountUpperLimit;

    /**
     * 优先级(同一场景下)
     */
    private Integer priority;

    /**
     * 删除标记
     */
    private Integer yn;
}
