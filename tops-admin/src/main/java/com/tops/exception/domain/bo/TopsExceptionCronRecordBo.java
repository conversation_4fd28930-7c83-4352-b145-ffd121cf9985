package com.tops.exception.domain.bo;

import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;

/**
 * 定时任务明细业务对象 tops_exception_cron_record
 *
 * <AUTHOR>
 * @date 2024-09-09
 */

@Data
@EqualsAndHashCode
public class TopsExceptionCronRecordBo extends BaseEntity {

    /**
     * 自增长的主键
     */
    @NotNull(message = "自增长的主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 异常平台id
     */
    @NotBlank(message = "异常平台id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long exceptionId;

    /**
     * 匹配的规则Id
     */
    @NotBlank(message = "匹配的规则Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ruleId;

    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 错误编码
     */
    @NotBlank(message = "错误编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String errorCode;

    /**
     * 事业部编码
     */
    @NotBlank(message = "事业部编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fulfillmentAccountNo;

    /**
     * 仓库编码
     */
    @NotBlank(message = "仓库编码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String warehouseNo;

    /**
     * 下次恢复时间
     */
    @NotNull(message = "下次恢复时间", groups = { AddGroup.class, EditGroup.class })
    private Date nextOperateTime;

    /**
     * 恢复次数上限
     */
    @NotNull(message = "恢复次数上限不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeCountUpperLimit;

    /**
     * 当前恢复次数
     */
    @NotNull(message = "当前恢复次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeCount;

    /**
     * 恢复间隔
     */
    @NotNull(message = "恢复间隔不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeInterval;

    /**
     * 恢复场景
     */
    @NotNull(message = "恢复场景", groups = { AddGroup.class, EditGroup.class })
    private String resumeScene;

    /**
     * 恢复别名
     */
    @NotNull(message = "恢复别名", groups = { AddGroup.class, EditGroup.class })
    private String resumeAlias;

    /**
     * 恢复状态，0表示待恢复，1表示恢复中，2表示恢复成功，3表示恢复失败
     */
    @NotNull(message = "恢复状态", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeStatus;

    /**
     * 恢复来源
     */
    private String resumeSource;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记
     */
    @NotNull(message = "删除标记,不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer yn;
}
