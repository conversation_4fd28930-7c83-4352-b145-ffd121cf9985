package com.tops.exception.domain.dto;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName TopsExceptionCronRecordDTO
 * @Description 用途
 * @date 2024年08月30日 6:18 PM
 */
@Data
public class TopsExceptionCronRecordDTO {
    /**
     * 异常平台id
     */
    private Long exceptionId;

    /**
     * 异常码信息
     */
    private String errorCode;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 规则Id
     */
    private String ruleId;

    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 事业部编码
     */
    private String fulfillmentAccountNo;

    /**
     * 仓库信息
     */
    private String warehouseNo;

    /**
     * 恢复次数上限
     */
    private Integer resumeCountUpperLimit;

    /**
     * 当前恢复次数
     */
    private Integer resumeCount;

    /**
     * 恢复间隔(分钟)
     */
    private Integer resumeInterval;

    /**
     * 恢复来源,记录是否由仓配异常中心恢复
     */
    private String resumeSource;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 恢复状态
     */
    private Integer resumeStatus;

    /**
     * 是否删除0-否 1-是
     */
    private Integer yn;

    //------------------查询字段--------------------
    /**
     * 创建时间
     */
    private Date createTimeStart;

    /**
     * 创建时间
     */
    private Date createTimeEnd;

    /**
     * 恢复状态 0-待恢复 1-恢复中 2-恢复成功 3-恢复失败
     */
    private List<Integer> resumeStatusList;

    /**
     * 下次恢复时间开始时间(精确到分钟)
     */
    private Date nextOperateTimeStart;

    /**
     * 下次恢复时间结束时间(精确到分钟)
     */
    private Date nextOperateTimeEnd;

    /**
     * 开始节点
     */
    private Integer pageStart;

    /**
     * 结束节点
     */
    private Integer pageSize;
}
