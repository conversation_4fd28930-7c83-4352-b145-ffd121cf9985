package com.tops.exception.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 定时任务配置视图对象 tops_exception_config
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@ExcelIgnoreUnannotated
public class TopsExceptionConfigVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ExcelProperty(value = "自增id")
    private Long id;

    /**
     * 业务身份
     */
    @ExcelProperty(value = "业务身份")
    private String businessUnit;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    private String businessType;

    /**
     * 事业部
     */
    @ExcelProperty(value = "事业部")
    private String fulfillmentAccountNo;

    /**
     * 仓编码
     */
    @ExcelProperty(value = "仓编码")
    private String warehouseNo;

    /**
     * 异常码
     */
    @ExcelProperty(value = "异常码")
    private String errorCode;

    /**
     * 首次恢复时间(分钟)
     */
    @ExcelProperty(value = "首次恢复时间(分钟)")
    private Integer firstResumeDelay;

    /**
     * 恢复间隔
     */
    @ExcelProperty(value = "恢复间隔")
    private Integer resumeInterval;

    /**
     * 场景
     */
    @ExcelProperty(value = "场景")
    private String resumeScene;

    /**
     * 恢复别名
     */
    @ExcelProperty(value = "恢复别名")
    private String resumeAlias;

    /**
     * 场景描述
     */
    @ExcelProperty(value = "场景描述")
    private String resumeDesc;

    /**
     * 最大恢复次数
     */
    @ExcelProperty(value = "最大恢复次数")
    private Integer resumeCountUpperLimit;

    /**
     * 最大恢复次数
     */
    @ExcelProperty(value = "优先级")
    private Integer priority;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标记
     */
    @ExcelProperty(value = "删除标记")
    private Integer yn;
}
