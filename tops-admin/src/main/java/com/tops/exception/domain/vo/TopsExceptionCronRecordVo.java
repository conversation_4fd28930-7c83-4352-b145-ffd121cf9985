package com.tops.exception.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 定时任务明细视图对象 tops_exception_cron_record
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Data
@ExcelIgnoreUnannotated
public class TopsExceptionCronRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长的主键
     */
    @ExcelProperty(value = "自增长的主键")
    private Long id;

    /**
     * 异常平台id
     */
    @ExcelProperty(value = "异常平台id")
    private Long exceptionId;

    /**
     * 匹配的规则Id
     */
    @ExcelProperty(value = "匹配的规则Id")
    private Long ruleId;

    /**
     * 业务身份
     */
    @ExcelProperty(value = "业务身份")
    private String businessUnit;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    private String businessType;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 错误编码
     */
    @ExcelProperty(value = "错误编码")
    private String errorCode;

    /**
     * 事业部编码
     */
    @ExcelProperty(value = "事业部编码")
    private String fulfillmentAccountNo;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码")
    private String warehouseNo;

    /**
     * 下次恢复时间
     */
    @ExcelProperty(value = "下次恢复时间")
    private Date nextOperateTime;

    /**
     * 恢复次数上限
     */
    @ExcelProperty(value = "恢复次数上限")
    private Integer resumeCountUpperLimit;

    /**
     * 当前恢复次数
     */
    @ExcelProperty(value = "当前恢复次数")
    private Integer resumeCount;

    /**
     * 恢复间隔
     */
    @ExcelProperty(value = "恢复间隔")
    private Integer resumeInterval;

    /**
     * 恢复状态，0表示待恢复，1表示恢复中，2表示恢复成功，3表示恢复失败
     */
    @ExcelProperty(value = "恢复状态，0表示待恢复，1表示恢复中，2表示恢复成功，3表示恢复失败")
    private Integer resumeStatus;

    /**
     * 恢复来源
     */
    @ExcelProperty(value = "恢复来源")
    private String resumeSource;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标记
     */
    @ExcelProperty(value = "删除标记")
    private Integer yn;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date ts;

}
