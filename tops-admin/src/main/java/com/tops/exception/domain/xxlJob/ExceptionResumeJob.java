package com.tops.exception.domain.xxlJob;

import com.tops.exception.service.TopsExceptionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName ExceptionResumeJob
 * @Description 用于异常恢复
 * @date 2024年09月05日 7:48 PM
 */
@Component
@Slf4j
public class ExceptionResumeJob {
    @Resource
    TopsExceptionService topsExceptionService;

    @XxlJob(value = "resumeExceptionJob")
    public ReturnT<String> resume(String param) {
        log.info("==========resumeStart===========");
        topsExceptionService.resume();
        return ReturnT.SUCCESS;
    }
}
