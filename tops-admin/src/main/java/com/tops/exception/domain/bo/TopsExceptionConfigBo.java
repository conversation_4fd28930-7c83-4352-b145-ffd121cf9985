package com.tops.exception.domain.bo;

import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import com.tops.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 定时任务配置业务对象 tops_exception_config
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Data
@EqualsAndHashCode
public class TopsExceptionConfigBo extends BaseEntity {

    /**
     * 自增id
     */
    @NotNull(message = "自增id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessType;

    /**
     * 事业部
     */
    private String fulfillmentAccountNo;

    /**
     * 仓编码
     */
    private String warehouseNo;

    /**
     * 异常码
     */
    @NotBlank(message = "异常码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String errorCode;

    /**
     * 首次恢复时间(分钟)
     */
    @NotNull(message = "首次恢复时间(分钟)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer firstResumeDelay;

    /**
     * 恢复间隔
     */
    @NotNull(message = "恢复间隔不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeInterval;

    /**
     * 场景
     */
    @NotBlank(message = "场景不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resumeScene;

    /**
     * 恢复别名
     */
    @NotBlank(message = "恢复别名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resumeAlias;

    /**
     * 场景描述
     */
    @NotBlank(message = "场景描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resumeDesc;

    /**
     * 最大恢复次数
     */
    @NotNull(message = "最大恢复次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer resumeCountUpperLimit;

    /**
     * 优先级(同一场景下)
     */
    @NotNull(message = "同场景下优先级不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer priority;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记
     */
    private Integer yn;
}
