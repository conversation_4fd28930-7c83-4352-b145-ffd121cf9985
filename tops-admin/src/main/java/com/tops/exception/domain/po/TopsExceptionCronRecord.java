package com.tops.exception.domain.po;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName TopsExcetionRecord
 * @Description 异常平台定时任务恢复记录
 * @date 2024年08月29日 9:33 PM
 */
@Data
public class TopsExceptionCronRecord {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 异常平台id
     */
    private Long exceptionId;

    /**
     * 匹配的规则id
     */
    private Long ruleId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 异常码信息
     */
    private String errorCode;

    /**
     * 事业部编码
     */
    private String fulfillmentAccountNo;

    /**
     * 仓库信息
     */
    private String warehouseNo;

    /**
     * 下次恢复时间(精确到分钟)
     */
    private Date nextOperateTime;

    /**
     * 恢复次数上限
     */
    private Integer resumeCountUpperLimit;

    /**
     * 当前恢复次数
     */
    private Integer resumeCount;

    /**
     * 恢复间隔(分钟)
     */
    private Integer resumeInterval;

    /**
     * 恢复状态 0-待恢复 1-恢复成功 2-恢复中 3-恢复失败
     */
    private Integer resumeStatus;


    /**
     * 恢复来源,记录是否由仓配异常中心恢复
     */
    private String resumeSource;

    /**
     * 恢复描述信息
     */
    private String resumeDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 删除标记 0-删除 1-存在
     */
    private Integer yn;

    /**
     * 时间戳, 消费mq更新数据库需要考虑逆序情况,
     * 使用异常平台更新时间，作为乐观锁
     */
    private Date ts;
}
