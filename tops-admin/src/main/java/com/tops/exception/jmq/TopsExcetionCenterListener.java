package com.tops.exception.jmq;

import com.jd.fastjson.JSON;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.tops.exception.domain.dto.ExceptionCenterMessageDTO;
import com.tops.exception.service.TopsExceptionService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TopsExcetionCenterListener
 * @Description 用途
 * @date 2024年09月03日 2:20 PM
 */
@Slf4j
@Service("exceptionCenterListener")
public class TopsExcetionCenterListener implements MessageListener {
    @Resource
    TopsExceptionService topsExceptionService;

    /**
     * 监听异常平台信息
     * https://joyspace.jd.com/pages/8XakNIiSEIELnNGbBTR6
     * @param list
     * @throws Exception
     */
    @Override
    public void onMessage(List<Message> list) throws Exception {
        try {
            for(Message message : list) {
                ExceptionCenterMessageDTO dto = JSON.parseObject(message.getText(), ExceptionCenterMessageDTO.class);
                topsExceptionService.upsertExceptionRecord(dto);
            }
        } catch (Exception ex) {
            log.error("消费mq失败, ex=", ex);
            throw ex;
        }
    }
}
