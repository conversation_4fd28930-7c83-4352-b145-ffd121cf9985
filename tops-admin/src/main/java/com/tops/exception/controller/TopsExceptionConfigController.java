package com.tops.exception.controller;

import com.tops.common.core.domain.model.LoginUser;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.helper.LoginHelper;
import com.tops.exception.domain.bo.TopsExceptionConfigBo;
import com.tops.exception.domain.vo.TopsExceptionConfigVo;
import com.tops.exception.service.ITopsExceptionConfigService;
import com.tops.exception.service.TopsExceptionService;
import java.util.Arrays;

import java.util.List;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.annotation.Log;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.poi.ExcelUtil;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/exceptionConfig")
public class TopsExceptionConfigController extends BaseController {

    @Resource
    private ITopsExceptionConfigService iTopsExceptionConfigService;

    /**
     * 查询定时任务配置列表
     */
    @SaCheckPermission("system:exceptionConfig:list")
    @GetMapping("/list")
    public TableDataInfo<TopsExceptionConfigVo> list(TopsExceptionConfigBo bo, PageQuery pageQuery) {
        return iTopsExceptionConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出定时任务配置列表
     */
    @SaCheckPermission("system:exceptionConfig:export")
    @Log(title = "定时任务配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsExceptionConfigBo bo, HttpServletResponse response) {
        List<TopsExceptionConfigVo> list = iTopsExceptionConfigService.queryVoList(bo);
        ExcelUtil.exportExcel(list, "定时任务配置", TopsExceptionConfigVo.class, response);
    }

    /**
     * 获取定时任务配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:exceptionConfig:query")
    @GetMapping("/{id}")
    public R<TopsExceptionConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsExceptionConfigService.queryById(id));
    }

    /**
     * 新增定时任务配置
     */
    @SaCheckPermission("system:exceptionConfig:add")
    @Log(title = "定时任务配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsExceptionConfigBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setCreateBy(loginUser.getUsername());
        return toAjax(iTopsExceptionConfigService.insertByBo(bo));
    }

    /**
     * 修改定时任务配置
     */
    @SaCheckPermission("system:exceptionConfig:edit")
    @Log(title = "定时任务配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsExceptionConfigBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        bo.setUpdateBy(loginUser.getUsername());
        return toAjax(iTopsExceptionConfigService.updateByBo(bo));
    }

    /**
     * 删除定时任务配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:exceptionConfig:remove")
    @Log(title = "定时任务配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsExceptionConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
