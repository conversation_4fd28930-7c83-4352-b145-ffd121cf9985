package com.tops.exception.controller;

import com.tops.exception.domain.bo.TopsExceptionCronRecordBo;
import com.tops.exception.domain.vo.TopsExceptionCronRecordVo;
import com.tops.exception.service.ITopsExceptionCronRecordService;
import java.util.List;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.annotation.Log;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.core.validate.QueryGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.common.core.page.TableDataInfo;

/**
 * 定时任务明细
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/exceptionCronRecord")
public class TopsExceptionCronRecordController extends BaseController {

    @Resource
    private ITopsExceptionCronRecordService iTopsExceptionCronRecordService;

    /**
     * 查询定时任务明细列表
     */
    @SaCheckPermission("system:exceptionCronRecord:list")
    @GetMapping("/list")
    public TableDataInfo<TopsExceptionCronRecordVo> list(TopsExceptionCronRecordBo bo, PageQuery pageQuery) {
        return iTopsExceptionCronRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出定时任务明细列表
     */
    @SaCheckPermission("system:exceptionCronRecord:export")
    @Log(title = "定时任务明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsExceptionCronRecordBo bo, HttpServletResponse response) {
        List<TopsExceptionCronRecordVo> list = iTopsExceptionCronRecordService.queryVoList(bo);
        ExcelUtil.exportExcel(list, "定时任务明细", TopsExceptionCronRecordVo.class, response);
    }

    /**
     * 获取定时任务明细详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:exceptionCronRecord:query")
    @GetMapping("/{id}")
    public R<TopsExceptionCronRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsExceptionCronRecordService.queryById(id));
    }

    /**
     * 新增定时任务明细
     */
    @SaCheckPermission("system:exceptionCronRecord:add")
    @Log(title = "定时任务明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsExceptionCronRecordBo bo) {
        return toAjax(iTopsExceptionCronRecordService.insertByBo(bo));
    }

    /**
     * 修改定时任务明细
     */
    @SaCheckPermission("system:exceptionCronRecord:edit")
    @Log(title = "定时任务明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsExceptionCronRecordBo bo) {
        return toAjax(iTopsExceptionCronRecordService.updateByBo(bo));
    }

    /**
     * 删除定时任务明细
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:exceptionCronRecord:remove")
    @Log(title = "定时任务明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsExceptionCronRecordService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
