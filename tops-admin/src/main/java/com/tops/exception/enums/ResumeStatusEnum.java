package com.tops.exception.enums;

import java.util.HashMap;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName ResolveStatusEnum
 * @Description 用途
 * @date 2024年09月03日 2:47 PM
 */
@Getter
public enum ResumeStatusEnum {
    NOT_RESUMED(0, "待恢复"),
    RESUMED(1, "已恢复"),
    RESUMING(2, "恢复中"),
    RESUME_FAILED(3, "恢复失败"),
    ;

    /**
     * 编码
     */
    private Integer code;

    /**
     * 描述
     */
    private String desc;

    ResumeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean canResume(Integer resolveStatus) {
        return NOT_RESUMED.getCode().equals(resolveStatus) || RESUMING.getCode().equals(resolveStatus);
    }

    private static final HashMap<Integer, String> cacheMap = new HashMap<>();

    static {
        for(ResumeStatusEnum resolveStatusEnum : ResumeStatusEnum.values()) {
            cacheMap.put(resolveStatusEnum.code, resolveStatusEnum.getDesc());
        }
    }

    public static String getDesc(Integer code) {
        return cacheMap.get(code);
    }
}
