package com.tops.exception.enums;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName ExceptionCodeEnum
 * @Description 用途
 * @date 2024年09月05日 8:45 PM
 */
@Getter
public enum ExceptionCodeEnum {
    RE_OCCUPY("8050260", "100", "库存不足"),
    ;

    /**
     * 异常平台异常码
     */
    private String errCode;

    /**
     * 订单中心操作码
     */
    private String operateCode;

    /**
     * 描述
     * @param errCode
     * @param operateCode
     */
    private String desc;

    private ExceptionCodeEnum(String errCode, String operateCode, String desc) {
        this.errCode = errCode;
        this.operateCode = operateCode;
        this.desc = desc;
    }

    private static final Map<String, String> codeMap = new HashMap<>();

    static {
        for(ExceptionCodeEnum exceptionCodeEnum : ExceptionCodeEnum.values()) {
            codeMap.put(exceptionCodeEnum.errCode, exceptionCodeEnum.operateCode);
        }
    }

    public static String getOperateCode(String errCode) {
        return codeMap.get(errCode);
    }
}
