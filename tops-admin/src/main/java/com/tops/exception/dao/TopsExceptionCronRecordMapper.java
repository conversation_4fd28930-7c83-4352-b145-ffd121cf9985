package com.tops.exception.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.common.core.mapper.BaseMapperPlus;
import com.tops.exception.domain.dto.TopsExceptionCronRecordDTO;
import com.tops.exception.domain.po.TopsExceptionCronRecord;
import com.tops.exception.domain.vo.TopsExceptionCronRecordVo;
import java.util.List;

/**
 * 定时任务明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@DS("tidb")
public interface TopsExceptionCronRecordMapper extends BaseMapperPlus<TopsExceptionCronRecordMapper, TopsExceptionCronRecord, TopsExceptionCronRecordVo> {
    /**
     * 按条件检索
     * @return
     */
    List<TopsExceptionCronRecord> selectByCriteria(TopsExceptionCronRecordDTO dto);

    /**
     * 按乐观锁更新
     */
    int updateSelective(TopsExceptionCronRecord record);
}
