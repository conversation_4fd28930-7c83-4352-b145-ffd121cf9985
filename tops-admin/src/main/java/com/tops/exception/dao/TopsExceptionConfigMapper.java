package com.tops.exception.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.common.core.mapper.BaseMapperPlus;
import com.tops.exception.domain.po.TopsExceptionConfig;
import com.tops.exception.domain.vo.TopsExceptionConfigVo;

/**
 * 异常平台查询配置信息
 * <AUTHOR>
 * @date 2024-09-02 16:43
 */
@DS("tidb")
public interface TopsExceptionConfigMapper extends BaseMapperPlus<TopsExceptionConfigMapper, TopsExceptionConfig, TopsExceptionConfigVo> {

}
