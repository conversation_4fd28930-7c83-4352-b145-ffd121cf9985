package com.tops.exception.service.impl;

import cn.jdl.oms.search.dto.Order;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.jd.eclp.core.ApiRequest;
import com.jd.eclp.core.ApiResponse;
import com.jd.eclp.exception.api.dto.ResumeOrderParam;
import com.jd.eclp.exception.api.handler.ResumeOrderHandler;
import com.jd.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsProfilerUtils;
import com.tops.common.utils.redis.RedisUtils;
import com.tops.exception.domain.bo.TopsExceptionConfigBo;
import com.tops.exception.domain.bo.TopsExceptionCronRecordBo;
import com.tops.exception.domain.dto.ExceptionCenterMessageDTO;
import com.tops.exception.domain.po.TopsExceptionConfig;
import com.tops.exception.domain.po.TopsExceptionCronRecord;
import com.tops.exception.enums.ResumeStatusEnum;
import com.tops.exception.service.ITopsExceptionConfigService;
import com.tops.exception.service.ITopsExceptionCronRecordService;
import com.tops.exception.service.TopsExceptionService;
import com.tops.order.adapter.TopsQueryOrderDetailAdapter;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TopsExceptionService
 * @Description 用途
 * @date 2024年09月03日 2:32 PM
 */
@Slf4j
@Service("topsExceptionService")
public class TopsExceptionServiceImpl implements TopsExceptionService {
    @Resource
    TopsQueryOrderDetailAdapter topsQueryOrderDetailAdapter;

    @Resource
    ITopsExceptionCronRecordService iTopsExceptionCronRecordService;

    @Resource
    ITopsExceptionConfigService iTopsExceptionConfigService;

    @Resource
    ResumeOrderHandler resumeOrderHandler;

    /**
     * 缓存数据库配置的异常码id
     */
    private LoadingCache<String, Boolean> exceptionCodeCache;

    private static final String RECORD_ANTI_LOCK_KEY = "EXCEPTION_RECORD_%s";

    //定义一个线程池，固定队列100，核心线程数为20
    ThreadPoolExecutor executor = new ThreadPoolExecutor(
        20,
        20,
        0,
        TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<>(100)
    );

    @PostConstruct
    public void initCache() {
        exceptionCodeCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Boolean>() {
                @Override
                public Boolean load(String exceptionCode) throws Exception {
                    log.info("[生成定时任务记录] 获取异常缓存信息:{}", exceptionCode);
                    TopsExceptionConfigBo query = new TopsExceptionConfigBo();
                    query.setErrorCode(exceptionCode);
                    query.setYn(1);
                    return iTopsExceptionConfigService.queryPoList(query).size() > 0;
                }
            });
    }

    @Override
    @DSTransactional
    public void upsertExceptionRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO) throws Exception {
        log.info("[生成异常记录] 请求信息为:{}", JSON.toJSONString(exceptionCenterMessageDTO));
        String antiRelockedKey = String.format(RECORD_ANTI_LOCK_KEY, exceptionCenterMessageDTO.getOrderNo());
        try {
            if (!RedisUtils.isExistsObject(antiRelockedKey)) {
                RedisUtils.setCacheObject(antiRelockedKey, true, Duration.ofSeconds(10));
            } else {
                log.info("[生成异常记录] 并发冲突, exceptionCenterMessageDTO:{}", JSON.toJSONString(exceptionCenterMessageDTO));
                throw new InternalFailureException("并发冲突: orderNo:" + exceptionCenterMessageDTO.getOrderNo());
            }

            // 判断当前编码是否已配置
            if (!exceptionCodeCache.get(exceptionCenterMessageDTO.getErrCode())) {
                log.info("[生成异常记录] 过滤未配置的异常code:{}", exceptionCenterMessageDTO.getErrCode());
                return;
            }

            TopsExceptionCronRecordBo bo = new TopsExceptionCronRecordBo();
            bo.setOrderNo(exceptionCenterMessageDTO.getOrderNo());
            bo.setErrorCode(exceptionCenterMessageDTO.getErrCode());
            bo.setYn(1);

            //查询是否有对应的异常记录, 如果么有记录则插入，有记录则更新
            List<TopsExceptionCronRecord> records = iTopsExceptionCronRecordService.queryList(bo);
            if (CollectionUtils.isEmpty(records)) {
                insertRecord(exceptionCenterMessageDTO);
            } else {
                if(ResumeStatusEnum.RESUMED.getCode().equals(exceptionCenterMessageDTO.getResolveStatus())) {
                    updateRecord(exceptionCenterMessageDTO, records);
                }
            }
        } catch (DependencyFailureException ex) {
            throw ex;
        } catch (InternalFailureException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("[生成定时任务记录异常]", ex);
            throw new InternalFailureException(ex.getMessage());
        } finally {
            RedisUtils.deleteObject(antiRelockedKey);
        }
    }

    @Override
    public void resume() {
        //1 搜索异常恢复时间小与当前, 状态为恢复中的定时任务
        Date queryTime = new Date();
        List<TopsExceptionCronRecord> records = iTopsExceptionCronRecordService.queryUnresolvedException(queryTime);

        for(TopsExceptionCronRecord record : records) {
            CompletableFuture.runAsync(() -> {
                resumeException(record, queryTime);
            }, executor);
        }
    }


    /**
     * 恢复单个任务
     */
    private void resumeException(TopsExceptionCronRecord record, Date queryTime) {
        //组装参数
        String traceId = UUID.randomUUID().toString();
        ResumeOrderParam resumeOrderParam = new ResumeOrderParam();
        resumeOrderParam.setOrderNo(record.getOrderNo());
        resumeOrderParam.setErrCode(Integer.valueOf(record.getErrorCode()));
        resumeOrderParam.setOperateUser("jdl-trade-ops");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ruleId", record.getRuleId());
        resumeOrderParam.setDataMessage(JSON.toJSONString(jsonObject));
        ApiRequest<ResumeOrderParam> apiRequest = new ApiRequest<>();
        apiRequest.setData(resumeOrderParam);

        try {
            ApiResponse<Void> response = null;
            try {
                log.info("[异常平台执行重处理请求接口] 重处理接口, resumeOrderParam:{}, traceId:{}", JSONObject.toJSONString(resumeOrderParam), traceId);
                response = resumeOrderHandler.doResume(apiRequest);
                log.info("[异常平台执行重处理请求接口] 重处理接口, response:{}, traceId:{}", JSONObject.toJSONString(response), traceId);
            } catch (Exception ex) {
                log.error("[异常平台执行重处理请求失败], traceId:{}, ex", traceId, ex);
            }

            record.setResumeCount(record.getResumeCount() + 1);
            //处理成功更新
            if(response != null && response.isSuccess()) {
                log.info("重受理执行成功,设置订单状态为已恢复, traceId:{}, orderNo:{}, queryTime:{}", traceId, record.getOrderNo(), queryTime);
                record.setResumeSource("jdl-trade-ops");
                record.setResumeStatus(ResumeStatusEnum.RESUMED.getCode());
            } else {
                log.info("重受理执行失败,设置订单状态为恢复中, traceId:{}, 订单号:{}, queryTime:{}", traceId, record.getOrderNo(), queryTime);
                if(Objects.equals(record.getResumeCount(), record.getResumeCountUpperLimit())) {
                    record.setResumeStatus(ResumeStatusEnum.RESUME_FAILED.getCode());
                } else {
                    record.setNextOperateTime(DateUtils.addMinutes(record.getNextOperateTime(), record.getResumeInterval()));
                }
            }

            record.setTs(queryTime);
            iTopsExceptionCronRecordService.updateSelectiveByTs(record);
        } catch (Exception ex) {
            log.error("[异常平台执行重处理请求失败],traceId:{}, ex", traceId, ex);
        }
    }


    /**
     * 根据异常平台信息更新数据
     * @param exceptionCenterMessageDTO
     * @throws DependencyFailureException
     * @throws InternalFailureException
     */
    public void insertRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO) throws DependencyFailureException, InternalFailureException {
        CallerInfo callerInfo = TopsProfilerUtils.registerInfo("insertExceptionRecord", "jdl-ops");
        try {
            List<TopsExceptionCronRecord> records = generateTopsExceptionRecord(exceptionCenterMessageDTO);
            records.forEach(topsExceptionCronRecord -> {
                iTopsExceptionCronRecordService.insertSelective(topsExceptionCronRecord);
            });
        } catch (DependencyFailureException dependencyFailureException) {
            Profiler.functionError(callerInfo);
            throw dependencyFailureException;
        } catch (InternalFailureException internalFailureException) {
            Profiler.functionError(callerInfo);
            Profiler.businessAlarm("insert exception record error", internalFailureException.getMessage());
            throw internalFailureException;
        } catch (Exception exception) {
            throw new InternalFailureException(exception.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 来自异常平台修改只更新状态，不更新其他信息
     * 未来如果有搬仓计划等更新需求需要重新查详情
     * @param exceptionCenterMessageDTO
     * @param records
     * @throws InternalFailureException
     */
    public void updateRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO, List<TopsExceptionCronRecord> records) throws InternalFailureException
    {
        CallerInfo callerInfo = TopsProfilerUtils.registerInfo("updateRecord", "jdl-ops");
        try {
            for(TopsExceptionCronRecord record : records) {
                //从异常平台来的记录只关注状态
                TopsExceptionCronRecord newRecord = convertTopsExceptionCronRecord(exceptionCenterMessageDTO, record);
                iTopsExceptionCronRecordService.updateSelective(newRecord);
            }
        } catch (Exception exception) {
            throw new InternalFailureException(exception.getMessage());
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private List<TopsExceptionCronRecord> generateTopsExceptionRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO) throws DependencyFailureException, InternalFailureException {
        Order order = topsQueryOrderDetailAdapter.queryOrderDetail(exceptionCenterMessageDTO.getOrderNo());
        List<TopsExceptionCronRecord> records = new ArrayList<>();

        if(order == null) {
            String msg = "订单不存在orderNo:" + exceptionCenterMessageDTO.getOrderNo();
            Profiler.businessAlarm("exception.record.order.not.exist", msg);
            throw new DependencyFailureException(msg);
        }

        TopsExceptionConfigBo query = new TopsExceptionConfigBo();
        query.setErrorCode(exceptionCenterMessageDTO.getErrCode());
        query.setYn(1);

        List<TopsExceptionConfig> configs = iTopsExceptionConfigService.queryPoList(query);
        // 根据场景进行分组, 相同场景的配置按照优先级排序
        Map<String, List<TopsExceptionConfig>> resumeSenceMap =
            configs.stream().collect(Collectors.groupingBy(TopsExceptionConfig::getResumeScene));
        for (String resumeScene : resumeSenceMap.keySet()) {
            //按优先级降序
            List<TopsExceptionConfig> subList = resumeSenceMap.get(resumeScene).stream()
                .sorted(Comparator.comparing(TopsExceptionConfig::getPriority).reversed())
                .collect(Collectors.toList());
            for (TopsExceptionConfig config : subList) {
                if(matchConfig(config, order)) {
                    if(config.getResumeCountUpperLimit() > 0) {
                        records.add(convertTopsExceptionCronRecord(exceptionCenterMessageDTO, order, config));
                        break;
                    } else {
                        //如中铁项目，挂库存不足异常后不想自动恢复，则不插入恢复记录
                        log.info("过滤不恢复的事业部 {}", JSON.toJSONString(config));
                        break;
                    }
                }
            }
        }

        //生成异常记录
        return records;
    }

    /**
     * 转换方法
     * 考虑到消费mq可能有逆序的情况，如果先收到的信息为已完成，也插入一条记录。
     * @return
     */
    private TopsExceptionCronRecord convertTopsExceptionCronRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO, Order order,
                                                                   TopsExceptionConfig config) {
        TopsExceptionCronRecord topsExceptionCronRecord = new TopsExceptionCronRecord();
        topsExceptionCronRecord.setExceptionId(exceptionCenterMessageDTO.getId());
        topsExceptionCronRecord.setBusinessUnit(order.getBusinessIdentity().getBusinessUnit());
        topsExceptionCronRecord.setBusinessType(order.getBusinessIdentity().getBusinessType());
        topsExceptionCronRecord.setExceptionId(exceptionCenterMessageDTO.getId());
        topsExceptionCronRecord.setRuleId(config.getId());
        topsExceptionCronRecord.setErrorCode(exceptionCenterMessageDTO.getErrCode());
        topsExceptionCronRecord.setOrderNo(exceptionCenterMessageDTO.getOrderNo());
        topsExceptionCronRecord.setFulfillmentAccountNo(order.getCustomerInfo().getAccountNo());
        topsExceptionCronRecord.setWarehouseNo(order.getConsignorInfo().getCustomerWarehouse().getWarehouseNo());
        topsExceptionCronRecord.setNextOperateTime(DateUtils.addMinutes(new Date(), config.getFirstResumeDelay()));
        topsExceptionCronRecord.setResumeCountUpperLimit(config.getResumeCountUpperLimit());
        topsExceptionCronRecord.setResumeStatus(exceptionCenterMessageDTO.getResolveStatus());
        topsExceptionCronRecord.setResumeInterval(config.getResumeInterval());
        topsExceptionCronRecord.setCreateBy(exceptionCenterMessageDTO.getCreateUser());
        topsExceptionCronRecord.setUpdateBy(exceptionCenterMessageDTO.getUpdateUser());
        if(StringUtils.isNotEmpty(exceptionCenterMessageDTO.getUpdateTimeStr())) {
            topsExceptionCronRecord.setTs(DateUtils.parseDate(exceptionCenterMessageDTO.getUpdateTimeStr()));
        } else if(StringUtils.isNotEmpty(exceptionCenterMessageDTO.getCreateTimeStr())) {
            topsExceptionCronRecord.setTs(DateUtils.parseDate(exceptionCenterMessageDTO.getCreateTimeStr()));
        } else {
            topsExceptionCronRecord.setTs(new Date());
        }

        return topsExceptionCronRecord;
    }

    /**
     * 转换方法
     * 对于异常恢复
     * @return
     */
    private TopsExceptionCronRecord convertTopsExceptionCronRecord(ExceptionCenterMessageDTO exceptionCenterMessageDTO, TopsExceptionCronRecord record) {
        record.setResumeStatus(exceptionCenterMessageDTO.getResolveStatus());

        //如果异常从 恢复-->重新挂异常，增加报警
        if(ResumeStatusEnum.RESUMED.getCode().equals(record.getResumeStatus())
             && ResumeStatusEnum.NOT_RESUMED.equals(exceptionCenterMessageDTO.getResolveStatus())) {
            log.error("异常恢复后重新挂异常，orderNo:{}", exceptionCenterMessageDTO.getOrderNo());
        }

        record.setResumeStatus(exceptionCenterMessageDTO.getResolveStatus());
        //乐观锁更新重试记录
        if(StringUtils.isNotEmpty(exceptionCenterMessageDTO.getUpdateTimeStr())) {
            record.setTs(DateUtils.parseDate(exceptionCenterMessageDTO.getUpdateTimeStr()));
        } else {
            record.setTs(new Date());
        }

        return record;
    }

    /**
     * 匹配配置
     * 目前匹配的维度是 事业部、业务类型、业务身份、仓编码
     * @param config
     * @param order
     * @return
     */
    private boolean matchConfig(TopsExceptionConfig config, Order order) {
        if (StringUtils.isNotEmpty(config.getBusinessUnit()) &&
            !config.getBusinessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {;
            return false;
        }

        if (StringUtils.isNotEmpty(config.getBusinessType()) &&
            !config.getBusinessType().equals(order.getBusinessIdentity().getBusinessType())) {
            return false;
        }

        if (StringUtils.isNotEmpty(config.getWarehouseNo()) &&
            !config.getWarehouseNo().equals(order.getConsignorInfo().getCustomerWarehouse().getWarehouseNo())) {
            return false;
        }

        if (StringUtils.isNotEmpty(config.getFulfillmentAccountNo()) &&
            !config.getFulfillmentAccountNo().equals(order.getCustomerInfo().getAccountNo())) {
            return false;
        }

        return true;
    }
}
