package com.tops.exception.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.utils.StringUtils;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tops.exception.dao.TopsExceptionCronRecordMapper;
import com.tops.exception.domain.bo.TopsExceptionCronRecordBo;
import com.tops.exception.domain.dto.TopsExceptionCronRecordDTO;
import com.tops.exception.domain.po.TopsExceptionCronRecord;
import com.tops.exception.domain.vo.TopsExceptionCronRecordVo;
import com.tops.exception.enums.ResumeStatusEnum;
import com.tops.exception.service.ITopsExceptionCronRecordService;
import java.util.Arrays;
import java.util.Date;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 定时任务明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@RequiredArgsConstructor
@Service
public class TopsExceptionCronRecordServiceImpl implements ITopsExceptionCronRecordService {

    @Resource
    private final TopsExceptionCronRecordMapper baseMapper;

    /**
     * 查询定时任务明细
     */
    @Override
    public TopsExceptionCronRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询定时任务明细列表
     */
    @Override
    public TableDataInfo<TopsExceptionCronRecordVo> queryPageList(TopsExceptionCronRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopsExceptionCronRecord> lqw = buildQueryWrapper(bo);
        Page<TopsExceptionCronRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<TopsExceptionCronRecordVo> queryVoList(TopsExceptionCronRecordBo bo) {
        LambdaQueryWrapper<TopsExceptionCronRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询定时任务明细列表
     */
    @Override
    public List<TopsExceptionCronRecord> queryList(TopsExceptionCronRecordBo bo) {
        LambdaQueryWrapper<TopsExceptionCronRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<TopsExceptionCronRecord> buildQueryWrapper(TopsExceptionCronRecordBo bo) {
        LambdaQueryWrapper<TopsExceptionCronRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getExceptionId() != null, TopsExceptionCronRecord::getExceptionId, bo.getExceptionId());
        lqw.eq(StringUtils.isNotBlank(bo.getRuleId()), TopsExceptionCronRecord::getRuleId, bo.getRuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessUnit()), TopsExceptionCronRecord::getBusinessUnit, bo.getBusinessUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), TopsExceptionCronRecord::getBusinessType, bo.getBusinessType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), TopsExceptionCronRecord::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getErrorCode()), TopsExceptionCronRecord::getErrorCode, bo.getErrorCode());
        lqw.eq(StringUtils.isNotBlank(bo.getFulfillmentAccountNo()), TopsExceptionCronRecord::getFulfillmentAccountNo, bo.getFulfillmentAccountNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseNo()), TopsExceptionCronRecord::getWarehouseNo, bo.getWarehouseNo());
        lqw.eq(bo.getNextOperateTime() != null, TopsExceptionCronRecord::getNextOperateTime, bo.getNextOperateTime());
        lqw.eq(bo.getResumeCountUpperLimit() != null, TopsExceptionCronRecord::getResumeCountUpperLimit, bo.getResumeCountUpperLimit());
        lqw.eq(bo.getResumeCount() != null, TopsExceptionCronRecord::getResumeCount, bo.getResumeCount());
        lqw.eq(bo.getResumeInterval() != null, TopsExceptionCronRecord::getResumeInterval, bo.getResumeInterval());
        lqw.eq(bo.getResumeStatus() != null, TopsExceptionCronRecord::getResumeStatus, bo.getResumeStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getResumeSource()), TopsExceptionCronRecord::getResumeSource, bo.getResumeSource());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateBy()), TopsExceptionCronRecord::getCreateBy, bo.getCreateBy());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateBy()), TopsExceptionCronRecord::getUpdateBy, bo.getUpdateBy());
        lqw.eq(bo.getYn() != null, TopsExceptionCronRecord::getYn, bo.getYn());
        return lqw;
    }

    /**
     * 新增定时任务明细
     */
    @Override
    public Boolean insertByBo(TopsExceptionCronRecordBo bo) {
        TopsExceptionCronRecord add = BeanUtil.toBean(bo, TopsExceptionCronRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增定时任务明细
     */
    @Override
    public Boolean insertSelective(TopsExceptionCronRecord record) {
        validEntityBeforeSave(record);
        return baseMapper.insert(record) > 0;
    }

    /**
     * 修改定时任务明细
     */
    @Override
    public Boolean updateByBo(TopsExceptionCronRecordBo bo) {
        TopsExceptionCronRecord update = BeanUtil.toBean(bo, TopsExceptionCronRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 修改定时任务明细
     */
    @Override
    public Boolean updateSelective(TopsExceptionCronRecord topsExceptionCronRecord) {
        validEntityBeforeSave(topsExceptionCronRecord);
        return baseMapper.updateById(topsExceptionCronRecord) > 0;
    }

    /**
     * 修改名
     */
    public Boolean updateSelectiveByTs(TopsExceptionCronRecord topsExceptionCronRecord) {
        return baseMapper.updateSelective(topsExceptionCronRecord) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsExceptionCronRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除定时任务明细
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<TopsExceptionCronRecord> queryUnresolvedException(Date queryTime) {
        TopsExceptionCronRecordDTO query = new TopsExceptionCronRecordDTO();
        query.setNextOperateTimeEnd(queryTime);
        query.setResumeStatusList(Arrays.asList(ResumeStatusEnum.NOT_RESUMED.getCode(), ResumeStatusEnum.RESUMING.getCode()));
        query.setPageStart(0);
        query.setPageSize(100);
        return baseMapper.selectByCriteria(query);
    }
}
