package com.tops.exception.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.tops.common.utils.StringUtils;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tops.exception.dao.TopsExceptionConfigMapper;
import com.tops.exception.domain.bo.TopsExceptionConfigBo;
import com.tops.exception.domain.po.TopsExceptionConfig;
import com.tops.exception.domain.vo.TopsExceptionConfigVo;
import com.tops.exception.service.ITopsExceptionConfigService;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collection;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 定时任务配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TopsExceptionConfigServiceImpl implements ITopsExceptionConfigService {

    @Resource
    private TopsExceptionConfigMapper topsExceptionConfigMapper;

    /**
     * 缓存数据库配置的异常码id
     */
    private LoadingCache<Long, String> exceptionResumeAliasCache;

    /**
     * 根据ruleId找到对应的处理别名
     */
    @PostConstruct
    public void initCache() {
        exceptionResumeAliasCache = CacheBuilder.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, String>() {
                    @Override
                    public String load(Long ruleId) throws Exception {
                        log.info("[生成定时任务记录] 获取异常缓存信息:{}", ruleId);
                        TopsExceptionConfigVo query = queryById(ruleId);
                        return query != null ? query.getResumeAlias() : "";
                    }
                });
    }

    /**
     * 查询定时任务配置
     */
    @Override
    public TopsExceptionConfigVo queryById(Long id){
        return topsExceptionConfigMapper.selectVoById(id);
    }

    /**
     * 查询定时任务配置列表
     */
    @Override
    public TableDataInfo<TopsExceptionConfigVo> queryPageList(TopsExceptionConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopsExceptionConfig> lqw = buildQueryWrapper(bo);
        Page<TopsExceptionConfigVo> result = topsExceptionConfigMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询定时任务配置列表
     */
    @Override
    public List<TopsExceptionConfigVo> queryVoList(TopsExceptionConfigBo bo) {
        LambdaQueryWrapper<TopsExceptionConfig> lqw = buildQueryWrapper(bo);
        return topsExceptionConfigMapper.selectVoList(lqw);
    }

    /**
     * 查询定时任务配置列表
     */
    @Override
    public List<TopsExceptionConfig> queryPoList(TopsExceptionConfigBo bo) {
        LambdaQueryWrapper<TopsExceptionConfig> lqw = buildQueryWrapper(bo);
        return topsExceptionConfigMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<TopsExceptionConfig> buildQueryWrapper(TopsExceptionConfigBo bo) {
        LambdaQueryWrapper<TopsExceptionConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessUnit()), TopsExceptionConfig::getBusinessUnit, bo.getBusinessUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), TopsExceptionConfig::getBusinessType, bo.getBusinessType());
        lqw.eq(StringUtils.isNotBlank(bo.getFulfillmentAccountNo()), TopsExceptionConfig::getFulfillmentAccountNo, bo.getFulfillmentAccountNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWarehouseNo()), TopsExceptionConfig::getWarehouseNo, bo.getWarehouseNo());
        lqw.eq(StringUtils.isNotBlank(bo.getErrorCode()), TopsExceptionConfig::getErrorCode, bo.getErrorCode());
        lqw.eq(bo.getFirstResumeDelay() != null, TopsExceptionConfig::getFirstResumeDelay, bo.getFirstResumeDelay());
        lqw.eq(bo.getResumeInterval() != null, TopsExceptionConfig::getResumeInterval, bo.getResumeInterval());
        lqw.eq(bo.getResumeCountUpperLimit() != null, TopsExceptionConfig::getResumeCountUpperLimit, bo.getResumeCountUpperLimit());
        lqw.eq(bo.getPriority() != null, TopsExceptionConfig::getPriority, bo.getPriority());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateBy()), TopsExceptionConfig::getCreateBy, bo.getCreateBy());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdateBy()), TopsExceptionConfig::getUpdateBy, bo.getUpdateBy());
        lqw.eq(bo.getYn() != null, TopsExceptionConfig::getYn, bo.getYn());
        return lqw;
    }

    /**
     * 新增定时任务配置
     */
    @Override
    public Boolean insertByBo(TopsExceptionConfigBo bo) {
        TopsExceptionConfig add = BeanUtil.toBean(bo, TopsExceptionConfig.class);
        validEntityBeforeSave(add);
        boolean flag = topsExceptionConfigMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改定时任务配置
     */
    @Override
    public Boolean updateByBo(TopsExceptionConfigBo bo) {
        TopsExceptionConfig update = BeanUtil.toBean(bo, TopsExceptionConfig.class);
        validEntityBeforeSave(update);
        return topsExceptionConfigMapper.updateById(update) > 0;
    }

    /**
     * 获取所有
     * @param entity
     */
    private void getValidEntityBeforeSave(TopsExceptionConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsExceptionConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除定时任务配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        return topsExceptionConfigMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public String getResumeAliasByRuleId(Long ruleId) throws ExecutionException {
        return exceptionResumeAliasCache.get(ruleId);
    }
}
