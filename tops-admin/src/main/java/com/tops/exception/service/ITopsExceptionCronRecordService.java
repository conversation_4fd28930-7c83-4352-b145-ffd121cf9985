package com.tops.exception.service;


import com.tops.common.core.domain.PageQuery;

import com.tops.common.core.page.TableDataInfo;
import com.tops.exception.domain.bo.TopsExceptionCronRecordBo;
import com.tops.exception.domain.po.TopsExceptionCronRecord;
import com.tops.exception.domain.vo.TopsExceptionCronRecordVo;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 定时任务明细Service接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface ITopsExceptionCronRecordService {

    /**
     * 查询定时任务明细
     */
    TopsExceptionCronRecordVo queryById(Long id);

    /**
     * 查询定时任务明细列表
     */
    TableDataInfo<TopsExceptionCronRecordVo> queryPageList(TopsExceptionCronRecordBo bo, PageQuery pageQuery);

    /**
     * 查询定时任务明细列表
     */
    List<TopsExceptionCronRecordVo> queryVoList(TopsExceptionCronRecordBo bo);

    /**
     * 查询定时任务明细列表
     */
    List<TopsExceptionCronRecord> queryList(TopsExceptionCronRecordBo bo);

    /**
     * 新增定时任务明细
     */
    Boolean insertByBo(TopsExceptionCronRecordBo bo);

    /**
     * 修改定时任务明细
     */
    Boolean updateByBo(TopsExceptionCronRecordBo bo);

    /**
     * 校验并批量删除定时任务明细信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询需要恢复的异常
     * @return
     */
    List<TopsExceptionCronRecord> queryUnresolvedException(Date queryTime);

    /**
     * 更新记录
     * @param topsExceptionCronRecord
     * @return
     */
    Boolean updateSelective(TopsExceptionCronRecord topsExceptionCronRecord);

    /**
     * 根据乐观锁更新记录
     * @param topsExceptionCronRecord
     * @return
     */
    Boolean updateSelectiveByTs(TopsExceptionCronRecord topsExceptionCronRecord);

    /**
     * 插入数据
     * @param record
     * @return
     */
    Boolean insertSelective(TopsExceptionCronRecord record);
}
