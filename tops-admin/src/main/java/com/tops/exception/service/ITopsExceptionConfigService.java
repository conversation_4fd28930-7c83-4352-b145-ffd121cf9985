package com.tops.exception.service;

import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.exception.domain.bo.TopsExceptionConfigBo;
import com.tops.exception.domain.po.TopsExceptionConfig;
import com.tops.exception.domain.vo.TopsExceptionConfigVo;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 定时任务配置Service接口
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public interface ITopsExceptionConfigService {

    /**
     * 查询定时任务配置
     */
    TopsExceptionConfigVo queryById(Long id);

    /**
     * 查询定时任务配置列表
     */
    TableDataInfo<TopsExceptionConfigVo> queryPageList(TopsExceptionConfigBo bo, PageQuery pageQuery);

    /**
     * 查询定时任务配置列表
     */
    List<TopsExceptionConfigVo> queryVoList(TopsExceptionConfigBo bo);

    /**
     * 查询定时任务配置列表
     */
    List<TopsExceptionConfig> queryPoList(TopsExceptionConfigBo bo);

    /**
     * 新增定时任务配置
     */
    Boolean insertByBo(TopsExceptionConfigBo bo);

    /**
     * 修改定时任务配置
     */
    Boolean updateByBo(TopsExceptionConfigBo bo);

    /**
     * 校验并批量删除定时任务配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    String getResumeAliasByRuleId(Long ruleId) throws ExecutionException;
}
