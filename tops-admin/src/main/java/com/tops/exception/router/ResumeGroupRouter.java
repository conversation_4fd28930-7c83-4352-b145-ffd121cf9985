package com.tops.exception.router;

import com.jd.eclp.core.ApiRequest;
import com.jd.eclp.exception.api.dto.ResumeOrderParam;
import com.jd.fastjson.JSON;
import com.jd.fastjson.JSONObject;
import com.jd.jsf.gd.client.GroupRouter;
import com.jd.jsf.gd.config.ConsumerGroupConfig;
import com.jd.jsf.gd.msg.Invocation;
import com.tops.exception.service.ITopsExceptionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

@Slf4j
public class ResumeGroupRouter implements GroupRouter, ApplicationContextAware {
    private static String RULE_ID = "ruleId";

    private ApplicationContext applicationContext;

    @Override
    public String router(Invocation invocation, ConsumerGroupConfig config) {
        // 根据接口+方法名 可以拿到参数 是否有注解啥的。
        Object[] objects = invocation.getArgs();
        ApiRequest<ResumeOrderParam> resumeOrderParam = (ApiRequest<ResumeOrderParam>) objects[0];
        JSONObject dataMessage = JSON.parseObject(resumeOrderParam.getData().getDataMessage());
        Long ruleId = dataMessage.getLong(RULE_ID);
        String alias = null;
        try {
            ITopsExceptionConfigService iTopsExceptionConfigService = applicationContext.getBean(ITopsExceptionConfigService.class);
            alias = iTopsExceptionConfigService.getResumeAliasByRuleId(ruleId);
        } catch (ExecutionException e) {
            log.error("从缓存中获取rule对应的alias失败{}", e.getMessage());
        }

        if(alias == null) {
            log.error("[查询恢复路由失败] ruleId: {} 没有匹配的路由", ruleId);
        }

        return alias;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
