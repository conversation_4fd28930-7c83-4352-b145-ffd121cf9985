package com.tops.extend.message.handle;

import com.tops.extend.message.dto.MessageDTO;

/**
 * ClassName:ISendMsgHandle
 * Package:com.tops.extend.message
 * Description:
 *
 * @date:2024/6/6 下午7:56
 * @author:WeiLiming
 */
public interface ISendMsgHandle {
    /**
     * 发送信息
     *
     * @param esReceiver 接受人
     * @param esTitle    标题
     * @param esContent  内容
     */
    void sendMsg(String esReceiver, String esTitle, String esContent);

    /**
     * 发送信息
     *
     * @param esReceiver 接受人
     * @param esTitle    标题
     * @param esContent  内容
     * @param detailURL  详情URL
     */
    void sendMsg(String esReceiver, String esTitle, String esContent, String detailURL);

    /**
     * 发送信息
     *
     * @param messageDTO
     */
    void sendMessage(MessageDTO messageDTO);

}
