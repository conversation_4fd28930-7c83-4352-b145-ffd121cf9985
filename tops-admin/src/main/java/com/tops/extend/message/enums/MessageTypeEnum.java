package com.tops.extend.message.enums;

import lombok.Getter;

/**
 * 消息类型
 */
@Getter
public enum MessageTypeEnum {

    /** 系统消息 */
    XT("system",  "系统消息"),
    /** 邮件消息 */
    EMAIL("email",  "邮件消息"),
    /** 钉钉消息 */
    DD("dongdong", "咚咚消息");

    /**
     * 消息类型
     */
    String type;
    /**
     * 类型说明
     */
    String note;

    MessageTypeEnum(String type, String note) {
        this.type = type;
        this.note = note;
    }

    /**
     * 根据type获取枚举
     *
     * @param type
     * @return
     */
    public static MessageTypeEnum valueOfType(String type) {
        for (MessageTypeEnum e : MessageTypeEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
