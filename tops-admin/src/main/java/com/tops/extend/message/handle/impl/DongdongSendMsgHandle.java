package com.tops.extend.message.handle.impl;

import com.alibaba.fastjson.JSONObject;
import com.jd.dd.open.gw.api.GrantService;
import com.jd.dd.open.gw.api.MessagePushService;
import com.jd.dd.open.gw.api.domain.*;
import com.tops.extend.message.dto.MessageDTO;
import com.tops.extend.message.handle.ISendMsgHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 发咚咚消息模板
 * @author: weiliming
 */
@Slf4j
@Component("dongdongSendMsgHandle")
public class DongdongSendMsgHandle implements ISendMsgHandle {
    @Value("${properties.dongdong.asp.id}")
    private String DONGDONG_ASP_ID;
    @Value("${properties.dongdong.secret}")
    private String DONGDONG_SECRET;
    @Value("${properties.dongdong.notice.id}")
    private String DONGDONG_NOTICE_ID;

    @Override
    public void sendMsg(String esReceiver, String esTitle, String esContent) {
        log.info("发微信消息模板");
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setToUser(esReceiver);
        messageDTO.setTitle(esTitle);
        messageDTO.setContent(esContent);
        messageDTO.setToAll(false);
        sendMessage(messageDTO);
    }

    /**
     * 发送信息
     *
     * @param esReceiver 接受人
     * @param esTitle    标题
     * @param esContent  内容
     * @param detailURL  详情URL
     */
    @Override
    public void sendMsg(String esReceiver, String esTitle, String esContent, String detailURL) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setToUser(esReceiver);
        messageDTO.setTitle(esTitle);
        messageDTO.setContent(esContent);
        messageDTO.setToAll(false);
        Map<String, Object> data = new HashMap<>();
        data.put("url", detailURL);
        messageDTO.setData(data);
        sendMessage(messageDTO);
    }

    @Override
    public void sendMessage(MessageDTO message) {
        String toErp = message.getToUser();
        List<String> toErps = new ArrayList<>(Arrays.asList(toErp.split(",")));
        //构造授权参数
        AccessSignature as = new AccessSignature();
        as.setAccessid(UUID.randomUUID().toString());
        as.setAccessToken(this.getAccessToken());
        as.setAspid(DONGDONG_ASP_ID);
        as.setVersion("4.3");
        as.setTimestamp(System.currentTimeMillis());
        //构造消息参数
        Message msg = new Message();
        JSONObject json = new JSONObject();
        json.put("type", "notice_message");
        json.put("ver", "4.3");
        //长度限制50
        json.put("title", message.getTitle());
        log.info("发送的名称为:{}", message.getTitle());
        //目前暂不支持换行，长度限制1500
        json.put("content", message.getContent());
        // ~加原来的应用标识  如原来的 ump -> ~ump，申请邮件反馈中会给出相应的noticeId
        json.put("noticeId", DONGDONG_NOTICE_ID);
        //企业版支持(推所有终端传 7 ，非所有端请联系我们确认)
        json.put("toTerminal", 7);
        //是否需要离线投递，申请通知时需要配置,0在线投递，1离线投递
        json.put("sla", 1);
        //代表发送的用户群体，ee代表国内ERP，泰国th.ee
        json.put("app", "ee");
        //一次最大500个
        json.put("tos", toErps);
        if (message.getData() != null) {
            JSONObject extend = new JSONObject();
            extend.put("url", message.getData().get("url"));
            json.put("extend", extend);
        }
        msg.setJsonMsg(json.toJSONString());
        log.info("【咚咚消息】发送入参：{},{}", JSONObject.toJSONString(as), JSONObject.toJSONString(msg));

        MessagePushResult messagePushResult = messagePushService.push(as, msg);
        if (messagePushResult.getCode() == 230070) {
            log.info("【咚咚消息】toErps:{} 发送咚咚成功", JSONObject.toJSONString(toErps));
        } else {
            log.info("【咚咚消息】toErps:{} 发送咚咚失败, 失败原因：{}", JSONObject.toJSONString(toErps), messagePushResult);
        }
    }

    @Autowired
    private GrantService grantService;
    @Autowired
    private MessagePushService messagePushService;

    public String getAccessToken() {
        String accessToken = null;
        AppSigInfo info = new AppSigInfo();
        info.setAspid(DONGDONG_ASP_ID);
        info.setSecret(DONGDONG_SECRET);
        info.setVersion("4.3");
        AccessSignatureResult result = grantService.refreshAccessSignature(info);
        if (result.getCode() == 230031) {
            accessToken = result.getAccessToken();
            log.info("通过咚咚接口获取accessToken成功{}", accessToken);
        }
        return accessToken;
    }

}
