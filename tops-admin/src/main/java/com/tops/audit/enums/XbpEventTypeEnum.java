package com.tops.audit.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum XbpEventTypeEnum {
    /**
     * 创建ticket
     */
    TICKET_CREATE("TICKET_CREATE"),
    /**
     * 更新ticket
     */
    TICKET_UPDATE("TICKET_UPDATE"),
    /**
     * 同步ticket
     */
    TICKET_SYNC("TICKET_SYNC"),
    /**
     * 申请单完成
     */
    TICKET_CLOSE("TICKET_CLOSE"),
    /**
     * 向上加签
     */
    TICKET_PREV_FLOWS_ADD("TICKET_PREV_FLOWS_ADD"),
    /**
     * 向下加签
     */
    TICKET_NEXT_FLOWS_ADD("TICKET_NEXT_FLOWS_ADD"),
    /**
     * 添加审批人
     */
    TICKET_APPROVERS_ADD("TICKET_APPROVERS_ADD"),
    /**
     * 审批人改变
     */
    TICKET_APPROVERS_CHANGE("TICKET_APPROVERS_CHANGE"),
    /**
     * 通过
     */
    TICKET_APPROVE("TICKET_APPROVE"),
    /**
     * 流程重启
     */
    TICKET_RESTART("TICKET_RESTART"),
    /**
     * 撤销
     */
    TICKET_REVOKE("TICKET_REVOKE"),
    /**
     * 驳回
     */
    TICKET_REJECT("TICKET_REJECT"),
    /**
     * ticeket 重提
     */
    TICKET_REMIND("TICKET_REMIND"),
    /**
     * ticket
     */
    TICKET_DESK_TRANSFER("TICKET_DESK_TRANSFER"),
    /**
     * ticket撤回上一步
     */
    TICKET_STEP_BACK("TICKET_STEP_BACK"),
    /**
     * ticket声明
     */
    TICKET_CLAIM("TICKET_CLAIM"),
    /**
     * 子流程追加
     */
    SUB_SHEET_ADD("SUB_SHEET_ADD"),
    /**
     * 子流程end
     */
    SUB_SHEET_END("SUB_SHEET_END"),
    /**
     * 创建评论
     */
    TICKET_COMMENT_CREATE("TICKET_COMMENT_CREATE"),
    /**
     * 评论更新
     */
    TICKET_COMMENT_UPDATE("TICKET_COMMENT_UPDATE"),
    /**
     * 评论删除
     */
    TICKET_COMMENT_DELETE("TICKET_COMMENT_DELETE"),
    /**
     * 收藏添加
     */
    TICKET_COLLECT_ADD("TICKET_COLLECT_ADD"),
    /**
     * 收藏取消
     */
    TICKET_COLLECT_CANCEL("TICKET_COLLECT_CANCEL");

    /**
     * 对应的数值
     */
    private String code;

    XbpEventTypeEnum(String code) {
        this.code = code;
    }

    public static XbpEventTypeEnum getEnumByCode(String code) {
        for (XbpEventTypeEnum value : XbpEventTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
