package com.tops.audit.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum XbpProcessEnum {
    CANCEL_ORDERS_PROCESS("cancelOrders", "取消订单审批流程"),
    CALLBACK_ORDERS_PROCESS("callbackOrders", "回传订单审批流程"),
    MODIFY_ORDERS_PROCESS("modifyOrders", "修改订单审批流程"),
    REACCEPT_ORDERS_PROCESS("reacceptOrders", "重处理订单审批流程"),
    SHUTDOWN_CONTAINER("shutdown", "关停容器流程"),
    SCALE_UP_GROUP("scaleUp", "扩容分组流程"),
    DUCC_MODIFY("duccModify", "ducc配置变更"),
    ;
    private String code;
    private String desc;

    XbpProcessEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static XbpProcessEnum getEnumByCode(String code) {
        for (XbpProcessEnum value : XbpProcessEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
