package com.tops.audit.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum XbpApprovalStatusEnum {
    REJECTED(-1, "驳回"),
    PROCESSING(0, "进行中"),
    FINISHED(1, "结束"),
    REVOKED(2, "撤回"),

    ;
    private int code;
    private String desc;

    XbpApprovalStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static XbpApprovalStatusEnum getEnumByCode(String code) {
        for (XbpApprovalStatusEnum value : XbpApprovalStatusEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
