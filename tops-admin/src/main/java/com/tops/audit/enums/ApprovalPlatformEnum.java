package com.tops.audit.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ApprovalPlatformEnum {
    XBP("XBP", "XBP");
    private String code;
    private String desc;

    ApprovalPlatformEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public static ApprovalPlatformEnum getEnumByCode(String code) {
        for (ApprovalPlatformEnum value : ApprovalPlatformEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
