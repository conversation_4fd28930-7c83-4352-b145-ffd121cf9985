package com.tops.audit.interceptor;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.jd.ssa.oidc.client.SsoClientConfig;
import com.jd.ssa.oidc.client.annotation.AnonymousAccess;
import com.jd.ssa.oidc.client.filter.CorsFilter;
import com.jd.ssa.oidc.client.interceptor.AbstractSsoInterceptor;
import com.tops.common.core.domain.entity.SysUser;
import com.tops.common.enums.UserStatus;
import com.tops.common.exception.user.UserException;
import com.tops.system.service.ISysUserService;
import com.tops.system.service.SysLoginService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


/**
 * 京东ERP SSO登录拦截器
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Getter
@Setter
@Slf4j
@Component
public class ErpSSOInterceptor extends AbstractSsoInterceptor implements HandlerInterceptor {

    /**
     * SSO原生变量--start
     */
    private String configFileName;
    @Autowired(
        required = false
    )
    private Environment environment;
    /**
     * SSO原生变量--end
     */

    /**
     * 若依登录服务
     */
    @Autowired
    private SysLoginService loginService;
    /**
     * 用户信息服务
     */
    @Autowired
    private ISysUserService userService;
    /**
     * 管理员权限列表
     */
    @Value("${admin.erp.set}")
    private String adminErpSetStr;
    /**
     * 管理员权限列表
     */
    @Value("${role.id.normal}")
    private Long roleIdNormal;
    /**
     * 管理员权限列表
     */
    private Set<String> ADMIN_ERP_SET = new HashSet<>();
    /**
     * 管理员权限列表配置分隔符
     */
    private final static String SEPARATOR = ",";
    /**
     * 管理员用户用户名
     */
    private final static String USER_NAME_ADMIN = "admin";

    /**
     * 构造函数
     */
    public ErpSSOInterceptor() {
    }

    /**
     * 属性初始化+属性打印
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        this.setSsoClientConfig(this.createSsoClientConfig());
        super.afterPropertiesSet();
        log.info("clientId:{}", this.ssoClientConfig.getClientId());
        log.info("clientSecret:{}", this.ssoClientConfig.getClientSecret());
        log.info("cookieName:{}", this.ssoClientConfig.getCookieName());
        log.info("cookieDomainName:{}", this.ssoClientConfig.getCookieDomainName());
        ADMIN_ERP_SET.addAll(Arrays.asList(adminErpSetStr.split(SEPARATOR)));
        log.info("ADMIN_ERP_SET:{}", JSON.toJSONString(ADMIN_ERP_SET));
    }

    /**
     * 属性初始化
     */
    private SsoClientConfig createSsoClientConfig() throws ServletException {
        if (this.ssoClientConfig != null) {
            return this.ssoClientConfig;
        } else if (StringUtils.isNotBlank(this.configFileName)) {
            return SsoClientConfig.getInstanceFromPropertiesFile(this.configFileName);
        } else if (this.environment != null) {
            return SsoClientConfig.getSsoClientConfigFromEnvironment(this.environment);
        } else {
            throw new ServletException("缺少配置信息，请参阅文档配置");
        }
    }

    /**
     * 前置处理，ERP登录成功后，根据用户ERP自动进行登录。（部分ERP可以通过变量admin.erp.set维护成若依管理员用户）
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        response.addHeader("Sso-Version", "1.0");
        log.info("ErpSSOInterceptor.preHandle 进入SSO登录拦截器");
        if ("Default".equalsIgnoreCase(this.ssoClientConfig.getErpSsoInterceptorCorsPolicy())) {
            CorsFilter.addCrossDomainHeader(request, response, CorsFilter.DEFAULT_ORIGIN, "Location,Origin,Referer,X-Requested-With,Content-Type,Accept,Host,Cookie,Authorization,Oidc-Temp-Ticket", "Location,Set-Cookie,Content-Type,Content-Length,Oidc-Temp-Ticket");
        }
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            log.info("ErpSSOInterceptor.preHandle option请求，不进行处理");
            return true;
        } else if (this.isExclude(request.getRequestURI())) {
            log.info("ErpSSOInterceptor.preHandle 进入SSO登录拦截器");
            return true;
        } else {
            if (handler instanceof HandlerMethod) {
                Annotation annotation = ((HandlerMethod) handler).getMethodAnnotation(AnonymousAccess.class);
                if (annotation != null) {
                    return true;
                }
            }
            LoginContext context = this.getLoginContext(request, response);
            return context != null ? this.preHandleWhenUserAccess(context, response) : this.preHandleWhenAnonymous(request, response);
        }
    }

    /**
     * ERP登录成功后，自动进行若依系统登录。
     */
    protected boolean preHandleWhenUserAccess(LoginContext context, HttpServletResponse response) {
        LoginContext.setLoginContext(context);
        String pin = context.getPin();
        log.info("ErpSSOInterceptor.preHandleWhenUserAccess context:{}", context);
        if (ADMIN_ERP_SET.contains(pin)) {
            pin = USER_NAME_ADMIN;
        }
        try {
            SysUser user = loginService.loadUserByUsername(pin);
        } catch (UserException e) {
            if (UserStatus.UNKNOWN.getCode().equals(e.getCode())) {
                SysUser user = new SysUser();
                user.setUserName(pin);
                user.setNickName(context.getNick());
                user.setEmail(context.getEmail());
                user.setPhonenumber(context.getMobile());
                user.setPassword(ISysUserService.PASSWORD_DEFAULT);
                user.setStatus(ISysUserService.STATUS_DEFAULT);
                Long[] roleIds = new Long[1];
                roleIds[0] = roleIdNormal;
                user.setRoleIds(roleIds);
                log.warn("当前用户为首次登录，创建新用户：{}", JSON.toJSONString(user));
                userService.insertUser(user);
            } else {
                throw e;
            }
        }
        String token = loginService.getToken(pin);
        Cookie cookie = new Cookie("Admin-Token", token);
        cookie.setPath("/");
        response.addCookie(cookie);
        log.info("ErpSSOInterceptor.preHandleWhenUserAccess pin:{},systemUser:{},token:{}", context.getPin(), pin, token);
        return true;
    }

    /**
     * 未登录
     */
    protected boolean preHandleWhenAnonymous(HttpServletRequest request, HttpServletResponse response) throws IOException {
        this.toAuthorizeUri(request, response);
        return false;
    }


    /**
     * 重写postHandle方法，用于处理请求后的操作
     *
     * @param httpServletRequest  HTTP请求对象
     * @param httpServletResponse HTTP响应对象
     * @param o                   当前处理的处理器
     * @param modelAndView        视图模型对象
     * @throws Exception 可能抛出的异常
     */
    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
    }

    /**
     * 在请求处理完成后执行的方法
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  处理程序
     * @param ex       异常对象
     * @throws Exception 可能抛出的异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LoginContext.remove();
    }

    /**
     * 设置配置文件名
     *
     * @param configFileName 配置文件名
     */
    public void setConfigFileName(String configFileName) {
        this.configFileName = configFileName;
    }

    /**
     * 设置环境变量
     *
     * @param environment 环境变量对象
     */
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

}
