package com.tops.audit.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.domain.dto.QueryApprovalCondition;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

import java.util.List;

public interface ApprovalRepository {

    /**
     * 从数据库中选择符合查询条件的审批实例
     * @param processInstanceId 审批实例ID
     * @return 符合条件的审批DTO
     * @throws InvalidRequestException 请求参数无效异常
     * @throws InternalFailureException 内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     */
    ApprovalDTO selectApproval(String processInstanceId) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 从数据库中选择符合查询条件的审批列表
     * @param condition 查询条件对象
     * @return 符合条件的审批DTO列表
     * @throws InvalidRequestException 请求参数无效异常
     * @throws InternalFailureException 内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     */
    List<ApprovalDTO> selectApprovals(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 从数据库中查询符合条件的审批信息列表并返回分页结果
     * @param condition 查询条件对象
     * @return 返回包含符合条件的审批信息列表的分页对象
     * @throws InvalidRequestException 当请求参数无效时抛出此异常
     * @throws InternalFailureException 当内部操作失败时抛出此异常
     * @throws DependencyFailureException 当依赖操作失败时抛出此异常
     */
    Page<ApprovalDTO> selectApprovalsPage(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 从数据库中选择带有流程的审批记录
     * @param condition 查询审批记录的条件
     * @return 包含审批DTO的分页对象
     * @throws InvalidRequestException 当请求无效时抛出
     * @throws InternalFailureException 内部错误时抛出
     * @throws DependencyFailureException 依赖失败时抛出
     */
    Page<ApprovalDTO> selectApprovalsWithFlow(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 插入批准列表
     * @param approvals 要插入的批准DTO列表
     * @throws InvalidRequestException 如果请求无效
     * @throws InternalFailureException 如果发生内部错误
     * @throws DependencyFailureException 如果依赖项失败
     */
    void insertApprovals(List<ApprovalDTO> approvals) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 更新批准记录
     * @param approvals 批准记录列表
     * @throws InvalidRequestException 当请求无效时抛出
     * @throws InternalFailureException 当内部错误发生时抛出
     * @throws DependencyFailureException 当依赖失败时抛出
     */
    void updateApprovals(List<ApprovalDTO> approvals) throws InvalidRequestException, InternalFailureException, DependencyFailureException;

    /**
     * 删除审批记录
     * @param processInstanceIds 要删除的流程实例ID列表
     * @throws InvalidRequestException 如果请求无效
     * @throws InternalFailureException 如果发生内部错误
     * @throws DependencyFailureException 如果存在依赖关系错误
     */
    void deleteApprovals(List<String> processInstanceIds) throws InvalidRequestException, InternalFailureException, DependencyFailureException;
}
