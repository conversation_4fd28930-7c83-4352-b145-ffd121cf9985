package com.tops.audit.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.domain.dto.QueryApprovalCondition;
import com.tops.audit.domain.po.ApprovalPO;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.repository.ApprovalRepository;
import com.tops.audit.repository.dao.ApprovalDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Slf4j
@Repository
public class ApprovalRepositoryImpl implements ApprovalRepository {


    @Autowired
    private ApprovalDao approvalDao;

    /**
     * 从数据库中选择符合查询条件的审批实例
     *
     * @param processInstanceId 审批实例ID
     * @return 符合条件的审批DTO
     * @throws InvalidRequestException    请求参数无效异常
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     */
    @Override
    public ApprovalDTO selectApproval(String processInstanceId) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        ApprovalPO approvalPO = approvalDao.selectApproval(processInstanceId);
        if (approvalPO == null) {
            log.warn("ApprovalRepositoryImpl.selectApproval processInstanceId:{}无对应流程实例", processInstanceId);
            throw new InvalidRequestException(String.format("ApprovalRepositoryImpl.selectApproval processInstanceId:%s无对应流程实例", processInstanceId));
        }
        return ApprovalDTO.fromPO(approvalPO);
    }

    /**
     * 从数据库中选择符合查询条件的审批列表
     *
     * @param condition 查询条件对象
     * @return 符合条件的审批DTO列表
     * @throws InvalidRequestException    请求参数无效异常
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     */
    @Override
    public List<ApprovalDTO> selectApprovals(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        throw new InternalFailureException("查询审批实例功能开发中，敬请期待");
    }

    /**
     * 从数据库中查询符合条件的审批信息列表并返回分页结果
     *
     * @param condition 查询条件对象
     * @return 返回包含符合条件的审批信息列表的分页对象
     * @throws InvalidRequestException    当请求参数无效时抛出此异常
     * @throws InternalFailureException   当内部操作失败时抛出此异常
     * @throws DependencyFailureException 当依赖操作失败时抛出此异常
     */
    @Override
    public Page<ApprovalDTO> selectApprovalsPage(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        throw new InternalFailureException("查询审批实例功能开发中，敬请期待");
    }

    /**
     * 从数据库中选择带有流程的审批记录
     *
     * @param condition 查询审批记录的条件
     * @return 包含审批DTO的分页对象
     * @throws InvalidRequestException    当请求无效时抛出
     * @throws InternalFailureException   内部错误时抛出
     * @throws DependencyFailureException 依赖失败时抛出
     */
    @Override
    public Page<ApprovalDTO> selectApprovalsWithFlow(QueryApprovalCondition condition) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        throw new InternalFailureException("查询审批实例功能开发中，敬请期待");
    }

    /**
     * 插入批准列表
     *
     * @param approvals 要插入的批准DTO列表
     * @throws InvalidRequestException    如果请求无效
     * @throws InternalFailureException   如果发生内部错误
     * @throws DependencyFailureException 如果依赖项失败
     */
    @Override
    @Transactional(rollbackFor = Throwable.class, timeout = 7)
    public void insertApprovals(List<ApprovalDTO> approvals) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        if (CollectionUtils.isEmpty(approvals)) {
            return;
        }
        Integer count = 0;
        try {
            for (ApprovalDTO approval : approvals) {
                ApprovalPO approvalPO = new ApprovalPO();
                approvalPO.setProcessId(approval.getProcessId());
                approvalPO.setProcessDesc(approval.getProcessDesc());
                approvalPO.setProcessInstanceId(approval.getProcessInstanceId());
                approvalPO.setInstanceStatus(approval.getInstanceStatus());
                approvalPO.setPlatform(approval.getPlatform());
                approvalPO.setInstanceContent(approval.getInstanceContent());
                approvalPO.setInstanceContentKey(approval.getInstanceContentKey());
                approvalPO.setCreateUser(approval.getCreateUser());
                approvalPO.setUdpateUser(approval.getCreateUser());
                approvalPO.setCreateTime(approval.getCreateTime());
                approvalPO.setUpdateTime(approval.getCreateTime());
                count += approvalDao.insertApproval(approvalPO);
            }
        } catch (Exception e) {
            log.error("ApprovalRepositoryImpl.insertApprovals 插入审批出现异常 exception：", e);
            throw new InternalFailureException("ApprovalRepositoryImpl.insertApprovals 插入审批出现异常");
        }
        if (!Objects.equals(approvals.size(), count)) {
            throw new InternalFailureException("ApprovalRepositoryImpl.insertApprovals 插入审批失败，实际插入数量与预期插入数量不符");
        }
    }

    /**
     * 更新批准记录
     *
     * @param approvals 批准记录列表
     * @throws InvalidRequestException    当请求无效时抛出
     * @throws InternalFailureException   当内部错误发生时抛出
     * @throws DependencyFailureException 当依赖失败时抛出
     */
    @Override
    public void updateApprovals(List<ApprovalDTO> approvals) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        if (CollectionUtils.isEmpty(approvals)) {
            return;
        }
        Integer count = 0;
        try {
            for (ApprovalDTO approval : approvals) {
                ApprovalPO approvalPO = new ApprovalPO();
                approvalPO.setProcessId(approval.getProcessId());
                approvalPO.setProcessDesc(approval.getProcessDesc());
                approvalPO.setProcessInstanceId(approval.getProcessInstanceId());
                approvalPO.setInstanceStatus(approval.getInstanceStatus());
                approvalPO.setPlatform(approval.getPlatform());
                approvalPO.setInstanceContent(approval.getInstanceContent());
                approvalPO.setInstanceContentKey(approval.getInstanceContentKey());
                approvalPO.setCreateUser(approval.getCreateUser());
                approvalPO.setUdpateUser(approval.getCreateUser());
                approvalPO.setCreateTime(approval.getCreateTime());
                approvalPO.setUpdateTime(approval.getCreateTime());
                count += approvalDao.updateApproval(approval.getProcessInstanceId(), approvalPO);
            }
        } catch (Exception e) {
            log.error("ApprovalRepositoryImpl.updateApprovals 插入审批出现异常 exception：", e);
            throw new InternalFailureException("ApprovalRepositoryImpl.updateApprovals 更新审批出现异常");
        }
        if (!Objects.equals(approvals.size(), count)) {
            throw new InternalFailureException("ApprovalRepositoryImpl.updateApprovals 更新审批失败，实际更新数量与预期更新数量不符");
        }
    }

    /**
     * 删除审批记录
     *
     * @param processInstanceIds 要删除的流程实例ID列表
     * @throws InvalidRequestException    如果请求无效
     * @throws InternalFailureException   如果发生内部错误
     * @throws DependencyFailureException 如果存在依赖关系错误
     */
    @Override
    public void deleteApprovals(List<String> processInstanceIds) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        throw new InternalFailureException("删除审批实例功能开发中，敬请期待");
    }
}
