package com.tops.audit.repository.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.audit.domain.po.ApprovalPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApprovalDao {

    /**
     * 查询审批信息
     *
     * @param processInstanceId 审批实例ID：必填项
     * @return 审批信息
     */
    ApprovalPO selectApproval(@Param("processInstanceId") String processInstanceId);

    /**
     * 分页查询审批信息 TODO wangqin83
     *
     * @param erp  审批实例创建人ERP
     * @param page 分页信息
     * @return 审批信息
     */
    List<ApprovalPO> selectApprovalsPage(String erp, Page page);

    /**
     * 插入审批信息
     *
     * @param approval 待插入的审批信息对象
     * @return 插入成功的数量
     */
    Integer insertApproval(@Param("po") ApprovalPO approval);

    /**
     * 更新审批信息
     *
     * @param processInstanceId 审批信息的唯一标识
     * @param approval          待更新的审批信息对象
     * @return 更新成功的数量
     */
    Integer updateApproval(@Param("processInstanceId") String processInstanceId, @Param("po") ApprovalPO approval);

    /**
     * 删除审批记录
     *
     * @param processInstanceIds 待删除的审批记录ID列表
     * @return 删除成功的数量
     */
    Integer deleteApprovals(List<String> processInstanceIds);
}
