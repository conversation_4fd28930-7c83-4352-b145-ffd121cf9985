package com.tops.audit.constants;

/**
 * 审批平台常量
 */
public interface AuditConstants {
    /**
     * XBP平台常量
     */
    interface XbpConstants {
        /**
         * 订单域常量
         */
        interface OrderConstants {
            /**
             * 取消订单流程 表单字段：提交人
             */
            String CANCEL_ORDER_FIELD_COMMITER = "提交人";
            /**
             * 取消订单流程 表单字段：取消背景说明
             */
            String CANCEL_ORDER_FIELD_DESCRIPTION = "取消背景说明";
            /**
             * 取消订单流程 表单字段：取消类型
             */
            String CANCEL_ORDER_FIELD_TYPE = "取消类型";
            /**
             * 取消订单流程 表单字段：取消订单列表
             */
            String CANCEL_ORDER_FIELD_ORDERS = "取消订单列表";
            /**
             * 取消订单流程 表单字段：物流订单号
             */
            String CANCEL_ORDER_FIELD_ORDERS_NO = "物流订单号";
            /**
             * 取消订单流程 表单字段：变更前订单状态
             */
            String CANCEL_ORDER_FIELD_STATUS_BEFORE = "变更前订单状态";
            /**
             * 取消订单流程 表单字段：变更后订单状态
             */
            String CANCEL_ORDER_FIELD_STATUS_AFTER = "变更后订单状态";

            /**
             * 背景说明
             */
            String XBP_MODIFY_REASON= "背景说明";
            /**
             * 表单字段：提交人
             */
            String XBP_COMMITER = "提交人";

            /**
             * 操作订单列表
             */
            String XBP_ORDERS = "操作订单列表";

            /**
             * 操作订单列表
             */
            String XBP_ORDERS_CALLBACK_STATUS = "回传状态";

            /**
             * 修改内容
             */
            String XBP_MODIFY_CONTENT = "修改内容";

            /**
             * 修改货品是否忽略行号
             */
            String XBP_MODIFY_CARGO_MATCH_TYPE = "修改货品是否忽略行号";
            /**
             * 应用名称
             */
            String XBP_JDOS_APP_NAME="应用名称";
            /**
             * 分组名称
             */
            String XBP_JDOS_GROUP_NAME="分组名称";
            /**
             * 异常IP集合
             */
            String XBP_JDOS_ABNORMAL_IP_LIST="异常IP集合";
            /**
             * 关停原因
             */
            String XBP_JDOS_SHUTDOWN_REASON="关停原因";
            /**
             * 扩容数量
             */
            String XBP_JDOS_SCALE_UP_COUNT="扩容数量";
            /**
             * 扩容原因
             */
            String XBP_JDOS_SCALE_UP_REASON="扩容原因";
            /**
             * DUCC配置调整原因
             */
            String XBP_DUCC_MODIFY_REASON="DUCC配置调整原因";
            /**
             * DUCC配置调整列表
             */
            String XBP_DUCC_MODIFY_TABLE="DUCC配置调整列表";
            /**
             * DUCC配置调整列表 表单字段：应用
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_APPLICATION = "应用";
            /**
             * DUCC配置调整列表 表单字段：命名空间
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_NAMESPACE = "命名空间";
            /**
             * DUCC配置调整列表 表单字段：配置
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_CONFIG = "配置";
            /**
             * DUCC配置调整列表 表单字段：环境
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_PROFILE = "环境";
            /**
             * DUCC配置调整列表 表单字段：配置项key
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_KEY = "配置项key";
            /**
             * DUCC配置调整列表 表单字段：更新后的值
             */
            String XBP_DUCC_MODIFY_TABLE_FIELD_NEW_VALUE = "更新后的值";
        }
    }
}
