package com.tops.audit.controller.test;

import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.service.ApprovalService;
import com.tops.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Slf4j
@RestController
@RequestMapping("/text")
public class TestApprovalController {
    @Autowired
    @Qualifier("xbpApprovalService")
    private ApprovalService approvalService;

    @RequestMapping("/submitApproval")
    @ResponseBody
    public R submitApproval(@RequestBody SubmitApprovalRequest request) throws Exception {
        try {
            request.setOperator("wangqin83");
            request.setOperateTime(new Date());
            approvalService.submitApproval(request);
        } catch (Exception e) {
            log.error("提交审批失败，exception:", e);
        }
        return R.ok();
    }
}
