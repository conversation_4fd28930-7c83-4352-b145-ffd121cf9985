package com.tops.audit.controller.sso;

import com.tops.common.core.domain.R;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/sso")
public class SsoController {
    @RequestMapping("/checkCookie")
    @ResponseBody
    public R checkCookie(HttpServletRequest request, HttpServletResponse response) throws Exception {
        return R.ok();
    }
}
