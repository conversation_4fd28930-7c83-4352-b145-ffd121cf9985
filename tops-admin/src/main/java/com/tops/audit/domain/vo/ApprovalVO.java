package com.tops.audit.domain.vo;

import java.util.Date;
import java.util.List;

public class ApprovalVO {
    /**
     * 流程ID
     */
    private String	processId;

    /**
     * 流程描述
     */
    private String	processDesc;

    /**
     * 流程实例ID
     */
    private String	processInstanceId;

    /**
     * 流程实例状态：XBP平台：驳回（-1），进行中（0），结束（1），撤回（2）
     */
    private String	instanceStatus;

    /**
     * 流程所属平台
     */
    private String	platform;

    /**
     * 流程实例内容
     */
    private String	instanceContent;

    /**
     * 流程实例内容唯一键，用于防止重复操作
     */
    private String	instanceContentKey;

    /**
     * 流程实例的状态流水信息 TODO wangqin83
     */
    private List<ApprovalFlowNodeVO> instanceFlowNodes;

    /**
     * 流程提交用户
     */
    private String	createUser;

    /**
     * 流程提交时间
     */
    private Date createTime;
}
