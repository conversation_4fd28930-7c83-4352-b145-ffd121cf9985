package com.tops.audit.domain.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ApprovalPO {
    /**
     * id
     */
    private Long	id;

    /**
     * 租户信息
     */
    private Long	tenantId;

    /**
     * 流程ID
     */
    private String	processId;

    /**
     * 流程描述
     */
    private String	processDesc;

    /**
     * 流程实例ID
     */
    private String	processInstanceId;

    /**
     * 流程实例状态：XBP平台：驳回（-1），进行中（0），结束（1），撤回（2）
     */
    private String	instanceStatus;

    /**
     * 流程所属平台
     */
    private String	platform;

    /**
     * 流程实例内容
     */
    private String	instanceContent;

    /**
     * 流程实例内容唯一键，用于防止重复操作
     */
    private String	instanceContentKey;

    /**
     * 流程提交用户
     */
    private String	createUser;

    /**
     * 流程提交用户
     */
    private String	udpateUser;

    /**
     * 流程提交时间
     */
    private Date	createTime;

    /**
     * 流程提交时间
     */
    private Date	updateTime;

    /**
     * 逻辑删除标志：1：未删除 0：已删除
     */
    private Integer	yn;
}
