package com.tops.audit.domain.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tops.audit.domain.po.ApprovalPO;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@Slf4j
public class ApprovalDTO {
    /**
     * 流程ID
     */
    private String processId;

    /**
     * 流程描述
     */
    private String processDesc;

    /**
     * 流程实例ID
     */
    private String processInstanceId;

    /**
     * 流程实例状态：XBP平台：驳回（-1），进行中（0），结束（1），撤回（2）
     */
    private String instanceStatus;

    /**
     * 流程所属平台
     */
    private String platform;

    /**
     * 流程实例内容
     */
    private String instanceContent;

    /**
     * 流程实例内容唯一键，用于防止重复操作
     */
    private String instanceContentKey;

    /**
     * 审批实例表单展示内容
     */
    private Map<String, String> instanceForm;
    /**
     * 审批实例表格展示内容
     */
    private Map<String, String> instanceTableForm;

    /**
     * 流程实例的状态流水信息 TODO wangqin83
     */
    private List<ApprovalFlowNodeDTO> instanceFlowNodes;

    /**
     * 评论
     */
    private String comment;

    /**
     * 流程提交用户
     */
    private String createUser;

    /**
     * 流程提交时间
     */
    private Date createTime;

    /**
     * po->dto
     */
    public static ApprovalDTO fromPO(ApprovalPO approvalPO) {
        if (approvalPO == null) {
            return null;
        }
        ApprovalDTO approvalDTO = new ApprovalDTO();
        approvalDTO.setProcessId(approvalPO.getProcessId());
        approvalDTO.setProcessDesc(approvalPO.getProcessDesc());
        approvalDTO.setProcessInstanceId(approvalPO.getProcessInstanceId());
        approvalDTO.setInstanceStatus(approvalPO.getInstanceStatus());
        approvalDTO.setPlatform(approvalPO.getPlatform());
        approvalDTO.setInstanceContent(approvalPO.getInstanceContent());
        approvalDTO.setInstanceContentKey(approvalPO.getInstanceContentKey());
//        approvalDTO.setInstanceForm();
//        approvalDTO.setInstanceTableForm();
//        approvalDTO.setInstanceFlowNodes();
//        approvalDTO.setComment();
        approvalDTO.setCreateUser(approvalPO.getCreateUser());
        approvalDTO.setCreateTime(approvalPO.getCreateTime());
        return approvalDTO;
    }

    /**
     * po->dto
     */
    public static List<ApprovalDTO> fromPO(List<ApprovalPO> approvals) {
        if (CollectionUtils.isEmpty(approvals)) {
            return new ArrayList<>();
        }
        return approvals.stream()
            .filter(Objects::nonNull)
            .map(ApprovalDTO::fromPO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    public <T> T parseInstanceContent(TypeReference<T> typeReference) throws InvalidRequestException, InternalFailureException {
        if (StringUtils.isBlank(this.instanceContent)) {
            log.warn("ApprovalDTO.parseInstanceContent instanceContent不可为空");
            throw new InvalidRequestException("ApprovalDTO.parseInstanceContent instanceContent不可为空");
        }
        if (typeReference == null) {
            log.warn("ApprovalDTO.parseInstanceContent typeReference不可为空");
            throw new InvalidRequestException("ApprovalDTO.parseInstanceContent typeReference不可为空");
        }
        T result = null;
        try {
            result = JSON.parseObject(this.instanceContent, typeReference);
        } catch (Exception e) {
            log.warn("ApprovalDTO.parseInstanceContent instanceContent格式错误：非完整正确JSON");
            throw new InvalidRequestException("ApprovalDTO.parseInstanceContent instanceContent格式错误：非完整正确JSON");
        }
        return result;
    }
}
