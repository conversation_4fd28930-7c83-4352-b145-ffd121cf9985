package com.tops.audit.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 审批实例状态流水DTO
 */
@Getter
@Setter
public class ApprovalFlowNodeDTO implements Serializable {
    /**
     * UUID
     */
    private static final long serialVersionUID = 4062589786757461711L;

    /**
     * 审批结果
     */
    private String approvalResult;
    /**
     * 流程提交用户
     */
    private String approvalUser;
    /**
     * 流程提交时间
     */
    private Date approvalTime;
}
