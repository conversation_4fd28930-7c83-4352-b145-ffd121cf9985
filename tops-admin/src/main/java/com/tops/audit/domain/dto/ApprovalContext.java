package com.tops.audit.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
public class ApprovalContext {
    /**
     * 审批实例信息
     */
    private ApprovalDTO approval;
    /**
     * 审批操作人信息
     */
    private UserDTO operator;

    public static ApprovalContext of() {
        return new ApprovalContext();
    }

    public ApprovalContext withPin(String pin) {
        if (this.operator == null) {
            this.operator = new UserDTO();
        }
        this.operator.setPin(pin);
        return this;
    }

    public ApprovalContext withProcessId(String processId) {
        if (this.approval == null) {
            this.approval = new ApprovalDTO();
        }
        this.approval.setProcessId(processId);
        return this;
    }

    public ApprovalContext withProcessInstanceId(String processInstanceId) {
        if (this.approval == null) {
            this.approval = new ApprovalDTO();
        }
        this.approval.setProcessInstanceId(processInstanceId);
        return this;
    }

    public ApprovalContext withComment(String comment) {
        if (this.approval == null) {
            this.approval = new ApprovalDTO();
        }
        this.approval.setComment(comment);
        return this;
    }

    public ApprovalContext withInstanceForm(Map<String, String> instanceForm) {
        if (this.approval == null) {
            this.approval = new ApprovalDTO();
        }
        this.approval.setInstanceForm(instanceForm);
        return this;
    }
    public ApprovalContext withInstanceTableForm(Map<String, String> instanceTableForm) {
        if (this.approval == null) {
            this.approval = new ApprovalDTO();
        }
        this.approval.setInstanceTableForm(instanceTableForm);
        return this;
    }
}
