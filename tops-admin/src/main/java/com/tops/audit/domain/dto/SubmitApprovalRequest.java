package com.tops.audit.domain.dto;

import com.jd.common.web.LoginContext;
import com.tops.audit.enums.ApprovalPlatformEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Getter
@Setter
public class SubmitApprovalRequest implements Serializable {
    /**
     * 序列化UUID
     */
    private static final long serialVersionUID = 372622034606311776L;

    /**
     * 流程编码
     * 示例：sample
     * @see com.tops.audit.enums.XbpProcessEnum
     */
    private String processCode;
    /**
     * 流程所属平台
     */
    private String platform;
    /**
     * 流程实例内容
     */
    private String instanceContent;
    /**
     * 流程实例内容唯一键，用于防止重复操作
     */
    private String instanceContentKey;
    /**
     * 审批实例表单展示内容
     */
    private Map<String, String> instanceForm;
    /**
     * 审批实例表格展示内容
     */
    private Map<String, String> instanceTableForm;
    /**
     * 流程提交用户
     */
    private String operator;
    /**
     * 流程提交时间
     */
    private Date operateTime;

    public static SubmitApprovalRequest getXbpSubmitRequest(String processId, String instanceContent, String instanceContentKey, Map<String, String> instanceForm) {
        SubmitApprovalRequest request = new SubmitApprovalRequest();
        request.setProcessCode(processId);
        request.setPlatform(ApprovalPlatformEnum.XBP.getCode());
        request.setInstanceContent(instanceContent);
        request.setInstanceContentKey(instanceContentKey);
        request.setInstanceForm(instanceForm);
        request.setOperator(LoginContext.getLoginContext().getPin());
        request.setOperateTime(new Date());
        return request;
    }
}
