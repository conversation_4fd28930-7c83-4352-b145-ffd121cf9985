package com.tops.audit.service;

import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

import java.util.List;

/**
 * 审批流服务
 */
public interface ApprovalService {
    /**
     * 提交审批流
     *
     * @throws InternalFailureException   内部处理异常
     * @throws DependencyFailureException 外部依赖异常
     * @throws InvalidRequestException    入参非法
     */
    void submitApproval(SubmitApprovalRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 撤回审批流 TODO wangqin83
     *
     * @throws InternalFailureException   内部处理异常
     * @throws DependencyFailureException 外部依赖异常
     * @throws InvalidRequestException    入参非法
     */
    void revokeApproval(String processInstanceId) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 查询审批流程状态流水 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    void queryApprovalFlow() throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 查询审批列表 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    List<ApprovalDTO> queryApprovals() throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 查询单个审批
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    ApprovalDTO queryApproval(String processInstanceId) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 更新审批实例处理结果 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    void updateApprovalStatus() throws InternalFailureException, DependencyFailureException, InvalidRequestException;
}
