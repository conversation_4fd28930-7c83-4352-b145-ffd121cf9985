package com.tops.audit.service.impl;

import com.alibaba.fastjson.JSON;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.config.ProcessProperties;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpApprovalStatusEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.repository.ApprovalRepository;
import com.tops.audit.service.BaseApprovalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Slf4j
@Component(value = "xbpApprovalService")

public class XbpApprovalService extends BaseApprovalService {
    @Autowired
    @Qualifier("xbpTicketServiceAdapter")
    private TicketServiceAdapter ticketServiceAdapter;

    @Autowired
    private ApprovalRepository approvalRepository;

    @Autowired
    private ProcessProperties processProperties;


    /**
     * 提交审批流
     *
     * @param request
     * @throws InternalFailureException   内部处理异常
     * @throws DependencyFailureException 外部依赖异常
     * @throws InvalidRequestException    入参非法
     */
    @Override
    public void submitApproval(SubmitApprovalRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        //参数合法性校验
        if (request == null) {
            log.warn("XbpApprovalService.submitApproval 提交审批失败，未传入必填参数request");
            throw new InvalidRequestException("XbpApprovalService.submitApproval 提交审批失败，未传入必填参数request");
        }
        if (StringUtils.isBlank(request.getProcessCode())) {
            log.warn("XbpApprovalService.submitApproval 提交审批失败，未传入必填参数request.processId");
            throw new InvalidRequestException("XbpApprovalService.submitApproval 提交审批失败，未传入必填参数request.processId");
        }
        if (XbpProcessEnum.getEnumByCode(request.getProcessCode()) == null) {
            log.warn("XbpApprovalService.submitApproval 提交审批失败，必填参数传值错误：request.processId");
            throw new InvalidRequestException("XbpApprovalService.submitApproval 提交审批失败，必填参数传值错误：request.processId");
        }
        //组建上下文
        ApprovalContext approvalContext = ApprovalContext.of()
            .withPin(request.getOperator())
            .withProcessId(processProperties.getProcessIds().get(request.getProcessCode()))
            .withInstanceForm(request.getInstanceForm())
            .withInstanceTableForm(request.getInstanceTableForm());
        //调用XBP接口提交审批
        String processInstanceId = ticketServiceAdapter.submit(approvalContext);
        //本地保存一份审批记录
        ApprovalDTO approval = new ApprovalDTO();
        approval.setProcessId(processProperties.getProcessIds().get(request.getProcessCode()));
        approval.setProcessDesc(XbpProcessEnum.getEnumByCode(request.getProcessCode()).getDesc());
        approval.setProcessInstanceId(processInstanceId);
        approval.setInstanceStatus(XbpApprovalStatusEnum.PROCESSING.getDesc());
        approval.setPlatform(ApprovalPlatformEnum.XBP.getCode());
        approval.setInstanceContent(request.getInstanceContent());
        approval.setInstanceContentKey(request.getInstanceContentKey());
        approval.setCreateUser(request.getOperator());
        approval.setCreateTime(request.getOperateTime());
        try {
            log.info("XbpApprovalService.submitApproval 插入审批记录，request:{}", JSON.toJSONString(approval));
            approvalRepository.insertApprovals(Collections.singletonList(approval));
            log.info("XbpApprovalService.submitApproval 插入审批记录成功");
        } catch (InvalidRequestException e) {
            log.warn("XbpApprovalService.submitApproval 插入审批记录失败：{}", e.getMsg());
            throw e;
        } catch (Exception e) {
            //TODO wangqin83 本地保存失败时，撤回已提交的XBP审批
            log.error("XbpApprovalService.submitApproval 插入审批记录失败");
            throw e;
        }

    }

    /**
     * 撤回审批流 TODO wangqin83
     *
     * @param processInstanceId
     * @throws InternalFailureException   内部处理异常
     * @throws DependencyFailureException 外部依赖异常
     * @throws InvalidRequestException    入参非法
     */
    @Override
    public void revokeApproval(String processInstanceId) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        throw new InternalFailureException("撤回审批实例功能开发中，敬请期待");
    }

    /**
     * 查询审批流程状态流水 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public void queryApprovalFlow() throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        throw new InternalFailureException("查询审批实例状态流水功能开发中，敬请期待");
    }

    /**
     * 查询审批列表 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public List<ApprovalDTO> queryApprovals() throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        throw new InternalFailureException("查询审批实例功能开发中，敬请期待");
    }

    /**
     * 查询单个审批 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public ApprovalDTO queryApproval(String processInstanceId) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        return approvalRepository.selectApproval(processInstanceId);
    }

    /**
     * 更新审批实例处理结果 TODO wangqin83
     *
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public void updateApprovalStatus() throws InternalFailureException, DependencyFailureException, InvalidRequestException {

    }
}
