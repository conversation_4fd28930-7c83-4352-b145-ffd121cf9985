package com.tops.audit.mq.listener;

import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.client.producer.Producer;
import com.jd.jmq.common.exception.JMQException;
import com.jd.jmq.common.message.Message;
import com.tops.common.config.ducc.CommonConfig;
import com.tops.common.utils.TopsEnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName XbpRetransmitListener
 * @Description 用途
 * @date 2024年09月24日 2:51 PM
 */
@Slf4j
@Component("xbpRetransmitListener")
public class XbpRetransmitListener implements MessageListener {
    @Resource(name = "jmq4producer")
    private Producer producer;

    @Value("${xbp.jmq.producer.retransmit-pre}")
    private String preTopic;

    @Value("${xbp.jmq.producer.retransmit-prod}")
    private String prodTopic;

    @Resource
    TopsEnvUtils topsEnvUtils;

    @Resource
    CommonConfig config;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            this.onMessage(message);
        }
    }

    private void onMessage(Message message) throws JMQException {
        //`businessId`由面板（`dashboardId`）、流程组（`groupId`）、流程（`processId`）、流程配置（`pId`，无用）和申请单（`ticketId`）构成，如`1-1-38-21-201`
        if (message.getBusinessId() == null) {
            return;
        }

        String[] businessList = message.getBusinessId().split("-");
        if (businessList.length < 2) {
            log.error("业务id格式错误, businessId:{}", message.getBusinessId());
            return;
        }

        String processId = businessList[2];
        log.info("流程id为{}, businessId:{}", processId, message.getBusinessId());
        if (topsEnvUtils.isDev()) {
            log.info("测试环境不对xbp做分流");
            message.setTopic(preTopic);
            producer.send(message);
        } else {
            //判断当前流程是否需要处理
            if (config.getPreProcessIds().contains(processId)) {
                log.info("转发流程id到预发环境, processId{}", processId);
                message.setTopic(preTopic);
                producer.send(message);
            } else if (config.getProdProcessIds().contains(processId)) {
                log.info("转发流程id到生产环境, processId{}", processId);
                message.setTopic(prodTopic);
                producer.send(message);
            }
        }

    }
}
