package com.tops.audit.mq.handler;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

public interface XbpMessageHandler {
    default boolean match(Integer processId) {
        return false;
    }

    void handle(Integer processId, Integer instanceId,String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException;
}
