package com.tops.audit.mq.listener;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;

import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.mq.handler.XbpMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * xbp审核消息回调接收
 *
 * <AUTHOR>
 * @date 2024-6-25
 */
@Slf4j
@Component("xbpListener")
public class XbpListener implements MessageListener {

    private final String XBP_MESSAGE_FIELD_BUSINESS_TYPE = "businessType";
    private final String XBP_MESSAGE_FIELD_EVENT_TYPE = "eventType";

    @Autowired
    private List<XbpMessageHandler> xbpMessageHandlers;


    /**
     * 处理单个消息
     *
     * @param message
     * @throws Exception
     */
    public void onMessage(Message message) throws Exception {
        process(message);
    }

    /**
     * 接收xbp消息
     *
     * @param messages
     * @throws Exception
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        if (messages == null || messages.isEmpty()) {
            return;
        }
        for (Message message : messages) {
            this.onMessage(message);
        }
    }

    /**
     * 处理消息
     *
     * @param message
     */
    public void process(Message message) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        //`businessId`由面板（`dashboardId`）、流程组（`groupId`）、流程（`processId`）、流程配置（`pId`，无用）和申请单（`ticketId`）构成，如`1-1-38-21-201`
        String[] businessList = message.getBusinessId().split("-");
        String processId = businessList[2];
        //判断当前流程是否需要处理
        List<XbpMessageHandler> matchedXbpMessageHandlers = xbpMessageHandlers.stream()
            .filter(xbpMessageHandler -> xbpMessageHandler.match(Integer.parseInt(processId)))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(matchedXbpMessageHandlers)) {
            return;
        }
        //解析XBP 消息报文 TODO wangqin83根据businessID实现幂等
        JSONObject jsonObject = JSON.parseObject(message.getText());
        String businessType = jsonObject.getString(XBP_MESSAGE_FIELD_BUSINESS_TYPE);
        if (!Objects.equals("TICKET", businessType)) {
            return;
        }
        String eventType = jsonObject.getString(XBP_MESSAGE_FIELD_EVENT_TYPE);
        XbpEventTypeEnum xbpEventTypeEnum = XbpEventTypeEnum.getEnumByCode(eventType);
        if (xbpEventTypeEnum == null) {
            return;
        }
        //根据流程实例ID进行回调处理
        Integer instanceId = Integer.valueOf(businessList[4]);
        log.info("当前的businessId:{},processId:{},instanceId:{},eventType:{}", message.getBusinessId(), processId, instanceId, eventType);
        matchedXbpMessageHandlers.forEach(xbpMessageHandler -> {
            try {
                xbpMessageHandler.handle(Integer.parseInt(processId), instanceId, eventType);
            } catch (InternalFailureException e) {
                log.error("XbpListener.process 内部服务异常，msg:{},e:", e.getMsg(), e);
                throw new RuntimeException(e);
            } catch (DependencyFailureException e) {
                log.error("XbpListener.process 依赖服务异常，msg:{},e:", e.getMsg(), e);
                throw new RuntimeException(e);
            } catch (InvalidRequestException e) {
                log.error("XbpListener.process 参数非法，msg:{},e:", e.getMsg(), e);
            }
        });
    }

}
