package com.tops.audit.mq.handler.impl;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.mq.handler.XbpMessageHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
//@Component("sampleMessageHandler")
public class SampleMessageHandler implements XbpMessageHandler {
//    @Value("${xbp.custom.processIds.sample}")
    private Integer handledProcessId;

    @Override
    public boolean match(Integer processId) {
        if (processId == null) {
            return false;
        }
        return Objects.equals(handledProcessId, processId);
    }

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        log.info("SampleMessageHandler.handle 模拟XBP回调处理");
        //TODO wangqin83 更新本地审批记录状态
        //TODO wangqin83 按照实际业务逻辑处理请求
    }
}
