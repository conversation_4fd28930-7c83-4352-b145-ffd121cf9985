package com.tops.audit.config;

import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Getter
@Setter
@ConfigurationProperties(prefix = "xbp.custom")
@Configuration
public class ProcessProperties {

    private Map<String, String> processIds = new HashMap<>();
}
