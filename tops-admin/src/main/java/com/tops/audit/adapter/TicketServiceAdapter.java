package com.tops.audit.adapter;

import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;

public interface TicketServiceAdapter {
    /**
     * 提交审批
     * @param context 审批上下文
     * @return 提交结果
     * @throws InternalFailureException 内部错误异常
     * @throws DependencyFailureException 依赖关系失败异常
     * @throws InvalidRequestException 无效请求异常
     */
    String submit(ApprovalContext context) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

    /**
     * 提交审批
     * @param context 审批上下文
     * @return 提交结果
     * @throws InternalFailureException 内部错误异常
     * @throws DependencyFailureException 依赖关系失败异常
     * @throws InvalidRequestException 无效请求异常
     */
    void comment(ApprovalContext context)throws InternalFailureException, DependencyFailureException, InvalidRequestException;

}
