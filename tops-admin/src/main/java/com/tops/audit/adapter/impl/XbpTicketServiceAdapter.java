package com.tops.audit.adapter.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.jd.xbp.jsf.api.TicketService;
import com.jd.xbp.jsf.api.request.ticket.CommentParam;
import com.jd.xbp.jsf.api.request.ticket.CreateParam;
import com.jd.xbp.jsf.api.response.XbpResponse;
import com.jd.xbp.jsf.api.response.ticket.Ticket;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("xbpTicketServiceAdapter")
public class XbpTicketServiceAdapter implements TicketServiceAdapter {

    @Autowired
    private TicketService ticketService;

    /**
     * 提交审批
     *
     * @param context 审批上下文
     * @return 提交结果
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖关系失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public String submit(ApprovalContext context) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (context == null || context.getApproval() == null) {
            log.warn("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval");
        }
        if (StringUtils.isBlank(context.getApproval().getProcessId())) {
            log.warn("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval.processId");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval.processId");
        }
        if (context.getOperator() == null) {
            log.warn("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator");
        }
        if (StringUtils.isBlank(context.getOperator().getPin())) {
            log.warn("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator.pin");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator.pin");
        }
        CreateParam createParam = new CreateParam();
        try {
            createParam.setProcessId(Integer.parseInt(context.getApproval().getProcessId()));
        } catch (NumberFormatException e) {
            log.warn("XbpTicketServiceAdapter.submit 提交审批失败，必填参数context.approval.processId应为数字类型");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，必填参数context.approval.processId应为数字类型");
        }
        createParam.setUsername(context.getOperator().getPin());
        Map<String, Object> applicationInfo = new HashMap<>();
        if (!CollectionUtils.isEmpty(context.getApproval().getInstanceForm())) {
            applicationInfo.putAll(context.getApproval().getInstanceForm());
        }
        createParam.setApplicationInfo(applicationInfo);

        Map<String, CreateParam.TableInfo> tableInfoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(context.getApproval().getInstanceTableForm())) {
            Map<String, String> tableForm = context.getApproval().getInstanceTableForm();
            tableForm.forEach((k, v) -> {
                String s = tableForm.get(k);
                List<Map<String, String>> data = JSON.parseObject(s, new TypeReference<List<Map<String, String>>>() {
                });
                CreateParam.TableInfo table = new CreateParam.TableInfo();
                table.setData(data);
                tableInfoMap.put(k, table);
            });
        }
        createParam.setTableInfo(tableInfoMap);
        XbpResponse<Integer> response = null;
        try {
            response = ticketService.create(createParam);
        } catch (Exception e) {
            log.error("XbpTicketServiceAdapter.submit 提交审批失败，request:{},msg:{},exception:", JSON.toJSONString(createParam), e.getMessage(), e);
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 提交审批失败，调用出现异常");
        }
        if (response == null || response.getData() == null) {
            log.error("XbpTicketServiceAdapter.submit 提交审批失败，request:{},response:{}", JSON.toJSONString(createParam), JSON.toJSONString(response));
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 提交审批失败，接口返回错误");
        }
        log.info("XbpTicketServiceAdapter.submit 提交审批成功，request:{},response:{}", JSON.toJSONString(createParam), JSON.toJSONString(response));
        return response.getData().toString();
    }

    /**
     * 提交审批
     *
     * @param context 审批上下文
     * @return 提交结果
     * @throws InternalFailureException   内部错误异常
     * @throws DependencyFailureException 依赖关系失败异常
     * @throws InvalidRequestException    无效请求异常
     */
    @Override
    public void comment(ApprovalContext context) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (context == null || context.getApproval() == null) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.approval");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval");
        }
        if (StringUtils.isBlank(context.getApproval().getProcessInstanceId())) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.approval.processInstanceId");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval.processInstanceId");
        }
        if (StringUtils.isBlank(context.getApproval().getComment())) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.approval.comment");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval.comment");
        }
        if (context.getOperator() == null) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.operator");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator");
        }
        if (StringUtils.isBlank(context.getOperator().getPin())) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.operator.pin");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.operator.pin");
        }
        XbpResponse<Integer> response = null;
        CommentParam commentParam = new CommentParam();

        try {
            commentParam.setTicketId(Integer.parseInt(context.getApproval().getProcessInstanceId()));
        } catch (NumberFormatException e) {
            log.warn("XbpTicketServiceAdapter.submit 提交评论失败，必填参数context.approval.processInstanceId应为数字类型");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交评论失败，必填参数context.approval.processInstanceId应为数字类型");
        }
        commentParam.setUsername(context.getOperator().getPin());
        commentParam.setComment(context.getApproval().getComment());
        try {
            response = ticketService.comment(commentParam);
        } catch (Exception e) {
            log.error("XbpTicketServiceAdapter.submit 提交评论失败，request:{},msg:{},exception:", JSON.toJSONString(commentParam), e.getMessage(), e);
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 提交评论失败，调用出现异常");
        }
        if (response == null || response.getData() == null) {
            log.error("XbpTicketServiceAdapter.submit 提交评论失败，request:{},response:{}", JSON.toJSONString(commentParam), JSON.toJSONString(response));
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 提交评论失败，接口返回错误");
        }
        log.info("XbpTicketServiceAdapter.submit 提交评论成功，request:{},response:{}", JSON.toJSONString(commentParam), JSON.toJSONString(response));
    }

    @Override
    public void get(ApprovalContext context) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (context == null || context.getApproval() == null) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.approval");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval");
        }
        if (StringUtils.isBlank(context.getApproval().getProcessInstanceId())) {
            log.warn("XbpTicketServiceAdapter.comment 提交评论失败，未传入必填参数context.approval.processInstanceId");
            throw new InvalidRequestException("XbpTicketServiceAdapter.submit 提交审批失败，未传入必填参数context.approval.processInstanceId");
        }
        XbpResponse<Ticket> response = null;
        Integer instanceId = null;
        try {
            instanceId = Integer.parseInt(context.getApproval().getProcessInstanceId());
            response = ticketService.get(instanceId);
            log.info("XbpTicketServiceAdapter.submit 查询表单详情成功，request:{},response:{}", JSON.toJSONString(instanceId), JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("XbpTicketServiceAdapter.submit 查询表单详情失败，request:{},msg:{},exception:", JSON.toJSONString(instanceId), e.getMessage(), e);
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 查询表单详情失败，调用出现异常");
        }
        if (response == null || response.getData() == null) {
            log.error("XbpTicketServiceAdapter.submit 查询表单详情失败，request:{},response:{}", JSON.toJSONString(instanceId), JSON.toJSONString(response));
            throw new DependencyFailureException("XbpTicketServiceAdapter.submit 查询表单详情失败，接口返回错误");
        }
        Ticket data = response.getData();
        //提交人ERP
        String submitERP = data.getUsername();
        //表单基础字段
        List<Ticket.ApplicationInfo> applicationInfos = data.getApplicationInfo();
        //表单表格字段
        List<Ticket.TableInfo> tableInfos = data.getTableInfo();

        context.withPin(submitERP);
        Map<String, String> instanceForm = new HashMap<>();
        for (Ticket.ApplicationInfo applicationInfo : applicationInfos) {
            instanceForm.put(applicationInfo.getItemName(), applicationInfo.getItemValue());
        }
        context.withInstanceForm(instanceForm);
        Map<String, String> instanceTableForm = new HashMap<>();
        for (Ticket.TableInfo tableInfo : tableInfos) {
            instanceTableForm.put(tableInfo.getName(), JSON.toJSONString(tableInfo.getData()));
        }
        context.withInstanceTableForm(instanceTableForm);
        log.info("XbpTicketServiceAdapter.submit 查询表单详情成功，转换后 context:{}", JSON.toJSONString(context));

    }
}
