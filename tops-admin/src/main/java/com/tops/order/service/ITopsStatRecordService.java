package com.tops.order.service;

import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.order.domain.bo.TopsStatRecordBo;
import com.tops.order.domain.vo.TopsChartData;
import com.tops.order.domain.vo.TopsStatRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单统计Service接口
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
public interface ITopsStatRecordService {

    /**
     * 查询订单统计
     */
    TopsStatRecordVo queryById(Long id);

    /**
     * 查询订单统计列表
     */
    TableDataInfo<TopsStatRecordVo> queryPageList(TopsStatRecordBo bo, PageQuery pageQuery);

    /**
     * 查询订单统计列表
     */
    List<TopsStatRecordVo> queryList(TopsStatRecordBo bo);

    /**
     * 新增订单统计
     */
    Boolean insertByBo(TopsStatRecordBo bo);

    /**
     * 修改订单统计
     */
    Boolean updateByBo(TopsStatRecordBo bo);

    /**
     * 校验并批量删除订单统计信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 渲染图表
     */
    TopsChartData renderOrderStatChart(TopsStatRecordBo bo);

    /**
     * 渲染图表
     */
    TopsChartData renderOrderFailStatChart(TopsStatRecordBo bo);

    /**
     * 渲染图表
     */
    TopsChartData orderFailCustomerStatChart(TopsStatRecordBo bo);    /**
     * 渲染图表
     */
    TopsChartData orderSuccessCustomerStatChart(TopsStatRecordBo bo);


}
