package com.tops.order.service;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.vo.WarehouseVO;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName EclpBasicDataQueryService
 * @Description eclp主数据查询
 * @date 2024年08月22日 8:48 PM
 */
public interface EclpBasicDataQueryService {
    /**
     * 查询仓基础信息
     * @param warehouseNo
     * @return
     */
    public WarehouseVO getWarehouse(String warehouseNo) throws DependencyFailureException, InternalFailureException, InvalidRequestException;

    /**
     * 查询仓基础信息
     * @param warehouseName
     * @return
     */
    public List<WarehouseVO> getWarehouseByWarehouseName(String warehouseName) throws DependencyFailureException, InternalFailureException, InvalidRequestException;
}
