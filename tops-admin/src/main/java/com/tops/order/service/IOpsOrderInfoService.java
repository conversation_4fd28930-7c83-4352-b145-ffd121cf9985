package com.tops.order.service;

import cn.jdl.oms.search.dto.Order;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.order.domain.bo.OpsOrderDetailBo;
import com.tops.order.domain.bo.OpsOrderDetailDecryptBo;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.vo.OpsOrderDetailDecryptVo;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.domain.vo.OrderModifyRecordsVO;

import java.util.Collection;
import java.util.List;

/**
 * 订单信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
public interface IOpsOrderInfoService {

    /**
     * 查询订单信息
     */
    OpsOrderInfoVo queryOrderDetail(OpsOrderInfoBo bo);

    /**
     * 订单信息解密
     * @param bo
     * @return OpsOrderDetailDecryptVo
     */
    OpsOrderDetailDecryptVo decryptOrderDetail(OpsOrderDetailDecryptBo bo);

    /**
     * 查询订单信息列表
     */
    TableDataInfo<OpsOrderInfoVo> queryPageList(OpsOrderInfoBo bo, PageQuery pageQuery);

    /**
     * 查询订单信息列表
     */
    List<OpsOrderInfoVo> queryList(OpsOrderInfoBo bo);

    /**
     * 新增订单信息
     */
    Boolean insertByBo(OpsOrderInfoBo bo);

    /**
     * 修改订单信息
     */
    Boolean updateByBo(OpsOrderInfoBo bo);

    /**
     * 校验并批量删除订单信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询订单信息
     */
    Order queryOrder(OpsOrderInfoBo bo);

    /**
     * 查询订单修改记录，入参订单号
     */
    List<OrderModifyRecordsVO> orderModifyRecords(OpsOrderDetailBo bo);
}
