package com.tops.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.tops.common.config.ducc.OrderStatusMonitorConfig;
import com.tops.common.config.ducc.OrderStatusMonitorConfigItem;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.order.constant.OrderStatusConstant;
import com.tops.order.domain.dto.OrderStatusCountDTO;
import com.tops.order.domain.dto.OrderStatusFlowQO;
import com.tops.order.domain.po.OrderStatusFlow;
import com.tops.order.domain.po.OrderStatusSnapshot;
import com.tops.order.domain.vo.AggOrderStatusRequest;
import com.tops.order.domain.vo.OrderStatusCountListVO;
import com.tops.order.domain.vo.OrderStatusCountVO;
import com.tops.order.domain.vo.OrderStatusMonitorVO;
import com.tops.order.domain.vo.QueryOrderStatusRequest;
import com.tops.order.enums.OrderStatusErrLevelEnum;
import com.tops.order.mapper.OrderStatusFlowMapper;
import com.tops.order.mapper.OrderStatusSnapshotMapper;
import com.tops.order.service.OrderReportService;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jose4j.json.internal.json_simple.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class OrderReportServiceImpl implements OrderReportService {
    @Resource
    OrderStatusFlowMapper orderStatusFlowMapper;

    @Resource
    OrderStatusSnapshotMapper orderStatusSnapshotMapper;

    @Resource
    OrderStatusMonitorConfig orderStatusMonitorConfig;

    /**
     * 查询订单状态(不考虑业务身份)
     *
     * @param queryOrderStatusRequest
     * @return
     */
    public List<OrderStatusCountListVO> getOrderStatus(QueryOrderStatusRequest queryOrderStatusRequest) throws InternalFailureException {
        List<OrderStatusCountListVO> result = new ArrayList<>();
        try {
            log.info("OrderReportServiceImpl.getOrderStatus queryOrderStatusRequest.queryTime:{}",JSON.toJSONString(queryOrderStatusRequest.getQueryTime()));
            List<String> businessUnitList = new ArrayList<>();
            if(StringUtils.isNotEmpty(queryOrderStatusRequest.getBusinessUnit()) &&
                !OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL.equals(queryOrderStatusRequest.getBusinessUnit())) {
                businessUnitList.add(queryOrderStatusRequest.getBusinessUnit());
            }

            //列表查询
            List<OrderStatusSnapshot> snapshotList = orderStatusSnapshotMapper.getOrderStatusSnapshotByUniqueKey(businessUnitList, queryOrderStatusRequest.getQueryTime());
            if (CollectionUtils.isEmpty(snapshotList)) {
                log.warn("获取订单快照数据为空(orderStatusSnapshotMapper.getOrderStatusSnapshotByUniqueKey), 业务身份={}, 查询时间={}", queryOrderStatusRequest.getBusinessUnit(),
                    queryOrderStatusRequest.getQueryTime());
                return result;
            }

            List<OrderStatusSnapshot> snapListWithBusinessUnit = snapshotList.stream().filter(orderStatusSnapshot ->
                !OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL.equals(orderStatusSnapshot.getBusinessUnit())).collect(Collectors.toList());
            /**map<orderstatus, List<OrderStatusCountVO>**/
            Map<String, List<OrderStatusCountVO>> businessStatusMap = new HashMap<>();
            for(OrderStatusSnapshot orderStatusSnapshot : snapListWithBusinessUnit) {
                List<OrderStatusCountVO> orderStatusCountVOS = JSON.parseArray(orderStatusSnapshot.getText(), OrderStatusCountVO.class);
                for(OrderStatusCountVO orderStatusCountVO : orderStatusCountVOS) {
                    if(!businessStatusMap.containsKey(orderStatusCountVO.getCustomOrderStatus())) {
                        businessStatusMap.put(orderStatusCountVO.getCustomOrderStatus(), new ArrayList<>(10));
                    }

                    businessStatusMap.get(orderStatusCountVO.getCustomOrderStatus()).add(orderStatusCountVO);
                }
            }

            //列表展示总量，鼠标悬浮之后展示具体信息
            Optional<OrderStatusSnapshot> baseVoOptional = snapshotList.stream().filter(orderStatusSnapshot ->
                OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL.equals(orderStatusSnapshot.getBusinessUnit())).findFirst();

            if(baseVoOptional.isPresent()) {
                OrderStatusSnapshot baseVo = baseVoOptional.get();
                List<OrderStatusCountVO> baseOrderStatusVOList = JSON.parseArray(baseVo.getText(), OrderStatusCountVO.class);
                //如果ALL不存在,默认的配置也需要展示
                Map<String, OrderStatusCountVO> baseOrderStatusMap = baseOrderStatusVOList.stream().collect(Collectors.toMap(
                    OrderStatusCountVO::getCustomOrderStatus, Function.identity(), (v1,v2) -> v1
                ));

                for(String customOrderStatus : orderStatusMonitorConfig.getRecordCustomOrderStatusSet()) {
                    OrderStatusCountListVO orderStatusCountListVO = new OrderStatusCountListVO();
                    OrderStatusCountVO baseOrderStatusVO = baseOrderStatusMap.get(customOrderStatus);
                    orderStatusCountListVO.setCustomOrderStatus(customOrderStatus);
                    if(baseOrderStatusVO != null) {
                        BeanUtils.copyProperties(baseOrderStatusVO, orderStatusCountListVO);
                        orderStatusCountListVO.setOrderStatusCountVOList(businessStatusMap.get(baseOrderStatusVO.getCustomOrderStatus()));
                    }

                    OrderStatusMonitorConfigItem configItem = orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig()
                        .get(customOrderStatus);
                    if(configItem != null) {
                        orderStatusCountListVO.setYellowExceptionCountConfig(configItem.getRetentionYellowUpperLimit());
                        orderStatusCountListVO.setRedExceptionCountConfig(configItem.getRetentionRedUpperLimit());
                        orderStatusCountListVO.setYellowTimeoutConfig(configItem.getTimeoutYellow());
                        orderStatusCountListVO.setRedTimeoutConfig(configItem.getTimeoutRed());
                    }

                    result.add(orderStatusCountListVO);
                }
            }
        } catch (Exception ex) {
            log.error("查询订单数据异常", ex);
            throw new InternalFailureException("列表查询异常, 请联系管理员或忽略");
        }

        return result;
    }


    /**
     * 每分钟记录快照数据
     * @param aggOrderStatusRequest
     */
    public void recordSnapshot(AggOrderStatusRequest aggOrderStatusRequest) {
        log.info("recordSnapshot start");
        try {
            List<OrderStatusCountVO> orderStatusCountVOS = aggOrderSnapshot(aggOrderStatusRequest);
            Map<String, List<OrderStatusCountVO>> orderStatusBusinessUnitCountVOSMap = orderStatusCountVOS.stream()
                .collect(Collectors.groupingBy(OrderStatusCountVO::getBusinessUnit));
            for(String businessUnit : orderStatusBusinessUnitCountVOSMap.keySet()) {
                OrderStatusSnapshot orderStatusSnapshot = new OrderStatusSnapshot();
                orderStatusSnapshot.setBusinessUnit(businessUnit);
                orderStatusSnapshot.setText(JSON.toJSONString(orderStatusBusinessUnitCountVOSMap.get(businessUnit)));
                orderStatusSnapshot.setSnapshotTime(aggOrderStatusRequest.getQueryTime());
                orderStatusSnapshot.setYn(1);
                orderStatusSnapshot.setTs(new Date());
                log.info("insert record snapshot:{}", JSON.toJSONString(orderStatusSnapshot));
                orderStatusSnapshotMapper.insertOrderStatusSnapshot(orderStatusSnapshot);
            }

            //汇总ALL的数据, 避免查询数据库两次，千万级数据聚合查询接近30s
            Map<String, List<OrderStatusCountVO>> statusCountVOSMap = orderStatusCountVOS.stream()
                .collect(Collectors.groupingBy(OrderStatusCountVO::getCustomOrderStatus));
            List<OrderStatusCountVO> orderStatusCountVOList = new ArrayList<>();
            for(String orderStatus : statusCountVOSMap.keySet()) {
                List<OrderStatusCountVO> statusCountVOList = statusCountVOSMap.get(orderStatus);
                OrderStatusCountVO allOrderStatusCountVO = new OrderStatusCountVO();
                allOrderStatusCountVO.setBusinessUnit(OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL);
                allOrderStatusCountVO.setCustomOrderStatus(orderStatus);
                allOrderStatusCountVO.setTotalCount(0);
                allOrderStatusCountVO.setYellowExceptionCount(0);
                allOrderStatusCountVO.setRedExceptionCount(0);
                for(OrderStatusCountVO orderStatusCountVO : statusCountVOList) {
                    allOrderStatusCountVO.setTotalCount(orderStatusCountVO.getTotalCount() + allOrderStatusCountVO.getTotalCount());
                    allOrderStatusCountVO.setYellowExceptionCount(orderStatusCountVO.getYellowExceptionCount() + allOrderStatusCountVO.getYellowExceptionCount());
                    allOrderStatusCountVO.setRedExceptionCount(orderStatusCountVO.getRedExceptionCount() + allOrderStatusCountVO.getRedExceptionCount());
                }

                allOrderStatusCountVO.setExceptionStatus(getRetentionStatus(allOrderStatusCountVO));
                orderStatusCountVOList.add(allOrderStatusCountVO);
            }

            //增加报警
            alarmCheck(orderStatusCountVOList);
            OrderStatusSnapshot allOrderStatusSnapshot = new OrderStatusSnapshot();
            allOrderStatusSnapshot.setBusinessUnit(OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL);
            allOrderStatusSnapshot.setYn(1);
            allOrderStatusSnapshot.setTs(new Date());
            allOrderStatusSnapshot.setSnapshotTime(aggOrderStatusRequest.getQueryTime());
            allOrderStatusSnapshot.setText(JSON.toJSONString(orderStatusCountVOList));
            log.info("insert record snapshot:{}", JSON.toJSONString(allOrderStatusSnapshot));
            orderStatusSnapshotMapper.insertOrderStatusSnapshot(allOrderStatusSnapshot);
        } catch (Exception ex) {
            if(ex instanceof DuplicateKeyException) {
                log.info("唯一索引冲突,忽略");
                return;
            }

            log.error("recordSnapshot failed", ex);
            throw ex;
        }
    }

    @Override
    public void backupOrderStatus(Date createTime) throws InternalFailureException {
        log.info("backup order status flow start");
        try {
            orderStatusFlowMapper.deleteByCreateTime(createTime);
        } catch (Exception ex) {
            log.error("订单状态数据结转失败", ex);
            throw new InternalFailureException("订单状态数据结转失败");
        }
    }

    /**
     * 聚合订单快找信息
     */
    private List<OrderStatusCountVO> aggOrderSnapshot(AggOrderStatusRequest aggOrderStatusRequest) {
        List<OrderStatusCountVO> result = new ArrayList<>();
        //看板查询，展示红绿灯
        log.info("OrderReportServiceImpl.getOrderStatus queryOrderStatusRequest.aggOrderStatusRequest:{}",JSON.toJSONString(aggOrderStatusRequest));
        Date queryTime = aggOrderStatusRequest.getQueryTime();
        Date receivedTime = TopsDateUtils.getStartDate(TopsDateUtils.add(queryTime, -3, Calendar.DATE));
        OrderStatusFlowQO orderStatusFlowQo = new OrderStatusFlowQO();
        orderStatusFlowQo.setCreateTime(receivedTime);
        if(!aggOrderStatusRequest.getBusinessUnitList().contains(OrderStatusConstant.ORDER_STATUS_BUSINESS_UNIT_ALL)) {
            orderStatusFlowQo.setBusinessUnitList(aggOrderStatusRequest.getBusinessUnitList());
        }

        List<OrderStatusCountDTO> totalCount = getOrderStatusCounts(orderStatusFlowQo);
        log.info("OrderReportServiceImpl.getOrderStatus totalCount:{}",JSON.toJSONString(totalCount));
        orderStatusFlowQo.setNextStatusTime(queryTime);
        List<OrderStatusCountDTO> yellowExceptionCount = getOrderStatusCounts(orderStatusFlowQo);
        orderStatusFlowQo.setNextStatusTime(null);
        orderStatusFlowQo.setNextStatusTimeRed(queryTime);
        List<OrderStatusCountDTO> redExceptionCount = getOrderStatusCounts(orderStatusFlowQo);

        log.info("OrderReportServiceImpl.getOrderStatus yellowCount:{}, redCount:{}",JSON.toJSONString(yellowExceptionCount),
            JSON.toJSONString(redExceptionCount));
        Map<String, Integer> exceptionYellowCountMap = yellowExceptionCount.stream().collect(Collectors.toMap(OrderStatusCountDTO::getUniqueKey, OrderStatusCountDTO::getCount));
        Map<String, Integer> exceptionRedCountMap = redExceptionCount.stream().collect(Collectors.toMap(OrderStatusCountDTO::getUniqueKey, OrderStatusCountDTO::getCount));
        result = totalCount.stream().map(orderStatusCountDTO -> {
            OrderStatusCountVO orderStatusCountVO = new OrderStatusCountVO();
            orderStatusCountVO.setTotalCount(orderStatusCountDTO.getCount());
            orderStatusCountVO.setBusinessUnit(orderStatusCountDTO.getBusinessUnit());
            orderStatusCountVO.setCustomOrderStatus(orderStatusCountDTO.getOrderStatusCustom());
            orderStatusCountVO.setYellowExceptionCount(Optional.ofNullable(exceptionYellowCountMap.get(orderStatusCountDTO.getUniqueKey())).orElse(0));
            orderStatusCountVO.setRedExceptionCount(Optional.ofNullable(exceptionRedCountMap.get(orderStatusCountDTO.getUniqueKey())).orElse(0));
            return orderStatusCountVO;
        }).collect(Collectors.toList());
        return result;
    }

    /**
     * @param orderStatusCountVO
     * @return
     */
    public String getRetentionStatus(OrderStatusCountVO orderStatusCountVO) {
        OrderStatusMonitorConfigItem config = orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig().get(orderStatusCountVO.getCustomOrderStatus());
        if(config == null) {
            log.info("没有配置相关状态告警,跳过检查{}", JSON.toJSONString(orderStatusCountVO));
            return OrderStatusErrLevelEnum.GREEN.getCode();
        }

        if (orderStatusCountVO.getRedExceptionCount() > config.getRetentionRedUpperLimit()) {
            return OrderStatusErrLevelEnum.RED.getCode();
        } else if (orderStatusCountVO.getYellowExceptionCount() > config.getRetentionYellowUpperLimit()) {
            return OrderStatusErrLevelEnum.YELLOW.getCode();
        } else {
            return OrderStatusErrLevelEnum.GREEN.getCode();
        }
    }

    /**
     * 检查是否触发告警
     * @param orderStatusCountVOS
     */
    private void alarmCheck(List<OrderStatusCountVO> orderStatusCountVOS) {
        for(OrderStatusCountVO orderStatusCountVO : orderStatusCountVOS) {
            OrderStatusMonitorConfigItem configItem = orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig()
                .get(orderStatusCountVO.getCustomOrderStatus());
            if(configItem == null) {
                log.info("没有配置滞留监控,跳过检查,vo:{}", JSON.toJSONString(orderStatusCountVO));
                continue;
            }

            if(OrderStatusErrLevelEnum.YELLOW.getCode().equals(orderStatusCountVO.getExceptionStatus())
                || OrderStatusErrLevelEnum.RED.getCode().equals(orderStatusCountVO.getExceptionStatus())) {
                OrderStatusMonitorVO orderStatusMonitorVO = new OrderStatusMonitorVO();
                orderStatusMonitorVO.setAlarmTime(new Date());
                orderStatusMonitorVO.setBusinessUnit(orderStatusCountVO.getBusinessUnit());
                orderStatusMonitorVO.setCustomOrderStatus(orderStatusCountVO.getCustomOrderStatus());
                orderStatusMonitorVO.setCurrYellowRetentionCount(orderStatusCountVO.getYellowExceptionCount());
                orderStatusMonitorVO.setCurrRedRetentionCount(orderStatusCountVO.getRedExceptionCount());
                orderStatusMonitorVO.setRedRetentionUpperLimit(configItem.getRetentionRedUpperLimit());
                orderStatusMonitorVO.setYellowRetentionUpperLimit(configItem.getRetentionYellowUpperLimit());
                orderStatusMonitorVO.setCustomStatusTotalCount(orderStatusCountVO.getTotalCount());
                log.error("订单滞留量超过报警阈值,触发告警{}", JSON.toJSONString(orderStatusMonitorVO));
            }
        }
    }

    private List<OrderStatusCountDTO> getOrderStatusCounts(OrderStatusFlowQO orderStatusFlowQo) {
        return CollectionUtils.isNotEmpty(orderStatusFlowQo.getBusinessUnitList()) ?
            orderStatusFlowMapper.getOrderStatusCountsByBusinessUnit(orderStatusFlowQo) :
            orderStatusFlowMapper.getAllOrderStatusCounts(orderStatusFlowQo);
    }
}
