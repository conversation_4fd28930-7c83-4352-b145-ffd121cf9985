package com.tops.order.service;

import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.vo.OpsCateStatVO;
import com.tops.order.domain.vo.OpsKVStatVO;
import com.tops.order.domain.vo.TopsOrderRequestVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单请求信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
public interface ITopsOrderRequestService {

    /**
     * 查询订单请求信息
     */
    TopsOrderRequestVo queryById(Long id);

    /**
     * 查询订单统计数量
     */
    Map<String, Integer> statisticsPanel(TopsOrderRequestBo bo);

    /**
     * 查询订单请求的数量
     */
    List<OpsKVStatVO> queryCount(String groupKey, TopsOrderRequestBo bo);

    /**
     * 查询订单请求的数量
     */
    List<OpsCateStatVO> queryCateCount(String groupKey, TopsOrderRequestBo bo);

    /**
     * 查询订单请求信息列表
     */
    TableDataInfo<TopsOrderRequestVo> queryPageList(TopsOrderRequestBo bo, PageQuery pageQuery);

    /**
     * 查询订单请求信息列表
     */
    List<TopsOrderRequestVo> queryList(TopsOrderRequestBo bo);


    /**
     * 根据客户单号查询请求记录集合
     *
     * @param bo
     * @return
     */
    List<TopsOrderRequestVo> queryRecordsByCustomerOrderNo(TopsOrderRequestBo bo);


    /**
     * 新增订单请求信息
     */
    Boolean insertByBo(TopsOrderRequestBo bo);

    /**
     * 修改订单请求信息
     */
    Boolean updateByBo(TopsOrderRequestBo bo);

    /**
     * 校验并批量删除订单请求信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
