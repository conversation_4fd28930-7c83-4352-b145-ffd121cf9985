package com.tops.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.service.ApprovalService;
import com.tops.order.adapter.CallbackOutboundOrderServiceAdapter;
import com.tops.order.adapter.ModifyOutboundOrderServiceAdapter;
import com.tops.order.domain.dto.CancelOrderApprovalContent;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.vo.CallbackOrderRequest;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.domain.vo.ModifyOrderRequest;
import com.tops.order.domain.vo.ReacceptOrderRequest;
import com.tops.order.service.OrderOperationService;
import com.tops.order.translator.xbp.XbpTranslator;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;

/**
 * 订单更新服务
 */
@Slf4j
@Component
public class OrderOperationServiceImpl implements OrderOperationService {
    /**
     * 每次最大可取消订单数
     */
    private final static Integer MAX_SIZE_CANCEL_ORDER_NOS = 50;

    @Autowired
    private ApprovalService approvalService;

    /**
     * 各工单转换器
     */
    @Resource
    List<XbpTranslator> xbpTranslators;


    /**
     * @param request 包含取消订单信息的请求对象，不能为null，并且必须包含要取消的订单号列表
     * @function 取消订单的方法
     * @returns [无返回值]
     */
    @Override
    public void cancelOrders(CancelOrdersRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (request == null) {
            log.warn("OrderOperationServiceImpl.cancelOrders 入参非法 request不可为null");
            throw new InvalidRequestException("request不可为null");
        }
        if (CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationServiceImpl.cancelOrders 入参非法 request.orderNos不可为空");
            throw new InvalidRequestException("请选择要取消的订单");
        }
        if (StringUtils.isBlank(request.getCancelType())) {
            log.warn("OrderOperationServiceImpl.cancelOrders 入参非法 request.cancelType不可为空");
            throw new InvalidRequestException("请选择要对订单进行的操作");
        }
        //每单审批表格只能有50行
        if (request.getOrderNos().size() > MAX_SIZE_CANCEL_ORDER_NOS) {
            log.warn(String.format("OrderOperationServiceImpl.cancelOrders 入参非法 request.orderNos最大数量为%s", MAX_SIZE_CANCEL_ORDER_NOS));
            throw new InvalidRequestException(String.format("选择订单数过多，每次最多可以取消订单数：%s", MAX_SIZE_CANCEL_ORDER_NOS));
        }
        //提交取消审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.CANCEL_ORDERS_PROCESS.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    @Override
    public void callbackOrders(CallbackOrderRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        //提交取消审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.CALLBACK_ORDERS_PROCESS.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    @Override
    public void modifyOrders(ModifyOrderRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        //提交取消审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.MODIFY_ORDERS_PROCESS.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    @Override
    public void reaccpetOrders(ReacceptOrderRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        //提交取消审批
        XbpTranslator xbpTranslator = getTranslator(XbpProcessEnum.REACCEPT_ORDERS_PROCESS.getCode());
        SubmitApprovalRequest approvalRequest = xbpTranslator.getXbpRequest(request);
        approvalService.submitApproval(approvalRequest);
    }

    private XbpTranslator getTranslator(String processCode) {
        return xbpTranslators.stream().filter(xbpTranslator -> xbpTranslator.isMatch(processCode)).findFirst().get();
    }
}
