package com.tops.order.service;

import com.tops.order.domain.bo.TopsOrderStatusBo;
import com.tops.order.domain.vo.TopsOrderStatusVo;

import java.util.List;

/**
 * ClassName:ITopsOrderStatusService
 * Package:com.tops.order.service
 * Description:
 *
 * @date:2024/8/6 下午8:34
 * @author:WeiLiming
 */
public interface ITopsOrderStatusService {
    /**
     * 查询订单状态
     * @param orderStatusBo
     * @return
     */
    public List<TopsOrderStatusVo> queryOrderStatusList(TopsOrderStatusBo orderStatusBo);

    /**
     * 查询订单标准状态
     * @param orderStatusBo
     * @return
     */
    public List<TopsOrderStatusVo> queryOrderStandardStatusList(TopsOrderStatusBo orderStatusBo);


    /**
     * 查询运单全程跟踪
     */
    public List<TopsOrderStatusVo> queryWaybillTracking(TopsOrderStatusBo orderStatusBo);

}
