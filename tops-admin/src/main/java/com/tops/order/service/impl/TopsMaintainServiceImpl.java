package com.tops.order.service.impl;

import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.jdl.oms.search.dto.Order;
import com.jd.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookResponse;
import com.tops.autobots.service.AutoBotsAsyncMessageService;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.utils.DateUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.TopsProfilerUtils;
import com.tops.order.adapter.CallbackOutboundOrderServiceAdapter;
import com.tops.order.adapter.TopsLogBookAdaptor;
import com.tops.order.adapter.TopsOrderRequestRecordAdapter;
import com.tops.order.adapter.TopsQueryOrderDetailAdapter;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import com.tops.order.service.ITopsMaintainService;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TopsMaintainServiceImpl
 * @Description 用途
 * @date 2024年08月27日 6:01 PM
 */
@Service("topsMaintainServiceImpl")
@Slf4j
public class TopsMaintainServiceImpl implements ITopsMaintainService {
    @Resource
    CallbackOutboundOrderServiceAdapter callbackOutboundOrderServiceAdapter;

    @Autowired
    private RestHighLevelClient client;

    @Resource
    private TopsQueryOrderDetailAdapter topsQueryOrderDetailAdapter;

    @Resource
    private TopsOrderRequestRecordAdapter topsOrderRequestRecordAdapter;

    @Resource
    private TopsLogBookAdaptor topsLogBookAdaptor;

    @Resource
    AutoBotsAsyncMessageService autoBotsAsyncMessageService;

    public static final ExecutorService GET_LOG_EXECUTOR = new ThreadPoolExecutor(
        10,
        10,
        1,
        TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
        new ThreadFactoryBuilder().setNamePrefix("tops-getLog-").build(),
        new ThreadPoolExecutor.CallerRunsPolicy());
    @Override
    public void resendCallbackRequest() throws Exception {
        callbackOutboundOrderServiceAdapter.resendCallbackRecord();
    }

    /**
     * 临时方案,查询取消一年前订单请求,后续转换成运维工具
     */
    @Override
    public void getCancelHistoryOrderRequest(Date beginTime, Date endTime) throws Exception {
        try {
            log.info("开始查询取消请求, beginTime:{}, endTime{}", beginTime, endTime);
            String[] indexes = TopsOrderRequestRecordAdapter.generateIndex(DateUtils.dateTime(beginTime), DateUtils.dateTime(endTime));
            SearchRequest searchRequest = new SearchRequest(indexes);
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            TermsQueryBuilder businessSceneBuilder = new TermsQueryBuilder("businessScene", "cancel", "cancelCallback");
//            TermsQueryBuilder businessSceneBuilder = new TermsQueryBuilder("businessScene", "callback");
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("createTime")
                .gte(DateUtils.dateTime(beginTime))
                .lt(DateUtils.dateTime(endTime))
                .timeZone("+08:00");
            TermQueryBuilder responseCode = new TermQueryBuilder("responseCode", "1");
            boolQuery.must(businessSceneBuilder).must(rangeQueryBuilder).must(responseCode);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.size(100); // 设置每次scroll获取的记录数
            sourceBuilder.query(boolQuery);
            sourceBuilder.fetchSource(false);

            searchRequest.source(sourceBuilder);
            searchRequest.scroll(TimeValue.timeValueMinutes(1L)); // 设置scroll上下文有效期
            log.info("取消运维工具, 查询订单远程请求记录RPC入参：{}", TopsJsonUtils.toJSONString(searchRequest));
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            log.info("取消运维工具, 查询订单远程请求记录RPC出参大小：{}", TopsJsonUtils.toJSONString(searchResponse.getHits().getHits().length));


            String scrollId = searchResponse.getScrollId();
//            List<String> scrollIdList = new ArrayList<>();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            int pageCount = 0;
            int totalCount = 0;
            int notCancelledCount = 0;
            //2023-10-20历史id
            Long historySequenceId = 20970397042L;
            while (searchHits != null && searchHits.length > 0) {
                CallerInfo callerInfo = TopsProfilerUtils.registerInfo("tempGetCancelOrder", "jdl-ops");

                try {
                    pageCount++;
                    //转换成callbackOrderRequest
                    List<Long> sequenceIds = Arrays.stream(searchHits).map(this::getOrderSequenceId).collect(Collectors.toList());
                    totalCount += sequenceIds.size();
                    List<Long> historySequenceIds = sequenceIds.stream().filter(sequenceId -> sequenceId < historySequenceId && sequenceId > 0).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(historySequenceIds)) {
                        notCancelledCount += historySequenceIds.size();
                        List<String> notCancelledOrderNos = historySequenceIds.stream().map(sequenceId -> "ESL000000" + sequenceId)
                            .collect(Collectors.toList());
                        log.info("被取消的历史订单{}", notCancelledOrderNos);
                    }

                    // 获取下一批数据
                    SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                    scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                    log.info("查询{}次, 共查询{}条数据,未取消订单{}条", pageCount, totalCount, notCancelledCount);
                    searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
//                    scrollIdList.add(scrollId);
//                    scrollId = searchResponse.getScrollId();
                    searchHits = searchResponse.getHits().getHits();
                } catch (Exception ex) {
                    log.error("遍历失败, ex=",  ex);
                    TopsProfilerUtils.functionError(callerInfo);
                } finally {
                    TopsProfilerUtils.registerInfoEnd(callerInfo);
                }
            }

            // 清除scroll上下文
//            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
//            clearScrollRequest.setScrollIds(scrollIdList);
//            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (Exception ex) {
            log.error("查询取消数据异常, ex", ex);
        }
    }

    /**
     * 反序列化请求信息，如果序列化有问题跳过
     */
    private Long getOrderSequenceId(SearchHit hit) {
        Long sequenceId = -1L;
        try {
            String orderNo = hit.getFields().get("_routing") != null ?  (String) hit.getFields().get("_routing").getValue() : "-1";
            sequenceId = Long.valueOf(orderNo.replace("ESL", ""));
        } catch (Exception ex) {
            Profiler.businessAlarm("callback.request.deserialize.error", "回传信息反序列化失败，请检查日志记录是否正确" + hit.getFields().get("_routing"));
            log.error("[执行回传接口] 回传请求序列化错误, requestStr:{}", hit.getFields().get("_routing"));
        }

        return sequenceId;
    }

    /**
     * 对外开放服务查询接口
     */
    public R<Order> getOrder(String orderNo) {
        try {
            log.info("查询订单详情orderNo={}", orderNo);
            return R.ok(topsQueryOrderDetailAdapter.queryOrderDetail(orderNo));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 orderNo{}", orderNo);
            return R.fail("查询订单详情失败,请稍后再试");
        }
    }

    /**
     * 对外开放服务查询接口,获取请求报文(上限30个)
     */
    public R<List<TopsOrderRequestEsRecord>> getOrderRecord(TopsOrderRequestBo bo) {
        try {
            log.info("查询请求信息={}", JSON.toJSONString(bo));
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageNum(1);
            pageQuery.setPageSize(30);
            return R.ok(topsOrderRequestRecordAdapter.queryRecords(bo, pageQuery));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        } catch (Exception e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        }
    }

    /**
     * 对外开放服务查询接口,获取接单报文
     */
    public R<TopsOrderRequestEsRecord> getOutboundReceiveRecord(TopsOrderRequestBo bo) {
        try {
            log.info("查询请求信息bo= {}", JSON.toJSONString(bo));
            if (StringUtils.isEmpty(bo.getOrderNo()) && StringUtils.isBlank(bo.getCustomerOrderNo()) && StringUtils.isBlank(bo.getCustomOrderNo())) {
                return R.fail("订单号或/客户订单号/自定义单号为空, 请输入相关单据");
            }

            return R.ok(topsOrderRequestRecordAdapter.getLastReceiveRecord(bo, true));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        } catch (Exception e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        }
    }

    /**
     * 对外开放服务查询接口,获取接单报文
     */
    public R<TopsOrderRequestEsRecord> getExpressReceiveRecord(TopsOrderRequestBo bo) {
        try {
            log.info("查询请求信息bo= {}", JSON.toJSONString(bo));
            if (StringUtils.isEmpty(bo.getOrderNo()) && StringUtils.isBlank(bo.getCustomerOrderNo()) && StringUtils.isBlank(bo.getCustomOrderNo())) {
                return R.fail("订单号或/客户订单号/自定义单号为空, 请输入相关单据");
            }

            return R.ok(topsOrderRequestRecordAdapter.getLastReceiveRecord(bo, false));
        } catch (DependencyFailureException e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        } catch (Exception e) {
            log.error("查询订单详情失败 bo{}", JSON.toJSONString(bo), e);
            return R.fail("查询请求记录失败,请稍后再试");
        }
    }


    @Override
    public void getLogsAndSendMessage(LogbookBO logbookBO) {
        try {
            log.info("查询日志信息logbookBO={}", JSON.toJSONString(logbookBO));
            // 异步查询日志信息
            CompletableFuture<List<LogbookResponse.LogBookData>> future = CompletableFuture.supplyAsync(() -> topsLogBookAdaptor.getLogBook(logbookBO), GET_LOG_EXECUTOR);
            // 异步发送消息
            future.thenAccept(dataList -> {
                log.info("异步发送消息 dataList={}", dataList);
                autoBotsAsyncMessageService.sendAsyncLogBookMessage(logbookBO, dataList);
            }).exceptionally(e -> {
                log.error("异步发送消息失败", e);
                return null;
            });
        } catch (Exception e) {
            log.error("查询日志失败", e);
        }
    }
}
