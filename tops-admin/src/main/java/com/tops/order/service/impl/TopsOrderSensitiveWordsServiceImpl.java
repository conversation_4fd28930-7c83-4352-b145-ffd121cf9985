package com.tops.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.utils.StringUtils;
import com.tops.order.adapter.TopsSensitiveWordJsfServiceAdapter;
import com.tops.order.domain.TopsOrderSensitiveWords;
import com.tops.order.domain.bo.TopsOrderSensitiveWordsBo;
import com.tops.order.domain.vo.TopsOrderSensitiveWordsVo;
import com.tops.order.mapper.TopsOrderSensitiveWordsMapper;
import com.tops.order.service.ITopsOrderSensitiveWordsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 敏感词校验Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RequiredArgsConstructor
@Service
public class TopsOrderSensitiveWordsServiceImpl implements ITopsOrderSensitiveWordsService {

    private final TopsOrderSensitiveWordsMapper baseMapper;

    /**
     * 查询敏感词校验
     */
    @Override
    public TopsOrderSensitiveWordsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    @Resource
    private TopsSensitiveWordJsfServiceAdapter sensitiveWordJsfServiceAdapter;

    /**
     * 查询敏感词校验列表
     */
    @Override
    public TableDataInfo<TopsOrderSensitiveWordsVo> queryPageList(TopsOrderSensitiveWordsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TopsOrderSensitiveWords> lqw = buildQueryWrapper(bo);
        Page<TopsOrderSensitiveWordsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询敏感词校验列表
     */
    @Override
    public List<TopsOrderSensitiveWordsVo> queryList(TopsOrderSensitiveWordsBo bo) {
        LambdaQueryWrapper<TopsOrderSensitiveWords> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TopsOrderSensitiveWords> buildQueryWrapper(TopsOrderSensitiveWordsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopsOrderSensitiveWords> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSensitiveWord()), TopsOrderSensitiveWords::getSensitiveWord, bo.getSensitiveWord());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), TopsOrderSensitiveWords::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getCate()), TopsOrderSensitiveWords::getCate, bo.getCate());
        lqw.eq(bo.getIsHit() != null, TopsOrderSensitiveWords::getIsHit, bo.getIsHit());
        lqw.eq(StringUtils.isNotBlank(bo.getFailureDescription()), TopsOrderSensitiveWords::getFailureDescription, bo.getFailureDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getCallStatus()), TopsOrderSensitiveWords::getCallStatus, bo.getCallStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSensitiveWordsList()), TopsOrderSensitiveWords::getSensitiveWordsList, bo.getSensitiveWordsList());
        lqw.eq(StringUtils.isNotBlank(bo.getExtension1()), TopsOrderSensitiveWords::getExtension1, bo.getExtension1());
        lqw.eq(StringUtils.isNotBlank(bo.getExtension2()), TopsOrderSensitiveWords::getExtension2, bo.getExtension2());
        lqw.eq(StringUtils.isNotBlank(bo.getExtension3()), TopsOrderSensitiveWords::getExtension3, bo.getExtension3());
        lqw.eq(StringUtils.isNotBlank(bo.getExtension4()), TopsOrderSensitiveWords::getExtension4, bo.getExtension4());
        lqw.eq(StringUtils.isNotBlank(bo.getExtension5()), TopsOrderSensitiveWords::getExtension5, bo.getExtension5());
        return lqw;
    }

    /**
     * 新增敏感词校验
     */
    @Override
    public Boolean insertByBo(TopsOrderSensitiveWordsBo bo) {
        TopsOrderSensitiveWords add = BeanUtil.toBean(bo, TopsOrderSensitiveWords.class);
        TopsOrderSensitiveWordsBo senBo = new TopsOrderSensitiveWordsBo();
        senBo.setSensitiveWord(bo.getSensitiveWord());
        senBo.setCate(bo.getCate());
        List<TopsOrderSensitiveWordsVo> list = this.queryList(senBo);
        boolean flag = false;
        if(!list.isEmpty()){
            bo.setExtension1("依据「敏感词」和「种类」数据库中存在重复数据");
            Long id = list.get(0).getId();
            bo.setId(id);
            updateByBo(bo);
        } else {
            // 调用敏感词服务，获取敏感词列表
            TopsOrderSensitiveWordsVo vo = sensitiveWordJsfServiceAdapter.checkSensitiveWord(bo.getSensitiveWord());
            add.setCallStatus(vo.getCallStatus());
            add.setSensitiveWordsList(vo.getSensitiveWordsList());
            add.setExtension1(vo.getExtension1());
            add.setExtension2(vo.getExtension2());
            add.setExtension3(vo.getExtension3());
            add.setIsHit(vo.getIsHit());
            add.setCallStatus(vo.getCallStatus());
            flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setId(add.getId());
            }
        }
        // validEntityBeforeSave(add);
        return flag;
    }

    /**
     * 修改敏感词校验
     */
    @Override
    public Boolean updateByBo(TopsOrderSensitiveWordsBo bo) {
        TopsOrderSensitiveWords update = BeanUtil.toBean(bo, TopsOrderSensitiveWords.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsOrderSensitiveWords entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除敏感词校验
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
