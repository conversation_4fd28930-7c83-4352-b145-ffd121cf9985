package com.tops.order.service;

import com.tops.order.domain.TopsOrderSensitiveWords;
import com.tops.order.domain.vo.TopsOrderSensitiveWordsVo;
import com.tops.order.domain.bo.TopsOrderSensitiveWordsBo;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 敏感词校验Service接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface ITopsOrderSensitiveWordsService {

    /**
     * 查询敏感词校验
     */
    TopsOrderSensitiveWordsVo queryById(Long id);

    /**
     * 查询敏感词校验列表
     */
    TableDataInfo<TopsOrderSensitiveWordsVo> queryPageList(TopsOrderSensitiveWordsBo bo, PageQuery pageQuery);

    /**
     * 查询敏感词校验列表
     */
    List<TopsOrderSensitiveWordsVo> queryList(TopsOrderSensitiveWordsBo bo);

    /**
     * 新增敏感词校验
     */
    Boolean insertByBo(TopsOrderSensitiveWordsBo bo);

    /**
     * 修改敏感词校验
     */
    Boolean updateByBo(TopsOrderSensitiveWordsBo bo);

    /**
     * 校验并批量删除敏感词校验信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
