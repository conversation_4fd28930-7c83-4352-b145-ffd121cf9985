package com.tops.order.service.impl;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.SmartPatternInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.search.dto.BaseInfo;
import cn.jdl.oms.search.dto.Order;
import cn.jdl.oms.search.dto.RefOrderInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.dto.GetOrderRequest;
import com.tops.order.dto.GetOrderResponse;
import com.tops.order.service.GetOrderService;
import com.tops.order.service.IOpsOrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class GetOrderServiceImpl implements GetOrderService {
    @Autowired
    private IOpsOrderInfoService opsOrderInfoService;

    private final static String BAICHUAN_FLAG = "1";

    @Override
    public GetOrderResponse getOrderDetail(GetOrderRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        GetOrderResponse getOrderResponse = new GetOrderResponse();
        Order order = new Order();
        try {
            OpsOrderInfoBo bo = new OpsOrderInfoBo();
            bo.setOrderNo(request.getOrderNo());
            bo.setBaichuanFlag(BAICHUAN_FLAG);
            Order returnOrder = opsOrderInfoService.queryOrder(bo);
            order = copyOrderData(returnOrder);
        } catch (Exception e) {
            log.error("GetOrderServiceImpl.getOrderDetail 查询订单详情失败，request:{},e:", JSON.toJSONString(request), e);
        } finally {

        }
        log.info("GetOrderServiceImpl.getOrderDetail 查询订单详情，request:{},response:{}", JSON.toJSONString(request), JSON.toJSONString(order));
        getOrderResponse.setOrder(JSON.toJSONString(order, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat));
        return getOrderResponse;
    }

    /**
     * 返回订单数据拼装
     *
     * @param order
     * @return
     */
    private Order copyOrderData(Order order) {
        Order returnOrder = new Order();
        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setOrderNo(order.getBaseInfo().getOrderNo());
        baseInfo.setOrderStatus(order.getBaseInfo().getOrderStatus());
        baseInfo.setCancelStatus(order.getBaseInfo().getCancelStatus());
        baseInfo.setOrderSign(order.getBaseInfo().getOrderSign());
        returnOrder.setBaseInfo(baseInfo);

        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessUnit(order.getBusinessIdentity().getBusinessUnit());
        returnOrder.setBusinessIdentity(identity);

        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setAccountNo(order.getCustomerInfo().getAccountNo());
        customerInfo.setAccountName(order.getCustomerInfo().getAccountName());
        returnOrder.setCustomerInfo(customerInfo);


        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setCustomerOrderNo(order.getChannelInfo().getCustomerOrderNo());
        channelInfo.setChannelOrderNo(order.getChannelInfo().getChannelOrderNo());
        channelInfo.setChannelNo(order.getChannelInfo().getChannelNo());
        channelInfo.setChannelName(order.getChannelInfo().getChannelName());
        channelInfo.setSystemCaller(order.getChannelInfo().getSystemCaller());
        returnOrder.setChannelInfo(channelInfo);


        if (CollectionUtils.isNotEmpty(order.getProductInfos())) {
            List<ProductInfo> productInfos = new ArrayList<>();
            for (ProductInfo productInfo : productInfos) {
                ProductInfo product = new ProductInfo();
                product.setProductNo(productInfo.getProductNo());
                product.setProductName(productInfo.getProductName());
                productInfos.add(product);
            }
            returnOrder.setProductInfos(productInfos);

        }

        if (order.getConsignorInfo() != null && order.getConsignorInfo().getCustomerWarehouse() != null) {
            ConsignorInfo consignorInfo = new ConsignorInfo();
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            warehouseInfo.setWarehouseNo(order.getConsignorInfo().getCustomerWarehouse().getWarehouseNo());
            warehouseInfo.setWarehouseName(order.getConsignorInfo().getCustomerWarehouse().getWarehouseName());
            warehouseInfo.setActualWarehouseNo(order.getConsignorInfo().getCustomerWarehouse().getActualWarehouseNo());
            warehouseInfo.setActualWarehouseName(order.getConsignorInfo().getCustomerWarehouse().getActualWarehouseName());
            consignorInfo.setCustomerWarehouse(warehouseInfo);
            returnOrder.setConsignorInfo(consignorInfo);

        }

        if (order.getConsigneeInfo() != null && order.getConsigneeInfo().getAddressInfo() != null) {
            ConsigneeInfo consigneeInfo = new ConsigneeInfo();
            AddressInfo addressInfo = new AddressInfo();
            addressInfo.setProvinceNameGis(order.getConsigneeInfo().getAddressInfo().getProvinceNameGis());
            addressInfo.setCityNameGis(order.getConsigneeInfo().getAddressInfo().getCityNameGis());
            addressInfo.setCountyNameGis(order.getConsigneeInfo().getAddressInfo().getCountyNameGis());
            addressInfo.setTownNameGis(order.getConsigneeInfo().getAddressInfo().getTownNameGis());
            consigneeInfo.setAddressInfo(addressInfo);
            returnOrder.setConsigneeInfo(consigneeInfo);

        }

        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setShipperNo(order.getShipmentInfo().getShipperNo());
        shipmentInfo.setShipperName(order.getShipmentInfo().getShipperName());
        returnOrder.setShipmentInfo(shipmentInfo);


        SmartPatternInfo smartPatternInfo = new SmartPatternInfo();
        SmartPatternInfo.StockOperationRule stockOperationRule = new SmartPatternInfo.StockOperationRule();
        stockOperationRule.setOccupyWay(order.getSmartPatternInfo().getStockOperationRule().getOccupyWay());
        stockOperationRule.setOccupyResult(order.getSmartPatternInfo().getStockOperationRule().getOccupyResult());
        stockOperationRule.setOccupyResultProcessWay(order.getSmartPatternInfo().getStockOperationRule().getOccupyResultProcessWay());
        smartPatternInfo.setStockOperationRule(stockOperationRule);

        SmartPatternInfo.WarehouseRule warehouseRule = new SmartPatternInfo.WarehouseRule();
        warehouseRule.setCargoOutboundWay(order.getSmartPatternInfo().getWarehouseRule().getCargoOutboundWay());
        smartPatternInfo.setWarehouseRule(warehouseRule);
        returnOrder.setSmartPatternInfo(smartPatternInfo);


        if (order.getRefOrderInfo() != null && CollectionUtils.isNotEmpty(order.getRefOrderInfo().getChildOrderNos())) {
            RefOrderInfo refOrderInfo = new RefOrderInfo();
            refOrderInfo.setWaybillNos(order.getRefOrderInfo().getWaybillNos());
            returnOrder.setRefOrderInfo(refOrderInfo);
        }
        return returnOrder;
    }
}
