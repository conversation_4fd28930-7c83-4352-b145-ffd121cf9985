package com.tops.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.jdl.oms.search.dto.Order;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.esotericsoftware.minlog.Log;
import com.jd.security.tdeclient.TDEClient;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.exception.OpsException;
import com.tops.order.adapter.TopsOrderModifyRecordsAdapter;
import com.tops.order.domain.OpsOrderInfo;
import com.tops.order.domain.bo.OpsOrderDetailBo;
import com.tops.order.domain.bo.OpsOrderDetailDecryptBo;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.vo.OpsOrderDetailDecryptVo;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.domain.vo.OrderModifyRecordsVO;
import com.tops.order.mapper.OpsOrderInfoMapper;
import com.tops.order.service.IOpsOrderInfoService;
import com.tops.order.service.TopsQueryOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 订单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@RequiredArgsConstructor
@Service
public class OpsOrderInfoServiceImpl implements IOpsOrderInfoService {

    private final OpsOrderInfoMapper baseMapper;

    @Resource(name = "topsQueryOrderServiceImpl")
    private TopsQueryOrderService queryOrderService;
    private final static String BAICHUAN_FLAG = "1";

    /**
     * 查询订单信息
     */
    @Override
    public OpsOrderInfoVo queryOrderDetail(OpsOrderInfoBo bo) {
        boolean isBaichuan = BAICHUAN_FLAG.equals(bo.getBaichuanFlag());
        OpsOrderInfoVo vo = new OpsOrderInfoVo();
        if (!isBaichuan) {
            // 非百川订单，走Eclp订单详情服务
        } else {
            // 百川订单,走百川订单详情服务;或兜底走百川
            vo = queryOrderService.queryOrderDetail(bo);
        }
        return vo;
    }

    @Override
    public OpsOrderDetailDecryptVo decryptOrderDetail(OpsOrderDetailDecryptBo bo) {
        OpsOrderDetailDecryptVo vo = new OpsOrderDetailDecryptVo();
        try {
            vo.setName(decrypt(bo.getName()));
            vo.setPhone(decrypt(bo.getPhone()));
            vo.setMobile(decrypt(bo.getMobile()));
            vo.setAddress(decrypt(bo.getAddress()));
        } catch (Exception e) {
            Log.error("解密失败,入参={}", TopsJsonUtils.toJSONString(bo), e);
            throw new OpsException("解密失败");
        }
        return vo;
    }

    /**
     * 解密
     *
     * @param pt 密文 默认编码UTF-8
     * @return 明文
     * @throws Exception
     */
    public String decrypt(String pt) throws Exception {
        String plaintext = null;
        if (TDEClient.CipherStatus.Decryptable == isDecryptable(pt)) {
            plaintext = tdeCpOrderClient.decryptString(pt);
        } else {
            plaintext = "无法解密";
        }
        return plaintext;
    }

    @Resource
    private TDEClient tdeCpOrderClient;

    /**
     * 判断是否可以解密
     *
     * @param base64ct 密文
     * @return 能解密返回 Decryptable，密文格式不对返回 Malformed, 不能解密返回 UnDecryptable。
     * @throws Exception
     */
    public TDEClient.CipherStatus isDecryptable(String base64ct) {
        if (base64ct == null || StringUtils.isEmpty(base64ct)) {
            return null;
        }
        return tdeCpOrderClient.isDecryptable(base64ct);
    }

    /**
     * 查询订单信息列表
     */
    @Override
    public TableDataInfo<OpsOrderInfoVo> queryPageList(OpsOrderInfoBo bo, PageQuery pageQuery) {
        // 1 判断query_key是否为Null
        Page<OpsOrderInfoVo> resultPage = null;
        List<OpsOrderInfoVo> resultList = null;
        TableDataInfo<OpsOrderInfoVo> tableDataInfo = null;
        if (StringUtils.isNotBlank(bo.getQueryKey())) {
            // 1.1 不为Null走数据库查询
            LambdaQueryWrapper<OpsOrderInfo> lqw = buildQueryWrapper(bo);
            resultPage = baseMapper.selectVoPage(pageQuery.build(), lqw);
        } else {
            // 1.2.1 为Null走实时查询
            if (isSimpleCondition(bo)) {
                resultList = queryOrderService.queryOrderInfoBySimple(bo);
                tableDataInfo = TableDataInfo.build(resultList);
                tableDataInfo.setFlag(1);
                return tableDataInfo;
            } else {
                resultPage = queryOrderService.queryOrderInfoByCompoundFactors(bo, pageQuery);
                tableDataInfo = TableDataInfo.build(resultPage);
                tableDataInfo.setFlag(2);
            }
        }
        if (resultPage == null) {
            return TableDataInfo.build("数据为空，请检查查询条件！");
        }
        return tableDataInfo;
    }


    /**
     * 判断是否为索引条件，当前只支持订单号和自定义单号
     */
    private boolean isSimpleCondition(OpsOrderInfoBo bo) {
        if (StringUtils.isNotBlank(bo.getOrderNo()) || StringUtils.isNotBlank(bo.getCustomOrderNo())) {
            return true;
        }
        return false;
    }

    /**
     * 查询订单信息列表
     */
    @Override
    public List<OpsOrderInfoVo> queryList(OpsOrderInfoBo bo) {
        LambdaQueryWrapper<OpsOrderInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OpsOrderInfo> buildQueryWrapper(OpsOrderInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OpsOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getQueryKey()), OpsOrderInfo::getQueryKey, bo.getQueryKey());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), OpsOrderInfo::getOrderNo, bo.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getParentOrderNo()), OpsOrderInfo::getParentOrderNo, bo.getParentOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerOrderNo()), OpsOrderInfo::getCustomerOrderNo, bo.getCustomerOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomOrderNo()), OpsOrderInfo::getCustomOrderNo, bo.getCustomOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelOrderNo()), OpsOrderInfo::getChannelOrderNo, bo.getChannelOrderNo());
        lqw.eq(StringUtils.isNotBlank(bo.getWaybillNo()), OpsOrderInfo::getWaybillNo, bo.getWaybillNo());
        //lqw.eq(StringUtils.isNotBlank(bo.getChannelSource()), OpsOrderInfo::getChannelSource, bo.getChannelSource());
        lqw.eq(StringUtils.isNotBlank(bo.getAccountNo()), OpsOrderInfo::getAccountNo, bo.getAccountNo());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), OpsOrderInfo::getCustomerName, bo.getCustomerName());
        //todo getBusinessUnit为list
        //lqw.eq(StringUtils.isNotBlank(bo.getBusinessUnit()), OpsOrderInfo::getBusinessUnit, bo.getBusinessUnit());
        //lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), OpsOrderInfo::getOrderStatus, bo.getOrderStatus());
        //lqw.eq(StringUtils.isNotBlank(bo.getOrderCustomStatus()), OpsOrderInfo::getOrderCustomStatus, bo.getOrderCustomStatus());
        //lqw.eq(StringUtils.isNotBlank(bo.getChannelOrderStatus()), OpsOrderInfo::getChannelOrderStatus, bo.getChannelOrderStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelOrderCancelStatus()), OpsOrderInfo::getChannelOrderCancelStatus, bo.getChannelOrderCancelStatus());
        //lqw.eq(StringUtils.isNotBlank(bo.getWaybillStatus()), OpsOrderInfo::getWaybillStatus, bo.getWaybillStatus());
        lqw.between(params.get("beginReceiveTime") != null && params.get("endReceiveTime") != null,
            OpsOrderInfo::getReceiveTime, params.get("beginReceiveTime"), params.get("endReceiveTime"));
        lqw.between(params.get("beginLastOperationTime") != null && params.get("endLastOperationTime") != null,
            OpsOrderInfo::getLastOperationTime, params.get("beginLastOperationTime"), params.get("endLastOperationTime"));
        //lqw.eq(StringUtils.isNotBlank(bo.getCustomOrderType()), OpsOrderInfo::getCustomOrderType, bo.getCustomOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getBaichuanFlag()), OpsOrderInfo::getBaichuanFlag, bo.getBaichuanFlag());
        return lqw;
    }

    /**
     * 新增订单信息
     */
    @Override
    public Boolean insertByBo(OpsOrderInfoBo bo) {
        OpsOrderInfo add = BeanUtil.toBean(bo, OpsOrderInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单信息
     */
    @Override
    public Boolean updateByBo(OpsOrderInfoBo bo) {
        OpsOrderInfo update = BeanUtil.toBean(bo, OpsOrderInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OpsOrderInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 查询订单数据
     *
     * @param bo
     * @return
     */
    @Override
    public Order queryOrder(OpsOrderInfoBo bo) {
        boolean isBaichuan = BAICHUAN_FLAG.equals(bo.getBaichuanFlag());
        Order vo = new Order();
        if (!isBaichuan) {
            // 非百川订单，走Eclp订单详情服务
        } else {
            // 百川订单,走百川订单详情服务;或兜底走百川
            vo = queryOrderService.queryOrder(bo);
        }
        return vo;
    }

    @Resource
    private TopsOrderModifyRecordsAdapter topsOrderModifyRecordsAdapter;

    @Override
    public List<OrderModifyRecordsVO> orderModifyRecords(OpsOrderDetailBo bo) {
        List<OrderModifyRecordsVO> result = topsOrderModifyRecordsAdapter.getOrderModifyRecord(bo);
        return result;
    }

}
