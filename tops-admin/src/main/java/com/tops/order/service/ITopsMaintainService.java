package com.tops.order.service;

import cn.jdl.oms.search.dto.Order;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookResponse;
import com.tops.common.core.domain.R;
import com.tops.order.adapter.TopsQueryOrderDetailAdapter;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ITopsMaintainService
 * @Description 运维工具临时接口
 * @date 2024年08月27日 6:00 PM
 */
public interface ITopsMaintainService {
    void resendCallbackRequest() throws Exception;

    void getCancelHistoryOrderRequest(Date beginTime, Date endTime) throws Exception;

    /**
     * 获取订单详情
     * @param orderNo
     * @return
     */
    R<Order> getOrder(String orderNo);

    /**
     * 获取订单请求记录
     * @param bo
     * @return
     */
    R<List<TopsOrderRequestEsRecord>> getOrderRecord(TopsOrderRequestBo bo);

    /**
     * 获取仓配接单记录
     * @param bo
     * @return
     */
    R<TopsOrderRequestEsRecord> getOutboundReceiveRecord(TopsOrderRequestBo bo);

    /**
     * 获取纯配接单记录
     * @param bo
     * @return
     */
    R<TopsOrderRequestEsRecord> getExpressReceiveRecord(TopsOrderRequestBo bo);


    /**
     * 查询日志信息,回调对应的工作流
     */
    public void getLogsAndSendMessage(LogbookBO logbookBO);
}
