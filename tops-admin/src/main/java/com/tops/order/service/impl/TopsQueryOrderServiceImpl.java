package com.tops.order.service.impl;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.search.api.SearchOrderService;
import cn.jdl.oms.search.dto.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.entity.SysDictData;
import com.tops.common.utils.TopsCastUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.exception.OpsException;
import com.tops.order.adapter.TopsQueryOrderDetailAdapter;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.service.TopsQueryOrderService;
import com.tops.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * ClassName:TopsQueryOrderServiceImpl
 * Package:com.jdl.ops.trade.order.service.impl
 * Description:
 *
 * @date:2024/5/23 下午1:24
 * @author:WeiLiming
 */
@Slf4j
@Service
public class TopsQueryOrderServiceImpl implements TopsQueryOrderService {

    private static final String OPS_BUSINESS_UNIT = "cn_jdl_supplychain";
    private static final String RESULT_SUCCESS_CODE = "1";
    private static final int MAX_SEARCH_DAYS = 15;
    //查询结果中包含的域（Base-订单基础信息，Identity-业务身份信息，Customer-客户信息，Channel-渠道信息，Solution-解决方案，
    // Product-产品信息，Consignor-发货信息，Consignee-收货信息，Cargo-货品信息，Goods-商品信息，Shipment-配送信息，
    // Finance-财务信息，Promotion-营销信息，RefOrder-关联单信息，SmartPattern-智能策略信息，Deduction-抵扣信息，
    // Extend-扩展信息，ReturnInfo-退货信息，Agreement-协议信息，InterceptInfo-拦截信息，Fulfillment-履约信息，
    // CustomsInfo-跨境报关信息，AttachmentInfo-附件信息，AttachFee-附加费列表，CustomsInfo-跨境报关信息，AttachmentInfo-附件信息，All-所有）
    private static final List<String> SEARCH_FIELD_LIST = Lists.newArrayList("Base", "Identity", "Channel", "RefOrder", "Customer");

    @Autowired
    private ISysDictTypeService iSysDictTypeService;
    @Autowired
    private SearchOrderService searchOrderService;
    @Autowired
    private TopsQueryOrderDetailAdapter topsQueryOrderDetailAdapter;


    @Override
    public OpsOrderInfoVo queryOrderDetail(OpsOrderInfoBo bo) {
        if (StringUtils.isBlank(bo.getOrderNo())){
            throw new OpsException("订单号不能为空");
        }
        OpsOrderInfoVo vo = topsQueryOrderDetailAdapter.queryOrderInfoBySimple(bo.getOrderNo());
        return vo;
    }

    /**
     * 批量查询订单，先支持1000个，后续可扩展
     *
     * @param bo
     * @return
     */
    @Override
    public List<OpsOrderInfoVo> queryOrderInfoBySimple(OpsOrderInfoBo bo) {
        List<String> orderNos = buildNoList(bo.getOrderNo());
        List<String>  customOrderNos = buildNoList(bo.getCustomOrderNo());
        List<OpsOrderInfoVo> comOrderInfos = topsQueryOrderDetailAdapter.queryOrderInfoBySimple(orderNos, customOrderNos);
        return comOrderInfos;
    }

    private List<String> buildNoList(String noStr) {
        if (StringUtils.isBlank(noStr)) {
            return null;
        }
        List<String> orderNos = Arrays.asList(noStr.split("[,\\n]"));
        return orderNos;
    }

    @Override
    public Page<OpsOrderInfoVo> queryOrderInfoByCompoundFactors(OpsOrderInfoBo bo, PageQuery pageQuery) {
        Map<String, String[]> queryMap = TopsCastUtils.objToStrArrMap(bo.getParams());
        SearchOrderRequest searchOrderRequest = buildSearchOrderRequest(bo, queryMap, pageQuery);
        buildSearchOrderRequest(searchOrderRequest, pageQuery);
        Page<OpsOrderInfoVo> page = new Page<>();

        try {
            log.info("物流订单查询入参，requestProfile={},searchOrderRequest={}", TopsJsonUtils.toJSONString(buildRequestProfile()), TopsJsonUtils.toJSONString(searchOrderRequest));
            SearchOrderResponse searchOrderResponse = searchOrderService.search(buildRequestProfile(), searchOrderRequest);
            log.info("物流订单查询出参，searchOrderResponse={}", TopsJsonUtils.toJSONString(searchOrderResponse));

            if (searchOrderResponse == null || !RESULT_SUCCESS_CODE.equals(searchOrderResponse.getCode())) {
                log.warn("订单查询服务返回结果异常");
                return null;
            }
            if (searchOrderResponse.getData() == null || CollectionUtils.isEmpty(searchOrderResponse.getData().getOrders())) {
                log.warn("订单查询服务返回结果数据为空，response={}", TopsJsonUtils.toJSONString(searchOrderResponse));
                return null;
            }
            List<OpsOrderInfoVo> voList = buildComOrderInfoList(bo, searchOrderResponse.getData().getOrders());
            page.setRecords(voList);
            if (page.getTotal() != 0) {
                page.setTotal(page.getTotal());
            } else {
                page.setTotal(searchOrderResponse.getData().getTotalCount());
            }
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            throw e;
        }
        return page;
    }

    /**
     * 查询订单，返回订单对象
     * @param bo
     * @return
     */
    @Override
    public Order queryOrder(OpsOrderInfoBo bo) {
        if (StringUtils.isBlank(bo.getOrderNo())){
            throw new OpsException("订单号不能为空");
        }
        return topsQueryOrderDetailAdapter.queryOrderInfo(bo.getOrderNo());
    }

    /**
     * 构建订单查询列表入参
     *
     * @param page
     * @param queryOrderInfo
     * @param queryMap
     * @return
     */
    private SearchOrderRequest buildSearchOrderRequest(OpsOrderInfoBo queryOrderInfo, Map<String, String[]> queryMap, PageQuery page) {
        SearchOrderRequest searchOrderRequest = new SearchOrderRequest();
        boolean needDefaultTime = true;
        BaseCondition baseCondition = new BaseCondition();
        ChannelCondition channelCondition = new ChannelCondition();
        CustomerCondition customerCondition = new CustomerCondition();

        baseCondition.setBusinessUnits(buildBusinessUnit(queryOrderInfo.getBusinessUnit()));
        if (StringUtils.isNotBlank(queryOrderInfo.getBaichuanFlag())) {
            baseCondition.setSyncSource(Integer.valueOf(queryOrderInfo.getBaichuanFlag()));
        }
        //设置订单单号
        if (StringUtils.isNotBlank(queryOrderInfo.getOrderNo())) {
            baseCondition.setOrderNos(strToList(queryOrderInfo.getOrderNo()));
            needDefaultTime = false;
        }

        //设置自定义单号
        if (StringUtils.isNotBlank(queryOrderInfo.getCustomOrderNo())) {
            baseCondition.setCustomOrderNos(strToList(queryOrderInfo.getCustomOrderNo()));
            needDefaultTime = false;
        }
        //设置渠道单号
        if (StringUtils.isNotBlank(queryOrderInfo.getChannelOrderNo())) {
            needDefaultTime = false;
            channelCondition.setChannelOrderNos(Splitter.on(',').splitToList(queryOrderInfo.getChannelOrderNo()));
        }
        //设置运单号
        if (StringUtils.isNotBlank(queryOrderInfo.getWaybillNo())) {
            RefOrderCondition refOrderCondition = new RefOrderCondition();
            refOrderCondition.setWaybillNos(strToList(queryOrderInfo.getWaybillNo()));
            searchOrderRequest.setRefOrderCondition(refOrderCondition);
            needDefaultTime = false;
        }
        //设置渠道来源
        if (CollectionUtils.isNotEmpty(queryOrderInfo.getChannelSource())) {
            channelCondition.setSystemCallers(queryOrderInfo.getChannelSource());
        }
        //设置客户单号
        if (StringUtils.isNotBlank(queryOrderInfo.getCustomerOrderNo())) {
            channelCondition.setCustomerOrderNos(strToList(queryOrderInfo.getCustomerOrderNo()));
            needDefaultTime = false;
        }
        //设置客户名称
        if (StringUtils.isNotBlank(queryOrderInfo.getCustomerName())) {
            baseCondition.setCustomOrderNos(strToList(queryOrderInfo.getCustomerName()));
        }

        //物流订单标准状态
        if (CollectionUtils.isNotEmpty(queryOrderInfo.getOrderStatus())) {
            List<Integer> idList = queryOrderInfo.getOrderStatus().stream().map(Integer::parseInt).collect(Collectors.toList());
            baseCondition.setOrderStandardStatuss(idList);
        }
        //设置客户账号
        if (StringUtils.isNotBlank(queryOrderInfo.getAccountNo())) {
            customerCondition.setAccountNos(strToList(queryOrderInfo.getAccountNo()));
        }

        /* 设置查询时间开始，最多支持1个月，默认为近7天*/
        Date startReceivedTime = null;
        Date endReceivedTime = null;
        try {
            startReceivedTime = TopsDateUtils.toDate(queryMap.get("beginReceiveTime")[0]);
            endReceivedTime = TopsDateUtils.toDate(queryMap.get("endReceiveTime")[0]);
        } catch (Exception e) {
            log.warn("时间获取异常，取默认最近15天");
        }

        if (needDefaultTime && (startReceivedTime == null || endReceivedTime == null)) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -MAX_SEARCH_DAYS);
            startReceivedTime = calendar.getTime();
            endReceivedTime = new Date();
        }
        if (startReceivedTime != null || endReceivedTime != null) {
            LocalDateTime startLDateTime = TopsDateUtils.toLocalDateTime(startReceivedTime);
            LocalDateTime endLDateTime = TopsDateUtils.toLocalDateTime(endReceivedTime);
            long betweenDays = Duration.between(startLDateTime, endLDateTime).toDays();
            if (betweenDays > MAX_SEARCH_DAYS) {
                throw new OpsException("查询创建时间超出" + MAX_SEARCH_DAYS + "天，暂不支持。");
            }
            baseCondition.setStartReceivedTime(startReceivedTime);
            baseCondition.setEndReceivedTime(endReceivedTime);
        }
        /* 设置查询时间结束 */

        /* 设置修改开始时间、结束时间 */
        Date startLastOperationTime = null;
        Date endLastOperationTime = null;
        try {
            startLastOperationTime = TopsDateUtils.toDate(queryMap.get("beginOperationTime")[0]);
            endLastOperationTime = TopsDateUtils.toDate(queryMap.get("endLastOperationTime")[0]);
        } catch (Exception e) {
            log.warn("未设置修改修改时间，可忽略");
        }
/*
        if (startLastOperationTime != null || endLastOperationTime != null) {
            LocalDateTime startLDateTime = TopsDateUtils.toLocalDateTime(startLastOperationTime);
            LocalDateTime endLDateTime = TopsDateUtils.toLocalDateTime(endLastOperationTime);
            long betweenDays = Duration.between(startLDateTime, endLDateTime).toDays();
            if (betweenDays > MAX_SEARCH_DAYS) {
                throw new OpsException("查询修改时间超出" + MAX_SEARCH_DAYS + "天，暂不支持。");
            }
            baseCondition.setStartInterceptTime(startLastOperationTime);
            baseCondition.setEndReceivedTime(endLastOperationTime);
        }
*/

        searchOrderRequest.setChannelCondition(channelCondition);
        searchOrderRequest.setBaseCondition(baseCondition);
        searchOrderRequest.setCustomerCondition(customerCondition);
        //设置分页信息
        cn.jdl.oms.search.dto.Page queryPage = new cn.jdl.oms.search.dto.Page();
        queryPage.setCurrent(page.getPageNum());
        queryPage.setSize(page.getPageSize());
        searchOrderRequest.setPage(queryPage);
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(OPS_BUSINESS_UNIT);
        searchOrderRequest.setBusinessIdentity(businessIdentity);
        searchOrderRequest.setIncludeFields(SEARCH_FIELD_LIST);
        searchOrderRequest.setIncludeSet(Lists.newArrayList("All"));
        return searchOrderRequest;
    }

    /**
     * 匹配业务身份
     *
     * @param businessUnitList
     * @return
     */
    private List<String> buildBusinessUnit(List<String> businessUnitList) {
        // 设置业务身份，优先取入参数据，若为空则从字典中获取
        if (CollectionUtils.isNotEmpty(businessUnitList)) {
            return businessUnitList;
        } else {
            List<SysDictData> sysDictDataVos = iSysDictTypeService.selectDictDataByType("ops_business_units");
            // List<DictModel> dictModelList = sysBaseApi.queryEnableDictItemsByCode("com_business_units");
            businessUnitList = sysDictDataVos.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
            return businessUnitList;
        }
    }

    /**
     * 列表查询二次构件，对于入参传入了orderNo、channelNo的需要进行分割查询，底层查询服务最多一次支持50条
     *
     * @param page
     * @param searchOrderRequest
     * @return
     */
    private void buildSearchOrderRequest(SearchOrderRequest searchOrderRequest, PageQuery page) {
        List<String> orderList = searchOrderRequest.getBaseCondition().getOrderNos();
        if (CollectionUtils.isNotEmpty(orderList)) {
            List<List<String>> orderListSplit = Lists.partition(orderList, page.getPageSize());
            searchOrderRequest.getBaseCondition().setOrderNos(orderListSplit.get(page.getPageNum() - 1));
            //page.setTotal(orderList.size() - 1);
            searchOrderRequest.getPage().setSize(page.getPageSize());
            searchOrderRequest.getPage().setCurrent(1);
            return;
        }

        if (searchOrderRequest.getChannelCondition() == null) {
            return;
        }
        List<String> channelOrderList = searchOrderRequest.getChannelCondition().getChannelOrderNos();
        if (CollectionUtils.isNotEmpty(channelOrderList)) {
            List<List<String>> orderListSplit = Lists.partition(channelOrderList, page.getPageSize());
            searchOrderRequest.getChannelCondition().setChannelOrderNos(orderListSplit.get(page.getPageNum() - 1));
            //page.setTotal(orderList.size() - 1);
            searchOrderRequest.getPage().setSize(page.getPageSize());
            searchOrderRequest.getPage().setCurrent(1);
        }
    }

    /**
     * 构建基础信息
     *
     * @return
     */
    private static RequestProfile buildRequestProfile() {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setTenantId("1000");
        requestProfile.setLocale("zh_CN");
        requestProfile.setTraceId(String.valueOf(System.currentTimeMillis()));
        return requestProfile;
    }

    /**
     * 构建订单查询列表出参
     *
     * @param orders
     * @return
     */
    private List<OpsOrderInfoVo> buildComOrderInfoList(OpsOrderInfoBo queryOrderInfo, List<Order> orders) {
        List<Future<OpsOrderInfoVo>> comOrderInfoFutures = new ArrayList<>();
        String queryKey = queryOrderInfo.getQueryKey();
        long startTime = System.currentTimeMillis();
        for (Order order : orders) {
            comOrderInfoFutures.add(topsQueryOrderDetailAdapter.buildComOrderInfo(queryKey, order));
        }
        List<OpsOrderInfoVo> comOrderInfos = new ArrayList<>();
        for (Future<OpsOrderInfoVo> future : comOrderInfoFutures) {
            try {
                comOrderInfos.add(future.get());
            } catch (InterruptedException e) {
                log.error("线程异常中断", e);
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                log.error("线程执行异常", e);
                throw new RuntimeException(e);
            }
        }
        log.info("订单列表共{}条，查询零售订单状态、运单状态共耗时：{}", orders.size(), System.currentTimeMillis() - startTime);
        return comOrderInfos;
    }

    private static List<String> strToList(String source) {
        return Arrays.stream(source.split("[,\n]"))
            .map(String::trim)  // 去掉前后空格
            .collect(Collectors.toList());
    }

}

