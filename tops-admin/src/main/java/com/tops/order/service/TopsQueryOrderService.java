package com.tops.order.service;

import cn.jdl.oms.search.dto.Order;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.core.domain.PageQuery;
import com.tops.order.domain.bo.OpsOrderInfoBo;
import com.tops.order.domain.vo.OpsOrderInfoVo;

import java.util.List;

public interface TopsQueryOrderService {

    /**
     *  京东物流(JDL)订单中心详情查询统一接口服务。只允许查询最近一年内订单。
     * @param bo
     * @return GetOrderResponse
     */
    public OpsOrderInfoVo queryOrderDetail(OpsOrderInfoBo bo);

    /**
     * 通过简单条件查询订单信息(订单号、自定义单号)
     * @param bo
     * @return
     */
    public List<OpsOrderInfoVo> queryOrderInfoBySimple(OpsOrderInfoBo bo);

    /**
     *  京东物流(JDL)订单中心详情查询统一接口服务。只允许查询最近一年内订单。
     * @param bo
     * @return SearchOrderResponse
     */
    public Page<OpsOrderInfoVo> queryOrderInfoByCompoundFactors(OpsOrderInfoBo bo, PageQuery pageQuery);


    /**
     *  京东物流(JDL)订单中心详情查询统一接口服务。只允许查询最近一年内订单。
     * @param bo
     * @return GetOrderResponse
     */
    public Order queryOrder(OpsOrderInfoBo bo);


}
