package com.tops.order.service.impl;

import com.tops.order.adapter.TopsOrderStatusFlowAdapter;
import com.tops.order.adapter.TopsWaybillStatusAdapter;
import com.tops.order.domain.bo.TopsOrderStatusBo;
import com.tops.order.domain.vo.TopsOrderStatusVo;
import com.tops.order.service.ITopsOrderStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ClassName:TopsOrderStatusServiceImpl
 * Package:com.tops.order.service.impl
 * Description:
 *
 * @date:2024/8/7 上午9:50
 * @author:WeiLiming
 */
@Service
public class TopsOrderStatusServiceImpl implements ITopsOrderStatusService {

    @Autowired
    private TopsOrderStatusFlowAdapter orderStatusFlowAdapter;
    @Autowired
    private TopsWaybillStatusAdapter waybillStatusAdapter;

    @Override
    public List<TopsOrderStatusVo> queryOrderStatusList(TopsOrderStatusBo orderStatusBo) {
        if (orderStatusBo == null || orderStatusBo.getOrderNo() == null) {
            return null;
        }
        List<TopsOrderStatusVo> orderStatusVos = orderStatusFlowAdapter.getOrderStatus(orderStatusBo.getOrderNo());
        return orderStatusVos;
    }

    @Override
    public List<TopsOrderStatusVo> queryOrderStandardStatusList(TopsOrderStatusBo orderStatusBo) {
        if (orderStatusBo == null || orderStatusBo.getOrderNo() == null) {
            return null;
        }
        List<TopsOrderStatusVo> orderStatusVos = orderStatusFlowAdapter.getOrderStandardStatus(orderStatusBo.getOrderNo());
        return orderStatusVos;
    }

    @Override
    public List<TopsOrderStatusVo> queryWaybillTracking(TopsOrderStatusBo orderStatusBo) {
        if (orderStatusBo == null || orderStatusBo.getWaybillNo() == null) {
            return null;
        }
        List<TopsOrderStatusVo> topsOrderStatusVos = waybillStatusAdapter.queryTrace(orderStatusBo.getWaybillNo());
        return topsOrderStatusVos;
    }
}
