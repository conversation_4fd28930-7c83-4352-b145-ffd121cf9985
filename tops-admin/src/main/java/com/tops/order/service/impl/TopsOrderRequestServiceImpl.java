package com.tops.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.service.DictService;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.exception.OpsException;
import com.tops.common.utils.redis.RedisUtils;
import com.tops.common.utils.spring.SpringUtils;
import com.tops.order.adapter.TopsOrderRequestRecordAdapter;
import com.tops.order.domain.TopsOrderRequest;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import com.tops.order.domain.vo.OpsCateStatVO;
import com.tops.order.domain.vo.OpsKVStatVO;
import com.tops.order.domain.vo.TopsOrderRequestVo;
import com.tops.order.enums.DataMatchTypeEnum;
import com.tops.order.mapper.TopsOrderRequestMapper;
import com.tops.order.service.ITopsOrderRequestService;
import com.tops.order.thread.AsyncRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单请求信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TopsOrderRequestServiceImpl implements ITopsOrderRequestService {

    private final TopsOrderRequestMapper baseMapper;
    @Autowired
    private TopsOrderRequestRecordAdapter topsOrderRequestRecordAdapter;
    @Autowired
    private AsyncRelationService asyncRelationService;
    private static final String REDIS_FAIL_ALL_PRFIX = "tops_order_fail_record_%s";
    private final DictService dictService;
    private final static String CREATE_STATUS_SUCCESS = "1";
    private final static String CREATE_STATUS_FAILURE = "0";


    /**
     * 查询订单请求信息
     */
    @Override
    public TopsOrderRequestVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public Map<String, Integer> statisticsPanel(TopsOrderRequestBo bo) {
        // 总失败单量
        int totalCount = 0;
        // 实际失败单量
        int failCount = 0;
        // 实际成功数量
        int successCount = 0;
        // 实际影响客户数量
        int customerCount = 0;
        List<TopsOrderRequestVo> voList = RedisUtils.getCacheObject(String.format(REDIS_FAIL_ALL_PRFIX, bo.getParams().get("beginCreateTime")));
        totalCount = voList.size();
        Set<String> customers = new HashSet<>();
        for (TopsOrderRequestVo vo : voList) {
            if (CREATE_STATUS_SUCCESS.equals(vo.getCreateStatus())) {
                successCount++;
            } else if (CREATE_STATUS_FAILURE.equals(vo.getCreateStatus())) {
                failCount++;
                customers.add(vo.getAccountNo());
            }
        }
        customerCount = customers.size();
        Map<String, Integer> map = new HashMap<>();
        map.put("totalCount", totalCount);
        map.put("failCount", failCount);
        map.put("successCount", successCount);
        map.put("customerCount", customerCount);
        return map;
    }

    /**
     * 构建展示记录集合
     *
     * @param list
     * @return
     */
    private List<TopsOrderRequestVo> buildVoList(List<TopsOrderRequestEsRecord> list) throws ExecutionException, InterruptedException {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 将原始列表按1000的大小拆分，每800条并发请求关联关系，再等待500ms，关联关系QPS：3000
        List<List<TopsOrderRequestEsRecord>> partitionedList = ListUtils.partition(list, 100);
        List<TopsOrderRequestVo> finalResult = new ArrayList<>();
        for (List<TopsOrderRequestEsRecord> partition : partitionedList) {

            List<String> orderNoList = partition.stream().map(TopsOrderRequestEsRecord::getOrderNo).collect(Collectors.toList());

            List<Future<TopsOrderRequestVo>> voFutures = new ArrayList<>();
            // 并行处理每个分区内的项目
            for (TopsOrderRequestEsRecord item : partition) {
                Future<TopsOrderRequestVo> voFuture = asyncRelationService.asyncBuildOrderRequestList(item);
                voFutures.add(voFuture);
            }
            // 等待当前分区的所有Future完成，并收集结果
            for (Future<TopsOrderRequestVo> future : voFutures) {
                try {
                    TopsOrderRequestVo vo = future.get();
                    if (vo != null) {
                        finalResult.add(vo);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error processing item: " + e.getMessage(), e);
                }
            }
            // 此时，当前1000条数据已经全部处理完毕
            log.info("Completed processing batch of " + partition.size() + " items");
            Thread.sleep(120);
        }
        return finalResult;
    }


    /**
     * 查询失败统计信息
     *
     * @param bo
     */
    public List<OpsKVStatVO> queryCount(String groupKey, TopsOrderRequestBo bo) {
        List<OpsKVStatVO> resultList = new ArrayList<>();
        List<TopsOrderRequestVo> voList = RedisUtils.getCacheObject(String.format(REDIS_FAIL_ALL_PRFIX, bo.getParams().get("beginCreateTime")));
        Map<String, Long> map = new HashMap<>();
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        // 计算某一维度的记录数量
        for (TopsOrderRequestVo vo : voList) {
            if ("businessUnit".equals(groupKey)) {
                map.compute(vo.getBusinessUnit(), (key, value) -> value == null ? 1L : value + 1L);
            } else if ("responseCode".equals(groupKey)) {
                map.compute(vo.getResponseCode(), (key, value) -> value == null ? 1L : value + 1L);
            }
        }
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            OpsKVStatVO vo = new OpsKVStatVO();
            if ("businessUnit".equals(groupKey)) {
                vo.setName(dictService.getDictLabel("ops_business_units", entry.getKey()));
            } else {
                vo.setName(dictService.getDictLabel("ops_order_response_msg", entry.getKey()));
            }
            vo.setValue(entry.getValue());
            vo.setDesc("具体描述");
            resultList.add(vo);
        }
        return resultList;
    }

    /**
     * 查询失败统计信息
     *
     * @param bo
     */
    public List<OpsCateStatVO> queryCateCount(String groupKey, TopsOrderRequestBo bo) {
        List<OpsCateStatVO> resultList = new ArrayList<>();
        List<TopsOrderRequestVo> voList = RedisUtils.getCacheObject(String.format(REDIS_FAIL_ALL_PRFIX, bo.getParams().get("beginCreateTime")));
        Map<String, Long> map = new HashMap<>();
        if (CollectionUtils.isEmpty(voList)) {
            return new ArrayList<>();
        }
        Table<String, String, Long> table = HashBasedTable.create();
        String CREATE_STATUS_SUCCESS = "重试成功";
        String CREATE_STATUS_FAILURE = "失败请求";

        // 计算某一维度的记录数量
        for (TopsOrderRequestVo vo : voList) {
            if ("accountNo".equals(groupKey)) {
                if (vo.getAccountNo() == null) {
                    continue;
                }
                String createStatus = "0".equals(vo.getCreateStatus()) ? CREATE_STATUS_FAILURE : CREATE_STATUS_SUCCESS;
                if (!table.contains(vo.getAccountNo(), CREATE_STATUS_FAILURE)) {
                    table.put(vo.getAccountNo(), CREATE_STATUS_FAILURE, 0L);
                }
                if (!table.contains(vo.getAccountNo(), CREATE_STATUS_SUCCESS)) {
                    table.put(vo.getAccountNo(), CREATE_STATUS_SUCCESS, 0L);
                }
                table.put(vo.getAccountNo(), createStatus, table.get(vo.getAccountNo(), createStatus) + 1L);
            }
        }
        OpsCateStatVO cateStatVO;
        for (String rowKey : table.rowKeySet()) {
            cateStatVO = new OpsCateStatVO();
            cateStatVO.setCategory(rowKey);
            OpsKVStatVO kvStatVO;
            for (Map.Entry<String, Long> entry : table.row(rowKey).entrySet()) {
                kvStatVO = new OpsKVStatVO();
                kvStatVO.setName(entry.getKey());
                kvStatVO.setValue(entry.getValue());
                if (cateStatVO.getData() == null) {
                    cateStatVO.setData(new ArrayList<>());
                }
                cateStatVO.getData().add(kvStatVO);
            }
            resultList.add(cateStatVO);
        }
        // 按照 category 维度计算对应 kvStatVO 内 value 加合从多到少排序
        resultList.sort((o1, o2) -> {
            long sum1 = o1.getData().stream().mapToLong(OpsKVStatVO::getValue).sum();
            long sum2 = o2.getData().stream().mapToLong(OpsKVStatVO::getValue).sum();
            return Long.compare(sum1, sum2); // 逆序排序
        });

        return resultList;
    }

    public static boolean isWithinDiffHours(Date startTime, Date endTime, int timeDiff) {
        // 判断startTime是否早于endTime
        if (startTime.compareTo(endTime) <= 0) {
            // 计算时间差，以毫秒为单位
            long diffInMillis = endTime.getTime() - startTime.getTime() - 1;
            // 将时间差转换为小时
            long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);
            // 判断时间差是否不超过2个小时
            return diffInHours <= timeDiff - 1;
        }

        return false;
    }

    /**
     * 查询订单请求信息列表
     */
    @Override
    public TableDataInfo<TopsOrderRequestVo> queryPageList(TopsOrderRequestBo bo, PageQuery pageQuery) {
        Page<TopsOrderRequestVo> result = new Page<>();
        List<TopsOrderRequestVo> voList = new ArrayList<>();
        TableDataInfo<TopsOrderRequestVo> tableDataInfo = null;
        if (CollectionUtils.isEmpty(bo.getExcludedResponseCode())) {
            bo.setExcludedResponseCode(Lists.newArrayList("1"));
        } else {
            bo.getExcludedResponseCode().add("1");
        }
        bo.getParams().put(DataMatchTypeEnum.DATA_MATCH_DISTINCT.getGroup(), DataMatchTypeEnum.DATA_MATCH_DISTINCT);
        try {
            log.info("查询订单请求列表页面入参：{}, 分页信息：{}", TopsJsonUtils.toJSONString(bo), TopsJsonUtils.toJSONString(bo));
            if (bo.getParams() == null || bo.getParams().get("beginCreateTime") == null || bo.getParams().get("endCreateTime") == null) {
                return TableDataInfo.build("请输入创建时间范围");
            }
            voList = queryList(bo);
            if (voList != null) {
                result.setRecords(voList);
                result.setTotal(pageQuery.getPageTotal() == null ? voList.size() : pageQuery.getPageTotal());
            } else {
                result.setRecords(new ArrayList<>());
            }
            RedisUtils.setCacheObject(String.format(REDIS_FAIL_ALL_PRFIX, bo.getParams().get("beginCreateTime")), result.getRecords(), Duration.ofSeconds(30L));
            log.info("查询订单请求列表页面出参大小：{}", voList == null ? 0 : voList.size());

            tableDataInfo = TableDataInfo.build(result);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw e;
            //tableDataInfo = TableDataInfo.build(e.getMessage());
        }
        /*LambdaQueryWrapper<TopsOrderRequest> lqw = buildQueryWrapper(bo);
        Page<TopsOrderRequestVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);*/
        return tableDataInfo;
    }

    /**
     * 查询
     */

    /**
     * 查询订单请求信息列表
     */
    @Override
    public List<TopsOrderRequestVo> queryList(TopsOrderRequestBo bo) {

        List<TopsOrderRequestVo> voList = null;
        String keyLock = String.format(REDIS_FAIL_ALL_PRFIX, "list");
        try {
            if (bo.getParams() == null || bo.getParams().get("beginCreateTime") == null || bo.getParams().get("endCreateTime") == null) {
                return null;
            }

            int diffHours = 0;
            String errorMsg = "";
            if (isSimpleRequest(bo)) {
                // 入参如果传入的订单号、客户单号，则查询时间范围为3个月
                Calendar calendar = Calendar.getInstance();
                int addDays = -28;
                /* 线上环境最大支持6个月，当前配置可支持5个月 */
                if ("prod".equals(SpringUtils.getActiveProfile())) {
                    addDays = -120;
                }
                calendar.add(Calendar.DAY_OF_YEAR, addDays);
                Date hisDay = calendar.getTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String hisDayStr = dateFormat.format(hisDay);
                bo.getParams().put("beginCreateTime", hisDayStr);
                bo.getParams().put("endCreateTime", dateFormat.format(new Date()));
                diffHours = -addDays * 24 + 25;
                errorMsg = diffHours / 24 / 30 + "个月";
            } else {
                if (!RedisUtils.setObjectIfAbsent(keyLock, "1", Duration.ofSeconds(30L))) {
                    throw new RuntimeException("当前有查询正在处理中，请稍后再试");
                }
                diffHours = 2;
                errorMsg = diffHours + "小时";
            }
            Date startTime = TopsDateUtils.toDate(bo.getParams().get("beginCreateTime").toString());
            Date endTime = TopsDateUtils.toDate(bo.getParams().get("endCreateTime").toString());

            if (!isWithinDiffHours(startTime, endTime, diffHours)) {
                throw new OpsException("仅支持" + errorMsg + "以内时间范围查询");
            }
            List<TopsOrderRequestEsRecord> failEsRecords = topsOrderRequestRecordAdapter.scrollRecords(bo);
            voList = buildVoList(failEsRecords);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            RedisUtils.deleteObject(keyLock);
        }
        return voList;
    }

    @Override
    public List<TopsOrderRequestVo> queryRecordsByCustomerOrderNo(TopsOrderRequestBo bo) {
        return Collections.emptyList();
    }

    /**
     * 是否为简单查询，可支持批量查询
     *
     * @param bo
     * @return
     */
    private boolean isSimpleRequest(TopsOrderRequestBo bo) {
        if (bo.getOrderNo() != null && StringUtils.isNotBlank(bo.getOrderNo())) {
            return true;
        }
        if (bo.getTraceId() != null && StringUtils.isNotBlank(bo.getTraceId())) {
            return true;
        }
        if (bo.getCustomerOrderNo() != null && StringUtils.isNotBlank(bo.getCustomerOrderNo())) {
            return true;
        }
        return false;
    }


    private List<TopsOrderRequestVo> queryList() {
        List<TopsOrderRequestVo> voList = new ArrayList<>();
        String[] ebuValues = {"EBU001", "EBU002", "EBU003", "EBU004", "EBU005"};
        String[] createValues = {"1", "0"};
        String[] responseValues = {"3-01-11020", "3-01-11001", "3-01-10002", "2-01-11059", "2-01-10004"};
        String[] unitValues = {"cn_jdl_cc-pop", "cn_jdl_sc-jdr-vmi", "cn_jdl_sc-pop", "cn_jdl_las-isv"};

        Random random = new Random();

        for (int i = 0; i < 100; i++) {
            TopsOrderRequestVo vo = new TopsOrderRequestVo();
            vo.setOrderNo("1111" + i);
            vo.setBusinessScene("receive");
            vo.setCreateTime(new Date());
            vo.setId(Long.valueOf(i));
            vo.setAccountNo(ebuValues[random.nextInt(ebuValues.length)]);
            vo.setResponseCode(responseValues[random.nextInt(responseValues.length)]);
            vo.setCreateStatus(createValues[random.nextInt(createValues.length)]);
            vo.setBusinessUnit(unitValues[random.nextInt(unitValues.length)]);
            voList.add(vo);
        }
        return voList;
    }

    private LambdaQueryWrapper<TopsOrderRequest> buildQueryWrapper(TopsOrderRequestBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopsOrderRequest> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAccountNo()), TopsOrderRequest::getAccountNo, bo.getAccountNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAgentSales()), TopsOrderRequest::getAgentSales, bo.getAgentSales());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessScene()), TopsOrderRequest::getBusinessScene, bo.getBusinessScene());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessType()), TopsOrderRequest::getBusinessType, bo.getBusinessType());
        //lqw.eq(StringUtils.isNotBlank(bo.getBusinessUnit()), TopsOrderRequest::getBusinessUnit, bo.getBusinessUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getCreatePin()), TopsOrderRequest::getCreatePin, bo.getCreatePin());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            TopsOrderRequest::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerOrderNo()), TopsOrderRequest::getCustomerOrderNo, bo.getCustomerOrderNo());
        lqw.eq(bo.getMessageId() != null, TopsOrderRequest::getMessageId, bo.getMessageId());
        lqw.eq(bo.getMqRetryMessage() != null, TopsOrderRequest::getMqRetryMessage, bo.getMqRetryMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getOperator()), TopsOrderRequest::getOperator, bo.getOperator());
        lqw.between(params.get("beginOperatorTime") != null && params.get("endOperatorTime") != null,
            TopsOrderRequest::getOperatorTime, params.get("beginOperatorTime"), params.get("endOperatorTime"));
        lqw.eq(StringUtils.isNotBlank(bo.getOrderMark()), TopsOrderRequest::getOrderMark, bo.getOrderMark());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), TopsOrderRequest::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getOrderStandardStatus() != null, TopsOrderRequest::getOrderStandardStatus, bo.getOrderStandardStatus());
        lqw.eq(bo.getOrderStatus() != null, TopsOrderRequest::getOrderStatus, bo.getOrderStatus());
        lqw.eq(bo.getPdqRetryMessage() != null, TopsOrderRequest::getPdqRetryMessage, bo.getPdqRetryMessage());
        lqw.eq(StringUtils.isNotBlank(bo.getRequest()), TopsOrderRequest::getRequest, bo.getRequest());
        lqw.eq(StringUtils.isNotBlank(bo.getResponse()), TopsOrderRequest::getResponse, bo.getResponse());
        //lqw.eq(StringUtils.isNotBlank(bo.getResponseCode()), TopsOrderRequest::getResponseCode, bo.getResponseCode());
        lqw.eq(StringUtils.isNotBlank(bo.getResponseMsg()), TopsOrderRequest::getResponseMsg, bo.getResponseMsg());
        lqw.eq(bo.getSkuSize() != null, TopsOrderRequest::getSkuSize, bo.getSkuSize());
        lqw.eq(StringUtils.isNotBlank(bo.getSoSource()), TopsOrderRequest::getSoSource, bo.getSoSource());
        lqw.eq(StringUtils.isNotBlank(bo.getSystemCaller()), TopsOrderRequest::getSystemCaller, bo.getSystemCaller());
        //lqw.eq(StringUtils.isNotBlank(bo.getCustomOrderType()), TopsOrderRequest::getCustomOrderType, bo.getCustomOrderType());
        lqw.eq(bo.getSystemId() != null, TopsOrderRequest::getSystemId, bo.getSystemId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), TopsOrderRequest::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getTopic()), TopsOrderRequest::getTopic, bo.getTopic());
        lqw.eq(StringUtils.isNotBlank(bo.getTraceId()), TopsOrderRequest::getTraceId, bo.getTraceId());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdatePin()), TopsOrderRequest::getUpdatePin, bo.getUpdatePin());
        lqw.between(params.get("beginUpdateTime") != null && params.get("endUpdateTime") != null,
            TopsOrderRequest::getUpdateTime, params.get("beginUpdateTime"), params.get("endUpdateTime"));
        lqw.eq(bo.getYn() != null, TopsOrderRequest::getYn, bo.getYn());
        return lqw;
    }

    /**
     * 新增订单请求信息
     */
    @Override
    public Boolean insertByBo(TopsOrderRequestBo bo) {
        TopsOrderRequest add = BeanUtil.toBean(bo, TopsOrderRequest.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单请求信息
     */
    @Override
    public Boolean updateByBo(TopsOrderRequestBo bo) {
        TopsOrderRequest update = BeanUtil.toBean(bo, TopsOrderRequest.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsOrderRequest entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单请求信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}

