package com.tops.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.constant.TopsConstants;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.enums.TopsStatCateEnum;
import com.tops.common.enums.TopsStatDimensionEnum;
import com.tops.common.enums.TopsStatTypeEnum;
import com.tops.common.utils.BeanCopyUtils;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.redis.RedisUtils;
import com.tops.order.adapter.TopsOrderRecordAdapter;
import com.tops.order.adapter.TopsOrderRequestRecordAdapter;
import com.tops.order.domain.TopsStatRecord;
import com.tops.order.domain.bo.TopsStatRecordBo;
import com.tops.order.domain.vo.TopsChartData;
import com.tops.order.domain.vo.TopsSeries;
import com.tops.order.domain.vo.TopsStatRecordVo;
import com.tops.order.mapper.TopsStatRecordMapper;
import com.tops.order.service.ITopsStatRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TopsStatRecordServiceImpl implements ITopsStatRecordService {

    private final TopsStatRecordMapper baseMapper;
    @Autowired
    private TopsOrderRecordAdapter topsOrderRecordAdapter;
    @Autowired
    private TopsOrderRequestRecordAdapter topsOrderRequestRecordAdapter;

    /**
     * 查询订单统计
     */
    @Override
    public TopsStatRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询订单统计列表
     */
    @Override
    public TableDataInfo<TopsStatRecordVo> queryPageList(TopsStatRecordBo bo, PageQuery pageQuery) {
        renderOrderStatChart(null);
        LambdaQueryWrapper<TopsStatRecord> lqw = buildQueryWrapper(bo);
        Page<TopsStatRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        return TableDataInfo.build(result);
    }

    /**
     * 查询订单统计列表
     * new TopsStatRecordBo(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode(), TopsStatCateEnum.CATE_DAY.getCode(), TopsDateUtils.toDate(date, "yyyy-MM-dd"), TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode(), businessUnit, count);
     * * 统计成功记录(接单成功，可以此来统计单量)
     * * 优先从数据库中获取，获取到的记录数量与时间间隔进行比较，
     * * 1、若间隔1天，则这一天优先从Redis获取，Redis没有的话从ES获取这一天的数据，取到后存入Redis(5分钟)(因全天数据不完整，先不入库)
     * * 2、若间隔>1天，则全量从ES获取，获取到后对数据库数据进行插入OR更新操作
     * * new TopsStatRecordBo(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode(), TopsStatCateEnum.CATE_DAY.getCode(), TopsDateUtils.toDate(date, "yyyy-MM-dd"), TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode(), businessUnit, count);
     * * 按照前面 5个字段进行获取，更新最后的count
     */
    /**
     * 1、判断endTime是否为今天，若是今天则
     * 全量从数据库中获取数据，若数据条数小于入参时间覆盖范围，则全量从远程获取数据。并更新DB
     * 若数据条数等于入参时间覆盖范围，则取出最后一条记录，判断更新时间与当前时间对比，若超过5分钟，则从远程获取当天的统计记录，通过id更新到数据库中
     * 情况1：当天为endTime查询，今天之前的数据
     * <p>
     * 最新逻辑：将时间段拆分为按天获取，哪天没有补哪天，当天数据只存Redis，不存入数据库
     * 1、写定时任务，每天凌晨0:30抓取前一天到当前时间的数据，并更新/插入到数据库中。
     * 2、将入参时间拆分为按天获取，哪天没有补哪天，当天数据只存Redis，不存入数据库
     *
     * @param bo
     * @return
     */
    public static final String TODAY_KEY_PREFIX = "tops_order_stat_today:";

    @Override
    public TopsChartData renderOrderStatChart(TopsStatRecordBo bo) {
        String beginTime = bo.getBeginTime();
        String endTime = bo.getEndTime();
        bo.setType(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode());
        bo.setCategory(TopsStatCateEnum.CATE_DAY.getCode());
        bo.setDimension(TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode());
        List<TopsStatRecordVo> localRecords;
        List<TopsStatRecordVo> remoteRecords = new ArrayList<>();
        List<TopsStatRecordVo> todayRecords;
        // 获取今日数据：判断时间范围若包含今天，则将今天的数据进行实时更新(缓存2分钟)
        boolean isContainToday = TopsDateUtils.isToday(endTime);
        if (isContainToday) {
            String startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            bo.setBeginTime(startOfDay);
            String redisKey = TODAY_KEY_PREFIX + ":" + bo.getType() + ":" + bo.getCategory() + ":" + bo.getDimension() + ":" + startOfDay;
            // 优先从Redis获取，若未取到则从ES获取，再存入Redis
            todayRecords = RedisUtils.getCacheObject(redisKey);
            if (CollectionUtils.isEmpty(todayRecords)) {
                todayRecords = topsOrderRequestRecordAdapter.statSuccessRecords(bo);
                if (CollectionUtils.isNotEmpty(todayRecords)) {
                    RedisUtils.setCacheObject(redisKey, todayRecords, Duration.ofMinutes(TopsConstants.PAGE_VALUE_EXPIRATION));
                } else {
                    log.error("ES中未获取到今天的统计记录");
                }
            }
            remoteRecords.addAll(todayRecords);
            // 重置时间，保留了今天之前的时间(不包括今天)
            bo.setBeginTime(beginTime);
            bo.setEndTime(startOfDay);
        }

        // 获取历史数据：从数据库获取，并判断天数是否相等，若不相等，拿出差集并从远程获取数据
        localRecords = queryList(bo);
        List<String[]> daysRange = TopsDateUtils.splitDateTimeRange(bo.getBeginTime(), bo.getEndTime());
        // 2.获取数据-若数据库中数据为空，则从远程全量获取数据，并更新到数据库中
        if (CollectionUtils.isEmpty(localRecords)) {
            List<TopsStatRecordVo> tempVos = topsOrderRequestRecordAdapter.statSuccessRecords(bo);
            List<TopsStatRecord> recordList = BeanCopyUtils.copyList(tempVos, TopsStatRecord.class);
            baseMapper.insertBatch(recordList);
            remoteRecords.addAll(tempVos);
            return convertToChartData(remoteRecords);
        }
        // 若所有日期均可从数据库中获取，则组装后直接返回
        Map<String, List<TopsStatRecordVo>> recordsMap = localRecords.stream()
            .collect(Collectors.groupingBy(
                record -> TopsDateUtils.toDateTimeStr(record.getCategoryValue()) // 转换Date为String
            ));
        if (recordsMap.size() == daysRange.size()) {
            remoteRecords.addAll(localRecords);
            return convertToChartData(remoteRecords);
        }

        // 若数据不完整，则取出本地无数据的日期，循环从远程获取数据。
        for (String[] dayTime : daysRange) {
            String beginTimeStr = dayTime[0];
            String endTimeStr = dayTime[1];
            if (recordsMap.get(beginTimeStr) != null) {
                continue;
            }
            bo.setBeginTime(beginTimeStr);
            bo.setEndTime(endTimeStr);
            List<TopsStatRecordVo> tempVo = topsOrderRequestRecordAdapter.statSuccessRecords(bo);
            if (CollectionUtils.isEmpty(tempVo)) {
                log.warn("远程未获取到统计数据，入参为：{}", TopsJsonUtils.toJSONString(bo));
                continue;
            }
            List<TopsStatRecord> recordList = BeanCopyUtils.copyList(tempVo, TopsStatRecord.class);
            baseMapper.insertBatch(recordList);
            remoteRecords.addAll(tempVo);
        }
        remoteRecords.addAll(localRecords);
        return convertToChartData(remoteRecords);
    }

    @Override
    public TopsChartData renderOrderFailStatChart(TopsStatRecordBo bo) {
        String beginTime = bo.getBeginTime();
        String endTime = bo.getEndTime();
        bo.setType(TopsStatTypeEnum.TYPE_ORDER_FAIL_COUNT.getCode());
        bo.setCategory(TopsStatCateEnum.CATE_DAY.getCode());
        bo.setDimension(TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode());
        List<TopsStatRecordVo> localRecords;
        List<TopsStatRecordVo> remoteRecords = new ArrayList<>();
        List<TopsStatRecordVo> todayRecords;
        // 获取今日数据：判断时间范围若包含今天，则将今天的数据进行实时更新(缓存2分钟)
        boolean isContainToday = TopsDateUtils.isToday(endTime);
        if (isContainToday) {
            String startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            bo.setBeginTime(startOfDay);
            String redisKey = TODAY_KEY_PREFIX + ":" + bo.getType() + ":" + bo.getCategory() + ":" + bo.getDimension() + ":" + startOfDay;
            // 优先从Redis获取，若未取到则从ES获取，再存入Redis
            todayRecords = RedisUtils.getCacheObject(redisKey);
            if (CollectionUtils.isEmpty(todayRecords)) {
                todayRecords = topsOrderRequestRecordAdapter.statFailRecords(bo);
                if (CollectionUtils.isNotEmpty(todayRecords)) {
                    RedisUtils.setCacheObject(redisKey, todayRecords, Duration.ofMinutes(TopsConstants.PAGE_VALUE_EXPIRATION));
                } else {
                    log.error("ES中未获取到今天的统计记录");
                }
            }
            remoteRecords.addAll(todayRecords);
            // 重置时间，保留了今天之前的时间(不包括今天)
            bo.setBeginTime(beginTime);
            bo.setEndTime(startOfDay);
        }

        // 获取历史数据：从数据库获取，并判断天数是否相等，若不相等，拿出差集并从远程获取数据
        localRecords = queryList(bo);
        List<String[]> daysRange = TopsDateUtils.splitDateTimeRange(bo.getBeginTime(), bo.getEndTime());
        // 2.获取数据-若数据库中数据为空，则从远程全量获取数据，并更新到数据库中
        if (CollectionUtils.isEmpty(localRecords)) {
            List<TopsStatRecordVo> tempVos = topsOrderRequestRecordAdapter.statFailRecords(bo);
            List<TopsStatRecord> recordList = BeanCopyUtils.copyList(tempVos, TopsStatRecord.class);
            baseMapper.insertBatch(recordList);
            remoteRecords.addAll(tempVos);
            return convertToChartData(remoteRecords);
        }
        // 若所有日期均可从数据库中获取，则组装后直接返回
        Map<String, List<TopsStatRecordVo>> recordsMap = localRecords.stream()
            .collect(Collectors.groupingBy(
                record -> TopsDateUtils.toDateTimeStr(record.getCategoryValue()) // 转换Date为String
            ));
        if (recordsMap.size() == daysRange.size()) {
            remoteRecords.addAll(localRecords);
            return convertToChartData(remoteRecords);
        }

        // 若数据不完整，则取出本地无数据的日期，循环从远程获取数据。
        for (String[] dayTime : daysRange) {
            String beginTimeStr = dayTime[0];
            String endTimeStr = dayTime[1];
            if (recordsMap.get(beginTimeStr) != null) {
                continue;
            }
            bo.setBeginTime(beginTimeStr);
            bo.setEndTime(endTimeStr);
            List<TopsStatRecordVo> tempVo = topsOrderRequestRecordAdapter.statFailRecords(bo);
            if (CollectionUtils.isEmpty(tempVo)) {
                log.warn("远程未获取到统计数据，入参为：{}", TopsJsonUtils.toJSONString(bo));
                continue;
            }
            List<TopsStatRecord> recordList = BeanCopyUtils.copyList(tempVo, TopsStatRecord.class);
            baseMapper.insertBatch(recordList);
            remoteRecords.addAll(tempVo);
        }
        remoteRecords.addAll(localRecords);
        return convertToChartData(remoteRecords);
    }

    @Override
    public TopsChartData orderFailCustomerStatChart(TopsStatRecordBo bo) {
        bo.setType(TopsStatTypeEnum.TYPE_ORDER_FAIL_COUNT.getCode());
        bo.setCategory(TopsStatCateEnum.CATE_HOUR.getCode());
        bo.setDimension(TopsStatDimensionEnum.DIMENSION_ACCOUNT_NO.getCode());

        List<TopsStatRecordVo> records = topsOrderRequestRecordAdapter.statCustomerFailRecords(bo);
        return convertToChartData(records);
    }

    @Override
    public TopsChartData orderSuccessCustomerStatChart(TopsStatRecordBo bo) {
        bo.setType(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode());
        bo.setCategory(TopsStatCateEnum.CATE_HOUR.getCode());
        bo.setDimension(TopsStatDimensionEnum.DIMENSION_ACCOUNT_NO.getCode());
        List<TopsStatRecordVo> records = topsOrderRequestRecordAdapter.statCustomerSuccessRecords(bo);
        return convertToChartData(records);
    }

    /**
     * 查询订单统计列表
     */
    @Override
    public List<TopsStatRecordVo> queryList(TopsStatRecordBo bo) {
        LambdaQueryWrapper<TopsStatRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TopsStatRecord> buildQueryWrapper(TopsStatRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopsStatRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getType()), TopsStatRecord::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), TopsStatRecord::getCategory, bo.getCategory());
        lqw.between(params.get("beginCategoryValue") != null && params.get("endCategoryValue") != null,
            TopsStatRecord::getCategoryValue, params.get("beginCategoryValue"), params.get("endCategoryValue"));
        lqw.eq(StringUtils.isNotBlank(bo.getDimension()), TopsStatRecord::getDimension, bo.getDimension());
        lqw.eq(StringUtils.isNotBlank(bo.getDimensionValue()), TopsStatRecord::getDimensionValue, bo.getDimensionValue());
        lqw.eq(bo.getStatCount() != null, TopsStatRecord::getStatCount, bo.getStatCount());
        return lqw;
    }

    /**
     * 新增订单统计
     */
    @Override
    public Boolean insertByBo(TopsStatRecordBo bo) {
        TopsStatRecord add = BeanUtil.toBean(bo, TopsStatRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改订单统计
     */
    @Override
    public Boolean updateByBo(TopsStatRecordBo bo) {
        TopsStatRecord update = BeanUtil.toBean(bo, TopsStatRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsStatRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单统计
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    public static TopsChartData convertToChartData(List<TopsStatRecordVo> recordList) {
        TopsChartData chartData = new TopsChartData();
        if (CollectionUtils.isEmpty(recordList)) {
            return chartData;
        }
        boolean isHour = TopsStatCateEnum.CATE_HOUR.getCode().equals(recordList.get(0).getCategory());
        SimpleDateFormat dateFormat = isHour
            ? new SimpleDateFormat("HH") : new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Map<String, Long>> groupedData = recordList.stream()
            .collect(Collectors.groupingBy(
                TopsStatRecordVo::getDimensionValue,
                Collectors.toMap(
                    record -> dateFormat.format(record.getCategoryValue()),
                    TopsStatRecordVo::getStatCount,
                    (oldValue, newValue) -> oldValue
                )
            ));
        // 获取所有的 categoryValue，并去重排序
        List<String> categories = recordList.stream()
            .map(TopsStatRecordVo::getCategoryValue)
            .map(dateFormat::format)
            .distinct()
            .sorted()
            .collect(Collectors.toList());


        chartData.setData(categories);

        // 填充 series 数据
        List<TopsSeries> seriesList = new ArrayList<>();
        groupedData.forEach((dimensionValue, dataMap) -> {
            TopsSeries series = new TopsSeries();
            series.setName(dimensionValue);
            series.setType(isHour ? "line" : "bar"); // 设置图表类型
            // 若是业务维度，则按照身份聚合
            if (dimensionValue.contains("pop")) {
                series.setStack("pop");
            } else if (dimensionValue.contains("isv") || dimensionValue.contains("ka-medium")) {
                series.setStack("isv");
            } else if (dimensionValue.contains("jdr")) {
                series.setStack("jdr");
            } else if (dimensionValue.contains("clps")) {
                series.setStack("clps");
            } else if (dimensionValue.contains("ka")) {
                series.setStack("ka");
            } else if (dimensionValue.contains("tc")) {
                series.setStack("ot");
            }
            // 按 categoryValue 的顺序填充数据
            List<Long> data = categories.stream()
                .map(dateStr -> dataMap.getOrDefault(dateStr, 0L))
                .collect(Collectors.toList());

            series.setData(data);
            seriesList.add(series);
        });
        chartData.setTopsSeries(seriesList);
        if (!isHour) {
            seriesList.add(calculateTotalSeries(chartData));
        }

        return chartData;
    }

    public static TopsSeries calculateTotalSeries(TopsChartData chartData) {
        List<String> categories = chartData.getData();
        List<TopsSeries> seriesList = chartData.getTopsSeries();

        // 初始化总和列表，长度与 categories 相同
        List<Long> totalData = new ArrayList<>(Collections.nCopies(categories.size(), 0L));

        // 累加每个 TopsSeries 的 data 列表中的值
        for (TopsSeries series : seriesList) {
            List<Long> data = series.getData();
            for (int i = 0; i < data.size(); i++) {
                totalData.set(i, totalData.get(i) + data.get(i));
            }
        }

        // 创建总和的 TopsSeries 对象
        TopsSeries totalSeries = new TopsSeries();
        totalSeries.setName("all");
        totalSeries.setType("line");
        totalSeries.setStack(null); // 总和不需要堆积
        totalSeries.setData(totalData);

        return totalSeries;
    }

    public static List<String[]> splitDateTimeRange(String startDateTimeStr, String endDateTimeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        LocalDateTime startDateTime = LocalDateTime.parse(startDateTimeStr, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endDateTimeStr, formatter);

        List<String[]> dateTimeRanges = new ArrayList<>();

        // 如果开始时间和结束时间在同一天，直接返回
        if (startDateTime.toLocalDate().equals(endDateTime.toLocalDate())) {
            dateTimeRanges.add(new String[]{startDateTimeStr, endDateTimeStr});
            return dateTimeRanges;
        }

        // 第一个时间段
        LocalDateTime nextDayMidnight = startDateTime.toLocalDate().atStartOfDay().plusDays(1);
        dateTimeRanges.add(new String[]{startDateTime.format(formatter), nextDayMidnight.format(formatter)});

        // 中间的每一天
        LocalDateTime currentStart = nextDayMidnight;
        while (currentStart.plusDays(1).isBefore(endDateTime)) {
            LocalDateTime currentEnd = currentStart.plusDays(1);
            dateTimeRanges.add(new String[]{currentStart.format(formatter), currentEnd.format(formatter)});
            currentStart = currentEnd;
        }

        // 最后一个时间段
        dateTimeRanges.add(new String[]{currentStart.format(formatter), endDateTime.format(formatter)});

        return dateTimeRanges;
    }

    public static void main(String[] args) {
        String startDateTime = "2024-06-01 01:00";
        String endDateTime = "2024-06-03 00:00";

        List<String[]> result = splitDateTimeRange(startDateTime, endDateTime);
        for (String[] range : result) {
            System.out.println(range[0] + " - " + range[1]);
        }
        System.out.println(TopsDateUtils.splitDateTimeRange(startDateTime, endDateTime).size());
    }
}
