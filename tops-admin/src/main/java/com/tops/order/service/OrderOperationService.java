package com.tops.order.service;

import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.vo.CallbackOrderRequest;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.domain.vo.ModifyOrderRequest;
import com.tops.order.domain.vo.ReacceptOrderRequest;

public interface OrderOperationService {
     void cancelOrders(CancelOrdersRequest request) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

     void callbackOrders(CallbackOrderRequest callbackOrderRequest) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

     void modifyOrders(ModifyOrderRequest modifyOrderRequest) throws InternalFailureException, DependencyFailureException, InvalidRequestException;

     void reaccpetOrders(ReacceptOrderRequest reacceptOrderRequest) throws InternalFailureException, DependencyFailureException, InvalidRequestException;
}
