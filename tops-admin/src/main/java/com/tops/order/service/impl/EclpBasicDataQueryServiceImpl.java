package com.tops.order.service.impl;

import com.alibaba.fastjson2.JSON;
import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.eclp.master.warehouse.service.WarehouseAdapterService;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.order.adapter.TopsWarehouseInfoAdapter;
import com.tops.order.domain.vo.WarehouseVO;
import com.tops.order.service.EclpBasicDataQueryService;
import com.tops.order.translator.basic.WarehouseTranslator;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName EclpBasicDataQueryServiceImpl
 * @Description 用途
 * @date 2024年08月22日 8:52 PM
 */
@Service
@Slf4j
public class EclpBasicDataQueryServiceImpl implements EclpBasicDataQueryService {
    @Resource
    TopsWarehouseInfoAdapter topsWarehouseInfoAdapter;

    @Resource
    WarehouseTranslator warehouseTranslator;

    @Resource
    TopsEnvUtils topsEnvUtils;

    @Override
    public WarehouseVO getWarehouse(String warehouseNo) throws DependencyFailureException, InternalFailureException, InvalidRequestException {
        if(StringUtils.isEmpty(warehouseNo)) {
            log.error("传入的仓库编码不能为空");
            throw new InvalidRequestException("warehouseNo不能为空");
        }

        Warehouse warehouse = null;
        try {
            warehouse = topsWarehouseInfoAdapter.queryWarehouse(warehouseNo);
            return warehouseTranslator.convertWarehouse(warehouse);
        } catch (DependencyFailureException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("getWarehouse 系统异常: warehouse:{}", JSON.toJSONString(warehouse), ex);
            throw new InternalFailureException(ex.getMessage());
        }
    }

    @Override
    public List<WarehouseVO> getWarehouseByWarehouseName(String warehouseName) throws DependencyFailureException, InternalFailureException, InvalidRequestException {
        if(StringUtils.isEmpty(warehouseName)) {
            log.error("传入的仓库名称不能为空");
            throw new InvalidRequestException("仓库名称不能为空");
        }

        List<WarehouseVO> warehouseVOS;
        try {
            List<Warehouse> warehouses = topsWarehouseInfoAdapter.fuzzyQueryByWarehouseName(warehouseName);
            warehouseVOS = warehouses.stream().map(warehouse -> warehouseTranslator.convertWarehouse(warehouse)).collect(Collectors.toList());
        } catch (DependencyFailureException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("getWarehouseByWarehouseName系统异常: warehouseName:{}", JSON.toJSONString(warehouseName), ex);
            throw new InternalFailureException(ex.getMessage());
        }

        return warehouseVOS;
    }
}
