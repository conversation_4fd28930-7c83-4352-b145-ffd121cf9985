package com.tops.order.service;

import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.vo.AggOrderStatusRequest;
import com.tops.order.domain.vo.OrderStatusCountListVO;
import com.tops.order.domain.vo.QueryOrderStatusRequest;

import java.util.Date;
import java.util.List;

/**
 * 订单状态监控报表服务
 */
public interface OrderReportService {
    List<OrderStatusCountListVO> getOrderStatus(QueryOrderStatusRequest queryOrderStatusRequest) throws InternalFailureException;

    void recordSnapshot(AggOrderStatusRequest aggOrderStatusRequest);

    /**
     * 订单状态数据清理
     * @param createTime
     */
    void backupOrderStatus(Date createTime) throws InternalFailureException;
}
