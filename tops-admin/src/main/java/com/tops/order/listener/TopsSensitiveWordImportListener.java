package com.tops.order.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tops.common.excel.ExcelListener;
import com.tops.common.excel.ExcelResult;
import com.tops.common.exception.ServiceException;
import com.tops.common.helper.LoginHelper;
import com.tops.common.utils.spring.SpringUtils;
import com.tops.order.domain.bo.TopsOrderSensitiveWordsBo;
import com.tops.order.domain.vo.TopsOrderSensitiveWordsVo;
import com.tops.order.service.ITopsOrderSensitiveWordsService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * ClassName:TopsSensitiveWordImportListener
 * Package:com.tops.order.listener
 * Description:
 *
 * @date:2024/12/20 上午11:36
 * @author:WeiLiming
 */
@Slf4j
public class TopsSensitiveWordImportListener extends AnalysisEventListener<TopsOrderSensitiveWordsVo> implements ExcelListener<TopsOrderSensitiveWordsVo> {

    private final ITopsOrderSensitiveWordsService topsOrderSensitiveWordsService;
    private final Boolean isUpdateSupport;
    private final String operName;
    private int successNum = 0;
    private int failureNum = 0;
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();

    public TopsSensitiveWordImportListener(Boolean isUpdateSupport) {
        this.topsOrderSensitiveWordsService = SpringUtils.getBean(ITopsOrderSensitiveWordsService.class);
        this.isUpdateSupport = isUpdateSupport;
        this.operName = LoginHelper.getUsername();
    }

    @Override
    public void invoke(TopsOrderSensitiveWordsVo wordsVo, AnalysisContext context) {
        TopsOrderSensitiveWordsBo topsOrderSensitiveWords = new TopsOrderSensitiveWordsBo();
        topsOrderSensitiveWords.setSensitiveWord(wordsVo.getSensitiveWord());
        topsOrderSensitiveWords.setType(wordsVo.getType());
        topsOrderSensitiveWords.setCate(wordsVo.getCate());
        topsOrderSensitiveWords.setCreateBy(operName);
        topsOrderSensitiveWords.setUpdateBy(operName);
        try {
            Thread.sleep(2);
            boolean flag = topsOrderSensitiveWordsService.insertByBo(topsOrderSensitiveWords);
            if (flag) {
                successNum++;
                //successMsg.append("<br/>").append(successNum).append("、字典标签 ").append(dictData.getDictLabel()).append(" 导入成功");
            } else {
                failureNum++;
                String msg = "<br/>" + failureNum + "、数据 " + wordsVo.getSensitiveWord() + " 导入失败：";
                failureMsg.append(msg);
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、数据 " + wordsVo.getSensitiveWord() + " 导入失败：";
            failureMsg.append(msg).append(e.getMessage());
            log.error(msg, e);
        }
    }
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }
    @Override
    public ExcelResult<TopsOrderSensitiveWordsVo> getExcelResult() {
        return new ExcelResult<TopsOrderSensitiveWordsVo>() {
            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
                    throw new ServiceException(failureMsg.toString());
                } else {
                    successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
                }
                return successMsg.toString();
            }
            @Override
            public List<TopsOrderSensitiveWordsVo> getList() {
                return null;
            }
            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
