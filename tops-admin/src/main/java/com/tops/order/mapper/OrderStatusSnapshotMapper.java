package com.tops.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.order.domain.po.OrderStatusSnapshot;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@DS("tidb")
public interface OrderStatusSnapshotMapper {
    OrderStatusSnapshot getOrderStatusSnapshotById(long id);
    List<OrderStatusSnapshot> getOrderStatusSnapshotByUniqueKey(@Param("businessUnitList") List<String> businessUnitList,
                                                                @Param("snapshotTime") Date snapshotTime);
    void insertOrderStatusSnapshot(OrderStatusSnapshot orderStatusSnapshot);
    void updateOrderStatusSnapshot(OrderStatusSnapshot orderStatusSnapshot);
    void deleteOrderStatusSnapshot(long id);
}
