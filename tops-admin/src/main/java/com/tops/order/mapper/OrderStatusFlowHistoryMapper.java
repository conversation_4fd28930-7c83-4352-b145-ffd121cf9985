package com.tops.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.order.domain.po.OrderStatusFlow;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderStatusFlowMapper
 * @Description 用途
 * @date 2024年01月08日 5:51 PM
 */
@DS("tidb")
public interface OrderStatusFlowHistoryMapper {
    void insert(OrderStatusFlow orderStatusFlow);
    void update(OrderStatusFlow orderStatusFlow);
    void deleteById(Long id);
    OrderStatusFlow findById(Long id);
    List<OrderStatusFlow> findByOrderNo(String orderNo);
}
