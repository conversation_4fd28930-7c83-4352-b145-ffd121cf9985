package com.tops.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.tops.order.domain.dto.OrderStatusCountDTO;
import com.tops.order.domain.dto.OrderStatusFlowQO;
import com.tops.order.domain.po.OrderStatusFlow;
import com.tops.order.domain.vo.OrderStatusMonitorVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderStatusFlowMapper
 * @Description 用途
 * @date 2024年01月08日 5:51 PM
 */
@DS("tidb")
public interface OrderStatusFlowMapper {
    void insert(OrderStatusFlow orderStatusFlow);
    void updateSelective(OrderStatusFlow orderStatusFlow);
    void deleteById(Long id);
    OrderStatusFlow findById(Long id);
    List<OrderStatusFlow> findByOrderNo(String orderNo);
    List<OrderStatusCountDTO> getAllOrderStatusCounts(OrderStatusFlowQO orderStatusFlowQo);

    List<OrderStatusCountDTO> getOrderStatusCountsByBusinessUnit(OrderStatusFlowQO orderStatusFlowQo);

    void deleteByCreateTime(@Param("createTime") Date createTime);
}
