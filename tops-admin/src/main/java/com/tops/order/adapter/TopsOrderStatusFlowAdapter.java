package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.search.api.GetOrderStatusService;
import cn.jdl.oms.search.dto.request.GetOrderStatusRequest;
import cn.jdl.oms.search.dto.response.GetOrderStatusResponse;
import cn.jdl.oms.search.dto.response.OrderStatus;
import com.tops.order.domain.vo.TopsOrderStatusVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName:TopsOrderStatusFlowAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/8/6 下午7:59
 * @author:WeiLiming
 */
@Service
public class TopsOrderStatusFlowAdapter {
    @Autowired
    private GetOrderStatusService getOrderStatusService;
    private static final String RESPONSE_CODE_SUCCESS = "1";

    /**
     * 获取订单状态流
     * @param orderNo
     * @return
     */
    public List<TopsOrderStatusVo> getOrderStatus(String orderNo) {
        GetOrderStatusRequest getOrderStatusRequest = new GetOrderStatusRequest();
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit("cn_jdl_supplychain");
        businessIdentity.setBusinessType("outbound_union");
        getOrderStatusRequest.setBusinessIdentity(businessIdentity);
        getOrderStatusRequest.setOrderNo(orderNo);
        GetOrderStatusResponse orderStatusResponse = getOrderStatusService.getOrderStatus(buildRequestProfile(), getOrderStatusRequest);
        if (orderStatusResponse == null || !RESPONSE_CODE_SUCCESS.equals(orderStatusResponse.getCode()) || CollectionUtils.isEmpty(orderStatusResponse.getData())) {
            return null;
        }
        List<TopsOrderStatusVo> topsOrderStatusVoList = new ArrayList<>();

        List<OrderStatus> orderStatusesResponse = orderStatusResponse.getData();
        for (OrderStatus orderStatus : orderStatusesResponse) {
            TopsOrderStatusVo topsOrderStatusVo = new TopsOrderStatusVo();
            topsOrderStatusVo.setStatusCode(orderStatus.getOrderStatus());
            topsOrderStatusVo.setStatusName(orderStatus.getOrderStatusName());
            topsOrderStatusVo.setOperator(orderStatus.getOperator());
            topsOrderStatusVo.setOperationSource(orderStatus.getOperationSource());
            topsOrderStatusVo.setOperationTime(orderStatus.getOperationTime());
            topsOrderStatusVoList.add(topsOrderStatusVo);
        }
        return topsOrderStatusVoList;
    }

    /**
     * 获取订单标准状态流
     * @param orderNo
     * @return
     */
    public List<TopsOrderStatusVo> getOrderStandardStatus(String orderNo) {
        GetOrderStatusRequest getOrderStatusRequest = new GetOrderStatusRequest();
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit("cn_jdl_supplychain");
        businessIdentity.setBusinessType("outbound_union");
        getOrderStatusRequest.setBusinessIdentity(businessIdentity);
        getOrderStatusRequest.setOrderNo(orderNo);
        GetOrderStatusResponse orderStatusResponse = getOrderStatusService.getOrderStatus(buildRequestProfile(), getOrderStatusRequest);
        if (orderStatusResponse == null || !RESPONSE_CODE_SUCCESS.equals(orderStatusResponse.getCode()) || CollectionUtils.isEmpty(orderStatusResponse.getData())) {
            return null;
        }
        List<TopsOrderStatusVo> topsOrderStatusVoList = new ArrayList<>();

        List<OrderStatus> orderStatusesResponse = orderStatusResponse.getData();
        for (OrderStatus orderStatus : orderStatusesResponse) {
            TopsOrderStatusVo topsOrderStatusVo = new TopsOrderStatusVo();
            topsOrderStatusVo.setStatusCode(orderStatus.getOrderStandardStatus());
            topsOrderStatusVo.setStatusName(orderStatus.getOrderStandardStatusName());
            topsOrderStatusVo.setOperator(orderStatus.getOperator());
            topsOrderStatusVo.setOperationSource(orderStatus.getOperationSource());
            topsOrderStatusVo.setOperationTime(orderStatus.getOperationTime());
            topsOrderStatusVoList.add(topsOrderStatusVo);
        }
        return topsOrderStatusVoList;
    }

    /**
     * 构建基础信息
     *
     * @return
     */
    private static RequestProfile buildRequestProfile() {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setTenantId("1000");
        requestProfile.setLocale("zh_CN");
        requestProfile.setTraceId(String.valueOf(System.currentTimeMillis()));
        return requestProfile;
    }
}
