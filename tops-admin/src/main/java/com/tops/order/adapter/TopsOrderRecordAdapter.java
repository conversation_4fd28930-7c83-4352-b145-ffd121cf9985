package com.tops.order.adapter;

/**
 * ClassName:TopsOrderRecordAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/6/12 下午3:32
 * @author:WeiLiming
 */

import com.tops.common.core.domain.entity.SysDictData;
import com.tops.common.enums.TopsStatCateEnum;
import com.tops.common.enums.TopsStatDimensionEnum;
import com.tops.common.enums.TopsStatTypeEnum;
import com.tops.common.utils.TopsDateUtils;
import com.tops.order.domain.bo.TopsStatRecordBo;
import com.tops.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TopsOrderRecordAdapter {
    @Autowired
    private RestHighLevelClient client;
    private static final String INDEX_RULE = "order_alias";

    public List<TopsStatRecordBo> statRecords(TopsStatRecordBo bo) {
        List<TopsStatRecordBo> results = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest(INDEX_RULE);

        try {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // Query
            BoolQueryBuilder boolQuery = null;
            boolQuery = QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("receivedTime").gte(bo.getBeginTime())
                    .lt(bo.getEndTime()))
                .must(QueryBuilders.termsQuery("businessUnit", buildBusinessUnit(null)));

            searchSourceBuilder.query(boolQuery);

            // Aggregations
            DateHistogramAggregationBuilder dateHistogramAgg = AggregationBuilders.dateHistogram("by_day").field("receivedTime")
                .dateHistogramInterval(DateHistogramInterval.DAY)
                .minDocCount(0)
                .format("yyyy-MM-dd").timeZone(ZoneId.of("Asia/Shanghai")); // Beijing time

            TermsAggregationBuilder termsAgg = AggregationBuilders.terms("by_business_unit").field("businessUnit").size(100);

            dateHistogramAgg.subAggregation(termsAgg);
            searchSourceBuilder.aggregation(dateHistogramAgg);
            searchSourceBuilder.size(0);

            searchRequest.source(searchSourceBuilder);

            // Execute search
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = searchResponse.getAggregations();

            Histogram byDayAgg = aggregations.get("by_day");

            for (Histogram.Bucket bucket : byDayAgg.getBuckets()) {
                String date = bucket.getKeyAsString();
                Terms byBusinessUnitAgg = bucket.getAggregations().get("by_business_unit");
                TopsStatRecordBo recordBo = null;

                for (Terms.Bucket subBucket : byBusinessUnitAgg.getBuckets()) {
                    String businessUnit = subBucket.getKeyAsString();
                    long count = subBucket.getDocCount();
                    recordBo = new TopsStatRecordBo(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode(), TopsStatCateEnum.CATE_DAY.getCode(), TopsDateUtils.toDate(date, "yyyy-MM-dd"), TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode(), businessUnit, count);
                    results.add(recordBo);
                }
            }
        } catch (Exception e) {
            log.error("订单统计异常", e);
        }
        return results;
    }

    @Autowired
    private ISysDictTypeService iSysDictTypeService;

    private List<String> buildBusinessUnit(List<String> businessUnitList) {
        // 设置业务身份，优先取入参数据，若为空则从字典中获取
        if (CollectionUtils.isNotEmpty(businessUnitList)) {
            return businessUnitList;
        } else {
            List<SysDictData> sysDictDataVos = iSysDictTypeService.selectDictDataByType("ops_business_units");
            // List<DictModel> dictModelList = sysBaseApi.queryEnableDictItemsByCode("com_business_units");
            businessUnitList = sysDictDataVos.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
            return businessUnitList;
        }
    }

}
