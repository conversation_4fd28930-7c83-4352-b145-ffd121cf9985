package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.relation.dict.RelationFieldKeyEnum;
import cn.jdl.oms.relation.model.OrderElement;
import cn.jdl.oms.relation.model.QueryOrderRelationRequest;
import cn.jdl.oms.relation.model.QueryOrderRelationResponse;
import cn.jdl.oms.relation.service.QueryOrderRelationService;
import com.alibaba.fastjson.JSON;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.order.domain.dto.ReceiveRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * ClassName:TopsQueryRelationAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/5/31 下午5:34
 * @author:WeiLiming
 */
@Slf4j
@Service
public class TopsQueryRelationAdapter {
    @Autowired
    private QueryOrderRelationService queryOrderRelationService;

    public String queryOrderRelation(ReceiveRequestDTO receiveRequestDTO) {
        CallerInfo callerInfo = Profiler.registerInfo("jdl-ops.adapter.QueryOrderRelationServiceAdapter.queryOrderRelation", "jdl-ops", true, false);
        QueryOrderRelationResponse response = null;
        log.warn("当前线程：" + Thread.currentThread().getName());
        long start = System.currentTimeMillis();
        RequestProfile profile = generateProfile(receiveRequestDTO);
        QueryOrderRelationRequest request = generateRequest(receiveRequestDTO);
        try {
            response = queryOrderRelationService.queryOrderRelation(profile, request);
            if (response == null || !response.isSuccess() || response.getData() == null || CollectionUtils.isEmpty(response.getData().getOrders())) {
                log.warn("关系入参={}, 客户订单号：{}，未获取到对应订单号。关系出参={}", JSON.toJSONString(request), receiveRequestDTO.getCustomerOrderNo(), JSON.toJSONString(response));
                return null;
            }
            if (response.getData().getOrders().size() > 1) {
                log.warn("客户订单号：{}，关联了多个订单，暂只取第一个", receiveRequestDTO.getCustomerOrderNo());
            }
            return response.getData().getOrders().get(0).getBaseInfo().getOrderNo();
        } catch (Throwable e) {
            log.error("QueryOrderRelationServiceAdapter.queryOrderRelation 查询订单关联关系失败 request:{},cost:{},msg:{},exception:", JSON.toJSONString(request), System.currentTimeMillis() - start, e.getMessage(), e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private static RequestProfile generateProfile(ReceiveRequestDTO receiveRequestDTO) {
        RequestProfile profile = new RequestProfile();
        profile.setLocale("zh_CN");
        profile.setTimeZone("GMT+8");
        profile.setTraceId(System.currentTimeMillis() + "");
        profile.setTenantId(receiveRequestDTO.getTenantId());
        return profile;
    }

    private static QueryOrderRelationRequest generateRequest(ReceiveRequestDTO receiveRequestDTO) {
        //业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit("cn_jdl_supplychain");
        businessIdentity.setBusinessType("outbound_union");
        //客户订单号
        OrderElement orderElement = new OrderElement();
        orderElement.setBusinessIdentity(businessIdentity);
        orderElement.setOrderNo(receiveRequestDTO.getCustomerOrderNo());
        //属性
        Map<String, Object> attrs = new HashMap<>();
        attrs.put(RelationFieldKeyEnum.ACCOUNT_NO.getCode(), receiveRequestDTO.getAccountNo());
        attrs.put(RelationFieldKeyEnum.SYSTEM_CALLER.getCode(), receiveRequestDTO.getSystemCaller());
        attrs.put(RelationFieldKeyEnum.BUSINESS_TYPE.getCode(), receiveRequestDTO.getBusinessType());
        attrs.put(RelationFieldKeyEnum.ORDER_TYPE.getCode(), "100");
        //创建请求对象
        QueryOrderRelationRequest queryRelationRequest = new QueryOrderRelationRequest();
        queryRelationRequest.setSource(orderElement);
        queryRelationRequest.setAttributes(attrs);
        return queryRelationRequest;
    }

}
