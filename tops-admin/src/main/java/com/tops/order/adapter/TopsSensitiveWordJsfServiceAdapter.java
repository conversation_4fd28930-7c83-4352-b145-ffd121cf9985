package com.tops.order.adapter;

import com.jd.sensitiveword.domain.SensitiveWordCheckRequest;
import com.jd.sensitiveword.domain.SensitiveWordCheckResult;
import com.jd.sensitiveword.domain.SensitiveWordLexeme;
import com.jd.sensitiveword.service.SensitiveWordJsfService;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.order.domain.vo.TopsOrderSensitiveWordsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * ClassName:TopsSensitiveWordJsfServiceAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/12/19 下午8:22
 * @author:WeiLiming
 */
@Slf4j
@Service
public class TopsSensitiveWordJsfServiceAdapter {
    private static final String SUB_CATEGORYS_ZERO = "0";
    private static final int USE_CATEGORY_500 = 512;
    private static final int RESULT_HIT_SS = 1;
    private static final int RESULT_HIT_NONE = 0;

    /**
     * 敏感词接口
     */
    @Resource
    private SensitiveWordJsfService sensitiveWordJsfService;
    /**
     * [内容安全]
     * 敏感词校验
     * 创建待审核文本请求
     *
     * @param text
     * @return
     */
    private SensitiveWordCheckRequest createSensitiveWordCheckRequest(String text) {
        Collection<String> subCategorys = new ArrayList<>();
        subCategorys.add(SUB_CATEGORYS_ZERO);
        return new SensitiveWordCheckRequest(text, USE_CATEGORY_500, subCategorys);
    }
    /**
     * 敏感词校验
     * @param auditText
     * @return
     */
    public TopsOrderSensitiveWordsVo checkSensitiveWord(String auditText) {
        TopsOrderSensitiveWordsVo vo = new TopsOrderSensitiveWordsVo();
        try {
            SensitiveWordCheckRequest sensitiveWordCheckRequest = createSensitiveWordCheckRequest(auditText);
            log.info("Start sensitiveWordJsfService.checkSensitiveWord sensitiveWordCheckRequest: {}", TopsJsonUtils.toJSONString(sensitiveWordCheckRequest));
            SensitiveWordCheckResult sensitiveWordCheckResult =
                sensitiveWordJsfService.checkSensitiveWord(sensitiveWordCheckRequest);
            log.info("End sensitiveWordJsfService.checkSensitiveWord result: {}", TopsJsonUtils.toJSONString(sensitiveWordCheckResult));

            if (sensitiveWordCheckResult == null) {
                vo.setIsHit(RESULT_HIT_NONE);
                vo.setExtension2("敏感词校验结果为空");
                vo.setExtension3("调用失败");
                return vo;
            }

            vo.setExtension3("调用成功");
            if (sensitiveWordCheckResult.isPass) {
                vo.setIsHit(RESULT_HIT_NONE);
                vo.setExtension2("未命中敏感词");
                return vo;
            }
            vo.setIsHit(RESULT_HIT_SS);
            vo.setCallStatus(String.valueOf(sensitiveWordCheckResult.state));
            List<SensitiveWordLexeme> words = sensitiveWordCheckResult.words;
            if (CollectionUtils.isEmpty(words)) {
                vo.setExtension2("命中敏感词，但敏感词列表为空");
            }
            vo.setSensitiveWordsList(TopsJsonUtils.toJSONString(words));
            return vo;
        } catch (Exception e) {
            log.error("审核文本发生异常.入参:{}", auditText, e);
            vo.setExtension2(e.getMessage());
            vo.setExtension3("调用失败");
            return vo;
        }
    }

}
