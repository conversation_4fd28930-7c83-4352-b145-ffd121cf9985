package com.tops.order.adapter;

import com.jd.order.sdk.domain.param.OrderInfoQueryVoParam;
import com.jd.order.sdk.domain.result.OrderStatusResult;
import com.jd.order.sdk.export.QueryOrderInfoService;
import com.tops.common.utils.TopsJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * ClassName:TopsJDOrderAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/8/23 下午4:41
 * @author:WeiLiming
 */
@Slf4j
@Component
public class TopsJDOrderAdapter {

    @Autowired
    private QueryOrderInfoService queryOrderInfoService;

    public String queryStatus(String orderId) {
        if (StringUtils.isBlank(orderId) || "null".equals(orderId) || "0".equals(orderId)) {
            return null;
        }
        Map<String, String> resultMap = new HashMap<>();
        try {
            OrderInfoQueryVoParam requestParam = new OrderInfoQueryVoParam();
            requestParam.setOrderId(orderId);
            requestParam.setPin("11");
            requestParam.setLanguage("zh_CN");
            requestParam.setBuId(301);
            requestParam.setBiz(1);
            com.jd.order.sdk.domain.param.ClientInfo clientInfo = new com.jd.order.sdk.domain.param.ClientInfo();
            clientInfo.setSystemName("ordertrack.360buy.com");
            clientInfo.setToken("2C0D7A247EBDC4846D916776C5F8BD98");
            String serverIp = null;
            try {
                InetAddress address = InetAddress.getLocalHost();
                serverIp = address.getHostAddress();
            } catch (Exception e) {
                log.error("获取serverIp异常", e);
            }
            clientInfo.setServerIP(StringUtils.isNotBlank(serverIp) ? serverIp : "127.0.0.1");
            clientInfo.setUserIP("127.0.0.1");
            requestParam.setClientInfo(clientInfo);
            log.info("调零售订单状态接口入参，requestParam{}", TopsJsonUtils.toJSONString(requestParam));
            OrderStatusResult result = queryOrderInfoService.queryOrderState(requestParam);
            log.info("调零售订单状态接口出参，result{}", TopsJsonUtils.toJSONString(result));
            if (result == null) {
                return null;
            }
            String status = String.valueOf(result.getOrderState());
            String cancelStatus = String.valueOf(result.getYn());
            resultMap.put("status", status);
            resultMap.put("cancelStatus", cancelStatus);
            return status;
        } catch (Exception e) {
            log.error("查询零售订单状态异常", e);
        }
        return null;
    }

}
