package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.report.api.order.ModifySomeDataOpsService;
import cn.jdl.oms.report.dto.request.ModifyOrderSomeDataOpsRequest;
import cn.jdl.oms.search.api.GetOrderService;
import cn.jdl.oms.search.dto.BaseInfo;
import cn.jdl.oms.search.dto.GetOrderRequest;
import cn.jdl.oms.search.dto.GetOrderResponse;
import cn.jdl.oms.search.dto.Order;
import cn.jdl.oms.search.dto.request.IncludeFieldEnum;
import com.jd.eclp.core.Null;
import com.jd.fastjson.JSON;
import com.jdl.cp.core.spec.ApiResult;
import com.tops.order.domain.dto.ModifyOrderApprovalContent;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.enums.CargoMatchTypEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName ModifyOutboundOrderServiceAdapter
 * @Description 用途
 * @date 2024年08月08日 10:17 PM
 */
@Slf4j
@Component
public class ModifyOutboundOrderServiceAdapter {

    @Resource
    private ModifySomeDataOpsService modifySomeDataOpsService;

    @Resource
    private GetOrderService getOrderService;

    private static final String DOMAIN_UN_SUPPORT_PATTERN = "%s暂不支持修改, 请联系数据组填写修数工单";

    /**
     * 查询订单信息报文
     *
     * @return
     */
    public List<XbpOperateResult> modifyOrder(List<String> orderNoList, ModifyOrderSomeDataOpsRequest request, Integer cargoMatchType) {
        List<XbpOperateResult> results = new ArrayList<>();
        Order order = request.getOrder();
        String traceId = String.valueOf(System.currentTimeMillis());

        for(String orderNo : orderNoList) {
            try {
                //校验修改的域是否支持,目前暂时只支持仓配
                if(order.getBaseInfo() == null) {
                    order.setBaseInfo(new BaseInfo());
                }

                order.getBaseInfo().setOrderNo(orderNo);
                String msg = validModifyRequest(order);
                if(StringUtils.isNotBlank(msg)) {
                    results.add(XbpOperateResult.fail(orderNo, msg));
                    continue;
                }

                RequestProfile profile = RequestProfile.ofHome().withTenantId("1000").withTraceId(traceId);
                //如果修改某个货品, 且是按照cargo+cargoLevel匹配，自动增加行号信息
                if(order.getCargoInfos() != null && CargoMatchTypEnum.CARGO_LEVEL.getCode().equals(cargoMatchType)) {
                    GetOrderRequest getOrderRequest = GenerateOrderRequest(order);
                    log.info("[执行修改接口] 修改货品信息,按行号匹配,需查询订单详情补上行号{}", JSON.toJSONString(getOrderRequest));
                    GetOrderResponse orderResponse = getOrderService.getOrder(profile, getOrderRequest);
                    log.info("[执行修改接口] 修改货品信息,按行号匹配,需查询订单详情补上行号, 查询结果", JSON.toJSONString(orderResponse));
                    if(orderResponse != null && orderResponse.getData() != null) {
                        copyCargoLineNo(request, orderResponse.getData(), cargoMatchType);
                    } else {
                        //查询不到订单详情,无法修改货品信息
                        results.add(XbpOperateResult.fail(orderNo, orderResponse.getMessage()));
                        continue;
                    }
                }

                com.jdl.cp.core.spec.RequestProfile requestProfile = com.jdl.cp.core.spec.RequestProfile.ofHome().
                    withTenantId("1000").withTraceId(traceId);
                log.info("[执行修改接口] 修改订单信息,修改请求:{}", JSON.toJSONString(request));
                ApiResult<Null> modifyResult = modifySomeDataOpsService.modifySomeDataWithSearchOrder(requestProfile,
                    request);
                log.info("[执行修改接口] 修改订单信息,修改结果:{}", JSON.toJSONString(modifyResult));
                if(modifyResult.isSuccess()) {
                    results.add(XbpOperateResult.success(orderNo));
                } else {
                    results.add(XbpOperateResult.fail(orderNo,StringUtils.truncate(modifyResult.getMessage(), 20)));
                }
            } catch (Exception ex) {
                log.info("[执行修改接口] 修改订单失败, ex:", ex);
                //大部分是超时错误，截取定长即可
                results.add(XbpOperateResult.fail(orderNo,StringUtils.truncate(ex.getMessage(), 50)));
            }
        }

        return results;
    }


    /**
     * 校验不能被修改的域
     * Agreement、AttachmentInfo、ReturnInfo、EnquiryInfo、Promotion、Deduction、Finance不支持修改
     * @param order
     * @return
     */
    private String validModifyRequest(Order order) {
        if(order.getPromotionInfo() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.PROMOTION.getDesc());
        }

        if(order.getAttachmentInfos() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.ATTACHMENT.getDesc());
        }

        if(order.getReturnInfo() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.RETURN.getDesc());
        }

        if(order.getEnquiryInfo() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.ENQUIRY.getDesc());
        }

        if(order.getDeductionInfos() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.DEDUCTION.getDesc());
        }

        if(order.getFinanceInfo() != null) {
            return String.format(DOMAIN_UN_SUPPORT_PATTERN, IncludeFieldEnum.FINANCE.getDesc());
        }

        return null;
    }

    /**
     * 生成查询订单详情请求(只查询货品)
     * @param order
     * @return
     */
    private GetOrderRequest GenerateOrderRequest(Order order) {
        // 当前仅支持销售出业务查询
        GetOrderRequest getOrderRequest = new GetOrderRequest();
        getOrderRequest.setOrderGroup("Outbound");
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(order.getBusinessIdentity().getBusinessUnit());
        getOrderRequest.setBusinessIdentity(businessIdentity);
        getOrderRequest.setIncludeFields(Arrays.asList(IncludeFieldEnum.CARGO.getCode()));
        getOrderRequest.setOrderNo(order.getBaseInfo().getOrderNo());
        return getOrderRequest;
    }

    /**
     * 复制货品行号信息(比如某个EMG需要选择cargoSign)
     * @param request
     * @param snapshot
     * @param cargoMatchType
     */
    private void copyCargoLineNo(ModifyOrderSomeDataOpsRequest request, Order snapshot, Integer cargoMatchType) {
        //根据货品类型进行匹配, 匹配成功后进行拷贝
        Map<String, CargoInfo> modifyCargoMap = request.getOrder().getCargoInfos().stream()
            .collect(Collectors.toMap(cargoInfo -> getUniqueLine(cargoInfo, cargoMatchType), Function.identity(), (v1, v2) -> v1));
        List<CargoInfo> modifyCargoList = new ArrayList<>();

        //部分修改操作类型也需要补上行号
        Map<String, Integer> cargoInfoOperateMap = new HashMap<>();

        for(CargoInfo snapCargoInfo : snapshot.getCargoInfos()) {
            String snapCargoInfoUniqueKey = getUniqueLine(snapCargoInfo, cargoMatchType);
            if(modifyCargoMap.containsKey(snapCargoInfoUniqueKey)) {
                CargoInfo newCargoInfo = new CargoInfo();
                BeanUtils.copyProperties(modifyCargoMap.get(snapCargoInfoUniqueKey), newCargoInfo);
                newCargoInfo.setCargoLevel(snapCargoInfo.getCargoLevel());
                newCargoInfo.setCargoLineNo(snapCargoInfo.getCargoLineNo());
                modifyCargoList.add(newCargoInfo);
            }

            if(request.getCargoOperateType() != null) {
                if(cargoInfoOperateMap.containsKey(snapCargoInfoUniqueKey)) {
                    cargoInfoOperateMap.put(getUniqueLine(snapCargoInfo, cargoMatchType), cargoInfoOperateMap.get(snapCargoInfoUniqueKey));
                }
            }
        }

        request.getOrder().setCargoInfos(modifyCargoList);
        request.setCargoOperateType(cargoInfoOperateMap);
    }

    /**
     * 生成货品唯一标识
     * @param cargoInfo
     * @param cargoMatchType
     * @return
     */
    private String getUniqueLine(CargoInfo cargoInfo, Integer cargoMatchType) {
        if(CargoMatchTypEnum.CARGO_LEVEL.getCode().equals(cargoMatchType)) {
            return cargoInfo.getCargoNo() + "-" + cargoInfo.getCargoLevel();
        }

        if(CargoMatchTypEnum.CARGO_LINE.getCode().equals(cargoMatchType)) {
           return cargoInfo.getCargoNo() + "-" + cargoInfo.getCargoLevel() + "-" + cargoInfo.getCargoLineNo();
        }

        return null;
    }
}
