package com.tops.order.adapter;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.jd.eclp.master.warehouse.service.WarehouseService;
import com.tops.common.exception.DependencyFailureException;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TopsWarehouseInfoAdapter
 * @Description 用途
 * @date 2024年08月22日 6:15 PM
 */
@Slf4j
@Service
public class TopsWarehouseInfoAdapter {
    @Resource
    private WarehouseService warehouseService;

    public Warehouse queryWarehouse(String warehouseNo) throws DependencyFailureException {
        try {
            log.info("根据仓库编码查询库房信息, warehouseNo:{}", warehouseNo);
            Warehouse warehouse = warehouseService.getWarehouseByWarehouseNo(warehouseNo);
            return warehouse;
        } catch (Exception ex) {
            log.error("根据仓库编码查询库房信息失败:", ex);
            throw new DependencyFailureException(ex.getMessage());
        }
    }

    public List<Warehouse> fuzzyQueryByWarehouseName(String warehouseName) throws DependencyFailureException {
        try {
            log.info("根据仓库名称模糊查询订单信息, warehouseName:{}", warehouseName);
            Warehouse warehouse = new Warehouse();
            warehouse.setWarehouseName(warehouseName);
            List<Warehouse> warehouseList = warehouseService.findWarehouseByNameAndRegion(warehouse);
            return warehouseList;
        } catch (Exception ex) {
            log.error("根据仓库名称模糊查询订单信息失败:", ex);
            throw new DependencyFailureException(ex.getMessage());
        }
    }
}
