package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.api.CancelOutboundOrderService;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.supplychain.model.CancelOutboundOrderRequest;
import cn.jdl.oms.supplychain.model.CancelOutboundOrderResponse;
import com.alibaba.fastjson.JSON;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.dto.CancelOrderResult;
import com.tops.order.enums.CancelOrderResultEnum;
import com.tops.order.enums.CancelOrderTypeEnum;
import javax.annotation.Resource;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Getter
@Setter
@Component
public class CancelOutboundOrderServiceAdapter {
    @Resource
    private CancelOutboundOrderService cancelOutboundOrderService;

    public Integer cancelOrder(CancelOrderDTO cancelOrderDTO) throws InvalidRequestException, InternalFailureException, DependencyFailureException {
        //TODO wangqin83 增加UMP打点
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setTraceId(System.currentTimeMillis() + "");
        requestProfile.setTenantId("1000");
        requestProfile.setLocale("zh_CN");
        requestProfile.setTimeZone("GMT+8");
        CancelOutboundOrderRequest cancelRequest = new CancelOutboundOrderRequest();
        CancelOutboundOrderResponse response = null;
        try {
            BusinessIdentity businessIdentity = new BusinessIdentity();
            businessIdentity.setBusinessUnit("cn_jdl_sc-isv");
            businessIdentity.setBusinessType("outbound_sale");

            ChannelInfo channelInfo = new ChannelInfo();
            channelInfo.setChannelOperateTime(new Date());
            channelInfo.setSystemCaller("OtherSystem");
            //TODO wangqin83 单个取消时允许用户输入
            Map<String, String> extendProps = new HashMap<>();
            extendProps.put("requestionId", "10000");
            //由前端选择取消（-1）还是拦截（1）
            extendProps.put("shipAllowCancelSign", cancelOrderDTO.getCancelType());
            if (Objects.equals(cancelOrderDTO.getCancelType(),CancelOrderTypeEnum.DELIVERY_INTERCEPT.getCode())){
                extendProps.put("deliveryIntercept", cancelOrderDTO.getCancelType());
            }
            cancelRequest.setBusinessIdentity(businessIdentity);
            cancelRequest.setOrderNo(cancelOrderDTO.getCargoNo());
            cancelRequest.setChannelInfo(channelInfo);
            cancelRequest.setOperator(cancelOrderDTO.getOperator());
            cancelRequest.setRemark(cancelOrderDTO.getRemark());
            cancelRequest.setExtendProps(extendProps);
            response = cancelOutboundOrderService.cancelOrder(requestProfile, cancelRequest);
            log.info("CancelOutboundOrderServiceAdapter.cancelOrder profile:{},requset:{},response:{}", JSON.toJSONString(requestProfile), JSON.toJSONString(cancelRequest), JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("CancelOutboundOrderServiceAdapter.cancelOrder profile:{},requset:{},exception:", JSON.toJSONString(requestProfile), JSON.toJSONString(cancelRequest), e);
            throw new DependencyFailureException("发起取消失败，请重试");
        }
        if (invokeFailForNotifyingError(response)){
            return CancelOrderResultEnum.CANCEL_NOTIFY_ERROR.getCode();
        }
        if (!invokeSuccess(response)){
            throw new InvalidRequestException(response.getMessage());
        }else{
            return response.getData().getCancelResult();
        }
    }

    private boolean invokeSuccess(CancelOutboundOrderResponse response) {
        if (response != null
            && Objects.equals(response.getCode(), "1")) {
            return true;
        }
        return false;
    }

    private boolean invokeFailForNotifyingError(CancelOutboundOrderResponse response) {
        if (response != null
            && Objects.equals(response.getCode(), "2-01-11025")) {
            return true;
        }
        return false;
    }

}
