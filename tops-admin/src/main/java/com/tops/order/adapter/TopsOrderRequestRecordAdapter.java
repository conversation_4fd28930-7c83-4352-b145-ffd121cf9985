package com.tops.order.adapter;

import com.alibaba.fastjson2.JSON;
import com.esotericsoftware.minlog.Log;
import com.tops.common.constant.TopsConstants;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.exception.OpsException;
import com.tops.common.utils.redis.RedisUtils;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.bo.TopsStatRecordBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import com.tops.order.domain.vo.TopsStatRecordVo;
import com.tops.order.enums.DataMatchTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ValueCountAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.MaxBucketPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName:TopsOrderRequestRecordAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/5/29 上午10:17
 * @author:WeiLiming
 */
@Slf4j
@Service
public class TopsOrderRequestRecordAdapter {
    @Autowired
    private RestHighLevelClient client;

    private static final String INDEX_RULE = "1000_new_outbound_order_flow_%s";

    private static final String EXPRESS_INDEX_RULE = "1000_express_order_flow_%s";


    private static final String INDEX_ALIAS = "1000_outbound_order_flow_alias";

    /**
     * 生成索引，若跨月则生成多个索引
     *
     * @param beginDateTime
     * @param endDateTime
     * @return
     */
    public static String[] generateIndex(String beginDateTime, String endDateTime) {
        List<String> yymmList = TopsDateUtils.getYearMonthsBetween(beginDateTime, endDateTime);
        String[] indexAttr = new String[yymmList.size()];
        for (int i = 0; i < yymmList.size(); i++) {
            indexAttr[i] = String.format(INDEX_RULE, yymmList.get(i));
        }
        return indexAttr;
    }

    /**
     * 生成索引，若跨月则生成多个索引
     *
     * @param beginDateTime
     * @param endDateTime
     * @return
     */
    public static String[] generateIndex(Date beginDateTime, Date endDateTime) {
        List<String> yymmList = TopsDateUtils.getYearMonthsBetween(beginDateTime, endDateTime);
        String[] indexAttr = new String[yymmList.size()];
        for (int i = 0; i < yymmList.size(); i++) {
            indexAttr[i] = String.format(INDEX_RULE, yymmList.get(i));
        }
        return indexAttr;
    }

    public static String[] generateExpressIndex(Date beginDateTime, Date endDateTime) {
        List<String> yymmList = TopsDateUtils.getYearMonthsBetween(beginDateTime, endDateTime);
        String[] indexAttr = new String[yymmList.size()];
        for (int i = 0; i < yymmList.size(); i++) {
            indexAttr[i] = String.format(EXPRESS_INDEX_RULE, yymmList.get(i));
        }
        return indexAttr;
    }

    private static BoolQueryBuilder buildBoolQueryBuilder(TopsOrderRequestBo bo) {
        if (bo.getParams().get("beginCreateTime") == null || bo.getParams().get("endCreateTime") == null) {
            throw new OpsException("请输入时间范围(必填)");
        }
        Date startTime = TopsDateUtils.toDate(bo.getParams().get("beginCreateTime").toString());
        Date endTime = TopsDateUtils.toDate(bo.getParams().get("endCreateTime").toString());
        String tenantId = bo.getTenantId() == null ? "1000" : bo.getTenantId();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
            .must(QueryBuilders.rangeQuery("createTime").from(startTime).to(endTime))
            .must(QueryBuilders.termQuery("tenantId", tenantId));
        if (CollectionUtils.isNotEmpty(bo.getExcludedResponseCode())) {
            boolQuery.mustNot(QueryBuilders.termsQuery("responseCode", bo.getExcludedResponseCode()));
        }
        if (CollectionUtils.isNotEmpty(bo.getBusinessUnit())) {
            boolQuery.must(QueryBuilders.termsQuery("businessUnit", bo.getBusinessUnit()));
        }
        if (bo.getBusinessScene() != null && !bo.getBusinessScene().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("businessScene", bo.getBusinessScene()));
        }

        if (bo.getResponseCode() != null && !bo.getResponseCode().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("responseCode", bo.getResponseCode()));
        }
        if (bo.getSystemCaller() != null && !bo.getSystemCaller().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("systemCaller", bo.getSystemCaller()));
        }
        if (bo.getOrderNo() != null && !bo.getOrderNo().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("orderNo", bo.getOrderNo()));
        }
        if (bo.getCustomerOrderNo() != null && !bo.getCustomerOrderNo().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("customerOrderNo", bo.getCustomerOrderNo()));
        }
        if (bo.getAccountNo() != null && !bo.getAccountNo().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("accountNo", bo.getAccountNo()));
        }
        if (bo.getSystemCaller() != null && !bo.getSystemCaller().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("systemCaller", bo.getSystemCaller()));
        }
        if (bo.getSoSource() != null && !bo.getSoSource().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("soSource", bo.getSoSource()));
        }
        if (bo.getTraceId() != null && !bo.getTraceId().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("traceId", bo.getTraceId()));
        }
        if (bo.getCreatePin() != null && !bo.getCreatePin().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("createPin", bo.getCreatePin()));
        }
        return boolQuery;
    }

    public Map<String, Long> getBusinessUnitRecordCount(TopsOrderRequestBo bo) throws IOException {
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(bo);
        TermsAggregationBuilder aggregation = AggregationBuilders.terms("by_businessUnit").field("businessUnit")
            // 按照客户单号进行去重，统计单据的失败数量，而非请求失败次数
            .subAggregation(AggregationBuilders.cardinality("distinct_customerOrderNo").field("customerOrderNo"));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQuery);
        searchSourceBuilder.aggregation(aggregation);
        searchSourceBuilder.size(0); // We do not need the actual documents, just the aggregation

        SearchRequest searchRequest = new SearchRequest(generateIndex(bo.getParams().get("createTime").toString(), bo.getParams().get("createTime").toString()));
        searchRequest.source(searchSourceBuilder);

        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

        Terms terms = searchResponse.getAggregations().get("by_businessUnit");
        return terms.getBuckets().stream()
            .collect(Collectors.toMap(Terms.Bucket::getKeyAsString, Terms.Bucket::getDocCount));
    }


    public List<TopsOrderRequestEsRecord> queryRecords(TopsOrderRequestBo bo, PageQuery pageQuery) throws Exception {
        SearchRequest searchRequest = new SearchRequest(generateIndex(bo.getParams().get("createTime").toString(), bo.getParams().get("createTime").toString()));
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(bo);

        int from = (pageQuery.getPageNum() - 1) * pageQuery.getPageSize();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery);

        sourceBuilder.size(pageQuery.getPageSize());
        sourceBuilder.from(from);// Adjust the size as needed

        searchRequest.source(sourceBuilder);

        SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
        long total = Objects.requireNonNull(response.getHits().getTotalHits()).value;
        pageQuery.setPageTotal(total);

        List<TopsOrderRequestEsRecord> records = new ArrayList<>();
        response.getHits().forEach(hit -> {
            TopsOrderRequestEsRecord record = getTopsOrderRequestEsRecord(hit);
            records.add(record);
        });

        return records;
    }


    private static @NotNull TopsOrderRequestEsRecord getTopsOrderRequestEsRecord(SearchHit hit) {
        Map<String, Object> sourceMap = hit.getSourceAsMap();
        TopsOrderRequestEsRecord record = new TopsOrderRequestEsRecord();
        // Populate the record object with data from sourceMap
        record.setAccountNo(String.valueOf(sourceMap.get("accountNo")));
        record.setAccountName(String.valueOf(sourceMap.get("accountName")));
        record.setAgentSales(String.valueOf(sourceMap.get("agentSales")));
        record.setBusinessScene(String.valueOf(sourceMap.get("businessScene")));
        record.setBusinessType(String.valueOf(sourceMap.get("businessType")));
        record.setBusinessUnit((String) sourceMap.get("businessUnit"));
        record.setCreatePin((String) sourceMap.get("createPin"));
        record.setCreateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli((Long) sourceMap.get("createTime")), ZoneId.systemDefault()));
        record.setCustomerOrderNo(String.valueOf(sourceMap.get("customerOrderNo")));
        record.setWaybillNo(String.valueOf(sourceMap.get("waybillNo")));
        record.setMessageId((Integer) sourceMap.get("messageId"));
        record.setMqRetryMessage((Boolean) sourceMap.get("mqRetryMessage"));
        record.setOperator((String) sourceMap.get("operator"));
        if (sourceMap.get("operatorTime") != null) {
            record.setOperatorTime(LocalDateTime.ofInstant(Instant.ofEpochMilli((Long) sourceMap.get("operatorTime")), ZoneId.systemDefault()));
        }
        record.setOrderMark((String) sourceMap.get("orderMark"));
        record.setOrderNo((String) sourceMap.get("orderNo"));
        record.setOrderStandardStatus((Integer) sourceMap.get("orderStandardStatus"));
        record.setOrderStatus((Integer) sourceMap.get("orderStatus"));
        if (sourceMap.get("orderStrategy") != null) {
            record.setOrderStrategy((Integer) sourceMap.get("orderStrategy"));
        }
        record.setPdqRetryMessage((Boolean) sourceMap.get("pdqRetryMessage"));
        record.setRequest((String) sourceMap.get("request"));
        record.setResponse((String) sourceMap.get("response"));
        record.setResponseCode((String) sourceMap.get("responseCode"));
        record.setResponseMsg((String) sourceMap.get("responseMsg"));
        record.setSkuSize((Integer) sourceMap.get("skuSize"));
        record.setSoSource((String) sourceMap.get("soSource"));
        record.setSystemCaller((String) sourceMap.get("systemCaller"));
        record.setSystemId((Integer) sourceMap.get("systemId"));
        record.setTenantId((String) sourceMap.get("tenantId"));
        record.setTopic((String) sourceMap.get("topic"));
        record.setTraceId((String) sourceMap.get("traceId"));
        record.setUpdatePin((String) sourceMap.get("updatePin"));
        if (sourceMap.get("updateTime") != null) {
            record.setUpdateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli((Long) sourceMap.get("updateTime")), ZoneId.systemDefault()));
        }
        record.setYn((Integer) sourceMap.get("yn"));
        return record;
    }

    public List<TopsOrderRequestEsRecord> scrollRecords(TopsOrderRequestBo bo) throws Exception {
        log.info("查询远程请求记录适配器入参：{}", TopsJsonUtils.toJSONString(bo));
        SearchRequest searchRequest = new SearchRequest(generateIndex(bo.getParams().get("beginCreateTime").toString(), bo.getParams().get("endCreateTime").toString()));
        BoolQueryBuilder boolQuery = buildBoolQueryBuilder(bo);
        //todo
        //Map<String, Long> count = getBusinessUnitRecordCount(bo);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQuery);
        sourceBuilder.sort("createTime", SortOrder.DESC);
        sourceBuilder.size(1000); // 设置每次scroll获取的记录数

        searchRequest.source(sourceBuilder);
        searchRequest.scroll(TimeValue.timeValueMinutes(1L)); // 设置scroll上下文有效期
        log.info("查询订单远程请求记录RPC入参：{}", TopsJsonUtils.toJSONString(searchRequest));
        SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
        log.info("查询订单远程请求记录RPC出参大小：{}", TopsJsonUtils.toJSONString(searchResponse.getHits().getHits().length));

        String scrollId = searchResponse.getScrollId();
        SearchHit[] searchHits = searchResponse.getHits().getHits();

        Map<String, TopsOrderRequestEsRecord> recordMap = new HashMap<>();
        List<TopsOrderRequestEsRecord> records = new ArrayList<>();
        int pageCount = 0;
        while (searchHits != null && searchHits.length > 0) {
            pageCount++;
            for (SearchHit hit : searchHits) {
                TopsOrderRequestEsRecord record = getTopsOrderRequestEsRecord(hit);
                records.add(record);
                recordMap.put(record.getCustomerOrderNo(), record);
            }

            // 获取下一批数据
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
            searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
            scrollId = searchResponse.getScrollId();
            searchHits = searchResponse.getHits().getHits();
            if (pageCount >= 100) {
                // while循环最多支持 100次，超过直接break
                Log.warn("查询订单远程请求记录数据超过100页，后面忽略");
                break;
            }
        }

        // 清除scroll上下文
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        if (DataMatchTypeEnum.DATA_MATCH_DISTINCT == bo.getParams().get(DataMatchTypeEnum.DATA_MATCH_DISTINCT.getGroup())) {
            records = new ArrayList<>(recordMap.values());
        }
        log.info("查询远程订单请求记录适配器出参数量：{}", records.size());
        return records;
    }


    /**
     * 统计成功记录(接单成功，可以此来统计单量)
     * 优先从数据库中获取，获取到的记录数量与时间间隔进行比较，
     * 1、若间隔1天，则这一天优先从Redis获取，Redis没有的话从ES获取这一天的数据，取到后存入Redis(5分钟)(因全天数据不完整，先不入库)
     * 2、若间隔>1天，则全量从ES获取，获取到后对数据库数据进行插入OR更新操作
     * new TopsStatRecordBo(TopsStatTypeEnum.TYPE_ORDER_COUNT.getCode(), TopsStatCateEnum.CATE_DAY.getCode(), TopsDateUtils.toDate(date, "yyyy-MM-dd"), TopsStatDimensionEnum.DIMENSION_BUSINESS_UNIT.getCode(), businessUnit, count);
     * 按照前面 5个字段进行获取，更新最后的count
     *
     * @param bo
     * @return
     */
    public List<TopsStatRecordVo> statSuccessRecords(TopsStatRecordBo bo) {
        List<TopsStatRecordVo> results = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest(generateIndex(bo.getBeginTime(), bo.getEndTime()));
        try {
            // 构建查询
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("createTime").gte(bo.getBeginTime()).lt(bo.getEndTime()))
                .must(QueryBuilders.termQuery("businessScene", "receive"))
                .must(QueryBuilders.termQuery("responseCode", 1));

            DateHistogramAggregationBuilder dateHistogramAggregation = AggregationBuilders
                .dateHistogram("by_day")
                .field("createTime")
                .dateHistogramInterval(DateHistogramInterval.DAY)
                .minDocCount(0)
                .format("yyyy-MM-dd")
                .subAggregation(AggregationBuilders.terms("by_business_unit").field("businessUnit").size(100));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .size(0)
                .query(boolQuery)
                .aggregation(dateHistogramAggregation);

            searchRequest.source(searchSourceBuilder);

            // 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析聚合结果
            Histogram byDayAggregation = searchResponse.getAggregations().get("by_day");
            for (Histogram.Bucket bucket : byDayAggregation.getBuckets()) {
                String date = bucket.getKeyAsString();
                Terms byBusinessUnitAgg = bucket.getAggregations().get("by_business_unit");
                TopsStatRecordVo record = null;
                for (Terms.Bucket subBucket : byBusinessUnitAgg.getBuckets()) {
                    String businessUnit = subBucket.getKeyAsString();
                    long count = subBucket.getDocCount();
                    record = new TopsStatRecordVo();
                    record.setType(bo.getType());
                    record.setCategory(bo.getCategory());
                    record.setDimension(bo.getDimension());
                    record.setCategoryValue(TopsDateUtils.toDate(date, "yyyy-MM-dd"));
                    record.setDimensionValue(businessUnit);
                    record.setStatCount(count);
                    results.add(record);
                }
            }
        } catch (Exception e) {
            log.error("Error executing Elasticsearch request", e);
        }
        return results;
    }


    /**
     * 统计接单每日异常记录
     *
     * @param bo
     * @return
     */
    public List<TopsStatRecordVo> statFailRecords(TopsStatRecordBo bo) {
        List<TopsStatRecordVo> results = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest(generateIndex(bo.getBeginTime(), bo.getEndTime()));
        try {
            // 构建查询
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("createTime").gte(bo.getBeginTime()).lt(bo.getEndTime()))
                .must(QueryBuilders.termQuery("businessScene", "receive"))
                .mustNot(QueryBuilders.termQuery("responseCode", 1));

            DateHistogramAggregationBuilder dateHistogramAggregation = AggregationBuilders
                .dateHistogram("by_day")
                .field("createTime")
                .dateHistogramInterval(DateHistogramInterval.DAY)
                .minDocCount(0)
                .format("yyyy-MM-dd")
                .subAggregation(AggregationBuilders.terms("by_response_code").field("responseCode").size(100));

            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .size(0)
                .query(boolQuery)
                .aggregation(dateHistogramAggregation);

            searchRequest.source(searchSourceBuilder);

            // 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析聚合结果
            Histogram byDayAggregation = searchResponse.getAggregations().get("by_day");
            for (Histogram.Bucket bucket : byDayAggregation.getBuckets()) {
                String date = bucket.getKeyAsString();
                Terms byBusinessUnitAgg = bucket.getAggregations().get("by_response_code");
                TopsStatRecordVo record = null;
                for (Terms.Bucket subBucket : byBusinessUnitAgg.getBuckets()) {
                    String businessUnit = subBucket.getKeyAsString();
                    long count = subBucket.getDocCount();
                    record = new TopsStatRecordVo();
                    record.setType(bo.getType());
                    record.setCategory(bo.getCategory());
                    record.setCategoryValue(TopsDateUtils.toDate(date, "yyyy-MM-dd"));
                    record.setDimension(bo.getDimension());
                    record.setDimensionValue(businessUnit);
                    record.setStatCount(count);
                    results.add(record);
                }
            }
        } catch (Exception e) {
            log.error("Error executing Elasticsearch request", e);
        }
        return results;
    }

    public List<TopsStatRecordVo> statCustomerFailRecords(TopsStatRecordBo bo) {
        // 获取日期
        String selectFailCategory = bo.getParams().get("orderFailCustomerDate") == null ?
            TopsDateUtils.toDateTimeStr(new Date()) : bo.getParams().get("orderFailCustomerDate").toString();
        // 错误码
        String selectFailCodeDimension = bo.getParams().get("orderFailCustomerResponseCode") == null ?
            "3-01-11001" : bo.getParams().get("orderFailCustomerResponseCode").toString();
        List<TopsStatRecordVo> results = new ArrayList<>();
        String redisKey = "tops_order_fail_customer_failCode:" + selectFailCategory + ":" + selectFailCodeDimension;
        if (RedisUtils.isExistsObject(redisKey)) {
            return RedisUtils.getCacheObject(redisKey);
        }
        //SearchRequest searchRequest = new SearchRequest(generateIndex(selectFailCategory, selectFailCategory));
        SearchRequest searchRequest = new SearchRequest(generateIndex(selectFailCategory, selectFailCategory));
        try {
            // 定义开始时间和结束时间
            String startTime = selectFailCategory + "T00:00:00Z";
            String endTime = selectFailCategory + "T23:59:59Z";
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("createTime").gte(startTime).lt(endTime))
                .must(QueryBuilders.termQuery("businessScene", "receive"))
                .must(QueryBuilders.termQuery("responseCode", selectFailCodeDimension));
            // 创建按小时分组的聚合
            DateHistogramAggregationBuilder byHour = AggregationBuilders.dateHistogram("by_hour")
                .field("createTime")
                .dateHistogramInterval(DateHistogramInterval.HOUR)
                .timeZone(ZoneId.of("+08:00"));
            // 创建每小时记录数的聚合
            ValueCountAggregationBuilder hourlyCount = AggregationBuilders.count("hourly_count")
                .field("accountNo");
            // 将每小时记录数聚合添加到按小时分组的聚合中
            byHour.subAggregation(hourlyCount);
            // 创建按 accountNo 分组的聚合
            TermsAggregationBuilder byAccountNo = AggregationBuilders.terms("by_accountNo")
                .field("accountNo")
                .size(100);
            // 将按小时分组的聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(byHour);
            // 创建最大每小时记录数的聚合
            MaxBucketPipelineAggregationBuilder maxHourlyCount = PipelineAggregatorBuilders.maxBucket("max_hourly_count", "by_hour>hourly_count");
            // 将最大每小时记录数的聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(maxHourlyCount);
            // 创建按最大每小时记录数排序的聚合
            BucketSortPipelineAggregationBuilder sortByMaxHourlyCount = PipelineAggregatorBuilders.bucketSort("sort_by_max_hourly_count",
                    Collections.singletonList(new org.elasticsearch.search.sort.FieldSortBuilder("max_hourly_count").order(SortOrder.DESC)))
                .size(20);
            // 将排序聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(sortByMaxHourlyCount);

            // 创建查询源构建器
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byAccountNo);
            searchSourceBuilder.size(0);

            searchRequest.source(searchSourceBuilder);

            // 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析按 accountNo 分组的聚合结果
            Terms AccountNoAgg = searchResponse.getAggregations().get("by_accountNo");

            for (Terms.Bucket accountNoBucket : AccountNoAgg.getBuckets()) {
                String accountNo = accountNoBucket.getKeyAsString();
                // 解析按小时分组的聚合结果
                Histogram hourAgg = accountNoBucket.getAggregations().get("by_hour");
                for (Histogram.Bucket hourBucket : hourAgg.getBuckets()) {
                    String hour = hourBucket.getKeyAsString();
                    long count = hourBucket.getDocCount();
                    TopsStatRecordVo record = new TopsStatRecordVo();
                    record.setType(bo.getType());
                    record.setCategory(bo.getCategory());
                    record.setCategoryValue(TopsDateUtils.toDate(hour));
                    record.setDimension(bo.getDimension());
                    record.setDimensionValue(accountNo);
                    record.setStatCount(count);
                    results.add(record);
                }
            }
            RedisUtils.setCacheObject(redisKey, results, Duration.ofMinutes(TopsConstants.PAGE_VALUE_EXPIRATION));
        } catch (Exception e) {
            log.error("Error executing Elasticsearch request", e);
        }
        return results;
    }


    public List<TopsStatRecordVo> statCustomerSuccessRecords(TopsStatRecordBo bo) {
        // 获取日期
        String selectFailCategory = bo.getParams().get("orderSuccessCustomerDate") == null ?
            TopsDateUtils.toDateTimeStr(new Date()) : bo.getParams().get("orderSuccessCustomerDate").toString();
        // 错误码
        String selectFailCodeDimension = bo.getParams().get("orderSuccessCustomerBusinessUnit") == null ?
            "cn_jdl_sc-pop" : bo.getParams().get("orderSuccessCustomerBusinessUnit").toString();
        List<TopsStatRecordVo> results = new ArrayList<>();
        String redisKey = "tops_order_success_customer_businessUnit:" + selectFailCategory + ":" + selectFailCodeDimension;
        if (RedisUtils.isExistsObject(redisKey)) {
            // return RedisUtils.getCacheObject(redisKey);
        }
        //SearchRequest searchRequest = new SearchRequest(generateIndex(selectFailCategory, selectFailCategory));
        SearchRequest searchRequest = new SearchRequest(generateIndex(selectFailCategory, selectFailCategory));
        try {
            // 定义开始时间和结束时间
            String startTime = selectFailCategory + "T00:00:00Z";
            String endTime = selectFailCategory + "T23:59:59Z";
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("createTime").gte(startTime).lt(endTime).timeZone("+08:00"))
                .must(QueryBuilders.termQuery("businessScene", "receive"))
                .must(QueryBuilders.termQuery("responseCode", "1"));
            // 创建按小时分组的聚合
            DateHistogramAggregationBuilder byHour = AggregationBuilders.dateHistogram("by_hour")
                .field("createTime")
                .dateHistogramInterval(DateHistogramInterval.HOUR)
                .timeZone(ZoneId.of("+08:00"));
            // 创建每小时记录数的聚合
            ValueCountAggregationBuilder hourlyCount = AggregationBuilders.count("hourly_count")
                .field("accountNo");
            // 将每小时记录数聚合添加到按小时分组的聚合中
            byHour.subAggregation(hourlyCount);
            // 创建按 accountNo 分组的聚合
            TermsAggregationBuilder byAccountNo = AggregationBuilders.terms("by_accountNo")
                .field("accountNo")
                .size(100);
            // 将按小时分组的聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(byHour);
            // 创建最大每小时记录数的聚合
            MaxBucketPipelineAggregationBuilder maxHourlyCount = PipelineAggregatorBuilders.maxBucket("max_hourly_count", "by_hour>hourly_count");
            // 将最大每小时记录数的聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(maxHourlyCount);
            // 创建按最大每小时记录数排序的聚合
            BucketSortPipelineAggregationBuilder sortByMaxHourlyCount = PipelineAggregatorBuilders.bucketSort("sort_by_max_hourly_count",
                    Collections.singletonList(new org.elasticsearch.search.sort.FieldSortBuilder("max_hourly_count").order(SortOrder.DESC)))
                .size(20);
            // 将排序聚合添加到按 accountNo 分组的聚合中
            byAccountNo.subAggregation(sortByMaxHourlyCount);

            // 创建查询源构建器
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQuery);
            searchSourceBuilder.aggregation(byAccountNo);
            searchSourceBuilder.size(0);

            searchRequest.source(searchSourceBuilder);

            // 执行查询
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);

            // 解析按 accountNo 分组的聚合结果
            Terms AccountNoAgg = searchResponse.getAggregations().get("by_accountNo");

            for (Terms.Bucket accountNoBucket : AccountNoAgg.getBuckets()) {
                String accountNo = accountNoBucket.getKeyAsString();
                // 解析按小时分组的聚合结果
                Histogram hourAgg = accountNoBucket.getAggregations().get("by_hour");
                for (Histogram.Bucket hourBucket : hourAgg.getBuckets()) {
                    String hour = hourBucket.getKeyAsString();
                    long count = hourBucket.getDocCount();
                    TopsStatRecordVo record = new TopsStatRecordVo();
                    record.setType(bo.getType());
                    record.setCategory(bo.getCategory());
                    record.setCategoryValue(TopsDateUtils.toDate(hour));
                    record.setDimension(bo.getDimension());
                    record.setDimensionValue(accountNo);
                    record.setStatCount(count);
                    results.add(record);
                }
            }
            RedisUtils.setCacheObject(redisKey, results, Duration.ofMinutes(TopsConstants.PAGE_VALUE_EXPIRATION));
        } catch (Exception e) {
            log.error("Error executing Elasticsearch request", e);
        }
        return results;
    }


    //统计回传记录
    public List<String> getCallbackRequest(List<String> orderNos, Integer orderStatus) throws Exception {
        SearchRequest searchRequest = new SearchRequest(INDEX_ALIAS);
        TermsQueryBuilder orderNoQueryBuilder = new TermsQueryBuilder("orderNo", orderNos);
        TermQueryBuilder businessSceneQueryBuilder = new TermQueryBuilder("businessScene", "callback");
        TermQueryBuilder orderStatusQueryBuilder = new TermQueryBuilder("orderStatus", orderStatus);

        BoolQueryBuilder queryBuilder = new BoolQueryBuilder().must(orderNoQueryBuilder).must(businessSceneQueryBuilder).must(orderStatusQueryBuilder);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(50);
        searchRequest.source(searchSourceBuilder);

        List<String> requestStrList = new ArrayList<>();
        try {
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = response.getHits().getHits();
            for (SearchHit hit : searchHits) {
                requestStrList.add(hit.getSourceAsString());
            }
        } catch (Exception ex) {
            log.error("error getCallbackRequest", ex);
            throw ex;
        }

        return requestStrList;
    }

    /**
     *
     * @param bo
     * @param outboundFlag 是否仓配订单
     * @return
     * @throws Exception
     */
    public TopsOrderRequestEsRecord getLastReceiveRecord(TopsOrderRequestBo bo, Boolean outboundFlag) throws Exception {
        Date endTime = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endTime);
        calendar.add(Calendar.DATE, -60);
        SearchRequest searchRequest = new SearchRequest(outboundFlag ? generateIndex(calendar.getTime(), endTime) : generateExpressIndex(calendar.getTime(), endTime));
        TermQueryBuilder businessSceneQueryBuilder = new TermQueryBuilder("businessScene", bo.getBusinessScene());
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder().must(businessSceneQueryBuilder);

        if(StringUtils.isNotBlank(bo.getOrderNo())) {
            queryBuilder.must(new TermsQueryBuilder("orderNo", Arrays.asList(bo.getOrderNo())));
        }

        if(StringUtils.isNotBlank(bo.getCustomerOrderNo())) {
            queryBuilder.must(new TermsQueryBuilder("customerOrderNo", Arrays.asList(bo.getCustomerOrderNo())));
        }

        if(StringUtils.isNotBlank(bo.getCustomOrderNo())) {
            queryBuilder.must(new TermsQueryBuilder("customOrderNo", Arrays.asList(bo.getCustomOrderNo())));
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(1);
        searchRequest.source(searchSourceBuilder);
        searchSourceBuilder.sort("createTime", SortOrder.DESC);

        try {
            log.info("getLastReceiveRecord request:{}", JSON.toJSONString(searchRequest));
            SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
            log.info("getLastReceiveRecord request:{}", JSON.toJSONString(response));
            SearchHit[] searchHits = response.getHits().getHits();
            if(searchHits != null && searchHits.length > 0) {
                return getTopsOrderRequestEsRecord(searchHits[0]);
            } else {
                return null;
            }
        } catch (Exception ex) {
            log.error("error getCallbackRequest", ex);
            throw ex;
        }
    }
}
