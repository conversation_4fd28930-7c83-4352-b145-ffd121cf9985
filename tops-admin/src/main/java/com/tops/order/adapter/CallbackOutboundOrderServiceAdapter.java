package com.tops.order.adapter;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.api.CallBackOutboundOrderService;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.supplychain.model.CallBackOutboundOrderRequest;
import cn.jdl.oms.supplychain.model.CallBackOutboundOrderResponse;
import com.alibaba.fastjson.JSONObject;
import com.esotericsoftware.minlog.Log;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.TopsProfilerUtils;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName CallbackOutboundOrderServiceAdapter
 * @Description 用途
 * @date 2024年08月08日 10:17 PM
 */
@Slf4j
@Component
public class CallbackOutboundOrderServiceAdapter {
    @Resource
    private TopsOrderRequestRecordAdapter topsOrderRequestRecordAdapter;

    @Resource
    private CallBackOutboundOrderService callBackOutboundOrderService;

    @Autowired
    private RestHighLevelClient client;

    /**
     * 查询回传某状态的报文，进行回溯
     * @return
     */
    public List<XbpOperateResult> callbackOrder(List<String> orderNoList, Integer status) {
        List<XbpOperateResult> results = new ArrayList<>();
        List<String> requestStr;
        try {
            requestStr = topsOrderRequestRecordAdapter.getCallbackRequest(orderNoList, status);
        } catch (Exception ex) {
            log.error("[执行回传接口] 回传接口执行失败: es查询失败, 错误信息:", ex);
            return orderNoList.stream().map(orderNo -> XbpOperateResult.fail(orderNo, "es查询失败"))
                .collect(Collectors.toList());
        }

        Map<String, CallBackOutboundOrderRequest> requestMap = requestStr.stream().map(this::getCallbackOutBoundRequest)
            .filter(request -> request != null)
            .collect(Collectors.toMap(CallBackOutboundOrderRequest::getOrderNo, Function.identity(), (v1, v2) -> v1));
        for(String orderNo : orderNoList) {
            if(requestMap.containsKey(orderNo)) {
                CallBackOutboundOrderRequest callBackOutboundOrderRequest = requestMap.get(orderNo);
                RequestProfile profile = RequestProfile.ofHome().withTenantId("1000").withTraceId(System.currentTimeMillis());
                try {
                    CallBackOutboundOrderResponse response = callBackOutboundOrderService.callBackOrder(profile,
                        callBackOutboundOrderRequest);
                    if(response != null && "1".equals(response.getCode())) {
                        results.add(XbpOperateResult.success(orderNo));
                    } else {
                        //response除超时不会为空
                        String message = response == null ? "未知异常" : response.getMessage();
                        results.add(XbpOperateResult.fail(orderNo, message));
                    }
                } catch (Exception ex) {
                    log.error("[执行回传接口] 回传接口执行失败: 调用回传接口失败:", ex);
                    //大部分是超时错误，截取定长即可
                    results.add(XbpOperateResult.fail(orderNo, StringUtils.truncate(ex.getMessage(), 50)));
                }
            } else {
                results.add(XbpOperateResult.fail(orderNo, "es中无对应记录,或者请求信息反序列化失败"));
            }
        }

        return results;
    }


    /**
     * 反序列化请求信息，如果序列化有问题跳过
     */
    private CallBackOutboundOrderRequest getCallbackOutBoundRequest(String requestStr) {
        CallBackOutboundOrderRequest request = null;
        try {
            JSONObject record = JSONObject.parseObject(requestStr);
            request = JSONObject.parseObject(record.getString("request"), CallBackOutboundOrderRequest.class);
        } catch (Exception ex) {
            Profiler.businessAlarm("callback.request.deserialize.error", "回传信息反序列化失败，请检查日志记录是否正确" + requestStr);
            log.error("[执行回传接口] 回传请求序列化错误, requestStr:{}", requestStr);
        }

        return request;
    }

    /**
     * 临时方案,重新执行指定请求,后续转换成运维工具
     */
    public void resendCallbackRecord() throws Exception {
        try {
            log.info("开始查询回传请求");
            SearchRequest searchRequest = new SearchRequest("1000_new_outbound_order_flow_202408");
            BoolQueryBuilder boolQuery = new BoolQueryBuilder();
            TermQueryBuilder businessSceneBuilder = new TermQueryBuilder("businessScene", "callback");
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder("updateTime").gte("2024-08-27 9:00:00")
                .lte("2024-08-27 17:00:00").timeZone("+08:00");
            TermQueryBuilder businessUnitBuilder = new TermQueryBuilder("businessUnit", "cn_jdl_cc-isv");
            TermsQueryBuilder orderStatusBuilder = new TermsQueryBuilder("orderStatus", "10014");
            TermsQueryBuilder accountBuilder = new TermsQueryBuilder("accountNo", "EBU4418046573961");
            boolQuery.must(businessSceneBuilder).must(rangeQueryBuilder).must(businessUnitBuilder).must(orderStatusBuilder)
                .must(accountBuilder);

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(boolQuery);
            sourceBuilder.size(100); // 设置每次scroll获取的记录数
            sourceBuilder.query(boolQuery);

            searchRequest.source(sourceBuilder);
            searchRequest.scroll(TimeValue.timeValueMinutes(5L)); // 设置scroll上下文有效期
            log.info("回传运维工具, 查询订单远程请求记录RPC入参：{}", TopsJsonUtils.toJSONString(searchRequest));
            SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
            log.info("回传运维工具, 查询订单远程请求记录RPC出参大小：{}", TopsJsonUtils.toJSONString(searchResponse.getHits().getHits().length));

            String scrollId = searchResponse.getScrollId();
            List<String> scrollIdList = new ArrayList<>();
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            int pageCount = 0;
            int successCount = 0;
            int noStatusCount = 0;
            while (searchHits != null && searchHits.length > 0) {
                pageCount++;
                //转换成callbackOrderRequest
                List<CallBackOutboundOrderRequest> requests = Arrays.stream(searchHits).map(SearchHit::getSourceAsString)
                    .map(this::getCallbackOutBoundRequest).collect(Collectors.toList());
                RequestProfile profile = RequestProfile.ofHome().withTenantId("1000").withTraceId(System.currentTimeMillis());

                for(CallBackOutboundOrderRequest request : requests) {
                    if(request.getOrderStatus() == null) {
                        noStatusCount++;
                        continue;
                    }

                    try {
                        CallBackOutboundOrderRequest newRequest = new CallBackOutboundOrderRequest();
                        newRequest.setBusinessIdentity(request.getBusinessIdentity());
                        ChannelInfo channelInfo = new ChannelInfo();
                        channelInfo.setChannelOperateTime(request.getChannelInfo().getChannelOperateTime());
                        channelInfo.setSystemCaller(request.getChannelInfo().getSystemCaller());
                        newRequest.setChannelInfo(channelInfo);
                        newRequest.setOrderStatus(request.getOrderStatus());
                        newRequest.setOrderStatusOperateTime(request.getOrderStatusOperateTime());
                        newRequest.setOrderNo(request.getOrderNo());
                        newRequest.setOperator(request.getOperator());

                        CallBackOutboundOrderResponse response = callBackOutboundOrderService.callBackOrder(profile,
                            newRequest);
                        if(response != null && "1".equals(response.getCode())) {
                            successCount++;
                        } else {
                            log.error("[执行回传接口] 回传接口执行失败: 调用回传接口失败orderNo:{},response{}", newRequest.getOrderNo(),
                                JSONObject.toJSON(newRequest));
                        }
                    } catch (Exception ex) {
                        log.error("[执行回传接口] 回传接口执行失败: 调用回传接口失败:", ex);
                    }
                }

                log.info("回传运维工具, 执行成功{}单, 无状态{}单", successCount, noStatusCount);

                // 获取下一批数据
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                searchResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollIdList.add(scrollId);
                scrollId = searchResponse.getScrollId();
                searchHits = searchResponse.getHits().getHits();
                if (pageCount >= 100) {
                    // while循环最多支持 100次，超过直接break
                    Log.warn("查询订单远程请求记录数据超过100页，后面忽略");
                    break;
                }
            }

            // 清除scroll上下文
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.setScrollIds(scrollIdList);
            client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (Exception ex) {
            log.error("回传执行异常, ex", ex);
        }
    }
}
