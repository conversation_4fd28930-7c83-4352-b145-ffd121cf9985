package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
//cn.jdl.oms.api.GetOrderModifyRecordService
import cn.jdl.oms.api.GetOrderModifyRecordService;
import cn.jdl.oms.search.dto.DataInfo;
import cn.jdl.oms.search.dto.OrderModifyRecord;
import cn.jdl.oms.search.dto.request.GetOrderModifyRecordRequest;
import cn.jdl.oms.search.dto.response.GetOrderModifyRecordResponse;
import com.tops.common.exception.ServiceException;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.order.domain.bo.OpsOrderDetailBo;
import com.tops.order.domain.vo.OrderModifyRecordsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TopsOrderModifyRecordsAdapter {
    @Autowired
    private GetOrderModifyRecordService getOrderModifyRecordService;


    public List<OrderModifyRecordsVO> getOrderModifyRecord(OpsOrderDetailBo bo) {
        GetOrderModifyRecordRequest getOrderModifyRecordRequest = new GetOrderModifyRecordRequest();
        getOrderModifyRecordRequest.setOrderNo(bo.getOrderNo());

        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit("cn_jdl_bsc-platform");
        getOrderModifyRecordRequest.setBusinessIdentity(businessIdentity);

        RequestProfile requestProfile = buildRequestProfile();

        GetOrderModifyRecordResponse getOrderModifyRecordResponse = getOrderModifyRecordService.getOrderModifyRecord(requestProfile, getOrderModifyRecordRequest);
        if (getOrderModifyRecordResponse == null || !"1".equals(getOrderModifyRecordResponse.getCode())) {
            throw new ServiceException("获取底层数据异常，请联系技术人员处理");
        }
        if (CollectionUtils.isEmpty(getOrderModifyRecordResponse.getData())) {
            return null;
        }
        List<OrderModifyRecordsVO> orderModifyRecordsVOS = new ArrayList<>();

        for (OrderModifyRecord record : getOrderModifyRecordResponse.getData()) {
            // 外层记录
            OrderModifyRecordsVO vo = new OrderModifyRecordsVO();
            vo.setOperator(record.getOperator());
            vo.setOperateTime(record.getOperateTime());
            vo.setSystemCaller(record.getChannelInfo().getSystemCaller());
            vo.setSystemSubCaller(record.getChannelInfo().getSystemSubCaller());
            vo.setOriginalData(TopsJsonUtils.toJSONString(record.getSnapShotInfoList()));
            vo.setLatestData(TopsJsonUtils.toJSONString(record.getModifyInfoList()));
            OrderModifyRecordsVO.ModifyDetailVO modifyDetailVO;
            List<OrderModifyRecordsVO.ModifyDetailVO> modifyDetail = new ArrayList<>();

            // 详细字段修改记录
            for (DataInfo dataInfo : record.getModifyInfoList()) {
                modifyDetailVO = new OrderModifyRecordsVO.ModifyDetailVO();
                String field = dataInfo.getField();
                String latestValue = dataInfo.getValue();
                String originalValue = "";
                for (DataInfo ssidataInfo : record.getSnapShotInfoList()) {
                    if (field.equals(ssidataInfo.getField())) {
                        originalValue = ssidataInfo.getValue();
                    }
                }
                modifyDetailVO.setField(field);
                modifyDetailVO.setOriginalValue(originalValue);
                modifyDetailVO.setLatestValue(latestValue);
                modifyDetail.add(modifyDetailVO);
            }
            vo.setModifyDetail(modifyDetail);
            orderModifyRecordsVOS.add(vo);
        }

        return orderModifyRecordsVOS;
    }

    /**
     * 构建基础信息
     *
     * @return
     */
    private static RequestProfile buildRequestProfile() {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setTenantId("1000");
        requestProfile.setLocale("zh_CN");
        requestProfile.setTraceId(String.valueOf(System.currentTimeMillis()));
        return requestProfile;
    }

}
