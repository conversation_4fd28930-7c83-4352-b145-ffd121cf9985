package com.tops.order.adapter;

import com.google.gson.Gson;
import com.jd.fastjson.JSON;
import com.tops.autobots.domain.LogbookBO;
import com.tops.autobots.domain.LogbookRequest;
import com.tops.autobots.domain.LogbookResponse;
import com.tops.jdos.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 查询logbook日志
 * @Author: zhuhongru1
 * @CreateDate: 2025/4/16 21:01
 * @Copyright: Copyright (c) 2021 JDL.CN All Rights Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Slf4j
@Service
public class TopsLogBookAdaptor {
    private static final Gson gson = new Gson();

    public List<LogbookResponse.LogBookData> getLogBook(LogbookBO bo) {
        List<LogbookRequest> logbookRequests = bo.getGroups().stream()
            .map(group -> {
                LogbookRequest request = new LogbookRequest();
                long operateTime = bo.getOperateTime();
                request.setGroups(Collections.singletonList(group));
                request.setStartTimestamp(operateTime - 120000L);
                request.setEndTimestamp(operateTime + 120000L);
                request.setSource("jdos-release");
                request.setSystemName("jdl-oms");
                request.setAppName(bo.getAppName());
                request.setQueryString(bo.getQueryString());
                return request;
            })
            .collect(Collectors.toList());

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("token", "token-chenquanbao");


        // 使用 parallelStream 并行发送 HTTP 请求
        List<LogbookResponse.LogBookData> responseData = logbookRequests.parallelStream()
            .map(logbookRequest -> {
                try {
                    String response = HttpUtil.post(
                        "tianwei-gateway.jd.local",
                        "/tianmeng/v1/logbook/log/history/search",
                        JSON.toJSONString(logbookRequest),
                        headers
                    );
                    return gson.fromJson(response, LogbookResponse.class);
                } catch (Exception e) {
                    log.error("查询logbook失败", e);
                    return null; // 或者返回默认的 LogbookResponse
                }
            })
            .filter(Objects::nonNull) // 过滤掉失败的请求
            .map(LogbookResponse::getData)
            .filter(logBookData -> CollectionUtils.isNotEmpty(logBookData.getLogs()))
            .collect(Collectors.toList());
        return responseData;
    }
}
