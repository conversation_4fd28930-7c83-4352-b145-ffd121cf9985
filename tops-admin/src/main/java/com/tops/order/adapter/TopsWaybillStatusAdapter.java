package com.tops.order.adapter;

import com.jd.etms.waybill.api.WaybillQueryApi;
import com.jd.etms.waybill.api.WaybillTraceApi;
import com.jd.etms.waybill.constant.EnumCallState;
import com.jd.etms.waybill.domain.BaseEntity;
import com.jd.etms.waybill.domain.PackageState;
import com.jd.etms.waybill.domain.WaybillManageDomain;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.order.domain.vo.TopsOrderStatusVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * ClassName:TopsWaybillStatusAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/8/16 下午1:52
 * @author:WeiLiming
 */
@Slf4j
@Component
public class TopsWaybillStatusAdapter {
    @Autowired
    private WaybillTraceApi waybillTraceApi;
    @Autowired
    private WaybillQueryApi waybillQueryApi;

    public String queryStatus(String waybillNo) {
        if (StringUtils.isBlank(waybillNo) || "null".equals(waybillNo) || "0".equals(waybillNo)) {
            return null;
        }
        BaseEntity<WaybillManageDomain> baseEntity = null;
        try {
            log.info("调用运单状态入参，waybillNo={}", waybillNo);
            baseEntity = waybillQueryApi.getWaybillStateDetailByWaybillCode(waybillNo);
            log.info("调用运单状态出参，waybillNo={}", TopsJsonUtils.toJSONString(baseEntity));
            if (baseEntity == null || baseEntity.getResultCode() != EnumCallState.SUCCESS.getKey()) {
                return null;
            }
            if (baseEntity.getData() == null) {
                return null;
            }
            WaybillManageDomain waybillManageDomain = baseEntity.getData();
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("status", String.valueOf(waybillManageDomain.getWaybillState()));
            return resultMap.get("status");
        } catch (Exception e) {
            log.error("查询运单状态异常", e);
        }
        return null;
    }

    public List<TopsOrderStatusVo> queryTrace(String waybillNo) {
        if (StringUtils.isBlank(waybillNo) || "null".equals(waybillNo) || "0".equals(waybillNo)) {
            return null;
        }
        BaseEntity<List<PackageState>> baseEntity = null;
        try {
            log.info("调用运单跟踪入参，{}", waybillNo);
            baseEntity = waybillTraceApi.getPkStateByWCode(waybillNo);
            log.info("调用运单跟踪出参，{}", TopsJsonUtils.toJSONString(baseEntity));
            if (baseEntity == null || baseEntity.getResultCode() != EnumCallState.SUCCESS.getKey()) {
                return null;
            }
            if (baseEntity.getData() == null || CollectionUtils.isEmpty(baseEntity.getData())) {
                return null;
            }
            List<PackageState> waybillManageDomain = baseEntity.getData();
            return buildDDTraceList(waybillManageDomain);
        } catch (Exception e) {
            log.error("查询运单全程跟踪异常", e);
        }
        return null;
    }

    private List<TopsOrderStatusVo> buildDDTraceList(List<PackageState> waybillManageDomain) {
        if (CollectionUtils.isEmpty(waybillManageDomain)) {
            return null;
        }
        List<TopsOrderStatusVo> ddTraceDTOS = new ArrayList<>();
        TopsOrderStatusVo dto = null;
        int i = 1;
        for (PackageState ps : waybillManageDomain) {
            dto = new TopsOrderStatusVo();
            dto.setOperator(ps.getOperatorUser());
            dto.setOperationTime(ps.getCreateTime());
            dto.setStatusCode(Integer.valueOf(ps.getState()));
            dto.setStatusName(ps.getRemark());
            ddTraceDTOS.add(dto);
        }
        Collections.reverse(ddTraceDTOS);
        return ddTraceDTOS;
    }



}
