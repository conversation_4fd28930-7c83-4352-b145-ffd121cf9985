package com.tops.order.adapter;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.search.api.BatchGetOrderService;
import cn.jdl.oms.search.api.GetOrderService;
import cn.jdl.oms.search.dto.*;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.common.utils.exception.OpsException;
import com.tops.order.domain.vo.OpsOrderInfoVo;
import com.tops.order.domain.vo.OpsOrderPersonVo;
import com.tops.order.domain.vo.OpsWarehouseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * ClassName:TopsQueryOrderDetailAdapter
 * Package:com.tops.order.adapter
 * Description:
 *
 * @date:2024/5/31 下午5:23
 * @author:WeiLiming
 */
@Slf4j
@Service
public class TopsQueryOrderDetailAdapter {
    @Resource
    private BatchGetOrderService batchGetOrderService;
    @Resource
    private GetOrderService getOrderService;
    @Autowired
    private static final String OPS_BUSINESS_UNIT = "cn_jdl_supplychain";
    //查询结果中包含的域（Base-订单基础信息，Identity-业务身份信息，Customer-客户信息，Channel-渠道信息，Solution-解决方案，
    // Product-产品信息，Consignor-发货信息，Consignee-收货信息，Cargo-货品信息，Goods-商品信息，Shipment-配送信息，
    // Finance-财务信息，Promotion-营销信息，RefOrder-关联单信息，SmartPattern-智能策略信息，Deduction-抵扣信息，
    // Extend-扩展信息，ReturnInfo-退货信息，Agreement-协议信息，InterceptInfo-拦截信息，Fulfillment-履约信息，
    // CustomsInfo-跨境报关信息，AttachmentInfo-附件信息，AttachFee-附加费列表，CustomsInfo-跨境报关信息，AttachmentInfo-附件信息，All-所有）
    private static final List<String> SEARCH_FIELD_LIST = Lists.newArrayList("Base", "Identity", "Channel", "RefOrder", "Customer");

    private final static List<String> JD_SOURCE_LIST = Lists.newArrayList("POP", "VMI", "Dropship", "JDRetail");

    private static final List<String> DETAIL_FIELD_LIST = Lists.newArrayList("All");

    @Autowired
    private TopsWaybillStatusAdapter topsWaybillStatusAdapter;

    @Autowired
    private TopsJDOrderAdapter topsJDOrderAdapter;

    public OpsOrderInfoVo queryOrderInfoBySimple(String orderNo) {
        GetOrderRequest getOrderRequest = new GetOrderRequest();
        // 当前仅支持销售出业务查询
        getOrderRequest.setOrderGroup("Outbound");
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(OPS_BUSINESS_UNIT);
        getOrderRequest.setBusinessIdentity(businessIdentity);
        getOrderRequest.setIncludeFields(DETAIL_FIELD_LIST);
        getOrderRequest.setIncludeSet(Lists.newArrayList("All"));
        getOrderRequest.setOrderNo(orderNo);
        return queryOrderDetail(buildRequestProfile(), getOrderRequest);
    }

    public List<OpsOrderInfoVo> queryOrderInfoBySimple(List<String> orderNos, List<String> customOrderNos) {
        BatchGetOrderRequest batchGetOrderRequest = new BatchGetOrderRequest();
        // 当前仅支持销售出业务查询
        batchGetOrderRequest.setOrderGroup("Outbound");
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(OPS_BUSINESS_UNIT);
        batchGetOrderRequest.setBusinessIdentity(businessIdentity);
        batchGetOrderRequest.setIncludeFields(SEARCH_FIELD_LIST);
        batchGetOrderRequest.setIncludeSet(Lists.newArrayList("All"));
        int partitionCount = 100;
        // 若入参既有订单号，又有自定义单号，则以订单号为主  分片
        List<List<String>> list = CollectionUtils.isNotEmpty(orderNos) ? Lists.partition(orderNos, partitionCount) : Lists.partition(customOrderNos, partitionCount);
        List<Future<List<OpsOrderInfoVo>>> comOrderInfoFutures = new ArrayList<>();
        for (List<String> noList : list) {
            if (CollectionUtils.isNotEmpty(orderNos)) {
                batchGetOrderRequest.setOrderNos(noList);
            } else if (CollectionUtils.isNotEmpty(customOrderNos)) {
                batchGetOrderRequest.setCustomOrderNos(noList);
            } else {
                throw new OpsException("订单号或自定义单号不能为空");
            }
            Future<List<OpsOrderInfoVo>> listFuture = queryOrderList(buildRequestProfile(), batchGetOrderRequest);
            comOrderInfoFutures.add(listFuture);
        }
        List<OpsOrderInfoVo> comOrderInfos = new ArrayList<>();
        for (Future<List<OpsOrderInfoVo>> future : comOrderInfoFutures) {
            try {
                comOrderInfos.addAll(future.get());
            } catch (InterruptedException e) {
                log.error("线程异常中断", e);
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                log.error("线程执行异常", e);
                throw new RuntimeException(e);
            }
        }

        return comOrderInfos;
    }

    /**
     * 构建基础信息
     *
     * @return
     */
    private static RequestProfile buildRequestProfile() {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setTenantId("1000");
        requestProfile.setLocale("zh_CN");
        requestProfile.setTraceId(String.valueOf(System.currentTimeMillis()));
        return requestProfile;
    }

    @Async("opsBusinessThreadPool")
    public Future<List<OpsOrderInfoVo>> queryOrderList(RequestProfile requestProfile, BatchGetOrderRequest batchGetOrderRequest) {
        BatchGetOrderResponse response = batchGetOrderService.batchGetOrder(requestProfile, batchGetOrderRequest);
        List<OpsOrderInfoVo> list = new ArrayList<>();
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            for (Order order : response.getData()) {
                list.add(buildVo(null, order));
            }
        }
        return new AsyncResult<>(list);
    }

    @Async("opsBusinessThreadPool")
    public Future<OpsOrderInfoVo> buildComOrderInfo(String queryKey, Order order) {
        OpsOrderInfoVo comOrderInfo = buildVo(queryKey, order);
        return new AsyncResult<>(comOrderInfo);
    }

    public OpsOrderInfoVo queryOrderDetail(RequestProfile requestProfile, GetOrderRequest getOrderRequest) {
        GetOrderResponse response = getOrderService.getOrder(requestProfile, getOrderRequest);
        if (response != null && response.getData() != null) {
            Order order = response.getData();
            return buildVo(QUERY_FLAG_DETAIL, order);
        }
        return null;
    }

    private OpsOrderInfoVo buildVo(String flag, Order order) {
        OpsOrderInfoVo comOrderInfo = new OpsOrderInfoVo();
        BaseInfo baseInfo = order.getBaseInfo();
        comOrderInfo.setOrderNo(order.getBaseInfo().getOrderNo());
        comOrderInfo.setOrderStatus(String.valueOf(order.getBaseInfo().getOrderStandardStatus()));
        comOrderInfo.setChannelOrderNo(order.getChannelInfo().getChannelOrderNo());
        comOrderInfo.setReceiveTime(baseInfo.getReceivedTime());
        comOrderInfo.setLastOperationTime(baseInfo.getLastOperatorTime());
        comOrderInfo.setCustomOrderNo(baseInfo.getCustomOrderNo());
        comOrderInfo.setBusinessUnit(order.getBusinessIdentity().getBusinessUnit());
        comOrderInfo.setChannelSource(order.getChannelInfo().getSystemCaller());
        comOrderInfo.setBaichuanFlag(String.valueOf(baseInfo.getSyncSource()));
        comOrderInfo.setCustomerName(baseInfo.getOrderCreatorNo());
        comOrderInfo.setCustomerOrderNo(order.getChannelInfo().getCustomerOrderNo());
        comOrderInfo.setOrderCreator(order.getBaseInfo().getOrderCreatorNo());
        comOrderInfo.setLastOperator(order.getBaseInfo().getLastOperator());
        if (order.getBaseInfo().getCancelStatus() != null) {
            comOrderInfo.setOrderCancelStatus(String.valueOf(order.getBaseInfo().getCancelStatus()));
        }


        //当前只有一个订单对应一个运单，因此取第一个
        if (order.getRefOrderInfo() != null && CollectionUtils.isNotEmpty(order.getRefOrderInfo().getWaybillNos())) {
            comOrderInfo.setWaybillNo(order.getRefOrderInfo().getWaybillNos().get(0));
        } else {
            comOrderInfo.setWaybillNo("无");
        }
// 取第一个运单号，一个仓单关联多个运单？待确认
        if (order.getRefOrderInfo() != null && CollectionUtils.isNotEmpty(order.getRefOrderInfo().getWaybillNos())) {
            comOrderInfo.setWaybillNo(order.getRefOrderInfo().getWaybillNos().get(0));
            String waybillStatus = topsWaybillStatusAdapter.queryStatus(comOrderInfo.getWaybillNo());
            comOrderInfo.setWaybillStatus(waybillStatus);
        }
        // 仅支持零售来源查询渠道订单状态
        if (JD_SOURCE_LIST.contains(comOrderInfo.getChannelSource())) {
            String resultString = topsJDOrderAdapter.queryStatus(comOrderInfo.getChannelOrderNo());
            comOrderInfo.setChannelOrderStatus(resultString);
        }
        comOrderInfo.setOrderCustomStatus(String.valueOf(baseInfo.getOrderStatus()));
        comOrderInfo.setAccountNo(order.getCustomerInfo().getAccountNo());
        if (QUERY_FLAG_DETAIL.equals(flag)) {
            buildOrderDetail(order, comOrderInfo);
        }
        return comOrderInfo;
    }

    /**
     * 补全订单详情信息
     *
     * @param order
     * @param comOrderInfo
     */
    private void buildOrderDetail(Order order, OpsOrderInfoVo comOrderInfo) {
        comOrderInfo.setOrderDetailJson(TopsJsonUtils.toJSONString(order));
        OpsOrderPersonVo consigneeVo = new OpsOrderPersonVo();
        ConsigneeInfo consigneeInfo = order.getConsigneeInfo();
        if (order.getConsigneeInfo() != null) {
            consigneeVo.setName(consigneeInfo.getConsigneeName());
            consigneeVo.setPhone(consigneeInfo.getConsigneePhone());
            consigneeVo.setMobile(consigneeInfo.getConsigneeMobile());
            AddressInfo addressInfo = consigneeInfo.getAddressInfo();
            if (addressInfo != null) {
                consigneeVo.setProvince(buildField(addressInfo.getProvinceNo(), addressInfo.getProvinceName()));
                consigneeVo.setCity(buildField(addressInfo.getCityNo(), addressInfo.getCityName()));
                consigneeVo.setCounty(buildField(addressInfo.getCountyNo(), addressInfo.getCountyName()));
                consigneeVo.setTown(buildField(addressInfo.getTownNo(), addressInfo.getTownName()));
                consigneeVo.setAddress(addressInfo.getAddress());
                consigneeVo.setProvinceGIS(buildField(addressInfo.getProvinceNoGis(), addressInfo.getProvinceNameGis()));
                consigneeVo.setCityGIS(buildField(addressInfo.getCityNoGis(), addressInfo.getCityNameGis()));
                consigneeVo.setCountyGIS(buildField(addressInfo.getCountyNoGis(), addressInfo.getCountyNameGis()));
                consigneeVo.setTownGIS(buildField(addressInfo.getTownNoGis(), addressInfo.getTownNameGis()));
            }
            comOrderInfo.setConsignee(consigneeVo);
        }
        if (order.getConsignorInfo() != null && order.getConsignorInfo().getCustomerWarehouse() != null) {
            OpsWarehouseVo warehouseVo = new OpsWarehouseVo();
            WarehouseInfo warehouseInfo = order.getConsignorInfo().getCustomerWarehouse();
            warehouseVo.setOrderWarehouseCode(warehouseInfo.getWarehouseNo());
            warehouseVo.setOrderWarehouseName(warehouseInfo.getWarehouseName());
            warehouseVo.setOrderWarehouseType(warehouseVo.getOrderWarehouseType() == null ? "" : String.valueOf(warehouseInfo.getWarehouseType()));
            warehouseVo.setActualWarehouseName(warehouseInfo.getActualWarehouseName());
            warehouseVo.setActualWarehouseCode(warehouseInfo.getActualWarehouseNo());
            warehouseVo.setActualWarehouseType(warehouseInfo.getActualWarehouseType() == null ? "" : String.valueOf(warehouseInfo.getActualWarehouseType()));
            warehouseVo.setPlanWarehouseCode(warehouseInfo.getPlanWarehouseNo());
            warehouseVo.setPlanWarehouseName(warehouseInfo.getPlanWarehouseName());
            warehouseVo.setPlanWarehouseName(warehouseInfo.getPlanWarehouseType() == null ? "" : String.valueOf(warehouseInfo.getPlanWarehouseType()));
            comOrderInfo.setWarehouse(warehouseVo);
        }
    }

    /**
     * todo vo转换放到service层
     */
    public Order queryOrderDetail(String orderNo) throws DependencyFailureException {
        GetOrderRequest getOrderRequest = new GetOrderRequest();
        // 当前仅支持销售出业务查询
        getOrderRequest.setOrderGroup("Outbound");
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(OPS_BUSINESS_UNIT);
        getOrderRequest.setBusinessIdentity(businessIdentity);
        getOrderRequest.setIncludeFields(DETAIL_FIELD_LIST);
        getOrderRequest.setIncludeSet(Lists.newArrayList("All"));
        getOrderRequest.setOrderNo(orderNo);

        log.info("订单查询结果request:{}", JSON.toJSONString(getOrderRequest));
        GetOrderResponse response = getOrderService.getOrder(buildRequestProfile(), getOrderRequest);
        log.info("订单查询结果response:{}", JSON.toJSONString(response));
        if (response != null && response.getData() != null) {
            return response.getData();
        } else if("3-01-10001".equals(response.getCode())) {
            return null;
        } else {
            throw new DependencyFailureException("订单查询失败response:" +
                JSON.toJSONString(response == null ? "未知异常" : response.getMessage()));
        }
    }

    private final String QUERY_FLAG_DETAIL = "DETAIL";

    private String buildField(String no, String name) {
        no = no == null ? "无" : no;
        name = name == null ? "无" : name;
        return "[" + no + "] " + name;
    }

    public static void main(String[] args) {
        Integer i = null;
        int j = 0;
        System.out.println(Objects.equals(j, i));
        System.out.println((i == j) || (i != null && i.equals(j)));
    }

    /**
     * 获取订单详情
     * @param orderNo
     * @return
     */
    public Order queryOrderInfo(String orderNo) {
        GetOrderRequest getOrderRequest = new GetOrderRequest();
        // 当前仅支持销售出业务查询
        getOrderRequest.setOrderGroup("Outbound");
        //设置查询业务身份
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(OPS_BUSINESS_UNIT);
        getOrderRequest.setBusinessIdentity(businessIdentity);
        getOrderRequest.setIncludeFields(DETAIL_FIELD_LIST);
        getOrderRequest.setIncludeSet(Lists.newArrayList("All"));
        getOrderRequest.setOrderNo(orderNo);

        GetOrderResponse response = getOrderService.getOrder(buildRequestProfile(), getOrderRequest);
        if (response != null && response.getData() != null) {
            return response.getData();
        }
        return null;
    }
}
