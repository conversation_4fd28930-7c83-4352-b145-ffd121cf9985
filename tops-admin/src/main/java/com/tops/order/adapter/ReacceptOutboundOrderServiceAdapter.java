package com.tops.order.adapter;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.api.CallBackOutboundOrderService;
import cn.jdl.oms.api.ReacceptOutboundOrderService;
import cn.jdl.oms.supplychain.model.CallBackOutboundOrderRequest;
import cn.jdl.oms.supplychain.model.CallBackOutboundOrderResponse;
import cn.jdl.oms.supplychain.model.ReacceptOutboundOrderRequest;
import cn.jdl.oms.supplychain.model.ReacceptOutboundOrderResponse;
import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.domain.vo.ReacceptOrderRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @ClassName CallbackOutboundOrderServiceAdapter
 * @Description 用途
 * @date 2024年08月08日 10:17 PM
 */
@Slf4j
@Component
public class ReacceptOutboundOrderServiceAdapter {
    @Resource
    private ReacceptOutboundOrderService reacceptOutboundOrderService;

    /**
     * 查询回传某状态的报文，进行回溯
     *
     * @return
     */
    public List<XbpOperateResult> reacceptOrder(List<String> orderNoList, ReacceptOutboundOrderRequest request) {
        List<XbpOperateResult> results = new ArrayList<>();
        RequestProfile profile = RequestProfile.ofHome().withTenantId("1000").withTraceId(System.currentTimeMillis());
        for (String orderNo : orderNoList) {
            request.setOrderNo(orderNo);
            try {
                log.info("[执行重处理请求接口] 重处理接口, request:{}", JSONObject.toJSONString(request));
                ReacceptOutboundOrderResponse response = reacceptOutboundOrderService.acceptOrder(profile, request);
                log.info("[执行重处理请求接口] 重处理接口, response:{}", JSONObject.toJSONString(response));
                if("1".equals(response.getCode())) {
                    results.add(XbpOperateResult.success(orderNo));
                } else {
                    results.add(XbpOperateResult.fail(orderNo, response.getMessage()));
                }
            } catch (Exception ex) {
                log.error("[执行重处理请求接口] 重处理接口执行失败: 调用重处理接口失败:", ex);
                //大部分是超时错误，截取定长即可
                results.add(XbpOperateResult.fail(orderNo, StringUtils.truncate(ex.getMessage(), 50)));
            }
        }

        return results;
    }
}
