package com.tops.order.mq.handler.impl;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.audit.service.ApprovalService;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.order.adapter.ModifyOutboundOrderServiceAdapter;
import com.tops.order.domain.dto.ModifyOrderApprovalContent;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.translator.dongdong.DongDongTranslator;
import com.tops.order.translator.ticket.TicketTranslator;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName ModifyOrdersMessageHandler
 * @Description 订单修改功能, 目前聚焦修数,不涉及订单下发。
 * 支持用户根据查询的内容，修改json，修改后调用数据组接口做转换
 * 目前支持货品/收货人信息/客户订单信息，后续逐步放开
 * 货品信息只能修改不能删除(删除建议写脚本)
 * @date 2024年08月08日 6:03 PM
 */
@Service
public class ModifyOrdersMessageHandler implements XbpMessageHandler {
    @Value("${xbp.custom.processIds.modifyOrders}")
    private Integer handledProcessId;

    @Resource
    private ApprovalService xbpApprovalService;

    @Resource
    private ModifyOutboundOrderServiceAdapter modifyOutboundOrderServiceAdapter;

    @Resource
    private TicketServiceAdapter ticketServiceAdapter;

    @Resource
    private TopsEnvUtils topsEnvUtils;

    @Resource
    private DongdongSendMsgHandle dongdongSendMsgHandle;

    @Resource
    private TicketTranslator ticketTranslator;

    @Resource
    private DongDongTranslator dongDongTranslator;

    /**
     * 列表默认长度
     */
    private static Integer DEFAULT_LENGTH = 50;

    private static final String DD_CONTENT_PATTERN = "共修改%d单,\n成功%d单,\n失败%d单,\n 发起人:%s";

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        ApprovalDTO approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());
        //流程结束回调
        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            ModifyOrderApprovalContent approvalContent = approvalDTO.parseInstanceContent(new TypeReference<ModifyOrderApprovalContent>() {
            });
            List<List<String>> orderSubLists = Lists.partition(approvalContent.getOrderNos(), DEFAULT_LENGTH);
            List<XbpOperateResult> operateResults = new ArrayList<>();

            //组织报文回传
            for (List<String> subList : orderSubLists) {
                operateResults.addAll(modifyOutboundOrderServiceAdapter.modifyOrder(subList, approvalContent.getRequest(), approvalContent.getCargoMatchType()));
            }

            ticketServiceAdapter.comment(ticketTranslator.getApprovalContext(instanceId, operateResults));

            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                String committerName = String.format("%s(%s)", approvalContent.getCommitterNickName(), approvalContent.getCommitterPin());

                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalDTO),
                    topsEnvUtils.getEnvDesc() + "订单修改结果",
                    dongDongTranslator.getDDMsgContent(committerName, operateResults),
                    dongDongTranslator.getDetailURL(instanceId.toString()));
            }
        }
    }

    @Override
    public boolean match(Integer processId) {
        return Objects.equals(handledProcessId, processId);
    }

    private String getDongDongReceiver(ApprovalDTO approvalDTO) {
        StringBuilder receiver = new StringBuilder(approvalDTO.getCreateUser()).append(",weiliming,lixiaoliang16");
        return receiver.toString();
    }
}
