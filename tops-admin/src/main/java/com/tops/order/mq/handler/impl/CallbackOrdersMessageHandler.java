package com.tops.order.mq.handler.impl;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.audit.service.ApprovalService;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.order.adapter.CallbackOutboundOrderServiceAdapter;
import com.tops.order.domain.dto.CallbackOrderApprovalContent;
import com.tops.order.domain.dto.CancelOrderResult;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.enums.CancelOrderResultEnum;
import com.tops.order.translator.dongdong.DongDongTranslator;
import com.tops.order.translator.ticket.TicketTranslator;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName CallbackOrderMessageHandler
 * @Description 调用回传接口, 用于回传有问题后回放历史请求
 * @date 2024年08月08日 6:03 PM
 */
@Service
public class CallbackOrdersMessageHandler implements XbpMessageHandler {
    @Resource
    private ApprovalService xbpApprovalService;

    @Resource
    CallbackOutboundOrderServiceAdapter callbackOutboundOrderServiceAdapter;

    @Value("${xbp.custom.processIds.callbackOrders}")
    private Integer handledProcessId;

    @Resource
    private TicketServiceAdapter ticketServiceAdapter;

    @Resource
    private TopsEnvUtils topsEnvUtils;

    @Resource
    private DongdongSendMsgHandle dongdongSendMsgHandle;

    @Resource
    private TicketTranslator ticketTranslator;

    @Resource
    private DongDongTranslator dongDongTranslator;

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException,
        DependencyFailureException, InvalidRequestException {
        ApprovalDTO approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());

        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            CallbackOrderApprovalContent approvalContent = approvalDTO.parseInstanceContent(new TypeReference<CallbackOrderApprovalContent>() {
            });
            List<List<String>> orderSubLists = Lists.partition(approvalContent.getOrderNos(), 50);
            List<XbpOperateResult> operateResults = new ArrayList<>();
            //组织报文回传
            for (List<String> subList : orderSubLists) {
                operateResults.addAll(callbackOutboundOrderServiceAdapter.callbackOrder(subList, approvalContent.getOrderStatus()));
            }

            ticketServiceAdapter.comment(ticketTranslator.getApprovalContext(instanceId, operateResults));

            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                String committerName = String.format("%s(%s)", approvalContent.getCommitterNickName(), approvalContent.getCommitterPin());

                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalDTO),
                    topsEnvUtils.getEnvDesc() + "订单回传结果",
                    dongDongTranslator.getDDMsgContent(committerName, operateResults),
                    dongDongTranslator.getDetailURL(instanceId.toString()));
            }
        }
    }

    @Override
    public boolean match(Integer processId) {
        return Objects.equals(handledProcessId, processId);
    }

    private String getDongDongReceiver(ApprovalDTO approvalDTO) {
        StringBuilder receiver = new StringBuilder(approvalDTO.getCreateUser()).append(",weiliming,lixiaoliang16");
        return receiver.toString();
    }
}
