package com.tops.order.mq.handler.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.constants.AuditConstants;
import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.service.ApprovalService;
import com.tops.audit.service.impl.XbpApprovalService;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.extend.message.dto.MessageDTO;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.order.adapter.CancelOutboundOrderServiceAdapter;
import com.tops.order.domain.dto.ApprovalContent;
import com.tops.order.domain.dto.CancelOrderApprovalContent;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.dto.CancelOrderResult;
import com.tops.order.enums.CancelOrderResultEnum;
import com.tops.order.enums.CancelOrderTypeEnum;
import com.tops.order.service.OrderOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("sampleMessageHandler")
public class CancelOrdersMessageHandler implements XbpMessageHandler {
    @Value("${xbp.custom.processIds.cancelOrders}")
    private Integer handledProcessId;
    @Autowired
    private CancelOutboundOrderServiceAdapter cancelOutboundOrderServiceAdapter;
    @Autowired
    private TicketServiceAdapter ticketServiceAdapter;

    @Autowired
    @Qualifier("xbpApprovalService")
    private ApprovalService xbpApprovalService;

    @Autowired
    private DongdongSendMsgHandle dongdongSendMsgHandle;
    @Autowired
    private TopsEnvUtils topsEnvUtils;

    @Override
    public boolean match(Integer processId) {
        if (processId == null) {
            return false;
        }
        return Objects.equals(handledProcessId, processId);
    }

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        if (instanceId == null) {
            log.warn("CancelOrdersMessageHandler.handle 流程实例Id为null，忽略该回调");
            return;
        }
        log.info("CancelOrdersMessageHandler.handle 模拟XBP回调处理");
        //流程结束回调
        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            //TODO wangqin83 更新本地审批记录状态：状态变更为开始处理
            xbpApprovalService.updateApprovalStatus();
            //TODO wangqin83 按照实际业务逻辑处理请求，并收集处理结果
            //查询当前要处理的订单
            //TODO wangqin83 针对查询审批实例 增加异常处理
            ApprovalDTO approvalDTO = null;
            CancelOrderApprovalContent approvalContent = null;
            try {
                approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());
                approvalContent = approvalDTO.parseInstanceContent(new TypeReference<CancelOrderApprovalContent>() {
                });
                approvalContent.setCommitterPin(approvalDTO.getCreateUser());
            } catch (InvalidRequestException e) {
                log.error("CancelOrdersMessageHandler.handle 查询审批记录失败，instanceId:{},msg:{},exception:", instanceId, e.getMsg(), e);
            }
            //取消订单有两个入口：1.trade ops 页面 2.用户手动提交表单
            //当用户手动提交表单时数据库未留存需要取消的订单信息，所以此时表单为准
            if (approvalContent == null) {
                approvalContent = new CancelOrderApprovalContent();
                //获取审批表单信息
                ApprovalContext approvalContext = new ApprovalContext();
                approvalContext.withProcessInstanceId(instanceId.toString());
                ticketServiceAdapter.get(approvalContext);
                //补充申请人
                approvalContent.setCommitterPin(approvalContext.getOperator().getPin());

                String cancelDTOJSON = approvalContext.getApproval().getInstanceTableForm().get(AuditConstants.XbpConstants.OrderConstants.CANCEL_ORDER_FIELD_ORDERS);
                List<CancelOrderDTO> cancelOrderDTOS = JSON.parseObject(cancelDTOJSON, new TypeReference<List<CancelOrderDTO>>() {
                });
                List<String> orderNos = cancelOrderDTOS.stream().map(CancelOrderDTO::getCargoNo).collect(Collectors.toList());
                approvalContent.setOrderNos(orderNos);
                log.info("CancelOrdersMessageHandler.handle 当前取消申请入口为：用户手动提交，根据表单内容补充审批相关信息。approvalContent:{}", JSON.toJSONString(approvalContent));
            }

            //先发起取消，如果取消成功或取消中，则直接返回，如果取消失败，则发起拦截，如果拦截失败，则返回取消失败。
            Map<Integer, List<CancelOrderResult>> cancelOrderResultMap = new HashMap<>();
            //实际处理订单
            Integer failCode = Objects.equals(approvalContent.getCancelType(), CancelOrderTypeEnum.CACEL.getCode()) ? CancelOrderResultEnum.CANCEL_FAIL.getCode() : CancelOrderResultEnum.CANCEL_INTERCEPT_FAIL.getCode();

            for (String orderNo : approvalContent.getOrderNos()) {
                CancelOrderResult cancelOrderResult = null;
                try {
                    CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
                    cancelOrderDTO.setCargoNo(orderNo);
                    cancelOrderDTO.setOperator(approvalContent.getCommitterPin());
                    cancelOrderDTO.setCancelType(CancelOrderTypeEnum.CACEL.getCode());
                    Integer cancelResultStatus = cancelOutboundOrderServiceAdapter.cancelOrder(cancelOrderDTO);
                    if (Objects.equals(cancelResultStatus, CancelOrderResultEnum.CANCEL_SUCCESS.getCode())
                        || Objects.equals(cancelResultStatus, CancelOrderResultEnum.CANCELLING.getCode())) {
                        List<CancelOrderResult> cancelOrderResults = cancelOrderResultMap.getOrDefault(cancelResultStatus, new ArrayList<>());
                        cancelOrderResultMap.put(cancelResultStatus, cancelOrderResults);
                        CancelOrderResultEnum orderResultEnum = CancelOrderResultEnum.getEnumByCode(cancelResultStatus);
                        String desc = "未知状态";
                        if (orderResultEnum != null) {
                            desc = orderResultEnum.getDesc();
                        }
                        cancelOrderResults.add(CancelOrderResult.of(orderNo, cancelResultStatus.toString(), desc));
                    } else {
                        //当取消失败时，尝试进行拦截
                        cancelOrderDTO.setCancelType(CancelOrderTypeEnum.DELIVERY_INTERCEPT.getCode());
                        Integer interceptResultStatus = cancelOutboundOrderServiceAdapter.cancelOrder(cancelOrderDTO);
                        List<CancelOrderResult> cancelOrderResults = cancelOrderResultMap.getOrDefault(interceptResultStatus, new ArrayList<>());
                        cancelOrderResultMap.put(interceptResultStatus, cancelOrderResults);
                        CancelOrderResultEnum orderResultEnum = CancelOrderResultEnum.getEnumByCode(interceptResultStatus);
                        String desc = "未知状态";
                        if (orderResultEnum != null) {
                            desc = orderResultEnum.getDesc();
                        }
                        cancelOrderResults.add(CancelOrderResult.of(orderNo, interceptResultStatus.toString(), desc));
                    }


                } catch (InvalidRequestException e) {
                    List<CancelOrderResult> cancelFails = cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_FAIL.getCode(), new ArrayList<>());
                    cancelOrderResultMap.put(failCode, cancelFails);
                    cancelFails.add(CancelOrderResult.fail(orderNo, e.getMsg()));
                    log.warn("CancelOrdersMessageHandler.handle 取消订单:{}失败，参数异常：{}，e:", orderNo, e.getMsg(), e);
                } catch (InternalFailureException e) {
                    List<CancelOrderResult> cancelFails = cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_FAIL.getCode(), new ArrayList<>());
                    cancelOrderResultMap.put(failCode, cancelFails);
                    cancelFails.add(CancelOrderResult.fail(orderNo, e.getMsg()));
                    log.error("CancelOrdersMessageHandler.handle 取消订单:{}失败，内部异常：{}，e:", orderNo, e.getMsg(), e);
                } catch (DependencyFailureException e) {
                    List<CancelOrderResult> cancelFails = cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_FAIL.getCode(), new ArrayList<>());
                    cancelOrderResultMap.put(failCode, cancelFails);
                    cancelFails.add(CancelOrderResult.fail(orderNo, e.getMsg()));
                    log.error("CancelOrdersMessageHandler.handle 取消订单:{}失败，外部依赖异常：{}，e:", orderNo, e.getMsg(), e);
                } catch (Exception e) {
                    List<CancelOrderResult> cancelFails = cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_FAIL.getCode(), new ArrayList<>());
                    cancelOrderResultMap.put(failCode, cancelFails);
                    cancelFails.add(CancelOrderResult.fail(orderNo, "未知异常，请联系研发wangqin83排查"));
                    log.error("CancelOrdersMessageHandler.handle 取消订单:{}失败，未知异常，e:", orderNo, e);
                }
            }
            List<CancelOrderResult> cancelOrderResults = new ArrayList<>();
            for (CancelOrderResultEnum cancelOrderResultEnum : CancelOrderResultEnum.values()) {
                cancelOrderResults.addAll(cancelOrderResultMap.getOrDefault(cancelOrderResultEnum.getCode(), new ArrayList<>()));
            }
            ApprovalContext context = new ApprovalContext()
                .withProcessInstanceId(instanceId.toString())
                .withPin("wangqin83")
                .withComment(getComment(cancelOrderResults));
            ticketServiceAdapter.comment(context);
            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                String committerName = String.format("%s(%s)", approvalContent.getCommitterNickName(), approvalContent.getCommitterPin());
                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalContent),
                    getDDMsgTitle(approvalContent.getCancelType()),
                    getDDMsgContent(approvalContent.getCancelType(), committerName, approvalContent.getOrderNos(), cancelOrderResultMap),
                    getDetailURL(instanceId.toString()));
            }
        }
        //TODO wangqin83 更新本地审批记录状态：状态变更为处理完成
        xbpApprovalService.updateApprovalStatus();
    }

    public String getComment(List<CancelOrderResult> cancelOrderResults) {
        TopsHtmlUtils.HtmlStringBuilder htmlStringBuilder = new TopsHtmlUtils.HtmlStringBuilder()
            .startTable();
        htmlStringBuilder.startRow()
            .startCol()
            .append("物流订单号")
            .endCol()
            .startCol()
            .append("处理结果编码")
            .endCol()
            .startCol()
            .append("处理结果描述")
            .endCol()
            .endRow();
        if (CollectionUtils.isNotEmpty(cancelOrderResults)) {
            for (CancelOrderResult cancelOrderResult : cancelOrderResults) {
                if (Objects.equals(cancelOrderResult.getCode(), "" + CancelOrderResultEnum.CANCEL_SUCCESS.getCode())
                    || Objects.equals(cancelOrderResult.getCode(), "" + CancelOrderResultEnum.CANCEL_INTERCEPT_SUCCESS.getCode())) {
                    htmlStringBuilder.startRow();
                } else {
                    htmlStringBuilder.startRowError();
                }
                htmlStringBuilder.startCol()
                    .append(cancelOrderResult.getOrderNo())
                    .endCol()
                    .startCol()
                    .append(cancelOrderResult.getCode())
                    .endCol()
                    .startCol()
                    .append(cancelOrderResult.getMsg())
                    .endCol()
                    .endRow();
            }
        }
        htmlStringBuilder.endTable();
        return htmlStringBuilder.toString();
    }

    private String getDDMsgTitle(String cancelType) {
        StringBuilder content = new StringBuilder();
        content.append(topsEnvUtils.getEnvDesc());
        if (Objects.equals(cancelType, CancelOrderTypeEnum.DELIVERY_INTERCEPT.getCode())) {
            content.append("：配送拦截订单处理结果通知");
        } else if (Objects.equals(cancelType, CancelOrderTypeEnum.CACEL.getCode())) {
            content.append("：取消订单处理结果通知");
        }
        return content.toString();
    }

    private String getDDMsgContent(String cancelType, String committer, List<String> totalCargoNos, Map<Integer, List<CancelOrderResult>> cancelOrderResultMap) {
        StringBuilder content = new StringBuilder();
        if (Objects.equals(cancelType, CancelOrderTypeEnum.DELIVERY_INTERCEPT.getCode())) {
            content.append(getDDMsgContentDeliveryIntercept(committer, totalCargoNos, cancelOrderResultMap));
        } else if (Objects.equals(cancelType, CancelOrderTypeEnum.CACEL.getCode())) {
            content.append(getDDMsgContentCancel(committer, totalCargoNos, cancelOrderResultMap));
        }
        return content.toString();
    }

    /**
     * 拼装咚咚消息内容-取消
     */
    private String getDDMsgContentCancel(String committer, List<String> totalCargoNos, Map<Integer, List<CancelOrderResult>> cancelOrderResultMap) {
        StringBuilder content = new StringBuilder();
        content.append("取消订单处理完成，本次共发起取消").append(totalCargoNos.size()).append("单：\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_FAIL.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_FAIL.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCELLING.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCELLING.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_SUCCESS.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_SUCCESS.getCode(), new ArrayList<>()).size()).append("单。\n");
        content.append("发起人：").append(committer).append("。");
        return content.toString();
    }

    /**
     * 拼装咚咚消息内容-配送拦截
     */
    private String getDDMsgContentDeliveryIntercept(String committer, List<String> totalCargoNos, Map<Integer, List<CancelOrderResult>> cancelOrderResultMap) {
        StringBuilder content = new StringBuilder();
        content.append("配送拦截订单处理完成，本次共发起配送拦截").append(totalCargoNos.size()).append("单：\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_INTERCEPT_FAIL.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_INTERCEPT_FAIL.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_INTERCEPTING.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_INTERCEPTING.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_INTERCEPT_SUCCESS.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_INTERCEPT_SUCCESS.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_INTERCEPT_PART_SUCCESS.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_INTERCEPT_PART_SUCCESS.getCode(), new ArrayList<>()).size()).append("单。\n")
            .append("- ").append(CancelOrderResultEnum.CANCEL_SUCCESS.getDesc()).append(":").append(cancelOrderResultMap.getOrDefault(CancelOrderResultEnum.CANCEL_SUCCESS.getCode(), new ArrayList<>()).size()).append("单。\n");
        content.append("发起人：").append(committer).append("。");
        return content.toString();
    }

    private String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }

    private String getDongDongReceiver(ApprovalContent approvalContent) {
        StringBuilder receiver = new StringBuilder(approvalContent.getCommitterPin()).append(",weiliming,lixiaoliang16");
        return receiver.toString();
    }
}
