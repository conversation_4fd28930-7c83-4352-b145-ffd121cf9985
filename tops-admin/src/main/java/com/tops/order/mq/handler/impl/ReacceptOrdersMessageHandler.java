package com.tops.order.mq.handler.impl;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.tops.audit.adapter.TicketServiceAdapter;
import com.tops.audit.domain.dto.ApprovalDTO;
import com.tops.audit.enums.XbpEventTypeEnum;
import com.tops.audit.mq.handler.XbpMessageHandler;
import com.tops.audit.service.ApprovalService;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.TopsEnvUtils;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import com.tops.order.adapter.ReacceptOutboundOrderServiceAdapter;
import com.tops.order.domain.dto.CallbackOrderApprovalContent;
import com.tops.order.domain.dto.ReacceptOrderApprovalContent;
import com.tops.order.domain.dto.XbpOperateResult;
import com.tops.order.enums.ReaccpetOperateEnum;
import com.tops.order.translator.dongdong.DongDongTranslator;
import com.tops.order.translator.ticket.TicketTranslator;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName ReacceptOrdersMessageHandler
 * @Description 重处理接口, 在异常平台开放按钮的基础上, 承载一些研发自驱的功能
 * 如越库绑定/解绑
 * @date 2024年08月08日 6:04 PM
 */
@Service
public class ReacceptOrdersMessageHandler implements XbpMessageHandler {
    @Value("${xbp.custom.processIds.reacceptOrders}")
    private Integer handledProcessId;

    @Resource
    private ApprovalService xbpApprovalService;

    @Resource
    ReacceptOutboundOrderServiceAdapter reacceptOutboundOrderServiceAdapter;

    @Resource
    private TicketTranslator ticketTranslator;

    @Resource
    private TicketServiceAdapter ticketServiceAdapter;

    @Resource
    private TopsEnvUtils topsEnvUtils;

    @Resource
    private DongdongSendMsgHandle dongdongSendMsgHandle;

    @Resource
    private DongDongTranslator dongDongTranslator;

    private static final String DD_CONTENT_PATTERN = "共重新处理%d单,\n成功%d单,\n失败%d单,\n 发起人:%s";

    @Override
    public void handle(Integer processId, Integer instanceId, String eventType) throws InternalFailureException, DependencyFailureException, InvalidRequestException {
        ApprovalDTO approvalDTO = xbpApprovalService.queryApproval(instanceId.toString());

        if (Objects.equals(eventType, XbpEventTypeEnum.TICKET_CLOSE.getCode())) {
            ReacceptOrderApprovalContent approvalContent = approvalDTO.parseInstanceContent(new TypeReference<ReacceptOrderApprovalContent>() {
            });
            List<List<String>> orderSubLists = Lists.partition(approvalContent.getOrderNos(), 50);
            List<XbpOperateResult> operateResults = new ArrayList<>();

            //组织报文回传
            for (List<String> subList : orderSubLists) {
                operateResults.addAll(reacceptOutboundOrderServiceAdapter.reacceptOrder(subList, approvalContent.getRequest()));
            }

            ticketServiceAdapter.comment(ticketTranslator.getApprovalContext(instanceId, operateResults));

            //预发环境、生产环境推送咚咚消息
            if (topsEnvUtils.isProd() || topsEnvUtils.isYfb()) {
                String committerName = String.format("%s(%s)", approvalContent.getCommitterNickName(), approvalContent.getCommitterPin());

                dongdongSendMsgHandle.sendMsg(getDongDongReceiver(approvalDTO),
                    topsEnvUtils.getEnvDesc() + ReaccpetOperateEnum.getDescByCode(approvalContent.getRequest().getOperationType()) + "结果",
                    dongDongTranslator.getDDMsgContent(committerName, operateResults),
                    dongDongTranslator.getDetailURL(instanceId.toString()));
            }
        }
    }

    private String getDDMsgContent(String committerName, List<XbpOperateResult> xbpOperateResults) {
        long successCount = xbpOperateResults.stream()
            .filter(xbpOperateResult -> xbpOperateResult.getCode().equals(String.valueOf(R.SUCCESS))).count();
        long failCount = xbpOperateResults.stream()
            .filter(xbpOperateResult -> xbpOperateResult.getCode().equals(String.valueOf(R.FAIL))).count();
        return String.format(DD_CONTENT_PATTERN, xbpOperateResults.size(), successCount, failCount, committerName);
    }

    private String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }

    @Override
    public boolean match(Integer processId) {
        return Objects.equals(handledProcessId, processId);
    }

    private String getDongDongReceiver(ApprovalDTO approvalDTO) {
        StringBuilder receiver = new StringBuilder(approvalDTO.getCreateUser()).append(",weiliming,lixiaoliang16");
        return receiver.toString();
    }
}
