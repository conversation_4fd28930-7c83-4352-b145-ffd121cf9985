package com.tops.order.translator.xbp;

import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.common.exception.InternalFailureException;

/**
 * <AUTHOR>
 * @ClassName XbpTranslator
 * @Description 生成XBP目标,根据isMatch筛选对应的转换器, 减少注入部分的代码
 * @date 2024年08月16日 10:41 AM
 */
public interface XbpTranslator {
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException;

    public boolean isMatch(String processName);
}
