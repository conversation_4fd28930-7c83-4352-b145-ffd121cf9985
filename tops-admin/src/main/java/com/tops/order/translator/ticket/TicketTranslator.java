package com.tops.order.translator.ticket;

import com.tops.audit.domain.dto.ApprovalContext;
import com.tops.common.core.domain.R;
import com.tops.common.utils.TopsHtmlUtils;
import com.tops.order.domain.dto.XbpOperateResult;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName TicketTranslator
 * @Description 生成工单评论转换层
 * @date 2024年08月16日 3:41 PM
 */
@Service
public class TicketTranslator {

    public ApprovalContext getApprovalContext(Integer instanceId, List<XbpOperateResult> operateResults) {
        ApprovalContext context = new ApprovalContext()
            .withProcessInstanceId(instanceId.toString())
            .withPin("org.wl.oms.cpzb1")
            .withComment(getComment(operateResults));
        return context;
    }

    private String getComment(List<XbpOperateResult> operateResults) {
        TopsHtmlUtils.HtmlStringBuilder htmlStringBuilder = new TopsHtmlUtils.HtmlStringBuilder()
            .startTable();
        htmlStringBuilder.startRow()
            .startCol()
            .append("物流订单号")
            .endCol()
            .startCol()
            .append("处理结果编码")
            .endCol()
            .startCol()
            .append("处理结果描述")
            .endCol()
            .endRow();
        if (CollectionUtils.isNotEmpty(operateResults)) {
            for (XbpOperateResult xbpOperateResult : operateResults) {
                if (Objects.equals(xbpOperateResult.getCode(), String.valueOf(R.SUCCESS))) {
                    htmlStringBuilder.startRow();
                } else {
                    htmlStringBuilder.startRowError();
                }
                htmlStringBuilder.startCol()
                    .append(xbpOperateResult.getOrderNo())
                    .endCol()
                    .startCol()
                    .append(xbpOperateResult.getCode())
                    .endCol()
                    .startCol()
                    .append(xbpOperateResult.getMsg())
                    .endCol()
                    .endRow();
            }
        }

        htmlStringBuilder.endTable();
        return htmlStringBuilder.toString();
    }
}
