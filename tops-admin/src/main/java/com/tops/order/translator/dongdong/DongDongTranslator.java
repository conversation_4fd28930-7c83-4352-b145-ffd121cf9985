package com.tops.order.translator.dongdong;

import com.tops.common.core.domain.R;
import com.tops.order.domain.dto.XbpOperateResult;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName DongDongTranslator
 * @Description 用途
 * @date 2024年08月16日 3:54 PM
 */
@Service
public class DongDongTranslator {
    private static final String DD_CONTENT_PATTERN = "共操作%d单,\n成功%d单,\n失败%d单,\n 发起人:%s";

    public String getDDMsgContent(String committerName, List<XbpOperateResult> xbpOperateResults) {
        long successCount = xbpOperateResults.stream()
            .filter(xbpOperateResult -> xbpOperateResult.getCode().equals(String.valueOf(R.SUCCESS))).count();
        long failCount = xbpOperateResults.stream()
            .filter(xbpOperateResult -> xbpOperateResult.getCode().equals(String.valueOf(R.FAIL))).count();
        return String.format(DD_CONTENT_PATTERN, xbpOperateResults.size(), successCount, failCount, committerName);
    }

    public String getDetailURL(String processInstanceId) {
        return String.format("http://xbp.jd.com/ticket/%s", processInstanceId);
    }
}
