package com.tops.order.translator.xbp.impl;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.dto.CancelOrderApprovalContent;
import com.tops.order.domain.dto.CancelOrderDTO;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.CANCEL_ORDER_FIELD_COMMITER;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.CANCEL_ORDER_FIELD_DESCRIPTION;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.CANCEL_ORDER_FIELD_ORDERS;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.CANCEL_ORDER_FIELD_TYPE;

/**
 * <AUTHOR>
 * @ClassName CancelXbpRequestTranslator
 * @Description 转换生成xbp工单
 * @date 2024年08月16日 10:53 AM
 */
@Component
public class CancelXbpRequestTranslator implements XbpTranslator {

    @Override
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException {
        if(!(request instanceof CancelOrdersRequest)) {
            throw new InternalFailureException("取消请求类型匹配错误");
        }

        CancelOrdersRequest cancelOrdersRequest = (CancelOrdersRequest) request;
        SubmitApprovalRequest approvalRequest = new SubmitApprovalRequest();
        approvalRequest.setProcessCode(XbpProcessEnum.CANCEL_ORDERS_PROCESS.getCode());
        approvalRequest.setPlatform(ApprovalPlatformEnum.XBP.getCode());
        //获取回调时要使用的报文
        List<String> cancelOrderNos = getCancelOrderNos(cancelOrdersRequest);
        CancelOrderApprovalContent approvalContent = new CancelOrderApprovalContent();
        approvalContent.setCancelType(cancelOrdersRequest.getCancelType());
        approvalContent.setOrderNos(cancelOrderNos);
        approvalContent.setCommitterPin(LoginContext.getLoginContext().getPin());
        approvalContent.setCommitterNickName(LoginContext.getLoginContext().getNick());
        approvalRequest.setInstanceContent(JSON.toJSONString(approvalContent));
        approvalRequest.setInstanceContentKey(getInstanceContentKey(cancelOrdersRequest));
        //获取审批实例前端展示信息：表单部分
        Map<String, String> instanceForm = getInstanceForm(cancelOrdersRequest);
        approvalRequest.setInstanceForm(instanceForm);
        //获取审批实例前端展示信息：表格部分
        Map<String, String> instanceTableForm = getInstanceTableForm(cancelOrdersRequest);
        approvalRequest.setInstanceTableForm(instanceTableForm);
        //获取操作人、操作时间
        approvalRequest.setOperator(LoginContext.getLoginContext().getPin());
        approvalRequest.setOperateTime(new Date());
        return approvalRequest;
    }

    @Override
    public boolean isMatch(String processName) {
        return XbpProcessEnum.CANCEL_ORDERS_PROCESS.getCode().equals(processName);
    }

    /**
     * 获取：取消流程实例-表单信息
     */
    private Map<String, String> getInstanceForm(CancelOrdersRequest request) {
        Map<String, String> instanceForm = new HashMap<>();
        instanceForm.put(CANCEL_ORDER_FIELD_COMMITER, LoginContext.getLoginContext().getPin());
        instanceForm.put(CANCEL_ORDER_FIELD_TYPE, "发起取消流程");
        instanceForm.put(CANCEL_ORDER_FIELD_DESCRIPTION, request.getDesc());
        return instanceForm;
    }

    /**
     * 获取：取消流程实例-表格信息
     */
    private Map<String, String> getInstanceTableForm(CancelOrdersRequest request) {
        List<CancelOrderDTO> cancelOrders = new ArrayList<>();
        for (String orderNo : request.getOrderNos()) {
            CancelOrderDTO cancelOrder = new CancelOrderDTO();
            cancelOrder.setCargoNo(orderNo);
            cancelOrders.add(cancelOrder);
        }
        Map<String, String> instanceTableForm = new HashMap<>();
        instanceTableForm.put(CANCEL_ORDER_FIELD_ORDERS, JSON.toJSONString(cancelOrders));
        return instanceTableForm;
    }

    /**
     * @param request 包含订单号的取消订单请求对象
     * @function 获取取消订单号列表，并按字典顺序排序
     * @returns 按字典顺序排序的订单号列表
     */
    private List<String> getCancelOrderNos(CancelOrdersRequest request) {
        return request.getOrderNos().stream().sorted().collect(Collectors.toList());
    }

    /**
     * @param request 取消订单请求对象
     * @function 根据取消订单请求获取实例内容键
     * @returns 实例内容键字符串
     */
    private String getInstanceContentKey(CancelOrdersRequest request) {
        return "";
    }
}
