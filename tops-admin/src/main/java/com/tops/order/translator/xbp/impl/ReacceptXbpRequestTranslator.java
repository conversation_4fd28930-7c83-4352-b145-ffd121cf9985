package com.tops.order.translator.xbp.impl;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.dto.ModifyOrderApprovalContent;
import com.tops.order.domain.dto.ReacceptOrderApprovalContent;
import com.tops.order.domain.vo.ModifyOrderRequest;
import com.tops.order.domain.vo.ReacceptOrderRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_COMMITER;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_MODIFY_CARGO_MATCH_TYPE;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_MODIFY_CONTENT;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_MODIFY_REASON;
import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.XBP_ORDERS;

/**
 * <AUTHOR>
 * @ClassName CallbackXbpRequestTranlator
 * @Description 转换生成xbp工单
 * @date 2024年08月16日 11:09 AM
 */
@Service
public class ReacceptXbpRequestTranslator implements XbpTranslator {
    @Override
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException {
        if(!(request instanceof ReacceptOrderRequest)) {
            throw new InternalFailureException("修改请求类型匹配错误");
        }

        ReacceptOrderRequest reacceptOrderRequest = (ReacceptOrderRequest) request;
        SubmitApprovalRequest approvalRequest = new SubmitApprovalRequest();
        approvalRequest.setProcessCode(XbpProcessEnum.REACCEPT_ORDERS_PROCESS.getCode());
        approvalRequest.setPlatform(ApprovalPlatformEnum.XBP.getCode());

        //获取回调时要使用的报文
        ReacceptOrderApprovalContent approvalContent = new ReacceptOrderApprovalContent();
        approvalContent.setRequest(reacceptOrderRequest.getRequest());
        approvalContent.setOrderNos(reacceptOrderRequest.getOrderNos());
        approvalContent.setCommitterPin(LoginContext.getLoginContext().getPin());
        approvalContent.setCommitterNickName(LoginContext.getLoginContext().getNick());
        approvalRequest.setInstanceContent(JSON.toJSONString(approvalContent));
        approvalRequest.setInstanceContentKey("");

        //获取审批实例前端展示信息：表单部分
        Map<String, String> instanceForm = getInstanceForm(reacceptOrderRequest);
        approvalRequest.setInstanceForm(instanceForm);
        //获取操作人、操作时间
        approvalRequest.setOperator(LoginContext.getLoginContext().getPin());
        approvalRequest.setOperateTime(new Date());
        return approvalRequest;
    }

    /**
     * 获取：修改流程实例-表单信息
     */
    private Map<String, String> getInstanceForm(ReacceptOrderRequest request) {
        Map<String, String> instanceForm = new HashMap<>();
        instanceForm.put(XBP_COMMITER, LoginContext.getLoginContext().getPin());
        instanceForm.put(XBP_MODIFY_REASON, request.getModifyReason());
        instanceForm.put(XBP_ORDERS, JSON.toJSONString(request.getOrderNos()));
        instanceForm.put(XBP_MODIFY_CONTENT, JSON.toJSONString(request.getRequest()));
        return instanceForm;
    }

    @Override
    public boolean isMatch(String processName) {
        return XbpProcessEnum.REACCEPT_ORDERS_PROCESS.getCode().equals(processName);
    }
}
