package com.tops.order.translator.xbp.impl;

import com.alibaba.fastjson.JSON;
import com.jd.common.web.LoginContext;
import com.tops.audit.domain.dto.SubmitApprovalRequest;
import com.tops.audit.enums.ApprovalPlatformEnum;
import com.tops.audit.enums.XbpProcessEnum;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.dto.CallbackOrderApprovalContent;
import com.tops.order.domain.vo.CallbackOrderRequest;
import com.tops.order.translator.xbp.XbpTranslator;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Service;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;

/**
 * <AUTHOR>
 * @ClassName CallbackXbpRequestTranlator
 * @Description 转换生成xbp工单
 * @date 2024年08月16日 11:09 AM
 */
@Service
public class CallbackXbpRequestTranslator implements XbpTranslator {
    @Override
    public SubmitApprovalRequest getXbpRequest(Object request) throws InternalFailureException {
        if(!(request instanceof CallbackOrderRequest)) {
            throw new InternalFailureException("回传请求类型匹配错误");
        }

        CallbackOrderRequest callbackOrderRequest = (CallbackOrderRequest) request;
        SubmitApprovalRequest approvalRequest = new SubmitApprovalRequest();
        approvalRequest.setProcessCode(XbpProcessEnum.CALLBACK_ORDERS_PROCESS.getCode());
        approvalRequest.setPlatform(ApprovalPlatformEnum.XBP.getCode());
        //获取回调时要使用的报文
        CallbackOrderApprovalContent approvalContent = new CallbackOrderApprovalContent();
        approvalContent.setOrderStatus(callbackOrderRequest.getOrderStatus());
        approvalContent.setOrderNos(callbackOrderRequest.getOrderNos());
        approvalContent.setCommitterPin(LoginContext.getLoginContext().getPin());
        approvalContent.setCommitterNickName(LoginContext.getLoginContext().getNick());
        approvalRequest.setInstanceContent(JSON.toJSONString(approvalContent));
        approvalRequest.setInstanceContentKey("");
        //获取审批实例前端展示信息：表单部分
        Map<String, String> instanceForm = getInstanceForm(callbackOrderRequest);
        approvalRequest.setInstanceForm(instanceForm);
        //获取操作人、操作时间
        approvalRequest.setOperator(LoginContext.getLoginContext().getPin());
        approvalRequest.setOperateTime(new Date());
        return approvalRequest;
    }

    /**
     * 获取：回传流程实例-表单信息
     */
    private Map<String, String> getInstanceForm(CallbackOrderRequest request) {
        Map<String, String> instanceForm = new HashMap<>();
        instanceForm.put(XBP_COMMITER, LoginContext.getLoginContext().getPin());
        instanceForm.put(XBP_MODIFY_REASON, request.getCallbackReason());
        instanceForm.put(XBP_ORDERS, JSON.toJSONString(request.getOrderNos()));
        instanceForm.put(XBP_ORDERS_CALLBACK_STATUS, String.valueOf(request.getOrderStatus()));
        return instanceForm;
    }

    @Override
    public boolean isMatch(String processName) {
        return XbpProcessEnum.CALLBACK_ORDERS_PROCESS.getCode().equals(processName);
    }
}
