package com.tops.order.translator.basic;

import com.jd.eclp.master.warehouse.domain.Warehouse;
import com.tops.order.domain.vo.WarehouseVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName WarehouseTranslator
 * @Description 用途
 * @date 2024年08月22日 9:51 PM
 */
@Service
public class WarehouseTranslator {
    public WarehouseVO convertWarehouse(Warehouse warehouse) {
        if (warehouse == null) {
            return null;
        }

        WarehouseVO vo = new WarehouseVO();
        vo.setWarehouseId(warehouse.getId());
        vo.setWarehouseNo(warehouse.getWarehouseNo());
        vo.setWarehouseType(Integer.parseInt(String.valueOf(warehouse.getType())));
        vo.setWarehouseName(warehouse.getWarehouseName());
        vo.setDistributionNo(warehouse.getDistributionNo());
        vo.setDistributionName(warehouse.getDistributionName());
        vo.setOrgNo(warehouse.getOrgNo());
        vo.setErpWarehouseNo(warehouse.getErpWarehouseNo());
        vo.setPartnerNo(warehouse.getPartnerNo());
        vo.setPartnerName(warehouse.getPartnerName());
        vo.setWarehouseProperty(warehouse.getWarehouseProperty() == null ? null : String.valueOf(warehouse.getWarehouseProperty()));
        vo.setProvinceNo(warehouse.getProvinceId() == null ? null : warehouse.getProvinceId().toString());
        vo.setCityNo(warehouse.getCityId() == null ? null : warehouse.getCityId().toString());
        vo.setCountyNo(warehouse.getCountyId() == null ? null : warehouse.getCountyId().toString());
        vo.setTownNo(warehouse.getTownId() == null ? null : warehouse.getTown().toString());
        vo.setProvinceName(warehouse.getProvince());
        vo.setCityName(warehouse.getCity());
        vo.setCountyName(warehouse.getCounty());
        vo.setTownName(warehouse.getTown());
        vo.setAddress(warehouse.getAddress());
        return vo;
    }
}
