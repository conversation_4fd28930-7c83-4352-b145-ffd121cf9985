package com.tops.order.enums;

public enum DataMatchTypeEnum {

    DATA_MATCH_DISTINCT("ResultMatch", "distinct", "结果去重");

    private final String group;
    private final String code;
    private final String description;

    DataMatchTypeEnum(String group, String code, String description) {
        this.group = group;
        this.code = code;
        this.description = description;
    }

    public String getGroup() {
        return group;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
