package com.tops.order.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName OrderStatusErrLevelEnum
 * @Description 用途
 * @date 2024年01月24日 11:21 AM
 */
@Getter
public enum OrderStatusErrLevelEnum {
    RED("red", "红灯"),
    YELLOW("yellow", "黄灯"),
    GREEN("green", "绿灯"),
    ;

    private OrderStatusErrLevelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 名称
     */
    private String code;

    /**
     * 描述
     */
    private String desc;
}
