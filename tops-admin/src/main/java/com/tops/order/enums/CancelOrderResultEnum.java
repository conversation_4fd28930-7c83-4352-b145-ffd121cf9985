package com.tops.order.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum CancelOrderResultEnum {
    CANCEL_FAIL(2, "取消失败"),
    CANCELLING(3, "取消中"),
    CANCEL_SUCCESS(1, "取消成功"),

    CANCEL_INTERCEPT_FAIL(7, "取消拦截失败"),
    CANCEL_INTERCEPTING(4, "取消拦截中"),
    LIFTED_INTERCEPT_SUCCESS(8, "解除拦截成功"),
    CANCEL_INTERCEPT_SUCCESS(5, "取消拦截成功"),
    CANCEL_INTERCEPT_PART_SUCCESS(6, "取消部分拦截成功"),

    CANCEL_NOTIFY_ERROR(100,"取消通知失败"),

    ;
    ;

    private Integer code;
    private String desc;

    CancelOrderResultEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CancelOrderResultEnum getEnumByCode(Integer code){
        for (CancelOrderResultEnum value : CancelOrderResultEnum.values()) {
            if (Objects.equals(value.getCode(),code)){
                return value;
            }
        }
        return null;
    }
}
