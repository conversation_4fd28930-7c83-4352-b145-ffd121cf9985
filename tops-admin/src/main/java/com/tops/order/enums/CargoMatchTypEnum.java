package com.tops.order.enums;

/**
 * <AUTHOR>
 * @ClassName CargoMatchEnum
 * @Description 用途
 * @date 2024年08月12日 3:26 PM
 */
public enum CargoMatchTypEnum {
    CARGO_LEVEL("0", "EMG+货品等级"),
    CARGO_LINE ("1", "EMG+货品等级+行号");

    private final String code;
    private final String description;

    CargoMatchTypEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
