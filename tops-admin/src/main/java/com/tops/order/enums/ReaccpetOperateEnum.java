package com.tops.order.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @ClassName ReaccpetOperateType
 * @Description 用途
 * @date 2024年08月16日 4:25 PM
 */
public enum ReaccpetOperateEnum {
    RE_OCCUPY("100", "重新预占"),
    PULL_OFC("200", "拉回零售OFC"),
    PULL_BACK("300", "拉回"),

    CROSS_DOCK_UNBIND("802", "越库拉回");

    @Getter
    private String code;

    @Getter
    private String desc;

    ReaccpetOperateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (ReaccpetOperateEnum operateEnum : ReaccpetOperateEnum.values()) {
            if (operateEnum.getCode().equals(code)) {
                return operateEnum.getDesc();
            }
        }
        return null;
    }
}
