package com.tops.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSON;
import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.common.utils.StringUtils;
import com.tops.order.domain.vo.CallbackOrderRequest;
import com.tops.order.domain.vo.CancelOrdersRequest;
import com.tops.order.domain.vo.ModifyOrderRequest;
import com.tops.order.domain.vo.ReacceptOrderRequest;
import com.tops.order.service.OrderOperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/order/operation")
public class OrderOperationController {
    @Autowired
    private OrderOperationService orderOperationService;

    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    @SaCheckPermission("order:orderInfo:cancel")
    public R<String> cancelOrders(@RequestBody CancelOrdersRequest request) {
        if (request == null) {
            log.warn("OrderOperationController.cancelOrders 入参非法 request 不可为Null");
            return R.fail("系统异常");
        }
        if (CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationController.cancelOrders 入参非法,订单编码不可为空 request:{}", JSON.toJSONString(request));
            return R.fail("请选择要删除的订单");
        }
        if (StringUtils.isBlank(request.getCancelType())){
            log.warn("OrderOperationController.cancelOrders 入参非法,取消类型不可为空 request:{}", JSON.toJSONString(request));
            return R.fail("请选择要对订单进行的操作");
        }
        R<String> response = R.ok();
        try {
            orderOperationService.cancelOrders(request);
        } catch (InternalFailureException e) {
            log.error("OrderOperationController.cancelOrders 内部异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return R.fail(e.getMsg());
        } catch (DependencyFailureException e) {
            log.error("OrderOperationController.cancelOrders 外部依赖异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return R.fail(e.getMsg());
        } catch (InvalidRequestException e) {
            log.warn("OrderOperationController.cancelOrders 入参非法 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMsg(), e);
            return R.fail(e.getMsg());
        } catch (Exception e) {
            log.error("OrderOperationController.cancelOrders 系统异常 request:{},msg:{},exception:", JSON.toJSONString(request), e.getMessage(), e);
            return R.fail("取消失败");
        }
        log.info("OrderOperationController.cancelOrders request:{},response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
        return response;
    }

    @RequestMapping(value = "/callback", method = RequestMethod.POST)
    public R<String> callbackOrders(@RequestBody CallbackOrderRequest request) {
        if(request == null) {
            log.warn("OrderOperationController.callbackOrder 入参非法 request 不可为Null");
            return R.fail("参数异常");
        }

        if(CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationController.callbackOrder 入参非法 没有选择订单信息");
            return R.fail("参数异常, 订单号必选");
        }

        if(request.getOrderStatus() == null) {
            log.warn("OrderOperationController.callbackOrder 入参非法 没有选择订单状态");
            return R.fail("参数异常, 订单状态必选");
        }

        try {
            orderOperationService.callbackOrders(request);
        } catch (InternalFailureException ex) {
            log.error("OrderOperationController.callbackOrder,内部错误,request:{}, exception:", JSON.toJSONString(request), ex);
            return R.fail(ex.getMsg());
        } catch (DependencyFailureException ex) {
            log.error("OrderOperationController.callbackOrder,外部依赖异常,");
            return R.fail(ex.getMsg());
        } catch (InvalidRequestException e) {
            log.error("OrderOperationController.callbackOrder,参数异常,");
            throw new RuntimeException(e);
        }

        return R.ok();
    }

    @RequestMapping(value = "/modify", method = RequestMethod.POST)
    public R<String> modifyOrders(@RequestBody ModifyOrderRequest request) {
        if(request == null) {
            log.warn("OrderOperationController.modifyOrders 入参非法 request 不可为Null");
            return R.fail("参数异常");
        }

        if(CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有选择订单信息");
            return R.fail("参数异常, 订单号必选");
        }

        if(request.getRequest() == null) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有修改内容");
            return R.fail("参数异常, 订单修改报文必填");
        }

        if(StringUtils.isBlank(request.getModifyReason())) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有修改原因");
            return R.fail("参数异常, 订单修改原因必填");
        }

        try {
            orderOperationService.modifyOrders(request);
        } catch (InternalFailureException ex) {
            log.error("OrderOperationController.modifyOrders,内部错误,request:{}, exception:", JSON.toJSONString(request), ex);
            return R.fail(ex.getMsg());
        } catch (DependencyFailureException ex) {
            log.error("OrderOperationController.modifyOrders,外部依赖异常,");
            return R.fail(ex.getMsg());
        } catch (InvalidRequestException e) {
            log.error("OrderOperationController.modifyOrders,参数依赖异常,");
            throw new RuntimeException(e);
        }

        return R.ok();
    }

    @RequestMapping(value = "/reaccept", method = RequestMethod.POST)
    public R<String> reaccpetOrders(@RequestBody ReacceptOrderRequest request) {
        if(request == null) {
            log.warn("OrderOperationController.reaccpetOrders 入参非法 request 不可为Null");
            return R.fail("参数异常");
        }

        if(CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationController.reaccpetOrders 入参非法 没有选择订单信息");
            return R.fail("参数异常, 订单号必选");
        }

        if(request.getRequest() == null) {
            log.warn("OrderOperationController.reaccpetOrders 入参非法 没有修改内容");
            return R.fail("参数异常, 订单状态必选");
        }

        if(StringUtils.isBlank(request.getModifyReason())) {
            log.warn("OrderOperationController.reaccpetOrders 入参非法 没有修改原因");
            return R.fail("参数异常, 订单状态必选");
        }

        try {
            orderOperationService.reaccpetOrders(request);
        } catch (InternalFailureException ex) {
            log.error("OrderOperationController.reaccpetOrders,内部错误,request:{}, exception:", JSON.toJSONString(request), ex);
            return R.fail(ex.getMsg());
        } catch (DependencyFailureException ex) {
            log.error("OrderOperationController.reaccpetOrders,外部依赖异常,");
            return R.fail(ex.getMsg());
        } catch (InvalidRequestException e) {
            log.error("OrderOperationController.reaccpetOrders,参数依赖异常,");
            throw new RuntimeException(e);
        }

        return R.ok();
    }

    @RequestMapping(value = "/modifyWarehouse", method = RequestMethod.POST)
    public R<String> modifyWarehouse(@RequestBody ModifyOrderRequest request) {
        if(request == null) {
            log.warn("OrderOperationController.modifyOrders 入参非法 request 不可为Null");
            return R.fail("参数异常");
        }

        if(CollectionUtils.isEmpty(request.getOrderNos())) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有选择订单信息");
            return R.fail("参数异常, 订单号必选");
        }

        if(request.getRequest() == null) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有修改内容");
            return R.fail("参数异常, 订单修改报文必填");
        }

        if(StringUtils.isBlank(request.getModifyReason())) {
            log.warn("OrderOperationController.modifyOrders 入参非法 没有修改原因");
            return R.fail("参数异常, 订单修改原因必填");
        }

        try {
            orderOperationService.modifyOrders(request);
        } catch (InternalFailureException ex) {
            log.error("OrderOperationController.modifyOrders,内部错误,request:{}, exception:", JSON.toJSONString(request), ex);
            return R.fail(ex.getMsg());
        } catch (DependencyFailureException ex) {
            log.error("OrderOperationController.modifyOrders,外部依赖异常,");
            return R.fail(ex.getMsg());
        } catch (InvalidRequestException e) {
            log.error("OrderOperationController.modifyOrders,参数依赖异常,");
            throw new RuntimeException(e);
        }

        return R.ok();
    }
}
