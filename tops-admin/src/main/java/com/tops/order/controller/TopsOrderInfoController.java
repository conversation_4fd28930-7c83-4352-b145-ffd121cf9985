package com.tops.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.order.domain.bo.*;
import com.tops.order.domain.vo.*;
import com.tops.order.service.IOpsOrderInfoService;
import com.tops.order.service.ITopsOrderRequestService;
import com.tops.order.service.ITopsOrderStatusService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.*;

/**
 * 订单信息
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/orderInfo")
public class TopsOrderInfoController extends BaseController {

    private final IOpsOrderInfoService iOpsOrderInfoService;

    /**
     * 查询订单信息列表
     */
    @SaCheckPermission("order:orderInfo:list")
    @Log(title = "订单列表", businessType = BusinessType.QUERY)
    @GetMapping("/list")
    public TableDataInfo<OpsOrderInfoVo> list(OpsOrderInfoBo bo, PageQuery pageQuery) {
        return iOpsOrderInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询订单信息列表
     * [{"field":"Jerry","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"},{"field":"Jerry","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"},{"field":"Jerry","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"}]
     */
    @SaCheckPermission("order:orderInfo:list")
    @Log(title = "订单修改记录", businessType = BusinessType.QUERY)
    @GetMapping("/orderModifyRecords")
    public List<OrderModifyRecordsVO> orderModifyRecords(OpsOrderDetailBo bo) {
        return iOpsOrderInfoService.orderModifyRecords(bo);
    }


    private final ITopsOrderRequestService iTopsOrderRequestService;

    /**
     * 查询订单信息列表, 支持订单号和客户单号查询，至少传入其一
     */
    @SaCheckPermission("order:orderInfo:list")
    @Log(title = "订单请求记录", businessType = BusinessType.QUERY)
    @GetMapping("/orderRequestRecords")
    public R<List<TopsOrderRequestVo>> orderRequestRecords(TopsOrderRequestBo bo) {
        if (bo.getParams() == null || bo.getParams().get("requestRadio") == null) {
            return R.fail("未知异常，无法查询订单请求记录");
        }
        if (StringUtils.isNotEmpty(bo.getOrderNo()) && StringUtils.isNotEmpty(bo.getCustomerOrderNo())) {
            return R.fail("订单号和客户单号不可同时传入，无法查询订单请求记录");
        }
        if ("orderNo".equals(bo.getParams().get("requestRadio"))) {
            if (StringUtils.isEmpty(bo.getOrderNo())) {
                return R.fail("订单号获取为空，无法查询订单请求记录");
            }
        } else if ("customerOrderNo".equals(bo.getParams().get("requestRadio"))) {
            if (StringUtils.isEmpty(bo.getCustomerOrderNo())) {
                return R.fail("客户订单号获取为空，无法查询订单请求记录");
            }
        } else {
            return R.fail("未知异常，无法查询订单请求记录");
        }
        Date operateTime = bo.getOperatorTime();
        Date beginDate = TopsDateUtils.dateAddDay(new Date(operateTime.getTime()), -60);
        Date endDate = TopsDateUtils.dateAddDay(new Date(operateTime.getTime()), 1);
        Map<String, Object> param = new HashMap<>();
        param.put("beginCreateTime", beginDate);
        param.put("endCreateTime", endDate);
        bo.setParams(param);
        List<TopsOrderRequestVo> vos = iTopsOrderRequestService.queryList(bo);
        return R.ok(vos);
    }


    /**
     * 获取订单信息详细信息
     *
     * @param bo 主键
     */
    //@SaCheckPermission("order:orderInfo:queryOrderDetail")
    @GetMapping("/queryOrderDetail")
    @Log(title = "订单详情-加密", businessType = BusinessType.QUERY)
    public R<OpsOrderInfoVo> queryOrderDetail(OpsOrderInfoBo bo) {
        return R.ok(iOpsOrderInfoService.queryOrderDetail(bo));
    }

    @Log(title = "订单详情-解密", businessType = BusinessType.QUERY)
    @GetMapping("/decryptOrderDetail")
    public R<OpsOrderDetailDecryptVo> decryptOrderDetail(OpsOrderDetailDecryptBo bo) {
        return R.ok(iOpsOrderInfoService.decryptOrderDetail(bo));
    }

    @Resource(name = "topsOrderStatusServiceImpl")
    private ITopsOrderStatusService topsOrderStatusService;

    /**
     * 查询订单状态流水
     *
     * @param bo
     * @return
     */
    @PostMapping("/queryOrderStatusFlow")
    public R<List<TopsOrderStatusVo>> queryOrderStatusFlow(@RequestBody TopsOrderStatusBo bo) {
        if (bo == null || bo.getOrderNo() == null) {
            return R.warn("入参订单号不能为空");
        }
        List<TopsOrderStatusVo> orderStatusVos = topsOrderStatusService.queryOrderStatusList(bo);
        if (CollectionUtils.isEmpty(orderStatusVos)) {
            return R.warn("订单流水为空");
        }
        orderStatusVos.get(0).setIconType("primary");
        orderStatusVos.get(0).setIconSize("large");
        return R.ok(orderStatusVos);
    }

    /**
     * 查询订单标准状态流水
     *
     * @param bo
     * @return
     */
    @GetMapping("/queryOrderStandardStatusFlow")
    public R<List<TopsOrderStatusVo>> queryOrderStandardStatusFlow(TopsOrderStatusBo bo) {
        if (bo == null || bo.getOrderNo() == null) {
            return R.warn("入参订单号不能为空");
        }
        List<TopsOrderStatusVo> orderStatusVos = topsOrderStatusService.queryOrderStandardStatusList(bo);
        if (CollectionUtils.isEmpty(orderStatusVos)) {
            return R.warn("订单流水为空");
        }
        return R.ok(orderStatusVos);
    }

    /**
     * 查询运单跟踪
     *
     * @param bo
     * @return
     */
    @GetMapping("/queryWaybillTracking")
    public R<List<TopsOrderStatusVo>> queryWaybillTracking(TopsOrderStatusBo bo) {
        if (bo == null || bo.getWaybillNo() == null) {
            return R.warn("入参运号不能为空");
        }
        List<TopsOrderStatusVo> orderStatusVos = topsOrderStatusService.queryWaybillTracking(bo);
        if (CollectionUtils.isEmpty(orderStatusVos)) {
            return R.warn("运单跟踪数据为空");
        }
        return R.ok(orderStatusVos);
    }


    /**
     * 导出订单信息列表
     */
    @SaCheckPermission("order:orderInfo:export")
    @Log(title = "订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OpsOrderInfoBo bo, HttpServletResponse response) {
        List<OpsOrderInfoVo> list = iOpsOrderInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单信息", OpsOrderInfoVo.class, response);
    }

    /**
     * 新增订单信息
     */
    @SaCheckPermission("order:orderInfo:add")
    @Log(title = "订单信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OpsOrderInfoBo bo) {
        return toAjax(iOpsOrderInfoService.insertByBo(bo));
    }

    /**
     * 修改订单信息
     */
    @SaCheckPermission("order:orderInfo:edit")
    @Log(title = "订单信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OpsOrderInfoBo bo) {
        return toAjax(iOpsOrderInfoService.updateByBo(bo));
    }

    /**
     * 删除订单信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:orderInfo:remove")
    @Log(title = "订单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iOpsOrderInfoService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
