package com.tops.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.order.domain.bo.TopsStatRecordBo;
import com.tops.order.domain.vo.TopsChartData;
import com.tops.order.domain.vo.TopsStatRecordVo;
import com.tops.order.service.ITopsStatRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 订单统计
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/statRecord")
public class TopsStatRecordController extends BaseController {

    private final ITopsStatRecordService iTopsStatRecordService;

    /**
     * 查询订单统计列表
     */
    @SaCheckPermission("order:statRecord:list")
    @GetMapping("/list")
    public TableDataInfo<TopsStatRecordVo> list(TopsStatRecordBo bo, PageQuery pageQuery) {
        return iTopsStatRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单统计列表
     */
    @SaCheckPermission("order:statRecord:export")
    @Log(title = "订单统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsStatRecordBo bo, HttpServletResponse response) {
        List<TopsStatRecordVo> list = iTopsStatRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单统计", TopsStatRecordVo.class, response);
    }

    /**
     * 获取订单统计详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:statRecord:query")
    @GetMapping("/{id}")
    public R<TopsStatRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsStatRecordService.queryById(id));
    }

    /**
     * 新增订单统计
     */
    @SaCheckPermission("order:statRecord:add")
    @Log(title = "订单统计", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsStatRecordBo bo) {
        return toAjax(iTopsStatRecordService.insertByBo(bo));
    }

    /**
     * 修改订单统计
     */
    @SaCheckPermission("order:statRecord:edit")
    @Log(title = "订单统计", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsStatRecordBo bo) {
        return toAjax(iTopsStatRecordService.updateByBo(bo));
    }

    /**
     * 删除订单统计
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:statRecord:remove")
    @Log(title = "订单统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsStatRecordService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 订单统计 renderOrderStatChart
     */
    @SaCheckPermission("order:statRecord:renderOrderStatChart")
    @GetMapping("/renderOrderStatChart")
    public TopsChartData renderOrderStatChart(TopsStatRecordBo bo) {
        return iTopsStatRecordService.renderOrderStatChart(bo);
    }

   /**
     * 订单失败统计 renderOrderFailStatChart
     */
    @SaCheckPermission("order:statRecord:renderOrderFailStatChart")
    @GetMapping("/renderOrderFailStatChart")
    public TopsChartData renderOrderFailStatChart(TopsStatRecordBo bo) {
        return iTopsStatRecordService.renderOrderFailStatChart(bo);
    }
    /**
     * 订单失败-商家小时级统计 orderFailCustomerStatChart
     */
    @SaCheckPermission("order:statRecord:orderFailCustomerStatChart")
    @GetMapping("/orderFailCustomerStatChart")
    public TopsChartData orderFailCustomerStatChart(TopsStatRecordBo bo) {
        return iTopsStatRecordService.orderFailCustomerStatChart(bo);
    }
    /**
     * 订单成功-商家小时级统计 orderFailCustomerStatChart
     */
    @SaCheckPermission("order:statRecord:orderSuccessCustomerStatChart")
    @GetMapping("/orderSuccessCustomerStatChart")
    public TopsChartData orderSuccessCustomerStatChart(TopsStatRecordBo bo) {
        return iTopsStatRecordService.orderSuccessCustomerStatChart(bo);
    }



}
