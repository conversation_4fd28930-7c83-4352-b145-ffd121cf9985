package com.tops.order.controller;

import com.tops.common.core.domain.R;
import com.tops.common.exception.DependencyFailureException;
import com.tops.common.exception.InternalFailureException;
import com.tops.common.exception.InvalidRequestException;
import com.tops.order.domain.vo.QueryWarehouseRequest;
import com.tops.order.domain.vo.WarehouseVO;
import com.tops.order.service.EclpBasicDataQueryService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @ClassName EclpBasicController
 * @Description 用途
 * @date 2024年08月23日 10:23 AM
 */
@Slf4j
@RestController
@RequestMapping("/basic")
public class EclpBasicController {
    @Resource
    EclpBasicDataQueryService eclpBasicDataQueryService;

    @GetMapping("/warehouse")
    public R<WarehouseVO> queryWarehouseByWarehouseNo(QueryWarehouseRequest request)  {
        try {
            WarehouseVO vo = eclpBasicDataQueryService.getWarehouse(request.getWarehouseNo());
            return R.ok(vo);
        } catch (DependencyFailureException e) {
            return R.fail("主数据服务异常, 请稍后重试");
        } catch (InternalFailureException e) {
            return R.fail("系统内部错误,请联系研发");
        } catch (InvalidRequestException e) {
            return R.fail("请求参数错误" + e.getMsg());
        }
    }

    @GetMapping("/warehouse/fuzzy_query")
    public R<List<WarehouseVO>> queryWarehouseByWarehouseName(QueryWarehouseRequest request)  {
        try {
            List<WarehouseVO> vos = eclpBasicDataQueryService.getWarehouseByWarehouseName(request.getWarehouseName());
            return R.ok(vos);
        } catch (DependencyFailureException e) {
            return R.fail("主数据服务异常, 请稍后重试");
        } catch (InternalFailureException e) {
            return R.fail("系统内部错误,请联系研发");
        } catch (InvalidRequestException e) {
            return R.fail("请求参数错误" + e.getMsg());
        }
    }
}
