package com.tops.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.excel.ExcelResult;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.order.domain.bo.TopsOrderSensitiveWordsBo;
import com.tops.order.domain.vo.TopsOrderSensitiveWordsVo;
import com.tops.order.listener.TopsSensitiveWordImportListener;
import com.tops.order.service.ITopsOrderSensitiveWordsService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 敏感词校验
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/orderSensitiveWords")
public class TopsOrderSensitiveWordsController extends BaseController {

    private final ITopsOrderSensitiveWordsService iTopsOrderSensitiveWordsService;

    /**
     * 查询敏感词校验列表
     */
    @SaCheckPermission("order:orderSensitiveWords:list")
    @GetMapping("/list")
    public TableDataInfo<TopsOrderSensitiveWordsVo> list(TopsOrderSensitiveWordsBo bo, PageQuery pageQuery) {
        return iTopsOrderSensitiveWordsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出敏感词校验列表
     */
    @SaCheckPermission("order:orderSensitiveWords:export")
    @Log(title = "敏感词校验", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsOrderSensitiveWordsBo bo, HttpServletResponse response) {
        List<TopsOrderSensitiveWordsVo> list = iTopsOrderSensitiveWordsService.queryList(bo);
        ExcelUtil.exportExcel(list, "敏感词校验", TopsOrderSensitiveWordsVo.class, response);
    }

    /**
     * 获取敏感词校验详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:orderSensitiveWords:query")
    @GetMapping("/{id}")
    public R<TopsOrderSensitiveWordsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsOrderSensitiveWordsService.queryById(id));
    }

    /**
     * 新增敏感词校验
     */
    @SaCheckPermission("order:orderSensitiveWords:add")
    @Log(title = "敏感词校验", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsOrderSensitiveWordsBo bo) {
        return toAjax(iTopsOrderSensitiveWordsService.insertByBo(bo));
    }

    /**
     * 修改敏感词校验
     */
    @SaCheckPermission("order:orderSensitiveWords:edit")
    @Log(title = "敏感词校验", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsOrderSensitiveWordsBo bo) {
        return toAjax(iTopsOrderSensitiveWordsService.updateByBo(bo));
    }

    /**
     * 删除敏感词校验
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:orderSensitiveWords:remove")
    @Log(title = "敏感词校验", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsOrderSensitiveWordsService.deleteWithValidByIds(Arrays.asList(ids), true));
    }


    /**
     * 导入数据
     *
     * @param file          导入文件
     * @param updateSupport 是否更新已存在数据
     */
    @Log(title = "敏感词导入", businessType = BusinessType.IMPORT)
    //@SaCheckPermission("system:user:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file, boolean updateSupport) throws Exception {
        ExcelResult<TopsOrderSensitiveWordsVo> result = ExcelUtil.importExcel(file.getInputStream(), TopsOrderSensitiveWordsVo.class, new TopsSensitiveWordImportListener(updateSupport));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "敏感词数据", TopsOrderSensitiveWordsVo.class, response);
    }
}
