package com.tops.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.order.domain.bo.TopsOrderRequestBo;
import com.tops.order.domain.vo.OpsCateStatVO;
import com.tops.order.domain.vo.OpsKVStatVO;
import com.tops.order.domain.vo.TopsOrderRequestVo;
import com.tops.order.service.ITopsOrderRequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 订单请求信息
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/order/orderRequest")
public class TopsOrderRequestController extends BaseController {

    private final ITopsOrderRequestService iTopsOrderRequestService;

    /**
     * 查询订单请求信息列表
     */
    @SaCheckPermission("order:orderRequest:list")
    @GetMapping("/list")
    public TableDataInfo<TopsOrderRequestVo> list(TopsOrderRequestBo bo, PageQuery pageQuery) {
        return iTopsOrderRequestService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询订单请求统计
     * @param bo
     * @return
     */
    @GetMapping("/statisticsPanel")
    public Map<String, Integer> statisticsPanel(TopsOrderRequestBo bo) {
        return iTopsOrderRequestService.statisticsPanel(bo);
    }

    /**
     * 查询订单请求统计
     * @param bo
     * @return
     */
    @GetMapping("/count")
    public List<OpsKVStatVO> count(String groupKey, TopsOrderRequestBo bo) {
        return iTopsOrderRequestService.queryCount(groupKey, bo);
    }
    /**
     * 查询订单请求统计
     * @param bo
     * @return
     */
    @GetMapping("/cateCount")
    public List<OpsCateStatVO> cateCount(String groupKey, TopsOrderRequestBo bo) {
        return iTopsOrderRequestService.queryCateCount(groupKey, bo);
    }
    /**
     * 导出订单请求信息列表
     */
    @SaCheckPermission("order:orderRequest:export")
    @Log(title = "订单请求信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsOrderRequestBo bo, HttpServletResponse response) {
        List<TopsOrderRequestVo> list = iTopsOrderRequestService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单请求信息", TopsOrderRequestVo.class, response);
    }

    /**
     * 获取订单请求信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("order:orderRequest:query")
    @GetMapping("/{id}")
    public R<TopsOrderRequestVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsOrderRequestService.queryById(id));
    }

    /**
     * 新增订单请求信息
     */
    @SaCheckPermission("order:orderRequest:add")
    @Log(title = "订单请求信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsOrderRequestBo bo) {
        return toAjax(iTopsOrderRequestService.insertByBo(bo));
    }

    /**
     * 修改订单请求信息
     */
    @SaCheckPermission("order:orderRequest:edit")
    @Log(title = "订单请求信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsOrderRequestBo bo) {
        return toAjax(iTopsOrderRequestService.updateByBo(bo));
    }

    /**
     * 删除订单请求信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("order:orderRequest:remove")
    @Log(title = "订单请求信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsOrderRequestService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
