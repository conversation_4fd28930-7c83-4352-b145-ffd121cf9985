package com.tops.order.controller;

import com.tops.common.core.domain.R;
import com.tops.common.exception.InternalFailureException;
import com.tops.order.domain.vo.OrderStatusCountListVO;
import com.tops.order.domain.vo.OrderStatusCountVO;
import com.tops.order.domain.vo.QueryOrderStatusRequest;
import com.tops.order.service.OrderReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName OrderMonitorController
 * @Description 用途
 * @date 2024年01月22日 2:42 PM
 */
@Slf4j
@RestController
@RequestMapping("/order/")
public class OrderMonitorController {
    @Resource
    OrderReportService orderReportService;
    @PostMapping(value = "/orderStatus/list")
    public R<List<OrderStatusCountListVO>> OrderStatusCountVO(@RequestBody QueryOrderStatusRequest queryOrderStatusRequest) throws InternalFailureException {
        List<OrderStatusCountListVO> result = orderReportService.getOrderStatus(queryOrderStatusRequest);
        return R.ok(result);
    }
}
