package com.tops.order.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 接单请求业务对象 tops_order_request
 *
 * <AUTHOR>
 * @date 2024-05-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopsOrderRequestBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 商家账号
     */
    @NotBlank(message = "商家账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountNo;

    /**
     * 代理销售
     */
    @NotBlank(message = "代理销售不能为空", groups = { AddGroup.class, EditGroup.class })
    private String agentSales;

    /**
     * 业务场景
     */
    @NotBlank(message = "业务场景不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessScene;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessType;

    /**
     * 业务身份
     */
    @NotBlank(message = "业务身份不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> businessUnit;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createPin;

    /**
     * 客户订单号
     */
    @NotBlank(message = "客户订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerOrderNo;

    /**
     * 客户订单号
     */
    private String customOrderNo;

    /**
     * 消息ID
     */
    @NotNull(message = "消息ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer messageId;

    /**
     * MQ重试消息
     */
    @NotNull(message = "MQ重试消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer mqRetryMessage;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "操作时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date operatorTime;

    /**
     * 订单标记
     */
    @NotBlank(message = "订单标记不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderMark;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 订单标准状态
     */
    @NotNull(message = "订单标准状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer orderStandardStatus;

    /**
     * 订单状态
     */
    @NotNull(message = "订单状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer orderStatus;

    /**
     * PDQ重试消息
     */
    @NotNull(message = "PDQ重试消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer pdqRetryMessage;

    /**
     * 请求报文
     */
    @NotBlank(message = "请求报文不能为空", groups = { AddGroup.class, EditGroup.class })
    private String request;

    /**
     * 响应报文
     */
    @NotBlank(message = "响应报文不能为空", groups = { AddGroup.class, EditGroup.class })
    private String response;

    /**
     * 响应代码responseCode
     */
    @NotBlank(message = "响应代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> responseCode;

    /**
     * 响应代码excludedResponseCode
     */
    @NotBlank(message = "排除响应代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> excludedResponseCode;

    /**
     * 响应消息
     */
    @NotBlank(message = "响应消息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String responseMsg;

    /**
     * SKU大小
     */
    @NotNull(message = "SKU大小不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer skuSize;

    /**
     * 订单来源
     */
    @NotBlank(message = "订单来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private String soSource;

    /**
     * 系统调用者
     */
    @NotBlank(message = "系统调用者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String systemCaller;

    /**
     * 订单类型
     */
    @NotBlank(message = "订单类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> customOrderType;

    /**
     * 系统ID
     */
    @NotNull(message = "系统ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer systemId;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String tenantId;

    /**
     * 主题
     */
    @NotBlank(message = "主题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String topic;

    /**
     * 追踪ID
     */
    @NotBlank(message = "追踪ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String traceId;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updatePin;

    /**
     * 是否有效
     */
    @NotNull(message = "是否有效不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer yn;


}
