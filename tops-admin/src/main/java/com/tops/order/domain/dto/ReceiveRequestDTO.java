package com.tops.order.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class ReceiveRequestDTO {
    private String tenantId;
    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 调用来源
     */
    private String systemCaller;
    /**
     * 用户账号
     */
    private String accountNo;
    /**
     * 商家订单号
     */
    private String customerOrderNo;
    /**
     * 下单时间
     */
    private Date operatorTime;
    /**
     * 错误码
     */
    private String responseCode;
    /**
     * 场景描述
     */
    private String responseMsg;
}
