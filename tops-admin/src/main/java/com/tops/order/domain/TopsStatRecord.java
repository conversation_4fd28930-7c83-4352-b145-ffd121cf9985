package com.tops.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;

/**
 * 订单统计对象 tops_stat_record
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_stat_record")
public class TopsStatRecord extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 统计类型：订单单量、异常记录
     */
    private String type;
    /**
     * 统计分类:时间天或小时
     */
    private String category;
    /**
     * 统计分类值(默认支持时间)
     */
    private Date categoryValue;
    /**
     * 维度
     */
    private String dimension;
    /**
     * 维度值(POP、ISV)
     */
    private String dimensionValue;
    /**
     * 数量
     */
    private Long statCount;
    /**
     * 时间戳
     */
    private Date ts;

}
