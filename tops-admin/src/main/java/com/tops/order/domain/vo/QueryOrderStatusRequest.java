package com.tops.order.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName QueryOrderStatusRequest
 * @Description 用途
 * @date 2024年01月22日 2:57 PM
 */

@Data
public class QueryOrderStatusRequest {
    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 查询时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date queryTime;
}
