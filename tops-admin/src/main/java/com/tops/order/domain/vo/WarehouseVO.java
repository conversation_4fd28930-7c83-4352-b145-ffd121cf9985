package com.tops.order.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName WarehouseInfo
 * @Description 用途
 * @date 2024年08月22日 8:14 PM
 */
@Data
public class WarehouseVO {
    /**
     /**
     * 库房ID
     */
    private Long warehouseId;
    /**
     * 库房编号
     */
    private String warehouseNo;

    /**
     * 库房类型
     */
    private Integer warehouseType;

    /**
     * 库房名称
     */
    private String warehouseName;

    /**
     * 配送中心编码
     */
    private String distributionNo;
    /**
     * 配送中心名称
     */
    private String distributionName;

    /**
     * 库房所属机构编号
     */
    private String orgNo;

    /**
     * 库房仓库号（erp统一编号）
     */
    private String erpWarehouseNo;

    /**
     * 合作伙伴编码
     */
    private String partnerNo;

    /**
     * 合作伙伴名称
     */
    private String partnerName;

    /**
     * 库房属性标识
     */
    private String warehouseProperty;

    /**
     * 省ID
     */
    private String provinceNo;

    /**
     * 市ID
     */
    private String cityNo;

    /**
     * 县ID
     */
    private String countyNo;

    /**
     * 区ID
     */
    private String townNo;

    /**
     * 省
     */
    private String provinceName;

    /**
     * 市
     */
    private String cityName;

    /**
     * 县
     */
    private String countyName;

    /**
     * 区
     */
    private String townName;

    /**
     * 详细地址
     */
    private String address;
}
