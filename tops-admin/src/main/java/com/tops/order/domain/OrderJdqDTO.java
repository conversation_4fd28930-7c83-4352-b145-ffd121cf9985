package com.tops.order.domain;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderJdqDTO
 * @Description 用于jdq的转换类
 * @date 2024年11月04日 10:09 AM
 */
@Data
public class OrderJdqDTO {
    @JSONField(name="order_status")
    private String orderStatus;

    @JSONField(name="order_no")
    private String orderNo;

    @JSONField(name="order_status_custom")
    private String orderStatusCustom;

    @JSONField(name="business_unit")
    private String businessUnit;

    @JSONField(name="business_type")
    private String businessType;

    @JSONField(name="received_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date receivedTime;

    @JSONField(name="update_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @JSONField(name="ts", format = "yyyy-MM-dd HH:mm:ss.SSSSSS")
    private Date ts;
}
