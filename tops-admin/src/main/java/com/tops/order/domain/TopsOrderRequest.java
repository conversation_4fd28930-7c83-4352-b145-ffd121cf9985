package com.tops.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tops.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 接单请求信息对象 tops_order_request
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_order_request")
public class TopsOrderRequest extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商家账号
     */
    private String accountNo;
    /**
     * 代理销售
     */
    private String agentSales;
    /**
     * 业务场景
     */
    private String businessScene;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 创建人
     */
    private String createPin;
    /**
     * 客户订单号
     */
    private String customerOrderNo;
    /**
     * 消息ID
     */
    private Integer messageId;
    /**
     * MQ重试消息
     */
    private Integer mqRetryMessage;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private Date operatorTime;
    /**
     * 订单标记
     */
    private String orderMark;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单标准状态
     */
    private Integer orderStandardStatus;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * PDQ重试消息
     */
    private Integer pdqRetryMessage;
    /**
     * 请求报文
     */
    private String request;
    /**
     * 响应报文
     */
    private String response;
    /**
     * 响应代码
     */
    private String responseCode;
    /**
     * 响应消息
     */
    private String responseMsg;
    /**
     * SKU大小
     */
    private Integer skuSize;
    /**
     * 订单来源
     */
    private String soSource;
    /**
     * 系统调用者
     */
    private String systemCaller;
    /**
     * 订单类型
     */
    private String customOrderType;
    /**
     * 系统ID
     */
    private Integer systemId;
    /**
     * 租户编号
     */
    private String tenantId;
    /**
     * 主题
     */
    private String topic;
    /**
     * 追踪ID
     */
    private String traceId;
    /**
     * 更新人
     */
    private String updatePin;
    /**
     * 是否有效
     */
    private Integer yn;

}
