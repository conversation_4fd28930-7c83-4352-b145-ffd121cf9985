package com.tops.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;
import org.noear.solon.annotation.Param;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单信息视图对象 tops_order_info
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@Data
@ExcelIgnoreUnannotated
public class OpsOrderInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询标识
     */
    @ExcelProperty(value = "查询标识")
    private String queryKey;

    /**
     * 订单号
     */
    @Param(description = "京东物流订单号")
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 父单单号
     */
    @Param(description = "京东物流父单号")
    @ExcelProperty(value = "父单单号")
    private String parentOrderNo;

    /**
     * 客户订单号
     */
    @Param(description = "客户订单号，代表商家单号或外部单号")
    @ExcelProperty(value = "客户订单号")
    private String customerOrderNo;

    /**
     * 自定义单号
     */
    @Param(description = "自定义单号，在供应链仓配场景上代表是京东运单号")
    @ExcelProperty(value = "自定义单号")
    private String customOrderNo;

    /**
     * 渠道单号
     */
    @Param(description = "渠道单号，在供应链仓配场景上代表是京东运单号")
    @ExcelProperty(value = "渠道单号")
    private String channelOrderNo;

    /**
     * 运单号
     */
    @Param(description = "运单号")
    @ExcelProperty(value = "运单号")
    private String waybillNo;

    /**
     * 渠道来源
     */
    @Param(description = "渠道来源")
    @ExcelProperty(value = "渠道来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_channel_source")
    private String channelSource;

    /**
     * 商家账号
     */
    @ExcelProperty(value = "商家账号")
    private String accountNo;

    /**
     * 客户名称
     */
    @Param(description = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 业务身份
     */
    @Param(description = "业务身份")
    @ExcelProperty(value = "业务身份", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_business_units")
    private String businessUnit;

    /**
     * 订单状态
     */
    @Param(description = "订单状态")
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_standard_status")
    private String orderStatus;

    /**
     * 订单自定义状态
     */
    @Param(description = "订单自定义状态")
    @ExcelProperty(value = "订单自定义状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_custom_order_type")
    private String orderCustomStatus;

    /**
     * 订单自定义状态
     */
    @Param(description = "订单自定义状态")
    @ExcelProperty(value = "订单取消状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_cancel_status")
    private String orderCancelStatus;

    /**
     * 渠道订单状态
     */
    @Param(description = "渠道订单状态")
    @ExcelProperty(value = "渠道订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_channel_status")
    private String channelOrderStatus;

    /**
     * 渠道取消状态
     */
    @Param(description = "渠道取消状态")
    @ExcelProperty(value = "渠道取消状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_channel_cancel")
    private String channelOrderCancelStatus;

    /**
     * 运单状态
     */
    @Param(description = "运单状态")
    @ExcelProperty(value = "运单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_waybill_status")
    private String waybillStatus;

    /**
     * 订单创建人
     */
    @Param(description = "订单创建人")
    @ExcelProperty(value = "订单创建人")
    private String orderCreator;

    /**
     * 订单创建时间
     */
    @Param(description = "订单创建时间")
    @ExcelProperty(value = "订单创建时间")
    private Date receiveTime;

    /**
     * 最后操作人
     */
    @ExcelProperty(value = "最后操作人")
    private String lastOperator;
    /**
     * 最后操作时间
     */
    @ExcelProperty(value = "最后操作时间")
    private Date lastOperationTime;

    /**
     * 订单类型
     */
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_custom_type")
    private String customOrderType;

    /**
     * 百川标识
     */
    @ExcelProperty(value = "百川标识", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_baichuan_flag")
    private String baichuanFlag;

    private String orderDetailJson;

    /**
     * 收货人信息
     */
    private OpsOrderPersonVo consignee;

    /**
     * 履约仓信息
     */
    private OpsWarehouseVo warehouse;

}
