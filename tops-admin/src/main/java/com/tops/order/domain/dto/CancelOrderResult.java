package com.tops.order.domain.dto;

import com.tops.common.core.domain.R;
import lombok.Getter;
import lombok.Setter;

/**
 * 取消订单处理结果
 */
@Getter
@Setter
public class CancelOrderResult {
    /**
     * 物流订单号
     */
    String orderNo;
    /**
     * 状态码
     */
    String code;
    /**
     * 描述
     */
    String msg;

    public static CancelOrderResult of(String orderNo,String code,String msg){
        CancelOrderResult cancelOrderResult = new CancelOrderResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(code);
        cancelOrderResult.setMsg(msg);
        return cancelOrderResult;
    }

    public static CancelOrderResult success(String orderNo){
        CancelOrderResult cancelOrderResult = new CancelOrderResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(""+R.SUCCESS);
        cancelOrderResult.setMsg("操作成功");
        return cancelOrderResult;
    }
    public static CancelOrderResult fail(String orderNo,String msg){
        CancelOrderResult cancelOrderResult = new CancelOrderResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(""+R.FAIL);
        cancelOrderResult.setMsg(msg);
        return cancelOrderResult;
    }
    public static CancelOrderResult fail(String orderNo,String code,String msg){
        CancelOrderResult cancelOrderResult = new CancelOrderResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(code);
        cancelOrderResult.setMsg(msg);
        return cancelOrderResult;
    }

}
