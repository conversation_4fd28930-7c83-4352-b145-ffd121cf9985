package com.tops.order.domain.vo;

import cn.jdl.oms.report.dto.request.ModifyOrderSomeDataOpsRequest;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName CallbackOrderRequest
 * @Description 用途
 * @date 2024年08月14日 10:50 AM
 */
@Data
public class ModifyOrderRequest {

    /**
     * 选择回放的自定义状态
     */
    private ModifyOrderSomeDataOpsRequest request;

    /**
     * 订单列表
     */
    private List<String> orderNos;

    /**
     * 修改原因
     */
    private String modifyReason;

    /**
     * 货品匹配类型
     */
    private Integer cargoMatchType;
}
