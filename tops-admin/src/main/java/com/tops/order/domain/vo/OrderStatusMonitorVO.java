package com.tops.order.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderStatusMonitorVO
 * @Description 订单状态监控报警
 * @date 2024年10月31日 7:55 PM
 */
@Data
public class OrderStatusMonitorVO {
    /**
     * 业务身份
     */
    @JSONField(name = "业务身份")
    private String businessUnit;

    /**
     * 自定义状态
     */
    @JSONField(name = "自定义状态")
    private String customOrderStatus;

    /**
     * 触发时间
     */
    @JSONField(name = "触发时间", format = "yyyy-MM-dd hh:mm:ss")
    private Date alarmTime;

    /**
     * 当前自定义状态滞留数量(黄灯)
     */
    @JSONField(name = "当前滞留数量(黄灯)")
    private int currYellowRetentionCount;

    /**
     * 当前自定义状态滞留数量(红灯)
     */
    @JSONField(name = "当前滞留数量(红灯)")
    private int currRedRetentionCount;

    /**
     * 当前自定义状态总数
     */
    @JSONField(name = "当前状态总数")
    private int customStatusTotalCount;

    /**
     * 当前占比
     */
    @JSONField(name = "黄色滞留上限")
    private int yellowRetentionUpperLimit;

    /**
     * 滞留状态上限
     */
    @JSONField(name = "红色滞留上限")
    private int redRetentionUpperLimit;
}
