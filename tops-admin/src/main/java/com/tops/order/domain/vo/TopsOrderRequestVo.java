package com.tops.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接单请求视图对象 tops_order_request
 *
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ExcelIgnoreUnannotated
public class TopsOrderRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 商家账号
     */
    @ExcelProperty(value = "商家账号")
    private String accountNo;

    /**
     * 商家账号名称
     */
    @ExcelProperty(value = "商家账号名称")
    private String accountName;

    /**
     * 商家账号名称
     */
    @ExcelProperty(value = "接单策略")
    private Integer orderStrategy;

    /**
     * 代理销售
     */
    @ExcelProperty(value = "代理销售")
    private String agentSales;

    /**
     * 业务场景
     */
    @ExcelProperty(value = "业务场景")
    private String businessScene;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务身份
     */
    @ExcelProperty(value = "业务身份", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_business_units")
    private String businessUnit;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createPin;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 客户订单号
     */
    @ExcelProperty(value = "客户订单号")
    private String customerOrderNo;

    /**
     * 消息ID
     */
    @ExcelProperty(value = "消息ID")
    private Integer messageId;

    /**
     * MQ重试消息
     */
    @ExcelProperty(value = "MQ重试消息")
    private Integer mqRetryMessage;

    /**
     * 操作人
     */
    @ExcelProperty(value = "操作人")
    private String operator;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间")
    private Date operatorTime;

    /**
     * 订单标记
     */
    @ExcelProperty(value = "订单标记")
    private String orderMark;

    /**
     * 订单号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单标准状态
     */
    @ExcelProperty(value = "订单标准状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_standard_status")
    private Integer orderStandardStatus;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_custom_status")
    private Integer orderStatus;

    /**
     * PDQ重试消息
     */
    @ExcelProperty(value = "PDQ重试消息")
    private Integer pdqRetryMessage;

    /**
     * 请求报文
     */
    @ExcelProperty(value = "请求报文")
    private String request;

    /**
     * 响应报文
     */
    @ExcelProperty(value = "响应报文")
    private String response;

    /**
     * 响应代码
     */
    @ExcelProperty(value = "响应代码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_response_msg")
    private String responseCode;

    /**
     * 响应消息
     */
    @ExcelProperty(value = "响应消息")
    private String responseMsg;

    /**
     * SKU大小
     */
    @ExcelProperty(value = "SKU大小")
    private Integer skuSize;

    /**
     * 订单来源
     */
    @ExcelProperty(value = "订单来源")
    private String soSource;

    /**
     * 系统调用者
     */
    @ExcelProperty(value = "系统调用者")
    private String systemCaller;

    /**
     * 订单类型
     */
    @ExcelProperty(value = "订单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "ops_order_custom_type")
    private String customOrderType;

    /**
     * 系统ID
     */
    @ExcelProperty(value = "系统ID")
    private Integer systemId;

    /**
     * 租户编号
     */
    @ExcelProperty(value = "租户编号")
    private String tenantId;

    /**
     * 主题
     */
    @ExcelProperty(value = "主题")
    private String topic;

    /**
     * 追踪ID
     */
    @ExcelProperty(value = "追踪ID")
    private String traceId;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updatePin;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 是否有效
     */
    @ExcelProperty(value = "是否有效")
    private Integer yn;

    /**
     * 系统ID
     */
    @ExcelProperty(value = "接单状态")
    private String createStatus;

}
