package com.tops.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 敏感词校验视图对象 tops_order_sensitive_words
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ExcelIgnoreUnannotated
public class TopsOrderSensitiveWordsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    /**
     * 敏感词
     */
    @ExcelProperty(value = "敏感词")
    private String sensitiveWord;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 种类
     */
    @ExcelProperty(value = "种类")
    private String cate;

    /**
     * 是否命中
     */
    @ExcelProperty(value = "是否命中")
    private Integer isHit;

    /**
     * 失败描述
     */
    @ExcelProperty(value = "失败描述")
    private String failureDescription;

    /**
     * 调用状态
     */
    @ExcelProperty(value = "调用状态")
    private String callStatus;

    /**
     * 敏感词清单
     */
    @ExcelProperty(value = "敏感词清单")
    private String sensitiveWordsList;

    /**
     * 扩展字段1
     */
    @ExcelProperty(value = "扩展字段1")
    private String extension1;

    /**
     * 扩展字段2
     */
    @ExcelProperty(value = "扩展字段2")
    private String extension2;

    /**
     * 扩展字段3
     */
    @ExcelProperty(value = "扩展字段3")
    private String extension3;

    /**
     * 扩展字段4
     */
    @ExcelProperty(value = "扩展字段4")
    private String extension4;

    /**
     * 扩展字段5
     */
    @ExcelProperty(value = "扩展字段5")
    private String extension5;


}
