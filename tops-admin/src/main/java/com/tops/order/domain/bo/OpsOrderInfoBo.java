package com.tops.order.domain.bo;

import com.tops.common.core.domain.BaseEntity;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 订单信息业务对象 tops_order_info
 *
 * <AUTHOR>
 * @date 2024-05-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsOrderInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 租户编号
     */
    @NotBlank(message = "租户编号不能为空", groups = { EditGroup.class })
    private String tenantId;

    /**
     * 查询标识
     */
    @NotBlank(message = "查询标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String queryKey;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;

    /**
     * 父单单号
     */
    @NotBlank(message = "父单单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrderNo;

    /**
     * 客户订单号
     */
    @NotBlank(message = "客户订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerOrderNo;

    /**
     * 自定义单号
     */
    @NotBlank(message = "自定义单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customOrderNo;

    /**
     * 渠道单号
     */
    @NotBlank(message = "渠道单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOrderNo;

    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String waybillNo;

    /**
     * 渠道来源
     */
    @NotBlank(message = "渠道来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> channelSource;

    /**
     * 商家账号
     */
    @NotBlank(message = "商家账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accountNo;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerName;

    /**
     * 业务身份
     */
    @NotBlank(message = "业务身份不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> businessUnit;

    /**
     * 订单状态
     */
    @NotBlank(message = "订单状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> orderStatus;

    /**
     * 订单自定义状态
     */
    @NotBlank(message = "订单自定义状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> orderCustomStatus;

    /**
     * 渠道订单状态
     */
    @NotBlank(message = "渠道订单状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> channelOrderStatus;

    /**
     * 渠道取消状态
     */
    @NotBlank(message = "渠道取消状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOrderCancelStatus;

    /**
     * 运单状态
     */
    @NotBlank(message = "运单状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> waybillStatus;

    /**
     * 订单创建时间
     */
    @NotNull(message = "订单创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String receiveTime;

    /**
     * 最后操作时间
     */
    @NotNull(message = "最后操作时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String lastOperationTime;

    /**
     * 订单类型
     */
    @NotBlank(message = "订单类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private List<String> customOrderType;

    /**
     * 百川标识
     */
    @NotBlank(message = "百川标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String baichuanFlag;

    /**
     * 创建部门
     */
    private Long createDept;


}
