package com.tops.order.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName:TopsOrderRequestEsRecord
 * Package:com.tops.order.domain.dto
 * Description:
 *
 * @date:2024/5/29 上午10:14
 * @author:WeiLiming
 */
@Data
public class TopsOrderRequestEsRecord {
    private String accountNo;
    private String accountName;
    private String agentSales;
    private String businessScene;
    private String businessType;
    private String businessUnit;
    private String createPin;
    private LocalDateTime createTime;
    private String customerOrderNo;
    private String waybillNo;
    private Integer messageId;
    private Boolean mqRetryMessage;
    private String operator;
    private LocalDateTime operatorTime;
    private String orderMark;
    private String orderNo;
    private Integer orderStandardStatus;
    private Integer orderStatus;
    private Integer orderStrategy;
    private Boolean pdqRetryMessage;
    private String request;
    private String response;
    private String responseCode;
    private String responseMsg;
    private Integer skuSize;
    private String soSource;
    private String systemCaller;
    private Integer systemId;
    private String tenantId;
    private String topic;
    private String traceId;
    private String updatePin;
    private LocalDateTime updateTime;
    private Integer yn;

}
