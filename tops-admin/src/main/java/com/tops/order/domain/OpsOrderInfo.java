package com.tops.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;

/**
 * 订单信息对象 tops_order_info
 *
 * <AUTHOR>
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_order_info")
public class OpsOrderInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 租户编号
     */
    private String tenantId;
    /**
     * 查询标识
     */
    private String queryKey;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 父单单号
     */
    private String parentOrderNo;
    /**
     * 客户订单号
     */
    private String customerOrderNo;
    /**
     * 自定义单号
     */
    private String customOrderNo;
    /**
     * 渠道单号
     */
    private String channelOrderNo;
    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 渠道来源
     */
    private String channelSource;
    /**
     * 商家账号
     */
    private String accountNo;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 订单自定义状态
     */
    private String orderCustomStatus;
    /**
     * 渠道订单状态
     */
    private String channelOrderStatus;
    /**
     * 渠道取消状态
     */
    private String channelOrderCancelStatus;
    /**
     * 运单状态
     */
    private String waybillStatus;
    /**
     * 订单创建时间
     */
    private Date receiveTime;
    /**
     * 最后操作时间
     */
    private Date lastOperationTime;
    /**
     * 订单类型
     */
    private String customOrderType;
    /**
     * 百川标识
     */
    private String baichuanFlag;
    /**
     * 创建部门
     */
    private Long createDept;

}
