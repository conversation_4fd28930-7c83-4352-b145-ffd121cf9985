package com.tops.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单统计视图对象 tops_stat_record
 *
 * <AUTHOR>
 * @date 2024-06-12
 */
@Data
@ExcelIgnoreUnannotated
public class TopsStatRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 统计类型：订单单量、异常记录
     */
    @ExcelProperty(value = "统计类型：订单单量、异常记录")
    private String type;

    /**
     * 统计分类:时间天或小时
     */
    @ExcelProperty(value = "统计分类:时间天或小时")
    private String category;

    /**
     * 统计分类值(默认支持时间)
     */
    @ExcelProperty(value = "统计分类值(默认支持时间)")
    private Date categoryValue;

    /**
     * 维度
     */
    @ExcelProperty(value = "维度")
    private String dimension;

    /**
     * 维度值(POP、ISV)
     */
    @ExcelProperty(value = "维度值(POP、ISV)")
    private String dimensionValue;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private Long statCount;


}
