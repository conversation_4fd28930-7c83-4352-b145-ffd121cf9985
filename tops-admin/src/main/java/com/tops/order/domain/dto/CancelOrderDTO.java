package com.tops.order.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import static com.tops.audit.constants.AuditConstants.XbpConstants.OrderConstants.*;
/**
 * 要取消的订单信息
 */
@Getter
@Setter
public class CancelOrderDTO {

    /**
     * 订单号
     */
    @JSONField(name = CANCEL_ORDER_FIELD_ORDERS_NO)
    private String cargoNo;
    /**
     * 变更前状态
     */
    @JSONField(name = CANCEL_ORDER_FIELD_STATUS_BEFORE)
    private String statusBefore;
    /**
     * 变更后状态
     */
    @JSONField(name = CANCEL_ORDER_FIELD_STATUS_AFTER)
    private String statusAfter;

    private String cancelType;

    private String remark;

    private String operator;

    private Date operateTime;

}
