package com.tops.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.tops.common.core.domain.BaseEntity;

/**
 * 敏感词校验对象 tops_order_sensitive_words
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_order_sensitive_words")
public class TopsOrderSensitiveWords extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 敏感词
     */
    private String sensitiveWord;
    /**
     * 类型
     */
    private String type;
    /**
     * 种类
     */
    private String cate;
    /**
     * 是否命中
     */
    private Integer isHit;
    /**
     * 失败描述
     */
    private String failureDescription;
    /**
     * 调用状态
     */
    private String callStatus;
    /**
     * 敏感词清单
     */
    private String sensitiveWordsList;
    /**
     * 扩展字段1
     */
    private String extension1;
    /**
     * 扩展字段2
     */
    private String extension2;
    /**
     * 扩展字段3
     */
    private String extension3;
    /**
     * 扩展字段4
     */
    private String extension4;
    /**
     * 扩展字段5
     */
    private String extension5;

}
