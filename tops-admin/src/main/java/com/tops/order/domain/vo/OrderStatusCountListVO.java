package com.tops.order.domain.vo;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName orderStatusCountListVO
 * @Description 用途
 * @date 2024年11月06日 5:53 PM
 */
@Data
public class OrderStatusCountListVO {
    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 自定义状态
     */
    private String customOrderStatus;

    /**
     * 异常状态(red--严重, yellow-警告, green-正常)
     */
    private String exceptionStatus;

    /**
     * 该状态总量
     */
    private Integer totalCount;

    /**
     * 异常数量(黄灯)
     */
    private Integer yellowExceptionCount;

    /**
     * 异常数量(红灯)
     */
    private Integer redExceptionCount;

    /**
     * 黄灯上限配置
     */
    private Integer yellowExceptionCountConfig;

    /**
     * 红灯上限配置
     */
    private Integer redExceptionCountConfig;

    /**
     * 黄灯超时配置
     */
    private Integer yellowTimeoutConfig;

    /**
     * 红灯超时配置
     */
    private Integer redTimeoutConfig;

    /**
     *  业务身份具体
     */
    List<OrderStatusCountVO> orderStatusCountVOList;
}
