package com.tops.order.domain.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName OrderStatusFlow
 * @Description 用途
 * @date 2024年01月08日 5:53 PM
 */
@Data
public class OrderStatusFlow {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单标准状态
     */
    private Integer orderStatusCustom;

    /**
     * 下一状态时间
     */
    private Date nextStatusTime;

    /**
     * 下一状态时间
     */
    private Date nextStatusTimeRed;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志,0-删除,1-存在
     */
    private Integer yn;

    /**
     * 时间戳
     */
    private Date ts;
}
