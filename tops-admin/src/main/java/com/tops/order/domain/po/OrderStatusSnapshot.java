package com.tops.order.domain.po;


import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @ClassName OrderStatusSnapShot
 * @Description 用于记录订单快照信息
 * @date 2024年01月22日 6:33 PM
 */
@Data
public class OrderStatusSnapshot {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务身份
     */
    private String businessUnit;
    /**
     * 快照时间
     */
    private Date snapshotTime;
    /**
     * 快照信息
     */
    private String text;

    /**
     * 快照信息
     */
    private String textWithBusinessUnit;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除0-未删除,1-删除
     */
    private int yn;

    /**
     * 时间戳
     */
    private Date ts;
}
