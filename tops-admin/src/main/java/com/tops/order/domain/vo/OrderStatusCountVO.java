package com.tops.order.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderStatusCountVO
 * @Description 用途
 * @date 2024年01月22日 3:01 PM
 */
@Data
public class OrderStatusCountVO {
    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 自定义状态
     */
    private String customOrderStatus;

    /**
     * 异常状态(red--严重, yellow-警告, green-正常)
     */
    private String exceptionStatus;

    /**
     * 该状态总量
     */
    private Integer totalCount;

    /**
     * 异常数量(黄灯)
     */
    private Integer yellowExceptionCount;

    /**
     * 异常数量(红灯)
     */
    private Integer redExceptionCount;
}
