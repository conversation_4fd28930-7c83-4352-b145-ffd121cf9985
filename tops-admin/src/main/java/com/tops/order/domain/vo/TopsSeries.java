package com.tops.order.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * ClassName:TopsSeries
 * Package:com.tops.order.domain.vo
 * Description:
 *
 * @date:2024/6/12 下午8:00
 * @author:WeiLiming
 */
@Data
public class TopsSeries {
    // 名称
    private String name;
    //图表类型 柱状图:bar 折线图:line 饼图:pie
    private String type;
    //柱状图中若同值，堆积在同一图中
    private String stack;
    //系列数据
    private List<Long> data;

    // Constructors, getters and setters
}
