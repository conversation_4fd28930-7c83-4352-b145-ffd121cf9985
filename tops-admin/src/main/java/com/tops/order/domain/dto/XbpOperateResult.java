package com.tops.order.domain.dto;

import com.tops.common.core.domain.R;
import lombok.Getter;
import lombok.Setter;

/**
 * xbp工单处理结果
 * @date 2024-08-09 10:18
 * <AUTHOR>
 */
@Getter
@Setter
public class XbpOperateResult {
    /**
     * 物流订单号
     */
    String orderNo;
    /**
     * 状态码
     */
    String code;
    /**
     * 描述
     */
    String msg;

    public static XbpOperateResult success(String orderNo){
        XbpOperateResult cancelOrderResult = new XbpOperateResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(""+R.SUCCESS);
        cancelOrderResult.setMsg("操作成功");
        return cancelOrderResult;
    }
    public static XbpOperateResult fail(String orderNo, String msg){
        XbpOperateResult cancelOrderResult = new XbpOperateResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(""+R.FAIL);
        cancelOrderResult.setMsg(msg);
        return cancelOrderResult;
    }
    public static XbpOperateResult fail(String orderNo, String code, String msg){
        XbpOperateResult cancelOrderResult = new XbpOperateResult();
        cancelOrderResult.setOrderNo(orderNo);
        cancelOrderResult.setCode(code);
        cancelOrderResult.setMsg(msg);
        return cancelOrderResult;
    }

}
