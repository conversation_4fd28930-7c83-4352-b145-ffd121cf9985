package com.tops.order.domain.dto;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderStatusFlowDTO
 * @Description 用途
 * @date 2024年11月08日 12:10 PM
 */
@Data
public class OrderStatusFlowQO {

    /**
     * 业务身份
     */
    private List<String> businessUnitList;

    /**
     * 订单标准状态
     */
    private List<Integer> orderStatusCustomList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 黄灯时间
     */
    private Date nextStatusTime;

    /**
     * 红灯时间
     */
    private Date nextStatusTimeRed;
}
