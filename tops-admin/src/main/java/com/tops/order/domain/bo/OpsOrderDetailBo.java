package com.tops.order.domain.bo;

import com.tops.common.core.domain.BaseEntity;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 订单信息业务对象 tops_order_info
 *
 * <AUTHOR>
 * @date 2024-05-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class OpsOrderDetailBo extends BaseEntity {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderNo;


    /**
     * 业务身份
     */
    @NotBlank(message = "业务身份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String businessUnit;

    /**
     * 父单单号
     */
    @NotBlank(message = "父单单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentOrderNo;

    /**
     * 客户订单号
     */
    @NotBlank(message = "客户订单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customerOrderNo;

    /**
     * 自定义单号
     */
    @NotBlank(message = "自定义单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String customOrderNo;

    /**
     * 渠道单号
     */
    @NotBlank(message = "渠道单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelOrderNo;

    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String waybillNo;


    /**
     * 订单创建时间
     */
    @NotNull(message = "订单创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date receiveTime;

    /**
     * 百川标识
     */
    @NotBlank(message = "百川标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String baichuanFlag;

}
