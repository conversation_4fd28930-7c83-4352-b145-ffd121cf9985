package com.tops.order.domain.bo;

import com.tops.common.core.domain.BaseEntity;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 订单统计业务对象 tops_stat_record
 *
 * <AUTHOR>
 * @date 2024-06-12
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopsStatRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    public TopsStatRecordBo() {
    }

    public TopsStatRecordBo(Date categoryValue, String dimensionValue, Long statCount) {
        this.categoryValue = categoryValue;
        this.dimensionValue = dimensionValue;
        this.statCount = statCount;
    }

    public TopsStatRecordBo(String type, String category, Date categoryValue, String dimension, String dimensionValue, Long statCount) {
        this.type = type;
        this.category = category;
        this.categoryValue = categoryValue;
        this.dimension = dimension;
        this.dimensionValue = dimensionValue;
        this.statCount = statCount;
    }

    public TopsStatRecordBo(String category, String type, String dimension) {
        this.category = category;
        this.type = type;
        this.dimension = dimension;
    }

    /**
     * 统计类型：订单单量、异常记录
     */
    @NotBlank(message = "统计类型：订单单量、异常记录不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 统计分类:时间天或小时
     */
    @NotBlank(message = "统计分类:时间天或小时不能为空", groups = {AddGroup.class, EditGroup.class})
    private String category;

    /**
     * 统计分类值(默认支持时间)
     */
    @NotNull(message = "统计分类值(默认支持时间)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date categoryValue;

    /**
     * 维度
     */
    @NotBlank(message = "维度不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dimension;

    /**
     * 维度值(POP、ISV)
     */
    @NotBlank(message = "维度值(POP、ISV)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String dimensionValue;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long statCount;

    public String getBeginTime() {
        //return super.getParams().get("beginCategoryValue").toString();
        return super.getParams().get("beginCategoryValue").toString();
        //return "2024-01-01 00:00:00";
    }

    public String setBeginTime(String beginTime) {
        super.getParams().put("beginCategoryValue", beginTime);
        return super.getParams().get("beginCategoryValue").toString();
    }

    public String getEndTime() {
        return super.getParams().get("endCategoryValue").toString();
        //return "2024-01-15 15:30:00";
    }

    public String setEndTime(String endTime) {
        super.getParams().put("endCategoryValue", endTime);
        return super.getParams().get("endCategoryValue").toString();
    }

}
