package com.tops.order.domain.vo;

import java.util.Date;
import java.util.List;

public class OrderModifyRecordsVO {

    /**
     * operateTime : 2016-05-03
     * systemCaller : Tom
     * systemSubCaller : California
     * operator : San Francisco
     * originalData : 3650 21st St, San Francisco
     * latestData : CA 94114
     * modifyDetail : [{"field":"<PERSON>","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"},{"field":"Jerry","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"},{"field":"<PERSON>","originalValue":"San Francisco","latestValue":"3650 21st St, San Francisco"}]
     */

    private Date operateTime;
    private String systemCaller;
    private String systemSubCaller;
    private String operator;
    private String originalData;
    private String latestData;
    private List<ModifyDetailVO> modifyDetail;

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getSystemCaller() {
        return systemCaller;
    }

    public void setSystemCaller(String systemCaller) {
        this.systemCaller = systemCaller;
    }

    public String getSystemSubCaller() {
        return systemSubCaller;
    }

    public void setSystemSubCaller(String systemSubCaller) {
        this.systemSubCaller = systemSubCaller;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOriginalData() {
        return originalData;
    }

    public void setOriginalData(String originalData) {
        this.originalData = originalData;
    }

    public String getLatestData() {
        return latestData;
    }

    public void setLatestData(String latestData) {
        this.latestData = latestData;
    }

    public List<ModifyDetailVO> getModifyDetail() {
        return modifyDetail;
    }

    public void setModifyDetail(List<ModifyDetailVO> modifyDetail) {
        this.modifyDetail = modifyDetail;
    }

    public static class ModifyDetailVO {
        /**
         * field : Jerry
         * originalValue : San Francisco
         * latestValue : 3650 21st St, San Francisco
         */

        private String field;
        private String originalValue;
        private String latestValue;

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public String getOriginalValue() {
            return originalValue;
        }

        public void setOriginalValue(String originalValue) {
            this.originalValue = originalValue;
        }

        public String getLatestValue() {
            return latestValue;
        }

        public void setLatestValue(String latestValue) {
            this.latestValue = latestValue;
        }
    }
}
