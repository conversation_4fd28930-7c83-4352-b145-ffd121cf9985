package com.tops.order.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName OrderStatusCountDTO
 * @Description 用途
 * @date 2024年01月22日 5:07 PM
 */
@Data
public class OrderStatusCountDTO {
    /**
     * 业务身份
     */
    private String businessUnit;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单自定义状态
     */
    private String orderStatusCustom;

    /**
     * 订单数量
     */
    private Integer count;

    /**
     * 异常订单数量
     */
    private Integer exceptionCount;

    public String getUniqueKey() {
        return businessUnit + "_" + orderStatusCustom + "_" + orderStatus;
    }
}
