package com.tops.order.domain.bo;

import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import com.tops.common.core.domain.BaseEntity;

/**
 * 敏感词校验业务对象 tops_order_sensitive_words
 *
 * <AUTHOR>
 * @date 2024-12-19
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopsOrderSensitiveWordsBo extends BaseEntity {

    /**
     * 唯一标识
     */
    @NotNull(message = "唯一标识不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 敏感词
     */
    @NotBlank(message = "敏感词不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sensitiveWord;

    /**
     * 类型
     */
    private String type;

    /**
     * 种类
     */
    @NotBlank(message = "种类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String cate;

    /**
     * 是否命中
     */
    private Integer isHit;

    /**
     * 失败描述
     */
    private String failureDescription;

    /**
     * 调用状态
     */
    private String callStatus;

    /**
     * 敏感词清单
     */
    private String sensitiveWordsList;

    /**
     * 扩展字段1
     */
    private String extension1;

    /**
     * 扩展字段2
     */
    private String extension2;

    /**
     * 扩展字段3
     */
    private String extension3;

    /**
     * 扩展字段4
     */
    private String extension4;

    /**
     * 扩展字段5
     */
    private String extension5;


}
