package com.tops.order.jdq;


import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.alibaba.fastjson.JSON;
import com.jd.bdp.jdq.JDQConfigUtil;
import com.jd.bdp.jdw.avro.JdwData;
import com.jd.bdp.jdw.avro.JdwDataSerializer;
import com.jd.bdp.jdw.avro.JdwDataUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import com.tops.common.config.ducc.OrderStatusMonitorConfig;
import com.tops.common.utils.TopsDateUtils;
import com.tops.order.domain.OrderJdqDTO;
import com.tops.order.domain.po.OrderStatusFlow;
import com.tops.order.mapper.OrderStatusFlowHistoryMapper;
import com.tops.order.mapper.OrderStatusFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @ClassName OrderStatusFlowConsumer
 * @Description 消费jdq信息
 * @date 2024年01月09日 11:13 AM
 */

@Component
@Slf4j
public class OrderStatusFlowConsumer {
    public static final ExecutorService JDQ_THREAD = new ThreadPoolExecutor(
        1,
        10,
        1,
        TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
        new ThreadFactoryBuilder().setNamePrefix("application-jdq-pool-").build(),
        new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private OrderStatusFlowMapper orderStatusFlowMapper;
    @Resource
    private OrderStatusFlowHistoryMapper orderStatusFlowHistoryMapper;
    @Resource
    private OrderStatusMonitorConfig orderStatusMonitorConfig;

    @Value("${spring.jdq.orderMonitor.username:}")
    private String userName;

    @Value("${spring.jdq.orderMonitor.applicationDomainName:}")
    private String applicationDomainName;

    @Value("${spring.jdq.orderMonitor.password:}")
    private String password;

    @Value("${spring.jdq.orderMonitor.topic:}")
    private String topic;

    public void consume() {
        //todo 配置容器生命周期, 优雅上下线改造
        Properties properties = new Properties();
        properties.putAll(JDQConfigUtil.getClientConfigs(userName,applicationDomainName,password));

        KafkaConsumer<String, byte[]> consumer = new KafkaConsumer(properties,new StringDeserializer(), new ByteArrayDeserializer());
        consumer.subscribe(Collections.singleton(topic));

        //todo 转jmq增加重试
        while (!Thread.currentThread().isInterrupted()) {
            ConsumerRecords<String, byte[]> records = consumer.poll( Duration.ofSeconds(10L));
            for (ConsumerRecord<String, byte[]> record : records) {
                byte[] bytes = record.value();
                JdwDataSerializer serializer = new JdwDataSerializer();
                JdwData jData = null;
                try {
                    jData = serializer.fromBytes(bytes);
                    String opt = jData.getOpt() != null ? jData.getOpt().toString() : null;
                    Map<String, String> srcMap = JdwDataUtils.transform(jData.getSrc());
                    Map<String, String> curMap = JdwDataUtils.transform(jData.getCur());
                    String businessUnit = MapUtils.isEmpty(srcMap) ? curMap.get("business_unit") :
                        srcMap.get("business_unit");

                    if(!orderStatusMonitorConfig.getRecordBusinessUnitSet().contains(businessUnit)) {
                        continue;
                    }

                    if("INSERT".equals(opt)) {
                        OrderJdqDTO currJdqDTO = JSON.parseObject(JSON.toJSONString(curMap), OrderJdqDTO.class);
                        handleInsert(currJdqDTO);
                    } else if("UPDATE".equals(opt)) {
                        OrderJdqDTO currJdqDTO = JSON.parseObject(JSON.toJSONString(curMap), OrderJdqDTO.class);
                        OrderJdqDTO srcJdqDTO = JSON.parseObject(JSON.toJSONString(srcMap), OrderJdqDTO.class);
                        Map<String, String> cus = JdwDataUtils.transform(jData.getCus());
                        currJdqDTO.setTs(new Date(Long.valueOf(cus.get("p_ts"))));
                        handleUpdate(srcJdqDTO, currJdqDTO);
                    }
                } catch (Exception e) {
                    log.error("save order fail", e);
                }
            }
        }

        log.info("执行结束");
        consumer.close();
    }

    @PostConstruct
    public void init() {
        log.info("OrderStatusFlowConsumer start");
        JDQ_THREAD.execute(() -> this.consume());
        log.info("OrderStatusFlowConsumer end");
    }

    public void handleInsert(OrderJdqDTO orderJdqDTO) {
        CallerInfo callerInfo = Profiler.registerInfo("order_main_insert");
        try {
            OrderStatusFlow flow = new OrderStatusFlow();
            flow.setOrderNo(orderJdqDTO.getOrderNo());
            flow.setOrderStatus(Integer.valueOf(orderJdqDTO.getOrderStatus()));
            flow.setYn(1);
            flow.setBusinessUnit(orderJdqDTO.getBusinessUnit());
            String orderStatusCustom = orderJdqDTO.getOrderStatusCustom();
            flow.setOrderStatusCustom(Integer.valueOf(orderStatusCustom));
            Date createTime = orderJdqDTO.getReceivedTime();
            flow.setCreateTime(createTime);
            flow.setUpdateTime(orderJdqDTO.getUpdateTime());
            flow.setNextStatusTime(TopsDateUtils.add(createTime, getYellowTimeout(orderStatusCustom), Calendar.MINUTE));
            flow.setNextStatusTimeRed(TopsDateUtils.add(createTime, getRedTimeout(orderStatusCustom), Calendar.MINUTE));
            flow.setTs(orderJdqDTO.getTs());
            orderStatusFlowMapper.insert(flow);
        } catch(Exception ex) {
            if(ex instanceof DuplicateKeyException) {
                log.info("主键冲突,忽略");
                return;
            }

            log.error("持久化异常 curMap:{}",  JSON.toJSONString(orderJdqDTO), ex);
            Profiler.functionError(callerInfo);
            throw ex;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    //todo 转mq之后增加事务
    public void handleUpdate(OrderJdqDTO srcJdqDTO, OrderJdqDTO curJdqDTO) {
        CallerInfo callerInfo = Profiler.registerInfo("order_main_update");
        try {
            //只看自定义状态是否修改，因为不同自定义状态会对应同一个标注状态
            if (curJdqDTO.getOrderStatusCustom() == null){
                return;
            }

            Integer orderStatus = curJdqDTO.getOrderStatus() == null ? Integer.valueOf(srcJdqDTO.getOrderStatus())
                : Integer.valueOf(curJdqDTO.getOrderStatus());
            Date updateTime = Optional.ofNullable(curJdqDTO.getUpdateTime()).orElse(new Date());
            Date lastUpdateTime = srcJdqDTO.getUpdateTime();
            String orderNo = srcJdqDTO.getOrderNo();

            Integer orderStatusCustom = Integer.valueOf(curJdqDTO.getOrderStatusCustom());
            Integer yellowTimeout = getYellowTimeout(srcJdqDTO.getOrderStatusCustom());
            Integer redTimeout = getRedTimeout(srcJdqDTO.getOrderStatusCustom());
            Date ts = Optional.ofNullable(curJdqDTO.getTs()).orElse(new Date());

            if(TopsDateUtils.dateDiff(Calendar.MINUTE, updateTime, lastUpdateTime) > yellowTimeout) {
                OrderStatusFlow history = new OrderStatusFlow();
                history.setOrderNo(srcJdqDTO.getOrderNo());

                //更新字段
                history.setTs(ts);
                //实际状态更新时间
                history.setYn(1);
                history.setBusinessUnit(srcJdqDTO.getBusinessUnit());
                history.setOrderStatusCustom(Integer.valueOf(srcJdqDTO.getOrderStatusCustom()));
                Date createTime = srcJdqDTO.getReceivedTime();
                history.setOrderStatus(Integer.valueOf(srcJdqDTO.getOrderStatus()));
                history.setCreateTime(createTime);
                history.setNextStatusTime(TopsDateUtils.add(lastUpdateTime, yellowTimeout, Calendar.MINUTE));
                history.setNextStatusTimeRed(TopsDateUtils.add(lastUpdateTime, redTimeout, Calendar.MINUTE));
                history.setUpdateTime(updateTime);
                orderStatusFlowHistoryMapper.insert(history);
            }

            //todo 增加逆序判断
            OrderStatusFlow flow = new OrderStatusFlow();
            flow.setOrderStatus(orderStatus);
            flow.setOrderStatusCustom(orderStatusCustom);
            flow.setOrderNo(orderNo);
            flow.setNextStatusTime(TopsDateUtils.add(updateTime, getYellowTimeout(curJdqDTO.getOrderStatusCustom()), Calendar.MINUTE));
            flow.setNextStatusTimeRed(TopsDateUtils.add(updateTime, getRedTimeout(curJdqDTO.getOrderStatusCustom()), Calendar.MINUTE));
            flow.setUpdateTime(updateTime);
            flow.setTs(ts);
            orderStatusFlowMapper.updateSelective(flow);
        } catch(Exception ex) {
            if(ex instanceof DuplicateKeyException) {
                log.info("主键冲突,忽略");
                return;
            }

            log.error("持久化异常,src:{}, cur:{}", JSON.toJSONString(srcJdqDTO), JSON.toJSONString(curJdqDTO), ex);
            Profiler.functionError(callerInfo);
            throw ex;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private int getYellowTimeout(String orderStatusCustom) {
        int timeout = orderStatusMonitorConfig.getDefaultTimeout();

        if(orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig().containsKey(orderStatusCustom)) {
            timeout = orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig().get(orderStatusCustom).getTimeoutYellow();
        }

        return timeout;
    }

    private int getRedTimeout( String orderStatusCustom) {
        int timeout = orderStatusMonitorConfig.getDefaultTimeout();

        if(orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig().containsKey(orderStatusCustom)) {
            timeout = orderStatusMonitorConfig.getOrderCustomStatusTimeoutConfig().get(orderStatusCustom).getTimeoutRed();
        }

        return timeout;
    }
}
