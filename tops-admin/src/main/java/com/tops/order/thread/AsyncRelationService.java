package com.tops.order.thread;

import com.tops.common.utils.StringUtils;
import com.tops.common.utils.TopsDateUtils;
import com.tops.order.adapter.TopsQueryRelationAdapter;
import com.tops.order.domain.dto.ReceiveRequestDTO;
import com.tops.order.domain.dto.TopsOrderRequestEsRecord;
import com.tops.order.domain.vo.TopsOrderRequestVo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.Future;

/**
 * ClassName:AsyncRelationService
 * Package:com.tops.order.thread
 * Description:
 *
 * @date:2024/5/31 下午5:45
 * @author:WeiLiming
 */
@Service
@EnableAsync
public class AsyncRelationService {
    @Autowired
    private TopsQueryRelationAdapter topsQueryRelationAdapter;

    @NotNull
    private static ReceiveRequestDTO getReceiveRequestDTO(TopsOrderRequestVo vo) {
        ReceiveRequestDTO receiveRequestDTO = new ReceiveRequestDTO();
        receiveRequestDTO.setAccountNo(vo.getAccountNo());
        receiveRequestDTO.setBusinessType(vo.getBusinessType());
        receiveRequestDTO.setBusinessUnit(vo.getBusinessUnit());
        receiveRequestDTO.setCustomerOrderNo(vo.getCustomerOrderNo());
        receiveRequestDTO.setTenantId("1000");
        receiveRequestDTO.setOperatorTime(new Date());
        receiveRequestDTO.setSystemCaller(vo.getSystemCaller());
        return receiveRequestDTO;
    }

    @Async("opsBusinessThreadPool")
    public Future<TopsOrderRequestVo> asyncBuildOrderRequestList(TopsOrderRequestEsRecord source) {
        TopsOrderRequestVo vo = new TopsOrderRequestVo();
        vo.setAccountNo(source.getAccountNo());
        vo.setAccountName(source.getAccountName());
        vo.setBusinessType(source.getBusinessType());
        vo.setOrderStrategy(source.getOrderStrategy());
        vo.setBusinessScene(source.getBusinessScene());
        vo.setCreatePin(source.getCreatePin());
        vo.setBusinessUnit(source.getBusinessUnit());
        vo.setCreateTime(TopsDateUtils.toDate(source.getCreateTime()));
        vo.setCustomerOrderNo(source.getCustomerOrderNo());
        vo.setRequest(source.getRequest());
        vo.setResponse(source.getResponse());
        vo.setResponseMsg(source.getResponseMsg());
        vo.setResponseCode(source.getResponseCode());
        vo.setTraceId(source.getTraceId());
        vo.setSystemCaller(source.getSystemCaller());
        vo.setCreatePin(source.getCreatePin());
        vo.setTopic(source.getTopic());
        vo.setOrderNo(source.getOrderNo());
        vo.setUpdatePin(source.getUpdatePin());
        if (source.getUpdateTime() != null) {
            vo.setUpdateTime(TopsDateUtils.toDate(source.getUpdateTime()));
        }
        if (source.getOperatorTime() != null) {
            vo.setOperatorTime(TopsDateUtils.toDate(source.getOperatorTime()));
        } else {
            vo.setOperatorTime(TopsDateUtils.toDate(source.getCreateTime()));
        }
        if (StringUtils.isNotEmpty(source.getOperator())) {
            vo.setOperator(source.getOperator());
        } else {
            vo.setOperator(source.getCreatePin());
        }
        vo.setSkuSize(source.getSkuSize());
        vo.setSystemId(source.getSystemId());
        vo.setSoSource(vo.getSoSource());
        vo.setAgentSales(source.getAgentSales());
        vo.setOrderMark(source.getOrderMark());
        vo.setCreateStatus(String.valueOf(source.getOrderStatus()));
        vo.setOrderStandardStatus(source.getOrderStandardStatus());

        // 只有接单的请求才会去判断是否接单成功
        if ("receive".equals(source.getBusinessScene())) {
            ReceiveRequestDTO receiveRequestDTO = getReceiveRequestDTO(vo);
            String orderNo = topsQueryRelationAdapter.queryOrderRelation(receiveRequestDTO);
            if (StringUtils.isNotBlank(orderNo)) {
                vo.setOrderNo(orderNo);
                // 接单成功
                vo.setCreateStatus("1");
            } else {
                // 接单失败
                vo.setCreateStatus("0");
            }
        } else if ("cancel".equals(source.getBusinessScene())) {

        }

        return new AsyncResult<>(vo);
    }

}
