package com.tops.duty.domain.bo;

import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;

/**
 * 值班信息业务对象 tops_duty_info
 *
 * <AUTHOR>
 * @date 2024-06-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TopsDutyInfoBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 小组ID
     */
    @NotBlank(message = "小组ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptId;

    /**
     * 小组名称
     */
    @NotBlank(message = "小组名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String deptName;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startDate;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startTime;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endDate;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String endTime;

    /**
     * 星期
     */
    @NotBlank(message = "星期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String startWeek;

    /**
     * erp
     */
    @NotBlank(message = "erp不能为空", groups = { AddGroup.class, EditGroup.class })
    private String erp;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 电话
     */
    @NotBlank(message = "电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;


}
