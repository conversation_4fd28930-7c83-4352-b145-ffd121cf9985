package com.tops.duty.domain.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DutyActionMsgDTO {
    /**
     * 在startDate和endDate日期范围内，排班时间分割为startTimeList，其他时间走默认
     */
    // 部门id
    private String deptId;
    // 组织架构部门名称
    private String deptName;
    // 组别id
    private String tipUrl;
    // 收件人
    private List<String> toUsers;
    private List<SubDutyActionMsgDTO> subDutyActionMsgDTOS;

    @Data
    public static class SubDutyActionMsgDTO {
        private String deptId;
        private String deptName; // 多级名称为：订单中心-仓配POP
        private String tipUrl;
        private List<String> toUsers;
        // 格式：时间，值班人列表
        private Map<String, List<DutyUserInfoDTO>> dutyUserInfoDTOMap;
    }

    @Data
    public static class DutyUserInfoDTO {
        private String name;
        private String erp;
        private String phone;
        private String email;
    }
}
