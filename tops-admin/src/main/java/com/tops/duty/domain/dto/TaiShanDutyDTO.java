package com.tops.duty.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ClassName:TaiShanDutyDTO
 * Package:org.jeecg.ops.mgt.domain.duty.dto
 * Description:
 *
 * @date:2024/6/06 21:07
 * @author:WeiLiming
 */
@NoArgsConstructor
@Data
public class TaiShanDutyDTO {
    @JsonProperty("rotaId")
    private Integer rotaId;
    @JsonProperty("startTime")
    private String startTime;
    @JsonProperty("endTime")
    private String endTime;
    @JsonProperty("shift")
    private ShiftDTO shift;

    @NoArgsConstructor
    @Data
    public static class ShiftDTO {
        @JsonProperty("id")
        private Integer id;
        @JsonProperty("rotaId")
        private Integer rotaId;
        @JsonProperty("mode")
        private String mode;
        @JsonProperty("modeId")
        private Integer modeId;
        @JsonProperty("shiftOrder")
        private Integer shiftOrder;
        @JsonProperty("groups")
        private List<GroupsDTO> groups;

        @NoArgsConstructor
        @Data
        public static class GroupsDTO {
            @JsonProperty("shiftId")
            private Integer shiftId;
            @JsonProperty("groupOrder")
            private Integer groupOrder;
            @JsonProperty("contacts")
            private List<String> contacts;
            @JsonProperty("contactsWithInfo")
            private List<ContactsWithInfoDTO> contactsWithInfo;

            @NoArgsConstructor
            @Data
            public static class ContactsWithInfoDTO {
                @JsonProperty("username")
                private String username;
                @JsonProperty("nickname")
                private String nickname;
                @JsonProperty("email")
                private String email;
                @JsonProperty("mobile")
                private String mobile;
                @JsonProperty("telephone")
                private Object telephone;
                @JsonProperty("departmentCode")
                private String departmentCode;
                @JsonProperty("departmentName")
                private String departmentName;
                @JsonProperty("headImg")
                private String headImg;
                @JsonProperty("deleted")
                private Boolean deleted;
            }
        }
    }
}
