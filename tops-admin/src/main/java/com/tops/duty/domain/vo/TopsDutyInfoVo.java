package com.tops.duty.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.tops.common.annotation.ExcelDictFormat;
import com.tops.common.convert.ExcelDictConvert;
import lombok.Data;
import java.util.Date;

import java.io.Serializable;

/**
 * 值班信息视图对象 tops_duty_info
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@ExcelIgnoreUnannotated
public class TopsDutyInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 小组ID
     */
    @ExcelProperty(value = "小组ID")
    private String deptId;

    /**
     * 小组名称
     */
    @ExcelProperty(value = "小组名称")
    private String deptName;

    /**
     * 开始日期
     */
    @ExcelProperty(value = "开始日期")
    private Date startDate;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private String startTime;

    /**
     * 结束日期
     */
    @ExcelProperty(value = "结束日期")
    private Date endDate;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private String endTime;

    /**
     * 星期
     */
    @ExcelProperty(value = "星期")
    private String startWeek;

    /**
     * erp
     */
    @ExcelProperty(value = "erp")
    private String erp;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;


}
