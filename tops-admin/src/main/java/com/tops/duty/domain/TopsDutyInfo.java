package com.tops.duty.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.tops.common.core.domain.BaseEntity;

/**
 * 值班信息对象 tops_duty_info
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tops_duty_info")
public class TopsDutyInfo extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 小组ID
     */
    private String deptId;
    /**
     * 小组名称
     */
    private String deptName;
    /**
     * 开始日期
     */
    private Date startDate;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束日期
     */
    private Date endDate;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 星期
     */
    private String startWeek;
    /**
     * erp
     */
    private String erp;
    /**
     * 姓名
     */
    private String name;
    /**
     * 电话
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 时间戳
     */
    private Date ts;

}
