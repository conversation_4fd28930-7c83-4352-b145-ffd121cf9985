package com.tops.duty.domain.dto;

import com.tops.duty.domain.vo.TopsDutyInfoVo;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
public class DutyMsgDTO {
    /**
     * startDate, 排班起始日期
     */
    private LocalDate startDate;
    /**
     * 在startDate和endDate日期范围内，排班时间分割为startTimeList，其他时间走默认
     */
    private int actionLimit;
    /**
     * 值班Url
     */
    private boolean onlySendMessage;
    /**
     * 值班Url
     */
    private String url;
    /**
     * 白名单 逗号分隔
     */
    private String whiteErp;

    /**
     * 指定消息接收人
     */
    private String toErps;

    /**
     * 增加接收人
     */
    private String extraErps;

    /**
     * 值班分组明细
     */
    private Map<String, List<TopsDutyInfoVo>> dutyInfoMap;
}
