package com.tops.duty.domain.dto;

import lombok.Data;

import java.time.LocalDate;

@Data
public class DutyActionDTO {
    /**
     * startDate, 排班起始日期
     */
    private LocalDate startDate;
    /**
     * 在startDate和endDate日期范围内，排班时间分割为startTimeList，其他时间走默认
     */
    private int actionLimit;
    /**
     * 值班Url
     */
    private boolean onlySendMessage;
    /**
     * 值班Url
     */
    private String url;
    /**
     * 白名单 逗号分隔
     */
    private String whiteErp;

    /**
     * 指定消息接收人
     */
    private String toErps;

    /**
     * 增加接收人
     */
    private String extraErps;

    /**
     * 泰山值班表ID，提醒展示的名称
     * 格式id:name;id:name;
     */
    private String dutyIdNameStr;

}
