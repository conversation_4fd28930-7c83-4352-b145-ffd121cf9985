package com.tops.duty.service;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Strings;
import com.jd.fastjson.JSON;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.TopsHttpUtils;
import com.tops.common.utils.TopsJsonUtils;
import com.tops.duty.domain.bo.TopsDutyInfoBo;
import com.tops.duty.domain.dto.DutyActionDTO;
import com.tops.duty.domain.dto.DutyActionMsgDTO;
import com.tops.duty.domain.dto.DutyActionMsgDTO.DutyUserInfoDTO;
import com.tops.duty.domain.dto.DutyActionMsgDTO.SubDutyActionMsgDTO;
import com.tops.duty.domain.dto.TaiShanDutyDTO;
import com.tops.duty.domain.dto.TaiShanDutyDTO.ShiftDTO.GroupsDTO;
import com.tops.duty.domain.dto.TaiShanDutyDTO.ShiftDTO.GroupsDTO.ContactsWithInfoDTO;
import com.tops.duty.domain.vo.TopsDutyInfoVo;
import com.tops.employee.domain.bo.TopsEmployeeInfoBo;
import com.tops.employee.domain.vo.TopsEmployeeInfoVo;
import com.tops.employee.service.ITopsEmployeeInfoService;
import com.tops.extend.message.dto.MessageDTO;
import com.tops.extend.message.enums.MessageTypeEnum;
import com.tops.extend.message.handle.impl.DongdongSendMsgHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 值班管理泰山服务
 *
 * <AUTHOR>
 * @date 2024/06/06
 */
@Slf4j
@Service
public class DutyActionTaishanService {

    // 常量定义
    private static final String QUERY_DUTYITEMLIST_URL = "http://rota.jd.com/api/v1/rota_hub/rotas/schedules/queries";
    private static final String CHINESE_REGEX = "[\u0391-\uFFE5]";
    private static final String DEFAULT_DEPT_NAME = "交易平台组";
    private static final int DEFAULT_GROUP_ORDER = 1;
    private static final int HISTORY_DAYS = -10;
    private static final int FUTURE_DAYS = 50;
    private static final String DEFAULT_TO_USERS = "weiliming";

    @Value("${duty.auth.token:Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJhdWQiOnsibmFtZSI6ImxvbmdjaHVucWlhbzEiLCJyb2xlcyI6WyJhZG1pbiJdfSwiaWF0IjoxNjkxNDA1NDk5LCJpc3MiOiJ0aXRhbiJ9.qkdk2sYlNiSqCDH8BffhOKggq1UvSXcR2MnCLEMmTF-Q5swhkZf8XDQSvdzl7BTIdJQnvDC_1XPOrddXukJmJA}")
    private String authToken;

    @Value("${duty.auth.user:yangjun8}")
    private String authUser;

    @Autowired
    private ITopsDutyInfoService topsDutyInfoService;

    @Resource
    private DongdongSendMsgHandle dongdongSendMsgHandle;

    @Autowired
    private ITopsEmployeeInfoService iTopsEmployeeInfoService;

    /**
     * 获取字符串的长度，如果有中文，则每个中文字符计为2位
     *
     * @param value 指定的字符串
     * @return 字符串的长度
     */
    public static int length(String value) {
        if (StringUtils.isEmpty(value)) {
            return 0;
        }

        int valueLength = 0;
        for (int i = 0; i < value.length(); i++) {
            // 获取一个字符
            String temp = value.substring(i, i + 1);
            // 判断是否为中文字符
            if (temp.matches(CHINESE_REGEX)) {
                valueLength += 2;  // 中文字符长度为2
            } else {
                valueLength += 1;  // 其他字符长度为1
            }
        }
        return valueLength;
    }

    /**
     * 处理值班任务
     *
     * @param dutyIdMap 值班ID映射表
     */
    public void actionDuty(Map<String, String> dutyIdMap) {
        if (MapUtils.isEmpty(dutyIdMap)) {
            log.warn("值班ID映射表为空，无法处理值班任务");
            return;
        }

        LocalDateTime startLocalDate = LocalDateTime.now().plusDays(HISTORY_DAYS);
        LocalDateTime endLocalDate = LocalDateTime.now().plusDays(FUTURE_DAYS);

        dutyIdMap.forEach((dutyId, dutyName) -> {
            try {
                // 获取单个值班表的值班记录信息
                List<TopsDutyInfoBo> dutyActionDTOList = buildDutyDTOs(dutyId, dutyName, startLocalDate, endLocalDate);
                // 存储单个值班表的值班记录信息
                updateDutyItem(dutyId, dutyActionDTOList);
            } catch (Exception e) {
                log.error("处理值班表失败，值班ID={}，值班名称={}", dutyId, dutyName, e);
            }
        });
    }

    /**
     * 构建值班信息列表
     *
     * @param dutyId    值班ID
     * @param dutyName  值班名称
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 值班信息列表
     */
    public List<TopsDutyInfoBo> buildDutyDTOs(String dutyId, String dutyName, LocalDateTime startTime, LocalDateTime endTime) {
        // 准备请求头
        Map<String, String> header = createRequestHeader();

        // 准备请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("rotaId", dutyId);
        requestBody.put("startTime", TopsDateUtils.toDateTimeStr(startTime));
        requestBody.put("endTime", TopsDateUtils.toDateTimeStr(endTime));

        // 从泰山值班表获取值班信息
        String dutyResponse = TopsHttpUtils.doPost(QUERY_DUTYITEMLIST_URL, JSON.toJSONString(requestBody), header);

        if (StringUtils.isEmpty(dutyResponse)) {
            log.warn("【值班记录】远程获取值班记录报文为空，值班表ID为:{}", dutyId);
            return Collections.emptyList();
        }

        log.debug("【值班记录】远程获取值班记录报文：{}", dutyResponse);

        List<TaiShanDutyDTO> tsDutyActionDTOList = TopsJsonUtils.parseListObject(dutyResponse, TaiShanDutyDTO.class);

        if (CollectionUtils.isEmpty(tsDutyActionDTOList)) {
            log.warn("【值班记录】远程获取值班记录，无值班记录信息或远程调用异常，值班ID为：{} ", dutyId);
            return Collections.emptyList();
        }

        return processDutyRecords(tsDutyActionDTOList, dutyId, dutyName, startTime, endTime);
    }

    /**
     * 创建请求头
     */
    private Map<String, String> createRequestHeader() {
        Map<String, String> header = new HashMap<>(2);
        header.put("X-OPERATE-USER", authUser);
        header.put("Authorization", authToken);
        return header;
    }

    /**
     * 处理值班记录
     */
    private List<TopsDutyInfoBo> processDutyRecords(List<TaiShanDutyDTO> tsDutyActionDTOList,
                                                    String dutyId, String dutyName,
                                                    LocalDateTime startTime, LocalDateTime endTime) {
        List<TopsDutyInfoBo> dutyActionDTOList = new ArrayList<>();
        LocalDateTime currentTime = startTime;

        for (TaiShanDutyDTO tsDutyActionDTO : tsDutyActionDTOList) {
            // 获取联系人信息
            ContactsWithInfoDTO contactsDTO = extractContactInfo(tsDutyActionDTO);
            if (contactsDTO == null) {
                continue;
            }

            LocalDateTime recordStartTime = TopsDateUtils.toLocalDateTime(tsDutyActionDTO.getStartTime());
            LocalDateTime recordEndTime = TopsDateUtils.toLocalDateTime(tsDutyActionDTO.getEndTime());

            // 遍历时间范围内的每一天
            while (currentTime.isBefore(endTime)) {
                if (currentTime.isAfter(recordEndTime) || recordStartTime.isAfter(currentTime)) {
                    break;
                }

                // 创建值班信息
                TopsDutyInfoBo dutyInfo = createDutyInfo(contactsDTO, dutyId, dutyName, currentTime);
                dutyActionDTOList.add(dutyInfo);

                currentTime = currentTime.plusDays(1);
            }
        }

        return dutyActionDTOList;
    }

    /**
     * 提取联系人信息
     */
    private ContactsWithInfoDTO extractContactInfo(TaiShanDutyDTO tsDutyActionDTO) {
        List<GroupsDTO> groups = tsDutyActionDTO.getShift().getGroups();

        for (GroupsDTO groupsDTO : groups) {
            if (groupsDTO.getGroupOrder() != DEFAULT_GROUP_ORDER) {
                continue;
            }

            List<ContactsWithInfoDTO> contacts = groupsDTO.getContactsWithInfo();
            if (CollectionUtils.isNotEmpty(contacts)) {
                return contacts.get(0);
            }
        }

        return null;
    }

    /**
     * 创建值班信息对象
     */
    private TopsDutyInfoBo createDutyInfo(ContactsWithInfoDTO contactsDTO, String dutyId, String dutyName, LocalDateTime time) {
        String dayOfWeek = time.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);

        TopsDutyInfoBo dutyInfo = new TopsDutyInfoBo();
        dutyInfo.setName(contactsDTO.getNickname());
        dutyInfo.setErp(contactsDTO.getUsername());
        dutyInfo.setPhone(contactsDTO.getMobile());
        dutyInfo.setEmail(contactsDTO.getEmail());
        dutyInfo.setDeptId(dutyId);
        dutyInfo.setDeptName(dutyName);
        dutyInfo.setStartWeek(dayOfWeek);
        dutyInfo.setStartDate(TopsDateUtils.toDate(time.toLocalDate()));
        dutyInfo.setStartTime("");

        return dutyInfo;
    }

    /**
     * 更新值班项目
     *
     * @param id             值班表ID
     * @param actionDutyList 新的值班信息列表
     * @return 更新结果，通常为null
     */
    public String updateDutyItem(String id, List<TopsDutyInfoBo> actionDutyList) {
        // 通过ID清理库中数据
        TopsDutyInfoBo topsDutyInfoBo = new TopsDutyInfoBo();
        topsDutyInfoBo.setDeptId(id);
        List<TopsDutyInfoVo> dutyInfoList = topsDutyInfoService.queryList(topsDutyInfoBo);

        if (CollectionUtils.isNotEmpty(dutyInfoList)) {
            log.info("该值班表id={}，待删除值班记录数量={}", id, dutyInfoList.size());
            List<Long> ids = dutyInfoList.stream()
                .map(TopsDutyInfoVo::getId)
                .collect(Collectors.toList());
            topsDutyInfoService.deleteWithValidByIds(ids, Boolean.TRUE);
        }

        if (CollectionUtils.isEmpty(actionDutyList)) {
            log.warn("该值班表id={}，无值班记录", id);
            return null;
        }

        // 批量保存最新数据
        actionDutyList.forEach(topsDutyInfoService::insertByBo);

        return null;
    }

    /**
     * 发送值班消息
     */
    public void sendDutyMsg(DutyActionDTO dutyActionDTO) {
        if (dutyActionDTO == null) {
            log.warn("值班动作DTO为空，无法发送值班消息");
            return;
        }

        // 获取当天值班信息
        TopsDutyInfoBo bo = new TopsDutyInfoBo();
        bo.setStartDate(TopsDateUtils.toDate(LocalDate.now()));
        List<TopsDutyInfoVo> dutyInfoList = topsDutyInfoService.queryList(bo);

        if (CollectionUtils.isEmpty(dutyInfoList)) {
            log.info("【排班】当日无可用值班记录，无需提醒");
            return;
        }

        // 根据DeptName进行分组
        Map<String, List<TopsDutyInfoVo>> actionDutyMap = dutyInfoList.stream()
            .collect(Collectors.groupingBy(TopsDutyInfoVo::getDeptName));

        // 分组之后内部根据时间进一步分隔
        List<DutyActionMsgDTO> dutyActionMsgDTOList = buildDutyDTOList(actionDutyMap);

        // 发送每个分组的消息
        dutyActionMsgDTOList.forEach(dutyActionMsgDTO ->
            buildAndSendMessage(dutyActionMsgDTO, dutyActionDTO));
    }

    /**
     * 构建各组值班信息
     */
    private List<DutyActionMsgDTO> buildDutyDTOList(Map<String, List<TopsDutyInfoVo>> actionDutyMap) {
        Map<String, DutyActionMsgDTO> dutyActionMsgDTOMap = new HashMap<>();

        actionDutyMap.forEach((subDeptName, actionDutyList) -> {
            // 创建子部门值班信息
            SubDutyActionMsgDTO subDutyActionMsgDTO = createSubDutyActionMsg(subDeptName, actionDutyList);

            // 将子部门值班信息添加到部门值班信息中
            DutyActionMsgDTO dutyActionMsgDTO = dutyActionMsgDTOMap.computeIfAbsent(
                DEFAULT_DEPT_NAME,
                k -> createDutyActionMsg()
            );

            dutyActionMsgDTO.getSubDutyActionMsgDTOS().add(subDutyActionMsgDTO);
        });

        return new ArrayList<>(dutyActionMsgDTOMap.values());
    }

    /**
     * 创建部门值班信息
     */
    private DutyActionMsgDTO createDutyActionMsg() {
        DutyActionMsgDTO dutyActionMsgDTO = new DutyActionMsgDTO();
        dutyActionMsgDTO.setSubDutyActionMsgDTOS(new ArrayList<>());
        dutyActionMsgDTO.setDeptId(null);
        dutyActionMsgDTO.setDeptName(DEFAULT_DEPT_NAME);
        dutyActionMsgDTO.setToUsers(Collections.singletonList("weiliming"));
        return dutyActionMsgDTO;
    }

    /**
     * 创建子部门值班信息
     */
    private SubDutyActionMsgDTO createSubDutyActionMsg(String subDeptName, List<TopsDutyInfoVo> actionDutyList) {
        SubDutyActionMsgDTO subDutyActionMsgDTO = new SubDutyActionMsgDTO();
        subDutyActionMsgDTO.setDeptName(subDeptName);

        // 按开始时间分组
        Map<String, List<DutyUserInfoDTO>> dutyUserInfoDTOMap = new HashMap<>();

        for (TopsDutyInfoVo dutyInfo : actionDutyList) {
            String startTime = dutyInfo.getStartTime();

            // 创建值班用户信息
            DutyUserInfoDTO dutyUserInfoDTO = new DutyUserInfoDTO();
            dutyUserInfoDTO.setErp(dutyInfo.getErp());
            dutyUserInfoDTO.setName(dutyInfo.getName());
            dutyUserInfoDTO.setPhone(dutyInfo.getPhone());

            // 将值班用户信息添加到对应时间的列表中
            dutyUserInfoDTOMap.computeIfAbsent(startTime, k -> new ArrayList<>())
                .add(dutyUserInfoDTO);
        }

        subDutyActionMsgDTO.setDutyUserInfoDTOMap(dutyUserInfoDTOMap);
        return subDutyActionMsgDTO;
    }

    /**
     * 构建并发送单条消息
     */
    private void buildAndSendMessage(DutyActionMsgDTO dutyActionMsgDTO, DutyActionDTO dutyActionDTO) {
        MessageDTO msgDTO = new MessageDTO();

        // 设置接收人
        String toUsers = determineToUsers(dutyActionDTO);
        msgDTO.setToUser(toUsers);

        // 设置消息类型
        msgDTO.setType(MessageTypeEnum.DD.getType());

        // 设置消息数据
        Map<String, Object> data = new HashMap<>();
        data.put("deptName", dutyActionMsgDTO.getDeptName());
        data.put("subDetp", dutyActionMsgDTO.getSubDutyActionMsgDTOS());
        data.put("url", dutyActionDTO.getUrl());
        msgDTO.setData(data);

        // 设置消息内容
        String content = buildMessageContent(dutyActionMsgDTO);
        msgDTO.setContent(content);

        // 设置消息标题和类别
        msgDTO.setTitle("【值班提醒】" + dutyActionMsgDTO.getDeptName());
        msgDTO.setCategory("2");  // 系统消息

        // 发送消息
        log.info("【排班】发送消息：接收人={}, 标题={}", toUsers, msgDTO.getTitle());
        dongdongSendMsgHandle.sendMessage(msgDTO);
    }

    /**
     * 确定消息接收人
     */
    private String determineToUsers(DutyActionDTO dutyActionDTO) {
        StringBuilder toUsers = new StringBuilder();

        // 优先使用配置的接收人
        if (StringUtils.isNotBlank(dutyActionDTO.getToErps())) {
            toUsers.append(dutyActionDTO.getToErps());
        } else {
            TopsEmployeeInfoBo bo = new TopsEmployeeInfoBo();
            // 0 开启；1 关闭
            bo.setDutyNotify(0);
            List<TopsEmployeeInfoVo> list = iTopsEmployeeInfoService.queryList(bo);
            if (CollectionUtils.isEmpty(list)) {
                return DEFAULT_TO_USERS;
            }
            list.forEach(topsEmployeeInfoVo -> toUsers.append(topsEmployeeInfoVo.getErp()).append(","));
            toUsers.append(DEFAULT_TO_USERS);
        }

        // 添加额外接收人
        if (StringUtils.isNotBlank(dutyActionDTO.getExtraErps())) {
            toUsers.append(",").append(dutyActionDTO.getExtraErps());
        }

        return toUsers.toString();
    }

    /**
     * 构建消息内容
     */
    private String buildMessageContent(DutyActionMsgDTO dutyActionMsgDTO) {
        StringBuilder content = new StringBuilder();

        for (SubDutyActionMsgDTO dto : dutyActionMsgDTO.getSubDutyActionMsgDTOS()) {
            content.append(dto.getDeptName()).append("：");
            boolean isMultiTime = false;

            for (Map.Entry<String, List<DutyUserInfoDTO>> entry : dto.getDutyUserInfoDTOMap().entrySet()) {
                if (isMultiTime) {
                    content.append("\n")
                        .append(Strings.padStart("", length(dto.getDeptName()) + 5, ' '));
                }
                isMultiTime = true;

                // 添加时间信息
                String startTime = entry.getKey();
                if (StringUtils.isNotBlank(startTime)) {
                    content.append("「").append(startTime).append("」");
                }

                // 添加值班人员信息
                for (DutyUserInfoDTO dutyUserInfo : entry.getValue()) {
                    content.append(dutyUserInfo.getName())
                        .append("(")
                        .append(dutyUserInfo.getErp())
                        .append(") ");
                }
            }
            content.append("\n");
        }

        return content.toString();
    }
}
