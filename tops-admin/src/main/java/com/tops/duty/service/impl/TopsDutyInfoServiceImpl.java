package com.tops.duty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.utils.StringUtils;
import com.tops.duty.domain.TopsDutyInfo;
import com.tops.duty.domain.bo.TopsDutyInfoBo;
import com.tops.duty.domain.vo.TopsDutyInfoVo;
import com.tops.duty.mapper.TopsDutyInfoMapper;
import com.tops.duty.service.ITopsDutyInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 值班信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@RequiredArgsConstructor
@Service
public class TopsDutyInfoServiceImpl implements ITopsDutyInfoService {

    private final TopsDutyInfoMapper baseMapper;

    /**
     * 查询值班信息
     */
    @Override
    public TopsDutyInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询值班信息列表
     */
    @Override
    public TableDataInfo<TopsDutyInfoVo> queryPageList(TopsDutyInfoBo bo, PageQuery pageQuery) {

        LambdaQueryWrapper<TopsDutyInfo> lqw = buildQueryWrapper(bo);
        Page<TopsDutyInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询值班信息列表
     */
    @Override
    public List<TopsDutyInfoVo> queryList(TopsDutyInfoBo bo) {
        LambdaQueryWrapper<TopsDutyInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TopsDutyInfo> buildQueryWrapper(TopsDutyInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TopsDutyInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getDeptId()), TopsDutyInfo::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), TopsDutyInfo::getDeptName, bo.getDeptName());
        lqw.eq(bo.getStartDate() != null, TopsDutyInfo::getStartDate, bo.getStartDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStartTime()), TopsDutyInfo::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndDate() != null, TopsDutyInfo::getEndDate, bo.getEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getEndTime()), TopsDutyInfo::getEndTime, bo.getEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStartWeek()), TopsDutyInfo::getStartWeek, bo.getStartWeek());
        lqw.eq(StringUtils.isNotBlank(bo.getErp()), TopsDutyInfo::getErp, bo.getErp());
        lqw.like(StringUtils.isNotBlank(bo.getName()), TopsDutyInfo::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), TopsDutyInfo::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), TopsDutyInfo::getEmail, bo.getEmail());
        lqw.between(params.get("beginStartDate") != null && params.get("endStartDate") != null,
            TopsDutyInfo::getStartDate, params.get("beginStartDate"), params.get("endStartDate"));
        return lqw;
    }

    /**
     * 新增值班信息
     */
    @Override
    public Boolean insertByBo(TopsDutyInfoBo bo) {
        TopsDutyInfo add = BeanUtil.toBean(bo, TopsDutyInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改值班信息
     */
    @Override
    public Boolean updateByBo(TopsDutyInfoBo bo) {
        TopsDutyInfo update = BeanUtil.toBean(bo, TopsDutyInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TopsDutyInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除值班信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
