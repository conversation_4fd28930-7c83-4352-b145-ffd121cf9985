package com.tops.duty.service;

import com.tops.duty.domain.TopsDutyInfo;
import com.tops.duty.domain.vo.TopsDutyInfoVo;
import com.tops.duty.domain.bo.TopsDutyInfoBo;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 值班信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
public interface ITopsDutyInfoService {

    /**
     * 查询值班信息
     */
    TopsDutyInfoVo queryById(Long id);

    /**
     * 查询值班信息列表
     */
    TableDataInfo<TopsDutyInfoVo> queryPageList(TopsDutyInfoBo bo, PageQuery pageQuery);

    /**
     * 查询值班信息列表
     */
    List<TopsDutyInfoVo> queryList(TopsDutyInfoBo bo);

    /**
     * 新增值班信息
     */
    Boolean insertByBo(TopsDutyInfoBo bo);

    /**
     * 修改值班信息
     */
    Boolean updateByBo(TopsDutyInfoBo bo);

    /**
     * 校验并批量删除值班信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
