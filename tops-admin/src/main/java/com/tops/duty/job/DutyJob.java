package com.tops.duty.job;

import com.tops.common.utils.TopsJsonUtils;
import com.tops.duty.domain.dto.DutyActionDTO;
import com.tops.duty.service.DutyActionTaishanService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class DutyJob {
    @Resource
    private DutyActionTaishanService dutyActionTaishanService;

    @XxlJob(value = "dutySyncJob")
    public ReturnT<String> dutySyncJob(String param) {
        // String dutyNameStr = "1386:订单仓配;1140:订单纯配;788:产品中心;1723:库存中心;207:货品中心;2034:交易数据";
        log.info("==========DutyJob.dutySyncJob===========param:{}", param);
        if (param == null || param.isEmpty()) {
            param = "1386:订单仓配;1140:订单纯配;788:产品中心;1723:库存中心;207:货品中心;2034:交易数据";
        }
        try {
            Map<String, String> idNameMap = new HashMap<>();
            String[] idNameArr = param.split(";");
            for (String idName : idNameArr) {
                String[] arr = idName.split(":");
                idNameMap.put(arr[0].trim(), arr[1].trim());
            }
            dutyActionTaishanService.actionDuty(idNameMap);
            XxlJobLogger.log("执行完成");
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("DutyJob.dutySyncJob.recordPanelData exception:", e);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "dutySendMsgJob")
    public ReturnT<String> dutySendMsgJob(String param) {
        log.info("dutySendMsgJob==》Start：{}", param);
        DutyActionDTO dutyActionDTO = TopsJsonUtils.parseObject(param, DutyActionDTO.class);
        log.info("dutySendMsgJob==》toJSONString：{}", TopsJsonUtils.toJSONString(dutyActionDTO));
        dutyActionTaishanService.sendDutyMsg(dutyActionDTO);
        log.info("dutySendMsgJob==》任务执行完成");
        return ReturnT.SUCCESS;
    }
}
