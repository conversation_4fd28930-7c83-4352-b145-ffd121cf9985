package com.tops.duty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.google.common.collect.Lists;
import com.tops.common.annotation.Log;
import com.tops.common.annotation.RepeatSubmit;
import com.tops.common.core.controller.BaseController;
import com.tops.common.core.domain.PageQuery;
import com.tops.common.core.domain.R;
import com.tops.common.core.page.TableDataInfo;
import com.tops.common.core.validate.AddGroup;
import com.tops.common.core.validate.EditGroup;
import com.tops.common.enums.BusinessType;
import com.tops.common.utils.TopsDateUtils;
import com.tops.common.utils.poi.ExcelUtil;
import com.tops.duty.domain.bo.TopsDutyInfoBo;
import com.tops.duty.domain.vo.TopsDuty3DayVo;
import com.tops.duty.domain.vo.TopsDutyInfoVo;
import com.tops.duty.service.DutyActionTaishanService;
import com.tops.duty.service.ITopsDutyInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;

/**
 * 值班信息
 *
 * <AUTHOR>
 * @date 2024-06-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/duty/dutyInfo")
public class TopsDutyInfoController extends BaseController {

    private final ITopsDutyInfoService iTopsDutyInfoService;
    @Resource
    private DutyActionTaishanService dutyActionTaishanService;

    /**
     * 首页功能：查询最近三天日历，传入某一日期，查出该日期周围的当天、前一天、后一天数据
     */
    //@SaCheckPermission("duty:dutyInfo:list")
    @GetMapping("/listDuty3Day")
    public TopsDuty3DayVo listDuty3Day(@RequestParam String dutyDate) {
        TopsDuty3DayVo duty3DayVo = new TopsDuty3DayVo();
        //初始化为日期0点
        TopsDutyInfoBo bo = new TopsDutyInfoBo();
        if (dutyDate == null || dutyDate.isEmpty()) {
            throw new RuntimeException("值班日期入参为空");
        }
        LocalDate localDate = TopsDateUtils.toLocalDate(dutyDate);
        if (localDate == null) {
            throw new RuntimeException("值班日期入参格式错误");
        }
        bo.getParams().put("beginStartDate", localDate);
        bo.getParams().put("endStartDate", localDate.plusDays(2));

        List<TopsDutyInfoVo> voList = iTopsDutyInfoService.queryList(bo);
        List<Map<String, String>> map = transformData(voList);

        List<String> cellList = null;

        // build header
        List<String> titles = Lists.newArrayList();
        titles.addAll(map.get(0).keySet());
        // build rows
        List<List<String>> rowList = Lists.newArrayList();
        for (Map<String, String> row : map) {
            cellList = Lists.newArrayList();
            cellList.addAll(row.values());
            rowList.add(cellList);
        }
        duty3DayVo.setTitles(titles);
        duty3DayVo.setValues(rowList);
        return duty3DayVo;
    }


    /**
     * 查询值班信息列表
     */
//    @SaCheckPermission("duty:dutyInfo:list")
    @GetMapping("/list")
    public TableDataInfo<TopsDutyInfoVo> list(TopsDutyInfoBo bo, PageQuery pageQuery) {
        //String dutyNameStr = "1386:订单仓配;1140:订单纯配;788:产品中心;1723:库存中心;207:货品中心;2034:交易数据";
        // 5分钟请求一次最新数据
        /*if (RedisUtils.setObjectIfAbsent("Tops_duty", dutyNameStr, Duration.ofSeconds(300L))) {
            Map<String, String> idNameMap = new HashMap<>();
            String[] idNameArr = dutyNameStr.split(";");
            for (String idName : idNameArr) {
                String[] arr = idName.split(":");
                idNameMap.put(arr[0].trim(), arr[1].trim());
            }
            dutyActionTaishanService.actionDuty(idNameMap);
        }*/
        return iTopsDutyInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出值班信息列表
     */
    @SaCheckPermission("duty:dutyInfo:export")
    @Log(title = "值班信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TopsDutyInfoBo bo, HttpServletResponse response) {
        List<TopsDutyInfoVo> list = iTopsDutyInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "值班信息", TopsDutyInfoVo.class, response);
    }

    /**
     * 获取值班信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("duty:dutyInfo:query")
    @GetMapping("/{id}")
    public R<TopsDutyInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iTopsDutyInfoService.queryById(id));
    }

    /**
     * 新增值班信息
     */
    @SaCheckPermission("duty:dutyInfo:add")
    @Log(title = "值班信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TopsDutyInfoBo bo) {
        return toAjax(iTopsDutyInfoService.insertByBo(bo));
    }

    /**
     * 修改值班信息
     */
    @SaCheckPermission("duty:dutyInfo:edit")
    @Log(title = "值班信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TopsDutyInfoBo bo) {
        return toAjax(iTopsDutyInfoService.updateByBo(bo));
    }

    /**
     * 删除值班信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("duty:dutyInfo:remove")
    @Log(title = "值班信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iTopsDutyInfoService.deleteWithValidByIds(Arrays.asList(ids), true));
    }


    public static List<Map<String, String>> transformData(List<TopsDutyInfoVo> inputData) {
        Map<String, Map<String, String>> groupedData = new LinkedHashMap<>();
        Set<String> dateSet = new TreeSet<>();

        // Step 1: Group data by 小组 and collect all dates
        for (TopsDutyInfoVo row : inputData) {
            String group = row.getDeptName();
            String date = TopsDateUtils.toDateStr(row.getStartDate());
            String name = row.getName() + ":" + row.getErp();

            groupedData.putIfAbsent(group, new LinkedHashMap<>());
            groupedData.get(group).put(date, name);
            dateSet.add(date);
        }

        // Step 2: Create the transformed data structure
        List<Map<String, String>> transformedData = new ArrayList<>();

        for (Map.Entry<String, Map<String, String>> entry : groupedData.entrySet()) {
            Map<String, String> newRow = new LinkedHashMap<>();
            newRow.put("小组", entry.getKey());

            for (String date : dateSet) {
                newRow.put(date, entry.getValue().getOrDefault(date, "无"));
            }

            transformedData.add(newRow);
        }

        return transformedData;
    }


}


