#!/bin/sh

BASEDIR=`dirname $0`/..
BASEDIR=`(cd "$BASEDIR"; pwd)`

# 应用名称和jar包路径
APP_NAME="TopsApplication"
SPRING_BOOT_JAR="$BASEDIR/lib/tops-admin-4.8.2.jar"
PID_FILE="$BASEDIR/bin/tops-admin.pid"

echo "=========================================="
echo "Stopping JDL-Trade-OPS Application..."
echo "=========================================="

# 查找应用进程的函数
find_app_processes() {
    # 更精确的进程查找，避免误杀
    if [ -f "$SPRING_BOOT_JAR" ]; then
        # Spring Boot jar模式：查找jar文件路径
        ps -ef | grep "java" | grep "tops-admin-4.8.2.jar" | grep -v grep | awk '{print $2}'
    else
        # 传统classpath模式：查找主类名
        ps -ef | grep "java" | grep "com.tops.TopsApplication" | grep -v grep | awk '{print $2}'
    fi
}

# 优雅停止进程的函数
graceful_stop() {
    local pid=$1
    local timeout=${2:-30}  # 默认30秒超时

    echo "Attempting graceful shutdown of process $pid..."

    # 发送TERM信号进行优雅停止
    if kill -TERM $pid 2>/dev/null; then
        echo "Sent TERM signal to process $pid"

        # 等待进程优雅停止
        local count=0
        while [ $count -lt $timeout ]; do
            if ! kill -0 $pid 2>/dev/null; then
                echo "Process $pid stopped gracefully after ${count} seconds"
                return 0
            fi
            sleep 1
            count=$((count + 1))
            if [ $((count % 5)) -eq 0 ]; then
                echo "Waiting for graceful shutdown... (${count}/${timeout}s)"
            fi
        done

        # 超时后强制停止
        echo "Graceful shutdown timeout, forcing stop..."
        if kill -KILL $pid 2>/dev/null; then
            echo "Process $pid force stopped"
            return 0
        else
            echo "Failed to stop process $pid"
            return 1
        fi
    else
        echo "Failed to send TERM signal to process $pid"
        return 1
    fi
}

# 查找应用进程
PIDPROC=""

# 首先尝试从PID文件获取进程ID
if [ -f "$PID_FILE" ]; then
    PID_FROM_FILE=$(cat "$PID_FILE")
    if kill -0 "$PID_FROM_FILE" 2>/dev/null; then
        echo "Found process from PID file: $PID_FROM_FILE"
        PIDPROC="$PID_FROM_FILE"
    else
        echo "Process in PID file ($PID_FROM_FILE) is not running, removing stale PID file"
        rm -f "$PID_FILE"
    fi
fi

# 如果PID文件中没有找到运行的进程，则通过进程列表查找
if [ -z "$PIDPROC" ]; then
    PIDPROC=$(find_app_processes)
fi

if [ -z "$PIDPROC" ]; then
    echo "No $APP_NAME processes found"
    # 清理PID文件
    [ -f "$PID_FILE" ] && rm -f "$PID_FILE"
else
    echo "Found $APP_NAME processes: $PIDPROC"

    # 停止所有找到的进程
    for PID in $PIDPROC; do
        echo "----------------------------------------"
        echo "Stopping process $PID..."
        if graceful_stop $PID 30; then
            echo "✓ Process $PID stopped successfully at $(date)"
        else
            echo "✗ Failed to stop process $PID"
        fi
    done

    # 清理PID文件
    [ -f "$PID_FILE" ] && rm -f "$PID_FILE" && echo "✓ PID file removed"
fi

# 停止Nginx（如果需要）
echo "----------------------------------------"
echo "Checking Nginx status..."

checkNginx() {
    ps -ef | grep nginx | grep -v grep &> /dev/null
    return $?
}

if checkNginx; then
    echo "Nginx is running, attempting to stop..."
    if sudo /export/servers/nginx/sbin/nginx -s quit 2>/dev/null; then
        echo "✓ Nginx graceful shutdown initiated"

        # 等待Nginx停止
        sleep 3
        if ! checkNginx; then
            echo "✓ Nginx stopped successfully"
        else
            echo "⚠ Nginx still running, trying force stop..."
            sudo /export/servers/nginx/sbin/nginx -s stop 2>/dev/null || echo "✗ Failed to stop Nginx"
        fi
    else
        echo "⚠ Failed to send quit signal to Nginx"
    fi
else
    echo "Nginx is not running"
fi

# 最终验证
echo "=========================================="
echo "Final verification..."

REMAINING_PROC=$(find_app_processes)
if [ -z "$REMAINING_PROC" ]; then
    echo "✓ All $APP_NAME processes stopped successfully"
    echo "✓ Application shutdown completed at $(date)"
    exit 0
else
    echo "✗ Some processes are still running: $REMAINING_PROC"
    echo "✗ Application shutdown incomplete"
    exit 1
fi
