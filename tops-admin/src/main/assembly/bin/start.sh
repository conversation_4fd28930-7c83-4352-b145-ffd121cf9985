#!/bin/sh
curl -s "http://pfinder-master.jd.com/access/script" -o /tmp/pfinder.sh ; source /tmp/pfinder.sh || :

BASEDIR=`dirname $0`/..
BASEDIR=`(cd "$BASEDIR"; pwd)`

# PID文件路径
PID_FILE="$BASEDIR/bin/tops-admin.pid"

# If a specific java binary isn't specified search for the standard 'java' binary
if [ -z "$JAVACMD" ] ; then
  if [ -n "$JAVA_HOME"  ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
      # IBM's JDK on AIX uses strange locations for the executables
      JAVACMD="$JAVA_HOME/jre/sh/java"
    else
      JAVACMD="$JAVA_HOME/bin/java"
    fi
  else
    JAVACMD=`which java`
  fi
fi


# 检查应用是否已经在运行
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "Application is already running with PID: $pid"
            echo "If you want to restart, please run stop.sh first"
            exit 1
        else
            echo "Removing stale PID file: $PID_FILE"
            rm -f "$PID_FILE"
        fi
    fi

    # 额外检查是否有相关进程在运行
    local existing_proc
    if [ -f "$BASEDIR/lib/tops-admin-4.8.2.jar" ]; then
        existing_proc=$(ps -ef | grep "java" | grep "tops-admin-4.8.2.jar" | grep -v grep | awk '{print $2}')
    else
        existing_proc=$(ps -ef | grep "java" | grep "com.tops.TopsApplication" | grep -v grep | awk '{print $2}')
    fi

    if [ -n "$existing_proc" ]; then
        echo "Found existing application processes: $existing_proc"
        echo "Please stop the existing processes first using stop.sh"
        exit 1
    fi
}

echo "=========================================="
echo "Starting JDL-Trade-OPS Application..."
echo "=========================================="

# 检查应用是否已经在运行
check_running

# 检查是否使用Spring Boot jar启动方式
SPRING_BOOT_JAR="$BASEDIR/lib/tops-admin-4.8.2.jar"
if [ -f "$SPRING_BOOT_JAR" ]; then
    echo "Using Spring Boot executable jar: $SPRING_BOOT_JAR"
    STARTUP_MODE="spring-boot-jar"
else
    echo "Using classpath mode"
    STARTUP_MODE="classpath"
fi

CLASSPATH="$BASEDIR"/conf:"$BASEDIR"/lib/*
LOGDIR="$BASEDIR/log/"

echo "CLASSPATH: $CLASSPATH"
echo "STARTUP_MODE: $STARTUP_MODE"

if [ ! -x "$JAVACMD" ] ; then
  echo "Error: JAVA_HOME is not defined correctly."
  echo "  We cannot execute $JAVACMD"
  exit 1
fi

if [ -z "$OPTS_MEMORY" ] ; then
    OPTS_MEMORY="-Xss1m -Xms2G -Xmx2G -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./java_pid<pid>.hprof"
fi

LogPath="/export/Logs/jdl-trade-ops/"
/bin/mkdir -p $LogPath

# 根据启动模式选择不同的启动方式
echo "Starting application..."
if [ "$STARTUP_MODE" = "spring-boot-jar" ]; then
    # Spring Boot executable jar 方式启动
    nohup "$JAVACMD" ${PFINDER_AGENT:-} $JAVA_OPTS \
      $JAVA_SPEC \
      $OPTS_MEMORY \
      -Dbasedir="$BASEDIR" \
      -Dfile.encoding="UTF-8" \
      -Dspring.config.additional-location="$BASEDIR/conf/" \
      -jar "$SPRING_BOOT_JAR" \
      >${LogPath}jdl-trade-ops.log 2>&1 &
    APP_PID=$!
else
    # 传统classpath方式启动
    nohup "$JAVACMD" ${PFINDER_AGENT:-} $JAVA_OPTS \
      $JAVA_SPEC \
      $OPTS_MEMORY \
      -classpath "$CLASSPATH" \
      -Dbasedir="$BASEDIR" \
      -Dfile.encoding="UTF-8" \
      com.tops.TopsApplication \
      >${LogPath}jdl-trade-ops.log 2>&1 &
    APP_PID=$!
fi

# 保存PID到文件
echo $APP_PID > "$PID_FILE"
echo "Application started with PID: $APP_PID"
echo "PID saved to: $PID_FILE"

# 等待应用启动
echo "Waiting for application to start..."
sleep 5

# 验证应用是否成功启动
if kill -0 $APP_PID 2>/dev/null; then
    echo "✓ Application is running (PID: $APP_PID)"
else
    echo "✗ Application failed to start"
    rm -f "$PID_FILE"
    echo "Check log file: ${LogPath}jdl-trade-ops.log"
    exit 1
fi

#sudo /export/servers/nginx/sbin/nginx

checkNginx() {
    ps -ef | grep nginx | grep -v grep &> /dev/null
    echo $?
}
nginxConfigTEST() {
    sudo /export/servers/nginx/sbin/nginx -t &> /dev/null
    echo $?
}
nginx=`checkNginx`

if [ $nginx -ne 0 ];
then
    echo "begin to start nginx"
    mkdir -p /dev/shm/nginx_temp/client_body/
    nginxConfigTEST &> /dev/null && sudo /export/servers/nginx/sbin/nginx &> /dev/null || echo "nginx configTest fail,please check your nginx config!"
    nginx=`checkNginx`
    if [ $nginx -eq 0 ];
    then
        echo "start nginx successed!"
    else
        echo "start nginx failed!"
    fi
else
    echo "The Nginx is running!"
    sudo /export/servers/nginx/sbin/nginx -s reload
fi

echo "=========================================="
echo "✓ JDL-Trade-OPS Application startup completed!"
echo "✓ Application PID: $APP_PID"
echo "✓ Log file: ${LogPath}jdl-trade-ops.log"
echo "✓ PID file: $PID_FILE"
echo "✓ Started at: $(date)"
echo "=========================================="
