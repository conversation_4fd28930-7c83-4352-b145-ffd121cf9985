#!/bin/sh

BASEDIR=`dirname $0`/..
BASEDIR=`(cd "$BASEDIR"; pwd)`

# 应用名称和jar包路径
APP_NAME="TopsApplication"
SPRING_BOOT_JAR="$BASEDIR/lib/tops-admin-4.8.2.jar"
PID_FILE="$BASEDIR/bin/tops-admin.pid"
LOG_FILE="/export/Logs/jdl-trade-ops/jdl-trade-ops.log"

echo "=========================================="
echo "JDL-Trade-OPS Application Status Check"
echo "=========================================="

# 检查PID文件
if [ -f "$PID_FILE" ]; then
    PID_FROM_FILE=$(cat "$PID_FILE")
    echo "PID file exists: $PID_FILE"
    echo "PID from file: $PID_FROM_FILE"
    
    # 检查进程是否真的在运行
    if kill -0 "$PID_FROM_FILE" 2>/dev/null; then
        echo "✓ Process $PID_FROM_FILE is running"
        
        # 获取进程详细信息
        echo "----------------------------------------"
        echo "Process Details:"
        ps -p "$PID_FROM_FILE" -o pid,ppid,user,start,time,command
        
        # 检查内存使用
        echo "----------------------------------------"
        echo "Memory Usage:"
        ps -p "$PID_FROM_FILE" -o pid,vsz,rss,pmem
        
    else
        echo "✗ Process $PID_FROM_FILE is not running (stale PID file)"
    fi
else
    echo "PID file not found: $PID_FILE"
fi

# 通过进程列表查找应用
echo "----------------------------------------"
echo "Searching for application processes..."

if [ -f "$SPRING_BOOT_JAR" ]; then
    # Spring Boot jar模式
    RUNNING_PROCS=$(ps -ef | grep "java" | grep "tops-admin-4.8.2.jar" | grep -v grep)
else
    # 传统classpath模式
    RUNNING_PROCS=$(ps -ef | grep "java" | grep "com.tops.TopsApplication" | grep -v grep)
fi

if [ -n "$RUNNING_PROCS" ]; then
    echo "✓ Found running application processes:"
    echo "$RUNNING_PROCS"
else
    echo "✗ No application processes found"
fi

# 检查端口占用
echo "----------------------------------------"
echo "Port Usage Check:"
PORT_8080=$(lsof -i :8080 2>/dev/null)
if [ -n "$PORT_8080" ]; then
    echo "✓ Port 8080 is in use:"
    echo "$PORT_8080"
else
    echo "✗ Port 8080 is not in use"
fi

# 检查Nginx状态
echo "----------------------------------------"
echo "Nginx Status:"
NGINX_PROCS=$(ps -ef | grep nginx | grep -v grep)
if [ -n "$NGINX_PROCS" ]; then
    echo "✓ Nginx is running:"
    echo "$NGINX_PROCS"
else
    echo "✗ Nginx is not running"
fi

# 检查日志文件
echo "----------------------------------------"
echo "Log File Status:"
if [ -f "$LOG_FILE" ]; then
    echo "✓ Log file exists: $LOG_FILE"
    echo "Log file size: $(du -h "$LOG_FILE" | cut -f1)"
    echo "Last modified: $(stat -c %y "$LOG_FILE" 2>/dev/null || stat -f %Sm "$LOG_FILE" 2>/dev/null)"
    
    echo "----------------------------------------"
    echo "Recent Log Entries (last 10 lines):"
    tail -10 "$LOG_FILE"
else
    echo "✗ Log file not found: $LOG_FILE"
fi

# 健康检查
echo "----------------------------------------"
echo "Health Check:"
if command -v curl >/dev/null 2>&1; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/ 2>/dev/null)
    if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "302" ] || [ "$HTTP_STATUS" = "404" ]; then
        echo "✓ HTTP service is responding (Status: $HTTP_STATUS)"
    else
        echo "✗ HTTP service is not responding (Status: $HTTP_STATUS)"
    fi
    
    # 检查健康检查端点
    HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health 2>/dev/null)
    if [ "$HEALTH_STATUS" = "200" ]; then
        echo "✓ Health endpoint is responding (Status: $HEALTH_STATUS)"
    else
        echo "⚠ Health endpoint status: $HEALTH_STATUS"
    fi
else
    echo "⚠ curl not available, skipping HTTP health check"
fi

# 总结状态
echo "=========================================="
echo "Status Summary:"

# 检查应用是否真正运行
APP_RUNNING=false
if [ -f "$PID_FILE" ]; then
    PID_FROM_FILE=$(cat "$PID_FILE")
    if kill -0 "$PID_FROM_FILE" 2>/dev/null; then
        APP_RUNNING=true
    fi
fi

if [ "$APP_RUNNING" = true ]; then
    echo "✓ Application Status: RUNNING"
    echo "✓ PID: $PID_FROM_FILE"
    echo "✓ Started at: $(ps -p "$PID_FROM_FILE" -o lstart= 2>/dev/null || echo "Unknown")"
    exit 0
else
    echo "✗ Application Status: NOT RUNNING"
    exit 1
fi
