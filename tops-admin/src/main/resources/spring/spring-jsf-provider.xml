<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd


	http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">

    <!--服务端配置服务端协议-->
    <jsf:server id="jsf" protocol="jsf" serialization="hessian"/>

    <!-- 仓配订单接单处理服务 -->
    <jsf:provider id="maintainService" interface="com.tops.order.service.ITopsMaintainService"
                  alias="${jsf.provider.maintainService.alias}" ref="topsMaintainServiceImpl"
                  timeout="${jsf.provider.maintainService.timeout}" delay="-1" server="jsf">
        <jsf:parameter key="token" value="2816ed2392804366b90f284baae6a378"/>
    </jsf:provider>
    <!-- 产品校验供给配置服务 -->
    <jsf:provider id="topsGetOrderService"
                  interface="com.tops.order.service.GetOrderService"
                  ref="getOrderServiceImpl" server="jsf"
                  alias="${jsf.provider.getOrder.alias}" register="true"
                  serialization="hessian">
        <jsf:parameter key="token" value="${jsf.provider.getOrder.token}" hide="true"/>
    </jsf:provider>

    <!-- 归因查询 -->
    <jsf:provider id="batrixTracerService" interface="com.tops.batrix.service.GetBatrixTracerLogService"
                  alias="${jsf.provider.batrixTracerService.alias}" ref="getBatrixTracerLogServiceImpl"
                  timeout="${jsf.provider.batrixTracerService.timeout}" delay="-1" server="jsf">
        <jsf:parameter key="token" value="jsf.provider.batrixTracerService.token"/>
    </jsf:provider>
</beans>
