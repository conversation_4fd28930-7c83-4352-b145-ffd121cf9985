--- # 临时文件存储位置 避免临时文件被系统清理报错
spring.servlet.multipart.location: /export/Log/jdl-ops

--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:9090/admin
  instance:
    service-host-type: IP
  username: tops
  password: 123456
# 当前环境
application:
  env: prod
--- # xxl-job 配置
xxl:
  job:
    admin:
      addresses: http://*************
    accessToken: xgDZtbr9P7Z84oN
    executor:
      appname: jdl-ops
      address:
      ip:
      port: 9999
      logpath: /export/Log/jdl-ops
      logretentiondays: 30

--- # 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: false
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          # jdbc 所有参数配置参考 https://Tops.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: ****************************************************************************************************************************************************************************************
          username: ${db.username}
          password: ${db.password}
        # 从库数据源
        slave:
          lazy: true
          type: ${spring.datasource.type}
          driverClassName: com.mysql.cj.jdbc.Driver
          url: ************************************************************************************************************************************************************************************************
          username:
          password:
        tidb:
          url: **********************************************************************************************************************
          username: order_admin
          password: cpOrder0815
        clickhouse:
          url: **********************************************************************************************************************
          username: batrix_tracer
          password: batrixTracer@123
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: *************************************
#          username: ROOT
#          password: root
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ******************************************************************************************************************************************
#          username: root
#          password: root
#        sqlserver:
#          type: ${spring.datasource.type}
#          driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: *******************************************************************************************************************
#          username: SA
#          password: root
      hikari:
        # 最大连接池数量
        maxPoolSize: 20
        # 最小空闲线程数量
        minIdle: 10
        # 配置获取连接等待超时的时间
        connectionTimeout: 30000
        # 校验超时时间
        validationTimeout: 5000
        # 空闲连接存活最大时间，默认10分钟
        idleTimeout: 600000
        # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
        maxLifetime: 1800000
        # 多久检查一次连接的活性
        keepaliveTime: 30000
  jdq:
    orderMonitor:
      username: C3818711b
      applicationDomainName: cporder.jd.com
      password: 8PuP9gUUFePCgyNF
      topic: jrdw-fk-t_order_main

--- # clickhouse 配置
spring:
  clickhouse:
    dbName: ${prod.clickhouse.db.name}
--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    host: redis-6cylaicd2lyd-proxy-nlb.jvessel-open-hb.jdcloud.com
    # 端口，默认为6379
    port: 6379
    password: ilJ2EJnt
    # 数据库索引
    database: 0
    # 密码(如没有密码请注释掉)
    # password:
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

redisson:
  # redis key前缀
  keyPrefix: jdl-tops-
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${tops.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: <EMAIL>
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: xxxxxxxxxx
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信 支持 阿里云 腾讯云 云片 等等各式各样的短信服务商
# https://wind.kim/doc/start 文档地址 各个厂商可同时使用
sms:
  # 阿里云 dysmsapi.aliyuncs.com
  alibaba:
    #请求地址 默认为 dysmsapi.aliyuncs.com 如无特殊改变可以不用设置
    requestUrl: dysmsapi.aliyuncs.com
    #阿里云的accessKey
    accessKeyId: xxxxxxx
    #阿里云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
  tencent:
    #请求地址默认为 sms.tencentcloudapi.com 如无特殊改变可不用设置
    requestUrl: sms.tencentcloudapi.com
    #腾讯云的accessKey
    accessKeyId: xxxxxxx
    #腾讯云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
    #短信sdkAppId
    sdkAppId: appid
    #地域信息默认为 ap-guangzhou 如无特殊改变可不用设置
    territory: ap-guangzhou
jsf:
  registry:
    provider: i.jsf.jd.com
  provider:
    maintainService:
      alias: tops-main
      timeout: 10000
    getOrder:
      alias: tops-prod
      token: 9d54aab3c99e
      threads: 1500
    batrixTracerService:
      alias: tops-prod
      timeout: 10000
      token:
  consumer:
    gw:
      alias: EDC_PRE_DATA_SERVICE
    jone:
      alias: joneapi
      token: InpeyljUjVajIjicoktiwepMy
      timeout: 20000
    grantService:
      alias: ee00
      token: timline.jd.com
    messagePushService:
      alias: ee00
      token: timline.jd.com
    oms:
      get:
        alias: jdl-order-search:0.0.1
        token: b9fc8f72149c48439c84f6bca5881696
        timeout: 1000
      search:
        alias: jdl-order-search:0.0.1
        token: 4bb708604fbb45879825df095ff265fc
        timeout: 3000
      searchScroll:
        alias: jdl-order-search:0.0.1
        token: da70937e1ce241e4a88701ba0b680398
        timeout: 3000
      batchGetOrderService:
        alias: jdl-order-search:0.0.1
        token: bada2337bfbf4490968d7591001a9ca5
        timeout: 3000
      relation:
        alias: jdl-order-relation:0.0.1
        token: 572434c24bfec02db4e9d167f57063cc
        timeout: 100
        retries: 1
        future-timeout: 200
      report:
        alias: jdl-order-search:0.0.1
        token: 34ceccc31ba44a0e977c088249ecf2bc
        timeout: 3000
      cancel:
        alias: jdl-oms
        token: Nh3HP0ar4A
        timeout: 1000
        retries: 2
      reaccept:
        alias: jdl-oms
        token: Nh3HP0ar4A
        timeout: 2000
        retries: 2
      callback:
        alias: jdl-oms
        token: Nh3HP0ar4A
        timeout: 2000
        retries: 2
      modify:
        alias: jdl-data-report-service-uat-c-prod:0.0.1
        token: A4B8C2D4E6F80123456789ABCDEF0123
        timeout: 2000
        retries: 2
      status:
        alias: jdl-order-search:0.0.1
        token: d074cdae63b4400b9b171683714ea7eb
        timeout: 3000
      getOrderModifyRecordService:
        alias: jdl-oms
        token: Nh3HP0ar4A
        timeout: 1000
        retries: 2
    waybill:
      query:
        alias: WAYBILL_OPEN
        timeout: 1000
      trace:
        alias: WAYBILL_OPEN
        timeout: 1000
    jd:
      orderMiddleware:
        alias: ioms-i18n-ht-s
        authToken: 174fbd3e-d696-4b14-bd66-a82004a339fe
        clientName: jdl-cp-oms
        timeout: 200
        retries: 2
      componentOrder:
        authToken: f6b2258a-1e9e-489c-b47a-325b2959f4b0
        clientName: ordertrack.360buy.com
        alias: ordersdk_component_xsrh_lf
        timeout: 500
    autobots:
      alias: llm-manage
      token: d0f64ac051d741aa8dff715791b31fea
      botId: 6247
      botSecret: 2713faf499d0461ab9b944bfb1a39099
    sequence:
      alias: cp-oms-pk:1.0.0
      timeout: 400
      token: p!k@t#o$k%e^n
    warehouse:
      query:
        alias: eclp:1.0.0
        timeout: 5000
        retries: 1
    sensitiveWordJsfService:
      query:
        alias: sensitiveword_log
        timeout: 1000
        retries: 2
    resumeOrderHandler:
      timeout: 5000
      alias: hub-prod:0.0.1
  open:
    api:
      alias: jsf-open-api
      timeout: 1000
      interfaces: "[{\"appName\":\"jdos_jdl-pms-service\",\"token\":\"SafVROYIftiNREZcoBmklaqwOppzSM\",\"interfaceName\":\"cn.jdl.pms.api.ProductRecommendationService\"},{\"appName\":\"jdos_jdl-pms-lpc\",\"token\":\"SafVROYIftiNREZcoBmklaqwOppzSM\",\"interfaceName\":\"com.jdl.prodcentre.api.service.ProductSupplyService\"},{\"appName\":\"jdos_jdl-pms-lpc\",\"token\":\"SafVROYIftiNREZcoBmklaqwOppzSM\",\"interfaceName\":\"com.jdl.prodcentre.api.service.ProductCheckService\"}]"
properties:
  dongdong:
    notice:
      id: ~me202005250755
    asp:
      id: 110.200.0000002.219
    secret: 23b358418a11f8c9f2a0f3abdb947ea1
ducc:
  app:
    name: jdos_jdl-ops
    token: f5f9425903cc4ead9d54aab3c99ee463
    namespace: jdl_ops
    hostPort: ducc.jd.local
    profile: prod
  http:
    env: prod
    url: http://ducc-api.jd.local/v1
spring:
  elasticsearch:
    rest:
      uris: http://es-nlb-es-aq10g5fqf1.jvessel-open-hb.jdcloud.com:9200
      connection-timeout: 10000
      request-timeout: 18000
      read-timeout: 18000
      username: cp_order
      password: cp_order!@0815
sso:
  clientId: lpc-web-manager
  clientSecret: 60b3b622618441508058bbadd8cb61ba
  endpoint: http://ssa.jd.com
  excludePath: /static,/rest,/duty/dutyInfo/list,/sse/**,/mcp-service/**
  serviceIndex: i.jsf.jd.local
  serviceAlias: bj-online
  callbackUri: http://trade.jdl.com/
  cookieName: jdl-trade-ops
admin:
  erp:
    set: weiliming,wangqin83,zhuhongru1
role:
  id:
    normal: 2
autoBots:
  callbackUrl: http://trade.jdl.com/prod-api/tops/autoBots/
# TODO wangqin83 待更新为生产配置
xbp:
  jsf:
    alias: PROD
    timeout: 1000
    retries: 3
    api:
      sign: bbbf01254a
      user: jdl_trade_ops
  jmq:
    consumer:
      topic: XBP_EVENT
      retransmit: trade_ops_xbp_prod
    producer:
      retransmit-pre: trade_ops_xbp_pre
      retransmit-prod: trade_ops_xbp_prod
  custom:
    processIds:
      cancelOrders: 21914
      callbackOrders: 23119
      modifyOrders: 23116
      reacceptOrders: 23121
      shutdown: 23701
      scaleUp: 23703
      duccModify: 1111111
jmq4:
  consumer:
    address: nameserver.jmq.jd.local:80
    user: jdlops
    password: 06c8ac06fd7e4b8e822750b18646486e
    app: jdlops
    epoll: false
  producer:
    retryTimes: 3
  topic:
    exception-center: exception_data_broadcast

aces:
  order:
    token: eyJzaWciOiJzN0pDR0ZCVWVDQmkwZDk3WkxNZTl1c2c4NjdaMEE5WXk3TEh3VEtrb0pNMExsYUhrckxXWkp3RXNnS3VYVXdQYTIrRmFSY3dQejU2eGswVmVRZGxYbmtoZVVEb3pyTWNFZmpuTHVVTC9qNWpGeHVFS3l0akUzS0FuQWZlMXdwOE91NDlvcW5leHNZdkRkckpXZHZ1ZmJPK2xMcTd4OTBWeTdybnhwRytnK2k0U3BlNW9UK2JTY1Y2emxKWlZ0QllxcjF4eW9FcWY5dGpDUWgrSk5md2hCSkU5UExRYUpCc3dHdDBhVzJGY1h4T2lidGNsS1dBZW44Nkl6SnNiMHA2cEVPdDhNdCt3REQxZzJ2REtNaEZyYmtzVzRDcHEwSkpFcW4vMkhVejdUN05MZ1lVV0JTc29mb1IxQzVzWVp1YU5NbHlScXBzN2o5aG1YazkzU3hHNkE9PSIsImRhdGEiOnsiYWN0IjoiY3JlYXRlIiwiZWZmZWN0aXZlIjoxNjkzNzU2ODAwMDAwLCJleHBpcmVkIjoxOTc3ODQwMDAwMDAwLCJpZCI6Ik1XVmlaV1EyTjJZdE1tUXdNeTAwTURObExUZzJNRGt0TWprNFlUUXlaRE14WVRrMSIsImtleSI6IkNWZDlkN012Mk1ydW43YWhjVmZpLzltZ0NUdUhTeTNJYnBWeitCbTg1ME09Iiwic2VydmljZSI6ImJhdHJpeF9mbG93cGxhdF9JdDd4NmhmZyIsInN0eXBlIjoxfSwiZXh0ZXJuYWxEYXRhIjp7InpvbmUiOiJDTi0wIn19
    rPath:
    isProd: true
easyops:
  host: uat.main.delta.jd.local
  token: jdl-ops
jdos:
  api:
    host: api.jcd-gateway.jd.com
    tenant: JDD
    erp: org.wljy1
    token: 0dc91b27-ff6b-44e6-8785-cc919047f0d8
pfinder:
  api:
    host: pfinder-api-open.jd.com
    token: ********************************
tops:
  jdos:
    datasource:
      mode: mysql
ump:
  api:
    host: open.ump.jd.com
    token: ********************************
trace:
  pFinder:
    period:
      statistic:
        count:
          max: 1000
  component:
    name:
      length:
        max: 250
sse:
  mcp:
    port: 8989
mcp:
  scanner:
    package: com.tops.mcp.server
