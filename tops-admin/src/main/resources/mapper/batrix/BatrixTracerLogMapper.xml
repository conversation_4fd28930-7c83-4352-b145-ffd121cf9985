<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tops.batrix.mapper.BatrixTracerLogMapper">
    <resultMap id="BaseResultMap" type="com.tops.batrix.dto.BatrixTracerLog">
        <result column="linkId" jdbcType="VARCHAR" property="linkId"/>
        <result column="appName" jdbcType="VARCHAR" property="appName"/>
        <result column="uri" jdbcType="VARCHAR" property="uri"/>
        <result column="businessUnit" jdbcType="VARCHAR" property="businessUnit"/>
        <result column="businessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="businessScene" jdbcType="VARCHAR" property="businessScene"/>
        <result column="nodeCode" jdbcType="VARCHAR" property="nodeCode"/>
        <result column="nodeName" jdbcType="VARCHAR" property="nodeName"/>
        <result column="nodeStatus" jdbcType="VARCHAR" property="nodeStatus"/>
        <result column="nodeGroup" jdbcType="VARCHAR" property="nodeGroup"/>
        <result column="nodeInParam" jdbcType="VARCHAR" property="nodeInParam"/>
        <result column="nodeOutParam" jdbcType="VARCHAR" property="nodeOutParam"/>
        <result column="nodeExt" jdbcType="VARCHAR" property="nodeExt"/>
        <result column="nodeOrder" jdbcType="INTEGER" property="nodeOrder"/>
        <result column="className" jdbcType="VARCHAR" property="className"/>
        <result column="threadName" jdbcType="VARCHAR" property="threadName"/>
        <result column="methodName" jdbcType="VARCHAR" property="methodName"/>
        <result column="logLevel" jdbcType="VARCHAR" property="logLevel"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="costTime" jdbcType="INTEGER" property="costTime"/>
        <result column="createTimestamp" jdbcType="BIGINT" property="createTimestamp"/>
    </resultMap>
    <select id="selectTracerLogList" resultType="com.tops.batrix.dto.BatrixTracerLog">
        select
        log.linkId,log.appName,log.uri,log.businessUnit,log.businessType,log.businessScene,log.nodeCode,
        log.nodeName,log.nodeGroup,log.nodeInParam,log.nodeOutParam,log.nodeExt,log.nodeOrder,
        log.className,log.threadName,log.methodName,log.logLevel,log.content,log.createTime,log.costTime,log.createTimestamp
        from
        ${dbName}.tracer_log_hot_cold_dis log
        where
        log.appName = #{dto.appCode}
        <if test="dto.uri != null and dto.uri != ''">
            and log.uri = #{dto.uri}
        </if>
        <if test="linkId != null and linkId != ''">
            and log.linkId = #{linkId}
        </if>
        <if test="dto.startTimeDate != null">
            and log.createTime >= #{dto.startTimeDate}
        </if>
        <if test="dto.endTimeDate != null">
            and  #{dto.endTimeDate}>= log.createTime
        </if>
    </select>
</mapper>
