<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tops.batrix.mapper.BatrixTracerModelMapper">
    <resultMap id="BaseResultMap" type="com.tops.batrix.dto.BatrixTracerModel">
        <result column="linkId" jdbcType="VARCHAR" property="linkId"/>
        <result column="appName" jdbcType="VARCHAR" property="appName"/>
        <result column="upstreamAppName" jdbcType="VARCHAR" property="upstreamAppName"/>
        <result column="userIp" jdbcType="VARCHAR" property="userIp"/>
        <result column="serverIp" jdbcType="VARCHAR" property="serverIp"/>
        <result column="uri" jdbcType="VARCHAR" property="uri"/>
        <result column="requestContent" jdbcType="VARCHAR" property="requestContent"/>
        <result column="responseContent" jdbcType="VARCHAR" property="responseContent"/>
        <result column="bizId" jdbcType="VARCHAR" property="bizId"/>
        <result column="traceId" jdbcType="VARCHAR" property="traceId"/>
        <result column="businessUnit" jdbcType="VARCHAR" property="businessUnit"/>
        <result column="businessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="businessScene" jdbcType="VARCHAR" property="businessScene"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="createTimestamp" jdbcType="BIGINT" property="createTimestamp"/>
        <result column="costTime" jdbcType="INTEGER" property="costTime"/>
    </resultMap>

    <select id="selectTracerModelList" resultType="com.tops.batrix.dto.BatrixTracerModel">
        select
            mod.linkId,mod.appName,mod.upstreamAppName,mod.userIp,mod.serverIp,mod.uri,mod.requestContent,
            mod.responseContent,mod.bizId,mod.traceId,mod.businessUnit,mod.businessType,mod.businessScene,
            mod.linkStatus,mod.createTime,mod.createTimestamp,mod.costTime,mod.intoDbTime,mod.intoDbTimestamp
        from
            ${dbName}.tracer_model_hot_cold_dis mod
        where
            mod.appName = #{dto.appCode}
        <if test="dto.uri != null and dto.uri != ''">
          and mod.uri = #{dto.uri}
        </if>
        <if test="dto.traceId != null">
            and mod.traceId = #{dto.traceId}
        </if>
        <if test="dto.bizId != null">
            and mod.bizId = #{dto.bizId}
        </if>
        <if test="dto.startTimeDate != null">
            and mod.createTime >= #{dto.startTimeDate}
        </if>
        <if test="dto.endTimeDate != null">
            and #{dto.endTimeDate}>= mod.createTime
        </if>
    </select>

    <select id="selectByAppUriLinkId" resultType="com.tops.batrix.dto.BatrixTracerModel">
        select
            mod.linkId,mod.appName,mod.upstreamAppName,mod.userIp,mod.serverIp,mod.uri,mod.requestContent,
            mod.responseContent,mod.bizId,mod.traceId,mod.businessUnit,mod.businessType,mod.businessScene,
            mod.linkStatus,mod.createTime,mod.createTimestamp,mod.costTime,mod.intoDbTime,mod.intoDbTimestamp
        from
            ${clickhouseDbName}.tracer_model_hot_cold_dis mod
        where
            mod.appName = #{appName}
          and mod.uri = #{uri}
          and mod.linkId = #{linkId}
    </select>

</mapper>
