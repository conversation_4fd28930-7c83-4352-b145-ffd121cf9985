# JDL Trade Ops 域名配置文件
# 适用于放置在 nginx/conf/domains/ 目录下

server {
    listen 80;
    server_name ************ localhost;

    # 日志配置
    access_log /var/log/nginx/jdl-trade-ops.access.log;
    error_log /var/log/nginx/jdl-trade-ops.error.log;

    # MCP服务器路径配置 - 优先级最高
    location /sse/mcp-server {
        proxy_pass http://127.0.0.1:8989;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE相关配置
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
        
        # 超时设置 - SSE需要长连接
        proxy_connect_timeout 30s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 默认反向代理配置 - 所有其他请求
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }

    # 健康检查端点（可选）
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 错误页面配置
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 可选：HTTPS配置（如果需要SSL）
# server {
#     listen 443 ssl;
#     server_name ************ localhost;
#     
#     ssl_certificate /path/to/certificate.crt;
#     ssl_certificate_key /path/to/private.key;
#     
#     # MCP服务器路径配置
#     location /sse/mcp-server {
#         proxy_pass http://127.0.0.1:8989;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         
#         # SSE相关配置
#         proxy_buffering off;
#         proxy_cache off;
#         proxy_set_header Connection '';
#         proxy_http_version 1.1;
#         chunked_transfer_encoding off;
#         
#         proxy_connect_timeout 30s;
#         proxy_send_timeout 300s;
#         proxy_read_timeout 300s;
#     }
#     
#     # 默认反向代理配置
#     location / {
#         proxy_pass http://127.0.0.1:8080;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
# }