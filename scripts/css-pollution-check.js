#!/usr/bin/env node

/**
 * CSS样式污染检查脚本
 * 用于检测和验证CSS样式冲突问题
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class CSSPollutionChecker {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.srcPath = path.join(__dirname, '../tops-ui-vue3/src');
  }

  /**
   * 运行所有检查
   */
  async runAllChecks() {
    console.log('🔍 开始CSS样式污染检查...\n');
    
    await this.checkImportantOveruse();
    await this.checkScopedUsage();
    await this.checkGlobalSelectors();
    await this.checkStyleConflicts();
    await this.checkBrowserCompatibility();
    
    this.generateReport();
  }

  /**
   * 检查!important过度使用
   */
  async checkImportantOveruse() {
    console.log('📋 检查!important过度使用...');
    
    const files = glob.sync('**/*.{vue,scss,css}', { cwd: this.srcPath });
    
    for (const file of files) {
      const filePath = path.join(this.srcPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 统计!important使用次数
      const importantMatches = content.match(/!important/g) || [];
      
      if (importantMatches.length > 5) {
        this.issues.push({
          type: 'important-overuse',
          file,
          count: importantMatches.length,
          message: `文件中使用了${importantMatches.length}次!important，可能存在样式冲突`
        });
      }
    }
  }

  /**
   * 检查scoped属性使用
   */
  async checkScopedUsage() {
    console.log('📋 检查scoped属性使用...');
    
    const vueFiles = glob.sync('**/*.vue', { cwd: this.srcPath });
    
    for (const file of vueFiles) {
      const filePath = path.join(this.srcPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 检查是否有style标签但没有scoped
      const styleMatches = content.match(/<style[^>]*>/g) || [];
      
      for (const styleTag of styleMatches) {
        if (!styleTag.includes('scoped') && !file.includes('layout/') && !file.includes('components/')) {
          this.warnings.push({
            type: 'missing-scoped',
            file,
            message: '页面组件缺少scoped属性，可能导致样式污染'
          });
        }
      }
    }
  }

  /**
   * 检查全局选择器
   */
  async checkGlobalSelectors() {
    console.log('📋 检查全局选择器...');
    
    const scssFiles = glob.sync('assets/styles/*.scss', { cwd: this.srcPath });
    
    const dangerousSelectors = [
      '.el-form-item',
      '.el-input',
      '.el-button',
      '.el-table',
      'div',
      'span',
      'p'
    ];
    
    for (const file of scssFiles) {
      const filePath = path.join(this.srcPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      for (const selector of dangerousSelectors) {
        const regex = new RegExp(`^\\s*${selector.replace('.', '\\.')}\\s*{`, 'gm');
        if (regex.test(content)) {
          this.warnings.push({
            type: 'dangerous-global-selector',
            file,
            selector,
            message: `使用了过于宽泛的全局选择器: ${selector}`
          });
        }
      }
    }
  }

  /**
   * 检查样式冲突
   */
  async checkStyleConflicts() {
    console.log('📋 检查样式冲突...');
    
    const conflicts = [
      {
        pattern: /width:\s*200px/g,
        files: [],
        message: '多个文件定义了相同的宽度值，可能需要统一管理'
      },
      {
        pattern: /\.el-form-item__content\s*{[^}]*width:/g,
        files: [],
        message: '多个文件修改了表单项内容宽度，可能导致冲突'
      }
    ];
    
    const files = glob.sync('**/*.{vue,scss}', { cwd: this.srcPath });
    
    for (const file of files) {
      const filePath = path.join(this.srcPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      for (const conflict of conflicts) {
        if (conflict.pattern.test(content)) {
          conflict.files.push(file);
        }
      }
    }
    
    for (const conflict of conflicts) {
      if (conflict.files.length > 2) {
        this.issues.push({
          type: 'style-conflict',
          files: conflict.files,
          message: conflict.message
        });
      }
    }
  }

  /**
   * 检查浏览器兼容性
   */
  async checkBrowserCompatibility() {
    console.log('📋 检查浏览器兼容性...');
    
    const files = glob.sync('**/*.{vue,scss,css}', { cwd: this.srcPath });
    
    const incompatibleFeatures = [
      { pattern: /:has\(/g, feature: ':has() 伪类', support: 'Chrome 105+, Firefox 121+' },
      { pattern: /container-query/g, feature: 'Container Queries', support: 'Chrome 105+, Firefox 110+' },
      { pattern: /@layer/g, feature: 'CSS Cascade Layers', support: 'Chrome 99+, Firefox 97+' }
    ];
    
    for (const file of files) {
      const filePath = path.join(this.srcPath, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      for (const feature of incompatibleFeatures) {
        if (feature.pattern.test(content)) {
          this.warnings.push({
            type: 'compatibility-issue',
            file,
            feature: feature.feature,
            support: feature.support,
            message: `使用了兼容性较差的CSS特性: ${feature.feature} (${feature.support})`
          });
        }
      }
    }
  }

  /**
   * 生成检查报告
   */
  generateReport() {
    console.log('\n📊 CSS样式污染检查报告');
    console.log('='.repeat(50));
    
    if (this.issues.length === 0 && this.warnings.length === 0) {
      console.log('✅ 未发现样式污染问题！');
      return;
    }
    
    if (this.issues.length > 0) {
      console.log(`\n🚨 发现 ${this.issues.length} 个严重问题:`);
      this.issues.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.message}`);
        if (issue.file) {
          console.log(`   文件: ${issue.file}`);
        }
        if (issue.files) {
          console.log(`   涉及文件: ${issue.files.join(', ')}`);
        }
        if (issue.count) {
          console.log(`   数量: ${issue.count}`);
        }
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️  发现 ${this.warnings.length} 个警告:`);
      this.warnings.forEach((warning, index) => {
        console.log(`\n${index + 1}. ${warning.message}`);
        console.log(`   文件: ${warning.file}`);
        if (warning.feature) {
          console.log(`   特性: ${warning.feature}`);
          console.log(`   支持: ${warning.support}`);
        }
        if (warning.selector) {
          console.log(`   选择器: ${warning.selector}`);
        }
      });
    }
    
    console.log('\n📋 修复建议:');
    console.log('1. 减少!important的使用，通过提高选择器特异性解决冲突');
    console.log('2. 为页面组件添加scoped属性');
    console.log('3. 避免使用过于宽泛的全局选择器');
    console.log('4. 将重复的样式定义提取到公共样式文件中');
    console.log('5. 使用兼容性更好的CSS特性');
    
    console.log('\n🎯 优先修复:');
    const highPriorityIssues = this.issues.filter(issue => 
      issue.type === 'important-overuse' || issue.type === 'style-conflict'
    );
    
    if (highPriorityIssues.length > 0) {
      console.log(`需要优先修复 ${highPriorityIssues.length} 个高优先级问题`);
    } else {
      console.log('无高优先级问题需要立即修复');
    }
  }
}

// 运行检查
if (require.main === module) {
  const checker = new CSSPollutionChecker();
  checker.runAllChecks().catch(console.error);
}

module.exports = CSSPollutionChecker;
